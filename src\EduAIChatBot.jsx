import React, { useState, useEffect, useRef } from "react";
import { getDoc, doc } from 'firebase/firestore';
import Faq from './Faq';
import Exams from "./Exams";
import Coding from "./Coding";
import { auth, db } from './firebaseConfig';
import axios from "axios";
import { sidebarItems } from './sidebarItems';
import { onAuthStateChanged } from 'firebase/auth';
import globalStyles from './styles.js';
import {
  FiMenu, FiX, FiChevronDown, FiChevronRight, FiFileText,
  FiCode, FiHelpCircle, FiAward, FiBook, FiUser, FiShield,
  FiSearch, FiUpload, FiLogIn, FiLogOut,
  FiBriefcase, FiBarChart2, FiLayers, FiCheckCircle, FiExternalLink,
  FiHeart, FiClock, FiRefreshCw
} from "react-icons/fi";
import { createClient } from '@supabase/supabase-js';
import { Bar } from 'react-chartjs-2';
import { Chart, BarElement, CategoryScale, LinearScale, Tooltip, Legend } from 'chart.js';
import ReactMarkdown from 'react-markdown';

Chart.register(BarElement, CategoryScale, LinearScale, Tooltip, Legend);

// Enhanced sidebar items with icons
const updatedSidebarItems = sidebarItems.map(item => {
  const iconMap = {
    "resume": <FiFileText />,
    "dsa": <FiCode />,
    "coding": <FiLayers />,
    "resources": <FiBriefcase />,
    "quizzes": <FiCheckCircle />,
    "aptitude": <FiBarChart2 />,
    "academics": <FiBook />,
    "faq": <FiHelpCircle />,
    "admin": <FiShield />
  };

  return {
    ...item,
    icon: iconMap[item.tab] || iconMap[item.title.toLowerCase()] || <FiAward />
  };
});

const EduAIChatBot = () => {
  // State declarations
  const [input, setInput] = useState("");
  const [messages, setMessages] = useState([]);
  const [userId, setUserId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [knowledge, setKnowledge] = useState("");
  const [activeTab, setActiveTab] = useState("dashboard");
  const [searchTerm, setSearchTerm] = useState("");
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [expandedMenus, setExpandedMenus] = useState({});
  const [user, setUser] = useState(null);

  const [resumeUploadLoading, setResumeUploadLoading] = useState(false);
  const [resumeUrl, setResumeUrl] = useState(null);
  const [resourceUploadLoading, setResourceUploadLoading] = useState(false);
  const [userResources] = useState([]);
  const ADMIN_EMAIL = '<EMAIL>';
  const [allUsers] = useState([]);
  const [adminTab, setAdminTab] = useState('users');
  const [notification, setNotification] = useState(null);
  const [activityLog, setActivityLog] = useState([]);
  const [resourcesTab, setResourcesTab] = useState('general'); // New state for resources tab
  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1024); // Responsive state
  const chatEndRef = useRef(null);

  // Optimized window resize handler with throttling
  useEffect(() => {
    let timeoutId = null;
    const handleResize = () => {
      if (timeoutId) clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setWindowWidth(window.innerWidth);
      }, 100); // Throttle to 100ms
    };

    window.addEventListener('resize', handleResize, { passive: true });
    return () => {
      window.removeEventListener('resize', handleResize);
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, []);

  // Enhanced DSA section states
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [favoriteCompanies, setFavoriteCompanies] = useState([]);
  const [recentCompanies, setRecentCompanies] = useState([]);
  const [showRevertButton, setShowRevertButton] = useState(true);

  // API configurations
  const API_KEY = "AIzaSyC6kHWto78QdqHz7Uu9RzEXb443ZO7tG5M";
  const SUPABASE_URL = 'https://gziaptswfepiveyylven.supabase.co';
  const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imd6aWFwdHN3ZmVwaXZleXlsdmVuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU2NzczNTYsImV4cCI6MjA2MTI1MzM1Nn0.wmqXZGffrox8E_PuCwbzh4xJEffsvFmZCVcF6WFAX6Q';
  const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

  // Company categories for enhanced DSA section
  const companyCategories = {
    'FAANG': ['Facebook', 'Apple', 'Amazon', 'Netflix', 'Google'],
    'Big Tech': ['Microsoft', 'Adobe', 'Salesforce', 'Oracle', 'IBM', 'Intel', 'Nvidia'],
    'Startups': ['Stripe', 'Airbnb', 'Uber', 'Lyft', 'DoorDash', 'Instacart', 'Coinbase'],
    'Finance': ['Goldman Sachs', 'JPMorgan', 'Morgan Stanley', 'BlackRock', 'Citadel', 'Two Sigma'],
    'Consulting': ['McKinsey', 'BCG', 'Bain', 'Deloitte', 'Accenture', 'PwC'],
    'E-commerce': ['Amazon', 'eBay', 'Shopify', 'Etsy', 'Wayfair', 'Booking.com'],
    'Gaming': ['Blizzard', 'Epic Games', 'Riot Games', 'Unity', 'Roblox'],
    'Indian': ['TCS', 'Infosys', 'Wipro', 'HCL', 'Flipkart', 'Paytm', 'Zomato', 'Swiggy']
  };

  // Complete list of companies
  const companies = [
     "Accenture", "Accolite", "Adobe", "Affirm", "Agoda", "Airbnb", "Airtel",
    "Akamar", "Akuna Capital", "Alibaba", "Altimetrik", "Amazon", "AMD",
    "Amdocs", "American Express", "Anduril", "Apple", "Arista Networks",
    "Arcesium", "Atlassian", "Attentive", "athenahealth", "Autodesk",
    "Avito", "Baidu", "Barclays", "BitGo", "BlackRock", "Blizzard",
    "Block", "Bloomberg", "BNY Mellon", "Boft", "Booking.com", "Bos",
    "BP", "ByteDance", "Cadence", "Capgemini", "Capital One", "CARS24",
    "carwale", "Cashfree", "Chewy", "Cisco", "Citadel", "Citrix",
    "Cloudera", "Cloudflare", "Cognizant", "Coinbase", "Commvault",
    "Confluent", "Coupang", "Coursera", "CrowdStrike", "Cruise",
    "Curefit", "Databricks", "Datadog", "DE Shaw", "Deloitte", "Dell",
    "Deliveroo", "Derantior", "Deutsche Bank", "Devflev", "Directi",
    "Disney", "Docusign", "DoorDash", "Dream11", "Dropbox", "DRW",
    "Dunzo", "eBay", "EPAM Systems", "Epic Systems", "Expedia",
    "FactSet", "Flexport", "Flipkart", "Freshworks", "GE Healthcare",
    "Geico", "Goldman Sachs", "Google", "Grab", "Grammarly", "Graviton",
    "Groww", "GSN Games", "Hashedin", "HCL", "HPE", "Hubspot",
    "Hudson River Trading", "Huawei", "IBM", "IMC", "Indeed", "Infosys",
    "InMobi", "Intel", "Intuit", "JPMorgan", "Jane Street",
    "Josh Technology", "Jump Trading", "Juspay", "Karat", "KLA",
    "LinkedIn", "LiveRamp", "Lowe's", "Lucid", "Lyft", "MakeMyTrip",
    "Mastercard", "MathWorks", "Media.net", "Meesho", "Mercari", "Meta",
    "Microsoft", "Millennium", "Mitsogo", "Moloco", "MongoDB",
    "Morgan Stanley", "Moveworks", "Myntra", "Nagarro", "NetApp",
    "Netflix", "Nextdoor", "Nielsen", "Nike", "Niantic", "Nordstrom",
    "Nutanix", "Nvidia", "Okta", "OKX", "OpenAI", "OpenText", "Oracle",
    "Otter.ai", "Oyo", "Ozon", "Palantir Technologies", "Palo Alto Networks",
    "PayPal", "Paytm", "Persistent Systems", "PhonePe", "Pinterest",
    "Pocket Gems", "Point72", "Pure Storage", "Qualcomm", "Quora",
    "Rakuten", "Razorpay", "RBC", "Reddit", "Revolut", "Robinhood",
    "Roblox", "Rubrik", "Salesforce", "Samsung", "SAP", "ServiceNow",
    "Shopify", "Siemens", "Sigmoid", "SIG", "Snowflake", "Snap", "Sofi",
    "Splunk", "Spotify", "Sprinklr", "Squarepoint Capital", "Stripe",
    "Swiggy", "TCS", "Tekion", "Tencent", "Tesla", "ThoughtSpot",
    "ThoughtWorks", "TikTok", "Tinkoff", "Trilogy", "Turing", "Turo",
    "Twilio", "Twitch", "Two Sigma", "Uber", "UiPath", "UKG",
    "Veeva Systems", "Verily", "Verkada", "Virtu Financial", "Visa",
    "VK", "VMware", "Walmart Labs", "WarnerMedia", "Wayfair",
    "Wells Fargo", "Wipro", "Wix", "Workday", "X", "Yahoo", "Yandex",
    "Yelp", "Zalando", "Zenefits", "Zepto", "Zeta", "Zillow", "Zoho",
    "Zomato", "ZScaler", "Zopsmart"
  ];

  // Quiz buttons data
  const quizButtons = [
    {
      title: "OP and CN Quiz",
      description: "Test your knowledge of Operating System and Computer Networks",
      link: "https://opcn.netlify.app",
    },
    {
      title: "OOPs and DBMS Quiz",
      description: "Challenge yourself with oops and dbms",
      link: "https://oopsanddbms.netlify.app/",
    },
    {
      title: "System Design Quiz",
      description: "Test your system design knowledge",
      link: "https://system-design041.netlify.app",
    },
    {
      title: "Quantitative Aptitude and Reasoning Quiz",
      description: "Practice common quant and reasoning questions",
      link: "https://quantandreasoning.netlify.app",
    },
    {
      title: "Cloud & DevOps Quiz",
      description: "Test your knowledge of Cloud and DevOps concepts",
      link: "https://cloud-devops.netlify.app",
    },
    {
      title: "DSA Quiz",
      description: "Data Structures and Algorithms quiz",
      link: "https://dsa041.netlify.app",
    },
    {
      title: "Operating System & Computer Networks Quiz",
      description: "Quiz on OS and Computer Networks",
      link: "https://opcn.netlify.app",
    },
     {
      title: "Web Development Quiz",
      description: "Quiz on Web Development topics",
      link: "https://web-dev041.netlify.app",

    },
  ];

  // Use centralized styles
  const styles = {
    ...globalStyles,
    appContainer: {
      ...globalStyles.appContainer,
      backgroundColor: globalStyles.currentTheme.background,
      color: globalStyles.currentTheme.text,
    },
    navbar: {
      ...globalStyles.navbarFixed,
      borderBottom: `1px solid ${globalStyles.currentTheme.border}`
    },
    sidebar: {
      ...globalStyles.sidebarFixed,
      backgroundColor: globalStyles.currentTheme.surface,
      borderRight: `1px solid ${globalStyles.currentTheme.border}`,
      transform: sidebarOpen ? 'translateX(0)' : 'translateX(-100%)',
    },
    sidebarItem: {
      ...globalStyles.sidebarItemEdu,
      color: globalStyles.currentTheme.text,
      background: globalStyles.currentTheme.surface,
      border: `1px solid ${globalStyles.currentTheme.border}`,
      transition: 'all 0.3s ease',
      '&:hover': {
        background: globalStyles.currentTheme.primary,
        color: 'white'
      }
    },
    sidebarItemActive: {
      ...globalStyles.sidebarItemActiveEdu,
      color: 'white',
      background: globalStyles.currentTheme.primary,
      border: `1px solid ${globalStyles.currentTheme.primary}`,
    },
    mainContent: {
      ...globalStyles.mainContentEdu,
      marginLeft: sidebarOpen ? '280px' : '0',
      backgroundColor: globalStyles.currentTheme.background,
      color: globalStyles.currentTheme.text,
      minHeight: '100vh'
    },
    card: {
      ...globalStyles.cardEdu,
      backgroundColor: globalStyles.currentTheme.surface,
      color: globalStyles.currentTheme.text,
      border: `1px solid ${globalStyles.currentTheme.border}`,
      boxShadow: `0 4px 6px ${globalStyles.currentTheme.shadow}`
    },
    buttonPrimary: {
      ...globalStyles.buttonPrimary,
    },
    inputField: {
      ...globalStyles.inputField,
      backgroundColor: '#fff',
      color: '#333',
      border: '1px solid #ddd',
      '&:focus': {
        borderColor: globalStyles.currentTheme.primary,
        outline: 'none',
        boxShadow: `0 0 0 2px ${globalStyles.currentTheme.primary}20`
      }
    },
    chatBubbleUser: {
      ...globalStyles.chatBubbleUser,
      backgroundColor: globalStyles.currentTheme.primary,
      color: 'white'
    },
    chatBubbleBot: {
      ...globalStyles.chatBubbleBot,
      backgroundColor: globalStyles.currentTheme.secondary,
      color: globalStyles.currentTheme.text,
      border: '1px solid transparent'
    },
    companyCard: {
      ...globalStyles.companyCardEdu,
    },
    quizCard: {
      ...globalStyles.quizCardEdu,
      backgroundColor: globalStyles.currentTheme.surface,
    },
    notification: {
      ...globalStyles.notification,
    }
  };

  // Helper function to apply styles with hover states
  const getStyle = (styleName, hover = false) => {
    const baseStyle = styles[styleName];
    if (typeof baseStyle === 'function') return baseStyle();
    if (hover && baseStyle['&:hover']) {
      return { ...baseStyle, ...baseStyle['&:hover'] };
    }
    return baseStyle;
  };

  // Fetch user profile
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (user) {
        setUserId(user.uid);
      } else {
        console.log('User is not authenticated');
        setLoading(false);
      }
    });
    return () => unsubscribe();
  }, []);

  useEffect(() => {
    if (userId) {
      const fetchUserProfile = async () => {
        const userRef = doc(db, "users", userId);
        const userDoc = await getDoc(userRef);

        if (userDoc.exists()) {
          const userData = userDoc.data();
          // Profile pic functionality can be added later
          console.log("User data loaded:", userData.dp);
        } else {
          console.log("No such user!");
        }
        setLoading(false);
      };
      fetchUserProfile();
    }
  }, [userId]);

  // Fetch training data
  useEffect(() => {
    fetch("/training-data.txt")
      .then((res) => res.text())
      .then((data) => setKnowledge(data))
      .catch((err) => console.error("Failed to load training data:", err));
  }, []);

  // Supabase auth state
  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user || null);
    });
    const { data: listener } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user || null);
    });
    return () => {
      listener?.subscription.unsubscribe();
    };
  }, [supabase.auth]);

  // Handle resume upload
  const handleResumeUpload = async (e) => {
    const file = e.target.files[0];
    if (!file || !user) return;
    setResumeUploadLoading(true);
    const filePath = `${user.id}/${file.name}`;
    const { error } = await supabase.storage.from('resumes').upload(filePath, file, { upsert: true });
    if (!error) {
      const { data: urlData } = supabase.storage.from('resumes').getPublicUrl(filePath);
      setResumeUrl(urlData.publicUrl);
      showNotification('Resume uploaded successfully!', 'success');
      logActivity('Uploaded a resume');
    } else {
      showNotification('Resume upload failed.', 'error');
    }
    setResumeUploadLoading(false);
  };

  // Handle resource upload
  const handleResourceUpload = async (e) => {
    const file = e.target.files[0];
    if (!file || !user) return;
    setResourceUploadLoading(true);
    const filePath = `${user.id}/${file.name}`;
    const { error } = await supabase.storage.from('resources').upload(filePath, file, { upsert: true });
    if (!error) {
      showNotification('Resource uploaded!', 'success');
      logActivity(`Uploaded resource: ${file.name}`);
    } else {
      showNotification('Resource upload failed.', 'error');
    }
    setResourceUploadLoading(false);
  };

  // Enhanced company click handler
  const handleCompanyClick = (company) => {
    // Add to recent companies
    setRecentCompanies(prev => {
      const filtered = prev.filter(c => c !== company);
      return [company, ...filtered].slice(0, 5); // Keep only 5 recent
    });

    logActivity(`Viewed ${company} DSA questions`);

    if (company.toLowerCase() === 'microsoft') {
      window.location.href = '/company-dsa/Microsoft_questions.html';
      return;
    }
    const formattedCompany = company.replace(/\s+/g, '');
    window.location.href = `/company-dsa/${formattedCompany}.html`;
  };

  // Toggle favorite company
  const toggleFavorite = (company, e) => {
    e.stopPropagation(); // Prevent company click
    setFavoriteCompanies(prev => {
      if (prev.includes(company)) {
        return prev.filter(c => c !== company);
      } else {
        return [...prev, company];
      }
    });
  };

  // Revert header color changes
  const revertHeaderChanges = () => {
    setShowRevertButton(false);
    showNotification('Header text color reverted to theme default!', 'success');

    // Actually revert the header text color by updating the DOM
    const eduNovaElement = document.querySelector('[data-header-title]');
    const subtitleElement = document.querySelector('[data-header-subtitle]');

    if (eduNovaElement) {
      eduNovaElement.style.color = '#333';
    }
    if (subtitleElement) {
      subtitleElement.style.color = '#333';
    }
  };

  // Get filtered companies based on category and search
  const getFilteredCompanies = () => {
    let filtered = companies;

    // Filter by category
    if (selectedCategory !== 'all') {
      const categoryCompanies = companyCategories[selectedCategory] || [];
      filtered = filtered.filter(company =>
        categoryCompanies.some(catCompany =>
          company.toLowerCase().includes(catCompany.toLowerCase()) ||
          catCompany.toLowerCase().includes(company.toLowerCase())
        )
      );
    }

    // Filter by search term
    filtered = filtered.filter(company =>
      company.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Sort companies
    if (sortBy === 'name') {
      filtered.sort();
    } else if (sortBy === 'favorites') {
      filtered.sort((a, b) => {
        const aFav = favoriteCompanies.includes(a);
        const bFav = favoriteCompanies.includes(b);
        if (aFav && !bFav) return -1;
        if (!aFav && bFav) return 1;
        return a.localeCompare(b);
      });
    }

    return filtered;
  };

  // Open quiz link
  const openQuizLink = (url) => {
    window.open(url, "_blank");
  };

  // Send message to chatbot
  const sendMessage = async () => {
    if (!input.trim()) return;

    const userMessage = { role: "user", content: input };
    setMessages((prev) => [...prev, userMessage]);
    setInput("");
    setLoading(true);

    try {
      const prompt = `You are a resume assistant. Help users improve their resumes, provide suggestions, and answer career-related questions. Use the following knowledge if it helps. If it's not relevant, use your own intelligence.\n\nKnowledge:\n${knowledge}\n\nQuestion: ${input}`;

      const res = await axios.post(
        `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`,
        {
          contents: [
            {
              parts: [{ text: prompt }],
            },
          ],
        },
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      const botReply =
        res.data.candidates?.[0]?.content?.parts?.[0]?.text ||
        "⚠ No response received.";
      const botMessage = { role: "bot", content: botReply };
      setMessages((prev) => [...prev, botMessage]);
    } catch (error) {
      console.error("Gemini API Error:", error);
      setMessages((prev) => [
        ...prev,
        { role: "bot", content: "❌ Error: " + error.message },
      ]);
    } finally {
      setLoading(false);
    }
  };

  // Authentication functionality can be added later if needed

  // Handle logout
  const handleLogout = async () => {
    await supabase.auth.signOut();
  };

  // Show notification
  const showNotification = (msg, type = 'info') => {
    setNotification({ msg, type });
    setTimeout(() => setNotification(null), 3000);
  };

  // Log activity
  const logActivity = (msg) => {
    setActivityLog(log => [
      { type: 'activity', date: new Date().toISOString(), msg },
      ...log.slice(0, 19)
    ]);
  };

  // Toggle menu
  const toggleMenu = (menu) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menu]: !prev[menu]
    }));
  };

  // Auto-scroll chat
  useEffect(() => {
    if (chatEndRef.current) chatEndRef.current.scrollIntoView({ behavior: 'smooth' });
  }, [messages, loading]);

  // Chart data
  const getLast7Days = () => {
    const days = [];
    for (let i = 6; i >= 0; i--) {
      const d = new Date();
      d.setDate(d.getDate() - i);
      days.push(d.toLocaleDateString());
    }
    return days;
  };

  const chartLabels = getLast7Days();
  const chartData = {
    labels: chartLabels,
    datasets: [
      {
        label: 'Resource Uploads',
        data: chartLabels.map(day => activityLog.filter(a => a.type === 'activity' && a.msg.startsWith('Uploaded resource') && new Date(a.date).toLocaleDateString() === day).length),
        backgroundColor: '#3182ce',
      },
      {
        label: 'Coding Practice',
        data: chartLabels.map(day => activityLog.filter(a => a.type === 'activity' && a.msg === 'Clicked coding practice link' && new Date(a.date).toLocaleDateString() === day).length),
        backgroundColor: '#805ad5',
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: { position: 'top' },
      tooltip: { enabled: true },
    },
    scales: {
      y: { beginAtZero: true, ticks: { stepSize: 1 } },
    },
  };

  return (
    <div style={getStyle('appContainer')}>
      {/* Top Navigation Bar */}
      <nav style={getStyle('navbar')}>
        <button
          style={{
            background: 'none',
            border: 'none',
            color: 'white', // Always white since navbar has gradient background
            marginRight: '20px',
            cursor: 'pointer',
            padding: '8px',
            borderRadius: '4px',
            transition: 'all 0.2s ease'
          }}
          onClick={() => setSidebarOpen(!sidebarOpen)}
          onMouseEnter={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.1)'}
          onMouseLeave={(e) => e.target.style.background = 'none'}
        >
          {sidebarOpen ? <FiX size={24} /> : <FiMenu size={24} />}
        </button>

        <div style={{ flex: 1, display: 'flex', alignItems: 'center' }}>
          <img
            src={require('./eduai-logo.jpg')}
            alt="EduAI Logo"
            style={{ height: '36px', marginRight: '12px' }}
          />
          <div>
            <div
              data-header-title
              style={{ fontWeight: 600, fontSize: '18px', color: 'white' }}
            >
              EDU NOVA
            </div>
            <div
              data-header-subtitle
              style={{ fontSize: '12px', opacity: 0.8, color: 'white' }}
            >
              AI POWERED LEARNING SYSTEM
            </div>
          </div>
        </div>

        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          {user ? (
            <>
              <div style={{
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                background: 'rgba(255, 255, 255, 0.2)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: 600,
                cursor: 'pointer',
                color: 'white',
                backdropFilter: 'blur(10px)'
              }}>
                {user.email === ADMIN_EMAIL ? <FiShield size={20} color="#4caf50" /> : user.email[0].toUpperCase()}
              </div>
              <button
                style={{
                  ...getStyle('buttonPrimary'),
                  background: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  backdropFilter: 'blur(10px)'
                }}
                onClick={handleLogout}
              >
                <FiLogOut /> Logout
              </button>
            </>
          ) : (
            <button
              style={{
                ...getStyle('buttonPrimary'),
                background: 'rgba(255, 255, 255, 0.2)',
                color: 'white',
                border: '1px solid rgba(255, 255, 255, 0.3)',
                backdropFilter: 'blur(10px)'
              }}
              onClick={() => { console.log('Login functionality to be implemented'); }}
            >
              <FiLogIn /> Login
            </button>
          )}
        </div>
      </nav>

      {/* Sidebar */}
      <aside style={getStyle('sidebar')}>
        <div style={{ padding: '16px' }}>
          {updatedSidebarItems.map((item, index) => (
            <div key={index}>
              <div
                style={{
                  ...getStyle('sidebarItem'),
                  ...(activeTab === item.tab ? getStyle('sidebarItemActive') : {}),
                  cursor: 'pointer',
                  transition: 'all 0.3s ease'
                }}
                onClick={() => {
                  setActiveTab(item.tab);
                  setSidebarOpen(false);
                }}
                onMouseEnter={(e) => {
                  if (activeTab !== item.tab) {
                    e.target.style.background = 'rgba(0, 0, 0, 0.05)';
                    e.target.style.transform = 'translateX(4px)';
                    e.target.style.borderLeft = `3px solid ${globalStyles.currentTheme.primary}`;
                  }
                }}
                onMouseLeave={(e) => {
                  if (activeTab !== item.tab) {
                    e.target.style.background = globalStyles.currentTheme.surface;
                    e.target.style.transform = 'translateX(0)';
                    e.target.style.borderLeft = '3px solid transparent';
                  }
                }}
              >
                <div style={{ marginRight: '12px' }}>{item.icon}</div>
                <span style={{ flex: 1 }}>{item.title}</span>
                {item.subItems.length > 0 && (
                  <div onClick={(e) => {
                    e.stopPropagation();
                    toggleMenu(item.title);
                  }}>
                    {expandedMenus[item.title] ? <FiChevronDown /> : <FiChevronRight />}
                  </div>
                )}
              </div>

              {item.subItems.length > 0 && expandedMenus[item.title] && (
                <div style={{ marginLeft: '32px' }}>
                  {item.subItems.map((subItem, subIndex) => (
                    <div
                      key={subIndex}
                      style={{
                        ...getStyle('sidebarItem'),
                        padding: '8px 16px 8px 32px',
                        fontSize: '14px',
                        opacity: 0.9
                      }}
                      onClick={() => {
                        setActiveTab(item.tab);
                        setSidebarOpen(false);
                      }}
                      onMouseEnter={(e) => {
                        e.target.style.background = 'rgba(0, 0, 0, 0.03)';
                        e.target.style.paddingLeft = '36px';
                        e.target.style.opacity = '1';
                      }}
                      onMouseLeave={(e) => {
                        e.target.style.background = globalStyles.currentTheme.surface;
                        e.target.style.paddingLeft = '32px';
                        e.target.style.opacity = '0.9';
                      }}
                    >
                      {subItem.title}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </aside>

      {/* Main Content */}
      <main style={getStyle('mainContent')}>
        {/* Overlay when sidebar is open on mobile */}
        {sidebarOpen && window.innerWidth < 768 && (
          <div
            style={{
              position: 'fixed',
              top: '64px',
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0,0,0,0.5)',
              zIndex: 800
            }}
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Dashboard Content */}
        {activeTab === "dashboard" && (
          <div style={{
            padding: '2rem 1rem',
            background: globalStyles.currentTheme.gradientLight,
            minHeight: '100vh',
            position: 'relative',
            overflow: 'hidden'
          }}>
            {/* Professional Background Pattern */}
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundImage: `
                radial-gradient(circle at 20% 30%, ${globalStyles.currentTheme.primary}08 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, ${globalStyles.currentTheme.primary}05 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, ${globalStyles.currentTheme.primary}03 0%, transparent 50%)
              `,
              zIndex: 0
            }} />

            {/* Subtle Grid Pattern */}
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundImage: `
                linear-gradient(${globalStyles.currentTheme.border} 1px, transparent 1px),
                linear-gradient(90deg, ${globalStyles.currentTheme.border} 1px, transparent 1px)
              `,
              backgroundSize: '50px 50px',
              opacity: 0.3,
              zIndex: 0
            }} />

            <div style={{ maxWidth: '1400px', margin: '0 auto', position: 'relative', zIndex: 1 }}>

              {/* Professional Hero Section */}
              <div style={{
                background: globalStyles.currentTheme.surface,
                backdropFilter: 'blur(20px)',
                borderRadius: '20px',
                padding: '3rem',
                marginBottom: '2rem',
                position: 'relative',
                overflow: 'hidden',
                border: `1px solid ${globalStyles.currentTheme.border}`,
                boxShadow: `0 20px 40px ${globalStyles.currentTheme.shadow}`
              }}>
                {/* Professional accent line */}
                <div style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '4px',
                  background: globalStyles.currentTheme.gradient
                }} />

                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'auto 1fr auto',
                  gap: '2rem',
                  alignItems: 'center',
                  position: 'relative',
                  zIndex: 1
                }}>
                  {/* Professional Avatar */}
                  <div style={{
                    width: '100px',
                    height: '100px',
                    borderRadius: '20px',
                    background: globalStyles.currentTheme.gradient,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '2.5rem',
                    fontWeight: 'bold',
                    color: 'white',
                    boxShadow: `0 10px 30px ${globalStyles.currentTheme.shadow}`,
                    border: `3px solid ${globalStyles.currentTheme.surface}`
                  }}>
                    {user ? user.email[0].toUpperCase() : '👤'}
                  </div>

                  {/* Welcome Content */}
                  <div>
                    <h1 style={{
                      margin: 0,
                      fontSize: '2.5rem',
                      fontWeight: 700,
                      marginBottom: '0.5rem',
                      color: globalStyles.currentTheme.text,
                      lineHeight: 1.2
                    }}>
                      Welcome back, {user ? user.email.split('@')[0] : 'User'}! 👋
                    </h1>
                    <p style={{
                      margin: 0,
                      fontSize: '1.1rem',
                      color: globalStyles.currentTheme.textLight,
                      fontWeight: 400,
                      marginBottom: '1.5rem'
                    }}>
                      Ready to continue your learning journey? Let's achieve your goals together.
                    </p>

                    {/* Professional Status Badges */}
                    <div style={{
                      display: 'flex',
                      gap: '1rem',
                      flexWrap: 'wrap'
                    }}>
                      <div style={{
                        background: globalStyles.currentTheme.primary + '15',
                        color: globalStyles.currentTheme.primary,
                        padding: '0.5rem 1rem',
                        borderRadius: '12px',
                        fontSize: '0.9rem',
                        fontWeight: 600,
                        border: `1px solid ${globalStyles.currentTheme.primary}30`
                      }}>
                        📈 Active Learner
                      </div>
                      <div style={{
                        background: globalStyles.currentTheme.primary + '15',
                        color: globalStyles.currentTheme.primary,
                        padding: '0.5rem 1rem',
                        borderRadius: '12px',
                        fontSize: '0.9rem',
                        fontWeight: 600,
                        border: `1px solid ${globalStyles.currentTheme.primary}30`
                      }}>
                        🎯 Goal Oriented
                      </div>
                    </div>
                  </div>

                  {/* Quick Stats */}
                  <div style={{
                    textAlign: 'center',
                    padding: '1rem',
                    background: globalStyles.currentTheme.secondary,
                    borderRadius: '16px',
                    border: `1px solid ${globalStyles.currentTheme.border}`
                  }}>
                    <div style={{
                      fontSize: '2rem',
                      fontWeight: 700,
                      color: globalStyles.currentTheme.primary,
                      marginBottom: '0.5rem'
                    }}>
                      {new Date().toLocaleDateString('en-US', { day: 'numeric' })}
                    </div>
                    <div style={{
                      fontSize: '0.9rem',
                      color: globalStyles.currentTheme.textLight,
                      fontWeight: 500
                    }}>
                      {new Date().toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}
                    </div>
                  </div>
                </div>
              </div>

              {/* Professional Analytics Cards */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
                gap: '1.5rem',
                marginBottom: '2rem'
              }}>
                {[
                  {
                    title: 'Learning Progress',
                    value: '78',
                    unit: '%',
                    icon: '📈',
                    trend: '+12%',
                    description: 'This month',
                    color: globalStyles.currentTheme.primary
                  },
                  {
                    title: 'Study Hours',
                    value: '47',
                    unit: 'hrs',
                    icon: '⏱️',
                    trend: '+8 hrs',
                    description: 'This week',
                    color: '#10B981'
                  },
                  {
                    title: 'Completed Tasks',
                    value: '23',
                    unit: 'tasks',
                    icon: '✅',
                    trend: '+5',
                    description: 'This week',
                    color: '#3B82F6'
                  },
                  {
                    title: 'Skill Rating',
                    value: '4.8',
                    unit: '/5.0',
                    icon: '⭐',
                    trend: '+0.3',
                    description: 'Overall',
                    color: '#F59E0B'
                  }
                ].map((stat, index) => (
                  <div key={index} style={{
                    background: globalStyles.currentTheme.surface,
                    borderRadius: '16px',
                    padding: '1.5rem',
                    position: 'relative',
                    overflow: 'hidden',
                    border: `1px solid ${globalStyles.currentTheme.border}`,
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-4px)';
                    e.currentTarget.style.boxShadow = `0 8px 25px ${globalStyles.currentTheme.shadow}`;
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = `0 4px 12px ${globalStyles.currentTheme.shadow}`;
                  }}>

                    {/* Subtle accent line */}
                    <div style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: '3px',
                      background: stat.color
                    }} />

                    <div style={{ position: 'relative', zIndex: 1 }}>
                      {/* Header */}
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        marginBottom: '1rem'
                      }}>
                        <div style={{
                          fontSize: '0.9rem',
                          fontWeight: 600,
                          color: globalStyles.currentTheme.textLight,
                          textTransform: 'uppercase',
                          letterSpacing: '0.5px'
                        }}>
                          {stat.title}
                        </div>
                        <div style={{
                          fontSize: '1.5rem',
                          opacity: 0.8
                        }}>
                          {stat.icon}
                        </div>
                      </div>

                      {/* Main Value */}
                      <div style={{
                        display: 'flex',
                        alignItems: 'baseline',
                        gap: '0.5rem',
                        marginBottom: '0.5rem'
                      }}>
                        <div style={{
                          fontSize: '2.5rem',
                          fontWeight: 700,
                          color: globalStyles.currentTheme.text,
                          lineHeight: 1
                        }}>
                          {stat.value}
                        </div>
                        <div style={{
                          fontSize: '1rem',
                          fontWeight: 500,
                          color: globalStyles.currentTheme.textLight
                        }}>
                          {stat.unit}
                        </div>
                      </div>

                      {/* Trend and Description */}
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between'
                      }}>
                        <div style={{
                          fontSize: '0.8rem',
                          color: globalStyles.currentTheme.textLight
                        }}>
                          {stat.description}
                        </div>
                        <div style={{
                          fontSize: '0.8rem',
                          fontWeight: 600,
                          color: stat.color,
                          background: stat.color + '15',
                          padding: '0.25rem 0.5rem',
                          borderRadius: '8px'
                        }}>
                          {stat.trend}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Professional Quick Actions */}
              <div style={{
                background: globalStyles.currentTheme.surface,
                borderRadius: '20px',
                padding: '2rem',
                marginBottom: '2rem',
                border: `1px solid ${globalStyles.currentTheme.border}`,
                boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,
                position: 'relative',
                overflow: 'hidden'
              }}>
                {/* Professional accent line */}
                <div style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '4px',
                  background: globalStyles.currentTheme.gradient
                }} />

                <h2 style={{
                  margin: '0 0 2rem 0',
                  fontSize: '1.5rem',
                  fontWeight: 600,
                  color: globalStyles.currentTheme.text,
                  textAlign: 'center'
                }}>
                  🚀 Quick Actions
                </h2>

                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                  gap: '1.5rem'
                }}>
                  {[
                    {
                      icon: '📊',
                      title: 'Take Assessment',
                      subtitle: 'Skill Evaluation',
                      desc: 'Evaluate your current skill level',
                      action: () => setActiveTab('quizzes'),
                      color: globalStyles.currentTheme.primary
                    },
                    {
                      icon: '💻',
                      title: 'Practice Coding',
                      subtitle: 'DSA Problems',
                      desc: 'Solve data structures & algorithms',
                      action: () => setActiveTab('dsa'),
                      color: '#3B82F6'
                    },
                    {
                      icon: '📄',
                      title: 'Resume Review',
                      subtitle: 'Career Enhancement',
                      desc: 'Get professional resume feedback',
                      action: () => setActiveTab('resume'),
                      color: '#10B981'
                    },
                    {
                      icon: '📚',
                      title: 'Study Resources',
                      subtitle: 'Learning Materials',
                      desc: 'Access curated study materials',
                      action: () => setActiveTab('resources'),
                      color: '#F59E0B'
                    }
                  ].map((action, index) => (
                    <div key={index}
                      onClick={action.action}
                      style={{
                        background: globalStyles.currentTheme.surface,
                        borderRadius: '16px',
                        padding: '1.5rem',
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        border: `1px solid ${globalStyles.currentTheme.border}`,
                        position: 'relative',
                        overflow: 'hidden',
                        boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'translateY(-4px)';
                        e.currentTarget.style.boxShadow = `0 8px 25px ${globalStyles.currentTheme.shadow}`;
                        e.currentTarget.style.borderColor = action.color;
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'translateY(0)';
                        e.currentTarget.style.boxShadow = `0 4px 12px ${globalStyles.currentTheme.shadow}`;
                        e.currentTarget.style.borderColor = globalStyles.currentTheme.border;
                      }}
                    >
                      {/* Subtle accent line */}
                      <div style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        height: '3px',
                        background: action.color
                      }} />

                      <div style={{ position: 'relative', zIndex: 1, textAlign: 'center' }}>
                        <div style={{
                          fontSize: '2.5rem',
                          marginBottom: '1rem',
                          opacity: 0.8
                        }}>
                          {action.icon}
                        </div>

                        <div style={{
                          fontSize: '1.1rem',
                          fontWeight: 600,
                          color: globalStyles.currentTheme.text,
                          marginBottom: '0.5rem'
                        }}>
                          {action.title}
                        </div>

                        <div style={{
                          fontSize: '0.9rem',
                          color: action.color,
                          fontWeight: 500,
                          marginBottom: '0.75rem'
                        }}>
                          {action.subtitle}
                        </div>

                        <div style={{
                          fontSize: '0.85rem',
                          color: globalStyles.currentTheme.textLight,
                          lineHeight: 1.4
                        }}>
                          {action.desc}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Professional Insights Section */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
                gap: '2rem',
                marginBottom: '2rem'
              }}>
                {/* Learning Progress Chart */}
                <div style={{
                  background: globalStyles.currentTheme.surface,
                  borderRadius: '16px',
                  padding: '1.5rem',
                  boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,
                  border: `1px solid ${globalStyles.currentTheme.border}`
                }}>
                  <h3 style={{
                    margin: '0 0 1.5rem 0',
                    fontSize: '1.2rem',
                    fontWeight: 600,
                    color: globalStyles.currentTheme.text,
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem'
                  }}>
                    📊 Weekly Progress
                  </h3>

                  {/* Simple Progress Visualization */}
                  <div style={{ marginBottom: '1rem' }}>
                    {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, index) => (
                      <div key={day} style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        marginBottom: '0.75rem',
                        padding: '0.5rem',
                        borderRadius: '8px',
                        background: index < 5 ? globalStyles.currentTheme.secondary : 'transparent'
                      }}>
                        <span style={{
                          fontSize: '0.9rem',
                          fontWeight: 500,
                          color: globalStyles.currentTheme.text,
                          minWidth: '40px'
                        }}>
                          {day}
                        </span>
                        <div style={{
                          flex: 1,
                          height: '8px',
                          background: globalStyles.currentTheme.border,
                          borderRadius: '4px',
                          margin: '0 1rem',
                          overflow: 'hidden'
                        }}>
                          <div style={{
                            height: '100%',
                            width: `${Math.random() * 80 + 20}%`,
                            background: index < 5 ? globalStyles.currentTheme.primary : globalStyles.currentTheme.border,
                            borderRadius: '4px',
                            transition: 'width 1s ease'
                          }} />
                        </div>
                        <span style={{
                          fontSize: '0.8rem',
                          color: globalStyles.currentTheme.textLight,
                          minWidth: '40px',
                          textAlign: 'right'
                        }}>
                          {index < 5 ? `${Math.floor(Math.random() * 3 + 1)}h` : '-'}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Goals & Achievements */}
                <div style={{
                  background: globalStyles.currentTheme.surface,
                  borderRadius: '16px',
                  padding: '1.5rem',
                  boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,
                  border: `1px solid ${globalStyles.currentTheme.border}`
                }}>
                  <h3 style={{
                    margin: '0 0 1.5rem 0',
                    fontSize: '1.2rem',
                    fontWeight: 600,
                    color: globalStyles.currentTheme.text,
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem'
                  }}>
                    🎯 Goals & Achievements
                  </h3>

                  <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                    {[
                      { title: 'Complete 5 DSA Problems', progress: 80, status: 'In Progress' },
                      { title: 'Finish Resume Review', progress: 100, status: 'Completed' },
                      { title: 'Take 3 Practice Tests', progress: 33, status: 'In Progress' }
                    ].map((goal, index) => (
                      <div key={index} style={{
                        padding: '1rem',
                        borderRadius: '12px',
                        background: globalStyles.currentTheme.secondary,
                        border: `1px solid ${globalStyles.currentTheme.border}`
                      }}>
                        <div style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          marginBottom: '0.5rem'
                        }}>
                          <span style={{
                            fontSize: '0.9rem',
                            fontWeight: 500,
                            color: globalStyles.currentTheme.text
                          }}>
                            {goal.title}
                          </span>
                          <span style={{
                            fontSize: '0.8rem',
                            fontWeight: 600,
                            color: goal.progress === 100 ? '#10B981' : globalStyles.currentTheme.primary,
                            background: goal.progress === 100 ? '#10B98115' : globalStyles.currentTheme.primary + '15',
                            padding: '0.25rem 0.5rem',
                            borderRadius: '6px'
                          }}>
                            {goal.status}
                          </span>
                        </div>
                        <div style={{
                          height: '6px',
                          background: globalStyles.currentTheme.border,
                          borderRadius: '3px',
                          overflow: 'hidden'
                        }}>
                          <div style={{
                            height: '100%',
                            width: `${goal.progress}%`,
                            background: goal.progress === 100 ? '#10B981' : globalStyles.currentTheme.primary,
                            borderRadius: '3px',
                            transition: 'width 1s ease'
                          }} />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Recent Activity & Resume Management */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: '1fr 1fr',
                gap: '2rem'
              }}>

                {/* Recent Activity */}
                <div style={{
                  background: globalStyles.currentTheme.surface,
                  borderRadius: '16px',
                  padding: '1.5rem',
                  boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,
                  border: `1px solid ${globalStyles.currentTheme.border}`
                }}>
                  <h3 style={{
                    margin: '0 0 1.5rem 0',
                    fontSize: '1.3rem',
                    fontWeight: 600,
                    color: globalStyles.currentTheme.text
                  }}>
                    📈 Recent Activity
                  </h3>

                  <div style={{
                    maxHeight: '250px',
                    overflowY: 'auto'
                  }}>
                    {activityLog.slice(0, 5).map((log, index) => (
                      <div key={index} style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '1rem',
                        padding: '0.75rem',
                        borderRadius: '8px',
                        marginBottom: '0.5rem',
                        background: index % 2 === 0 ? globalStyles.currentTheme.secondary : 'transparent',
                        transition: 'all 0.3s ease'
                      }}>
                        <div style={{
                          width: '10px',
                          height: '10px',
                          borderRadius: '50%',
                          background: log.type === 'login' ? '#4ECDC4' : '#45B7D1',
                          flexShrink: 0
                        }} />
                        <div style={{ flex: 1 }}>
                          <div style={{
                            fontSize: '0.9rem',
                            fontWeight: 500,
                            color: globalStyles.currentTheme.text,
                            marginBottom: '0.2rem'
                          }}>
                            {log.msg}
                          </div>
                          <div style={{
                            fontSize: '0.8rem',
                            color: globalStyles.currentTheme.textLight
                          }}>
                            {new Date(log.date).toLocaleString()}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Resume Management */}
                <div style={{
                  background: globalStyles.currentTheme.surface,
                  borderRadius: '16px',
                  padding: '1.5rem',
                  boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,
                  border: `1px solid ${globalStyles.currentTheme.border}`
                }}>
                  <h3 style={{
                    margin: '0 0 1.5rem 0',
                    fontSize: '1.3rem',
                    fontWeight: 600,
                    color: globalStyles.currentTheme.text
                  }}>
                    📄 Resume Management
                  </h3>

                  <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                    <label style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.75rem',
                      padding: '1rem',
                      borderRadius: '12px',
                      background: globalStyles.currentTheme.secondary,
                      border: `2px dashed ${globalStyles.currentTheme.border}`,
                      cursor: resumeUploadLoading ? 'not-allowed' : 'pointer',
                      transition: 'all 0.3s ease'
                    }}
                    onMouseEnter={(e) => {
                      if (!resumeUploadLoading) {
                        e.currentTarget.style.borderColor = globalStyles.currentTheme.primary;
                        e.currentTarget.style.background = globalStyles.currentTheme.primary + '10';
                      }
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.borderColor = globalStyles.currentTheme.border;
                      e.currentTarget.style.background = globalStyles.currentTheme.secondary;
                    }}>
                      <FiUpload size={20} color={globalStyles.currentTheme.primary} />
                      <div>
                        <div style={{
                          fontWeight: 600,
                          color: globalStyles.currentTheme.text,
                          marginBottom: '0.2rem'
                        }}>
                          {resumeUploadLoading ? 'Uploading...' : 'Upload Resume'}
                        </div>
                        <div style={{
                          fontSize: '0.8rem',
                          color: globalStyles.currentTheme.textLight
                        }}>
                          PDF files only
                        </div>
                      </div>
                      <input
                        type="file"
                        accept="application/pdf"
                        onChange={handleResumeUpload}
                        disabled={resumeUploadLoading}
                        style={{ display: 'none' }}
                      />
                    </label>

                    {resumeUrl && (
                      <a
                        href={resumeUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.75rem',
                          padding: '1rem',
                          borderRadius: '12px',
                          background: '#4ECDC4',
                          color: 'white',
                          textDecoration: 'none',
                          fontWeight: 600,
                          transition: 'all 0.3s ease'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.background = '#3DBDB6';
                          e.currentTarget.style.transform = 'translateY(-2px)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.background = '#4ECDC4';
                          e.currentTarget.style.transform = 'translateY(0)';
                        }}
                      >
                        <FiFileText size={20} />
                        <span>View Resume</span>
                      </a>
                    )}
                  </div>
                </div>
              </div>

              {/* Professional Footer Section */}
              <div style={{
                background: globalStyles.currentTheme.surface,
                borderRadius: '16px',
                padding: '2rem',
                marginTop: '2rem',
                border: `1px solid ${globalStyles.currentTheme.border}`,
                boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,
                textAlign: 'center'
              }}>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                  gap: '2rem',
                  marginBottom: '1.5rem'
                }}>
                  {/* Quick Stats */}
                  <div>
                    <h4 style={{
                      margin: '0 0 1rem 0',
                      fontSize: '1rem',
                      fontWeight: 600,
                      color: globalStyles.currentTheme.text
                    }}>
                      📈 Your Progress
                    </h4>
                    <div style={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '0.5rem'
                    }}>
                      <div style={{
                        fontSize: '0.9rem',
                        color: globalStyles.currentTheme.textLight
                      }}>
                        Total Study Time: <strong style={{ color: globalStyles.currentTheme.primary }}>47 hours</strong>
                      </div>
                      <div style={{
                        fontSize: '0.9rem',
                        color: globalStyles.currentTheme.textLight
                      }}>
                        Completed Tasks: <strong style={{ color: globalStyles.currentTheme.primary }}>23</strong>
                      </div>
                    </div>
                  </div>

                  {/* Quick Links */}
                  <div>
                    <h4 style={{
                      margin: '0 0 1rem 0',
                      fontSize: '1rem',
                      fontWeight: 600,
                      color: globalStyles.currentTheme.text
                    }}>
                      🔗 Quick Links
                    </h4>
                    <div style={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '0.5rem'
                    }}>
                      <button
                        onClick={() => setActiveTab('academics')}
                        style={{
                          background: 'none',
                          border: 'none',
                          color: globalStyles.currentTheme.primary,
                          fontSize: '0.9rem',
                          cursor: 'pointer',
                          textAlign: 'left',
                          padding: '0.25rem 0'
                        }}
                      >
                        📚 Academic Resources
                      </button>
                      <button
                        onClick={() => setActiveTab('coding')}
                        style={{
                          background: 'none',
                          border: 'none',
                          color: globalStyles.currentTheme.primary,
                          fontSize: '0.9rem',
                          cursor: 'pointer',
                          textAlign: 'left',
                          padding: '0.25rem 0'
                        }}
                      >
                        💻 Coding Practice
                      </button>
                    </div>
                  </div>

                  {/* Motivational Quote */}
                  <div>
                    <h4 style={{
                      margin: '0 0 1rem 0',
                      fontSize: '1rem',
                      fontWeight: 600,
                      color: globalStyles.currentTheme.text
                    }}>
                      💡 Daily Inspiration
                    </h4>
                    <div style={{
                      fontSize: '0.9rem',
                      color: globalStyles.currentTheme.textLight,
                      fontStyle: 'italic',
                      lineHeight: 1.4
                    }}>
                      "Success is not final, failure is not fatal: it is the courage to continue that counts."
                    </div>
                  </div>
                </div>

                {/* Bottom Footer */}
                <div style={{
                  borderTop: `1px solid ${globalStyles.currentTheme.border}`,
                  paddingTop: '1rem',
                  fontSize: '0.8rem',
                  color: globalStyles.currentTheme.textLight
                }}>
                  Keep learning, keep growing! 🚀 Your journey to success starts here.
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Chat Interface */}
        {activeTab === "resume" && (
          <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
            <div style={getStyle('card')}>
              <h2 style={{
                marginTop: 0,
                color: '#333'
              }}>Career Assistant</h2>
              <p style={{
                opacity: 0.8,
                marginBottom: '24px',
                color: '#666'
              }}>
                Get personalized resume advice and career guidance
              </p>

              {/* Chat messages */}
              <div style={{
                height: '50vh',
                overflowY: 'auto',
                marginBottom: '24px',
                padding: '16px',
                backgroundColor: '#f5f5f5',
                border: '1px solid #e0e0e0',
                borderRadius: '8px'
              }}>

                {messages.length === 0 ? (
                  <div style={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    textAlign: 'center',
                    opacity: 0.7
                  }}>
                    <div style={{ fontSize: '48px', marginBottom: '16px' }}>💬</div>
                    <h3 style={{
                      margin: 0,
                      color: '#333'
                    }}>Start a conversation</h3>
                    <p style={{
                      color: '#666'
                    }}>Ask about resumes, interviews, or career advice</p>
                  </div>
                ) : (
                  messages.map((msg, idx) => (
                    <div
                      key={idx}
                      style={{
                        ...(msg.role === 'user' ? getStyle('chatBubbleUser') : getStyle('chatBubbleBot')),
                        animation: 'fadeIn 0.3s ease'
                      }}
                    >
                      {msg.role === 'bot' ? (
                        <ReactMarkdown>{msg.content}</ReactMarkdown>
                      ) : (
                        msg.content
                      )}
                    </div>
                  ))
                )}
                {loading && (
                  <div style={getStyle('chatBubbleBot')}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <div style={{
                        width: '10px',
                        height: '10px',
                        borderRadius: '50%',
                        backgroundColor: '#1976d2',
                        animation: 'pulse 1.4s infinite ease-in-out'
                      }} />
                      <div style={{
                        width: '10px',
                        height: '10px',
                        borderRadius: '50%',
                        backgroundColor: '#1976d2',
                        animation: 'pulse 1.4s infinite ease-in-out',
                        animationDelay: '0.2s'
                      }} />
                      <div style={{
                        width: '10px',
                        height: '10px',
                        borderRadius: '50%',
                        backgroundColor: '#1976d2',
                        animation: 'pulse 1.4s infinite ease-in-out',
                        animationDelay: '0.4s'
                      }} />
                    </div>
                  </div>
                )}
                <div ref={chatEndRef} />
              </div>

              {/* Input area */}
              <form
                style={{ display: 'flex', gap: '12px' }}
                onSubmit={e => {
                  e.preventDefault();
                  sendMessage();
                }}
              >
                <input
                  type="text"
                  placeholder="Type your message..."
                  style={getStyle('inputField')}
                  value={input}
                  onChange={e => setInput(e.target.value)}
                  disabled={loading}
                />
                <button
                  type="submit"
                  style={{
                    ...getStyle('buttonPrimary'),
                    minWidth: '100px'
                  }}
                  disabled={loading || !input.trim()}
                >
                  {loading ? 'Sending...' : 'Send'}
                </button>
              </form>
            </div>
          </div>
        )}

        {/* Enhanced DSA Company Questions */}
        {activeTab === "dsa" && (
          <div style={{ padding: '24px' }}>
            <div style={getStyle('card')}>
              {/* Header with revert button */}
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
                <div>
                  <h2 style={{ marginTop: 0, marginBottom: '8px' }}>🚀 Company Wise DSA Questions</h2>
                  <p style={{ opacity: 0.8, margin: 0 }}>
                    Explore DSA questions from top companies with enhanced filtering and favorites
                  </p>
                </div>
                {showRevertButton && (
                  <button
                    onClick={revertHeaderChanges}
                    style={{
                      ...getStyle('buttonPrimary'),
                      background: '#ff6b6b',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      fontSize: '14px',
                      padding: '8px 16px',
                      border: 'none',
                      borderRadius: '8px',
                      color: 'white',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease'
                    }}
                    onMouseEnter={(e) => e.target.style.background = '#ff5252'}
                    onMouseLeave={(e) => e.target.style.background = '#ff6b6b'}
                  >
                    <FiRefreshCw size={16} />
                    Revert Header Color
                  </button>
                )}
              </div>

              {/* Category Tabs */}
              <div style={{
                display: 'flex',
                gap: '8px',
                marginBottom: '20px',
                flexWrap: 'wrap',
                borderBottom: '1px solid #eee',
                paddingBottom: '16px'
              }}>
                {['all', ...Object.keys(companyCategories)].map(category => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    style={{
                      padding: '8px 16px',
                      borderRadius: '20px',
                      border: selectedCategory === category
                        ? 'none'
                        : '1px solid #ddd',
                      background: selectedCategory === category
                        ? globalStyles.currentTheme.primary
                        : 'transparent',
                      color: selectedCategory === category
                        ? 'white'
                        : '#666',
                      cursor: 'pointer',
                      fontSize: '14px',
                      fontWeight: selectedCategory === category ? 600 : 400,
                      transition: 'all 0.3s ease',
                      textTransform: 'capitalize'
                    }}
                    onMouseEnter={(e) => {
                      if (selectedCategory !== category) {
                        e.target.style.background = '#f5f5f5';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (selectedCategory !== category) {
                        e.target.style.background = 'transparent';
                      }
                    }}
                  >
                    {category === 'all' ? '🌟 All' : `${category === 'FAANG' ? '🔥' : category === 'Big Tech' ? '💻' : category === 'Startups' ? '🚀' : category === 'Finance' ? '💰' : category === 'Indian' ? '🇮🇳' : '🏢'} ${category}`}
                  </button>
                ))}
              </div>

              {/* Controls Row */}
              <div style={{
                display: 'flex',
                gap: '16px',
                marginBottom: '24px',
                flexWrap: 'wrap',
                alignItems: 'center'
              }}>
                {/* Search box */}
                <div style={{ position: 'relative', flex: 1, minWidth: '300px' }}>
                  <div style={{
                    position: 'absolute',
                    left: '16px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    color: '#666'
                  }}>
                    <FiSearch size={20} />
                  </div>
                  <input
                    type="text"
                    placeholder="Search companies..."
                    style={{
                      ...getStyle('inputField'),
                      paddingLeft: '48px',
                      width: '100%'
                    }}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  {searchTerm && (
                    <button
                      style={{
                        position: 'absolute',
                        right: '16px',
                        top: '50%',
                        transform: 'translateY(-50%)',
                        background: 'none',
                        border: 'none',
                        color: '#666',
                        cursor: 'pointer'
                      }}
                      onClick={() => setSearchTerm("")}
                    >
                      <FiX size={20} />
                    </button>
                  )}
                </div>

                {/* Sort dropdown */}
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  style={{
                    ...getStyle('inputField'),
                    width: 'auto',
                    minWidth: '150px'
                  }}
                >
                  <option value="name">📝 Sort by Name</option>
                  <option value="favorites">⭐ Favorites First</option>
                </select>
              </div>

              {/* Recent Companies */}
              {recentCompanies.length > 0 && (
                <div style={{
                  marginBottom: '24px',
                  padding: '16px',
                  borderRadius: '12px',
                  background: '#f8f9fa',
                  border: '1px solid #e9ecef'
                }}>
                  <h3 style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    fontSize: '16px',
                    marginBottom: '12px',
                    color: '#333',
                    margin: '0 0 12px 0'
                  }}>
                    <FiClock color="#666" /> Recently Viewed
                  </h3>
                  <div style={{
                    display: 'flex',
                    gap: '8px',
                    flexWrap: 'wrap'
                  }}>
                    {recentCompanies.map(company => (
                      <button
                        key={company}
                        onClick={() => handleCompanyClick(company)}
                        style={{
                          padding: '6px 12px',
                          borderRadius: '16px',
                          border: `1px solid ${globalStyles.currentTheme.primary}`,
                          background: 'transparent',
                          color: globalStyles.currentTheme.primary,
                          cursor: 'pointer',
                          fontSize: '12px',
                          transition: 'all 0.3s ease'
                        }}
                        onMouseEnter={(e) => {
                          e.target.style.background = globalStyles.currentTheme.primary;
                          e.target.style.color = 'white';
                        }}
                        onMouseLeave={(e) => {
                          e.target.style.background = 'transparent';
                          e.target.style.color = globalStyles.currentTheme.primary;
                        }}
                      >
                        {company}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Companies grid */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fill, minmax(220px, 1fr))',
                gap: '16px',
                marginTop: '24px'
              }}>
                {getFilteredCompanies().map((company, index) => (
                  <div
                    key={index}
                    style={{
                      ...getStyle('companyCard'),
                      position: 'relative',
                      transform: favoriteCompanies.includes(company) ? 'scale(1.02)' : 'scale(1)',
                      border: favoriteCompanies.includes(company)
                        ? `2px solid ${globalStyles.currentTheme.primary}`
                        : `1px solid ${globalStyles.currentTheme.border}`,
                      background: globalStyles.currentTheme.surface,
                      color: globalStyles.currentTheme.text,
                      animation: `fadeIn 0.3s ease ${index * 0.1}s both`,
                      boxShadow: `0 4px 6px ${globalStyles.currentTheme.shadow}`
                    }}
                    onClick={() => handleCompanyClick(company)}
                  >
                    {/* Favorite button */}
                    <button
                      onClick={(e) => toggleFavorite(company, e)}
                      style={{
                        position: 'absolute',
                        top: '8px',
                        right: '8px',
                        background: 'none',
                        border: 'none',
                        cursor: 'pointer',
                        color: favoriteCompanies.includes(company) ? '#ff6b6b' : '#ccc',
                        transition: 'all 0.3s ease',
                        fontSize: '18px'
                      }}
                    >
                      <FiHeart fill={favoriteCompanies.includes(company) ? 'currentColor' : 'none'} />
                    </button>

                    {/* Company initial with gradient */}
                    <div style={{
                      width: '56px',
                      height: '56px',
                      borderRadius: '50%',
                      background: `linear-gradient(135deg, ${globalStyles.currentTheme.primary}, ${globalStyles.currentTheme.primaryDark})`,
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '24px',
                      fontWeight: 700,
                      marginBottom: '12px',
                      boxShadow: `0 4px 8px ${globalStyles.currentTheme.shadow}`
                    }}>
                      {company.charAt(0)}
                    </div>

                    {/* Company name */}
                    <div style={{
                      fontWeight: 600,
                      textAlign: 'center',
                      fontSize: '14px',
                      marginBottom: '8px'
                    }}>
                      {company}
                    </div>

                    {/* Mock stats */}
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      fontSize: '12px',
                      opacity: 0.7,
                      marginTop: '8px'
                    }}>
                      <span>📊 {Math.floor(Math.random() * 50) + 10} Questions</span>
                      <span>⭐ {(Math.random() * 2 + 3).toFixed(1)}</span>
                    </div>

                    {/* Category badge */}
                    {Object.entries(companyCategories).map(([category, companies]) => {
                      if (companies.some(c => c.toLowerCase() === company.toLowerCase())) {
                        return (
                          <div
                            key={category}
                            style={{
                              position: 'absolute',
                              top: '8px',
                              left: '8px',
                              background: globalStyles.currentTheme.primary,
                              color: 'white',
                              padding: '2px 6px',
                              borderRadius: '8px',
                              fontSize: '10px',
                              fontWeight: 600
                            }}
                          >
                            {category}
                          </div>
                        );
                      }
                      return null;
                    })}
                  </div>
                ))}
              </div>

              {/* No results message */}
              {getFilteredCompanies().length === 0 && (
                <div style={{
                  textAlign: 'center',
                  padding: '40px',
                  opacity: 0.7,
                  color: '#666'
                }}>
                  <div style={{ fontSize: '48px', marginBottom: '16px' }}>🔍</div>
                  <h3 style={{ color: '#333' }}>No companies found</h3>
                  <p style={{ color: '#666' }}>Try adjusting your search or category filter</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Quizzes Section */}
        {activeTab === "quizzes" && (
          <div style={{ padding: '24px' }}>
            <div style={getStyle('card')}>
              <h2 style={{ marginTop: 0 }}>Career Quizzes</h2>
              <p style={{ opacity: 0.8, marginBottom: '24px' }}>
                Test your knowledge with our career-focused quizzes!
              </p>

              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
                gap: '16px'
              }}>
                {quizButtons.map((quiz, index) => (
                  <div
                    key={index}
                    style={getStyle('quizCard')}
                    onClick={() => openQuizLink(quiz.link)}
                  >
                    <div>
                      <h3 style={{ margin: '0 0 8px 0' }}>{quiz.title}</h3>
                      <p style={{
                        margin: 0,
                        fontSize: '14px',
                        opacity: 0.8
                      }}>
                        {quiz.description}
                      </p>
                    </div>
                    <div style={{ color: '#1976d2' }}>
                      <FiExternalLink size={20} />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Other tabs */}
        {activeTab === "coding" && (
          <div style={{ padding: '24px' }}>
            <Coding />
          </div>
        )}
        {activeTab === "resources" && (
          <div style={{
            padding: '2rem 1rem',
            background: globalStyles.currentTheme.gradientLight,
            minHeight: '100vh'
          }}>
            <div style={{ maxWidth: '1400px', margin: '0 auto' }}>
              {/* Header */}
              <div style={{
                background: globalStyles.currentTheme.surface,
                borderRadius: '20px',
                padding: '2rem',
                marginBottom: '2rem',
                border: `1px solid ${globalStyles.currentTheme.border}`,
                boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,
                position: 'relative'
              }}>
                <div style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '4px',
                  background: globalStyles.currentTheme.gradient
                }} />

                <h1 style={{
                  margin: '0 0 1rem 0',
                  fontSize: '2.5rem',
                  fontWeight: 700,
                  color: globalStyles.currentTheme.text,
                  textAlign: 'center'
                }}>
                  📚 Study Resources & Materials
                </h1>
                <p style={{
                  margin: 0,
                  fontSize: '1.1rem',
                  color: globalStyles.currentTheme.textLight,
                  textAlign: 'center'
                }}>
                  Access comprehensive study materials, academic resources, and learning tools
                </p>
              </div>

              {/* Enhanced Tab Navigation with Sliding Underline */}
              <div style={{
                background: globalStyles.currentTheme.surface,
                borderRadius: '16px',
                padding: '1.5rem',
                marginBottom: '2rem',
                border: `1px solid ${globalStyles.currentTheme.border}`,
                boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`
              }}>
                {/* Tab Container with Sliding Underline */}
                <div
                  id="tab-navigation-container"
                  className="tab-navigation-container"
                  style={{
                    position: 'relative',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    background: globalStyles.currentTheme.secondary,
                    borderRadius: '12px',
                    padding: '0.5rem',
                    overflow: 'hidden'
                  }}
                >
                  {/* Fixed Static Background Bar */}
                  <div
                    className="static-background-bar"
                    style={{
                      position: 'absolute',
                      top: '0.5rem',
                      bottom: '0.5rem',
                      left: '0.5rem',
                      right: '0.5rem',
                      background: globalStyles.currentTheme.primary + '20',
                      borderRadius: '8px',
                      zIndex: 1,
                      pointerEvents: 'none' // Allow clicks to pass through to buttons
                    }}
                  />

                  {/* Tab Buttons */}
                  {[
                    { id: 'general', label: '📁 General Resources', icon: '📁', shortLabel: 'General' },
                    { id: 'academics', label: '🎓 Academic Materials', icon: '🎓', shortLabel: 'Academics' },
                    { id: 'tools', label: '🛠️ Study Tools', icon: '🛠️', shortLabel: 'Tools' }
                  ].map((tab, index) => (
                    <button
                      key={tab.id}
                      className={`tab-button ${resourcesTab === tab.id ? 'active' : ''}`}
                      onClick={() => setResourcesTab(tab.id)}
                      onMouseEnter={(e) => {
                        // Simplified hover effect
                        if (resourcesTab !== tab.id) {
                          e.target.style.color = globalStyles.currentTheme.primary;
                        }
                      }}
                      onMouseLeave={(e) => {
                        // Reset hover effect
                        if (resourcesTab !== tab.id) {
                          e.target.style.color = globalStyles.currentTheme.text;
                        }
                      }}
                      onFocus={(e) => {
                        // Keyboard navigation support
                        e.target.style.outline = `2px solid ${globalStyles.currentTheme.primary}`;
                        e.target.style.outlineOffset = '2px';
                      }}
                      onBlur={(e) => {
                        // Remove focus outline
                        e.target.style.outline = 'none';
                      }}
                      aria-label={`Switch to ${tab.label} tab`}
                      aria-selected={resourcesTab === tab.id}
                      role="tab"
                      tabIndex={0}
                      style={{
                        flex: 1,
                        position: 'relative',
                        zIndex: 20,
                        padding: '0.875rem 1rem',
                        minHeight: '44px', // Touch target requirement
                        border: 'none',
                        background: 'transparent',
                        color: resourcesTab === tab.id
                          ? 'white'
                          : globalStyles.currentTheme.text,
                        cursor: 'pointer',
                        fontSize: '1rem',
                        fontWeight: resourcesTab === tab.id ? 600 : 500,
                        borderRadius: '8px',
                        transition: 'all 0.3s ease',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: '0.5rem',
                        textAlign: 'center',
                        whiteSpace: 'nowrap',
                        userSelect: 'none',
                        WebkitTapHighlightColor: 'transparent' // Remove tap highlight on mobile
                      }}
                    >
                      {/* Simplified responsive display */}
                      <span>{tab.icon}</span>
                      <span style={{ marginLeft: '0.5rem' }}>
                        {windowWidth < 640 ? tab.shortLabel : tab.label.replace(tab.icon + ' ', '')}
                      </span>
                    </button>
                  ))}
                </div>

                {/* Tab Description */}
                <div style={{
                  textAlign: 'center',
                  marginTop: '1rem',
                  padding: '0.5rem',
                  fontSize: '0.9rem',
                  color: globalStyles.currentTheme.textLight,
                  fontStyle: 'italic'
                }}>
                  {resourcesTab === 'general' && 'Upload and manage your personal study materials and documents'}
                  {resourcesTab === 'academics' && 'Access comprehensive academic resources organized by subject'}
                  {resourcesTab === 'tools' && 'Utilize powerful study tools and utilities to enhance your learning'}
                </div>
              </div>

              {/* Content based on selected tab */}
              {resourcesTab === 'general' && (
                <div style={{
                  background: globalStyles.currentTheme.surface,
                  borderRadius: '16px',
                  padding: '2rem',
                  border: `1px solid ${globalStyles.currentTheme.border}`,
                  boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`
                }}>
                  <h2 style={{
                    margin: '0 0 1.5rem 0',
                    fontSize: '1.5rem',
                    fontWeight: 600,
                    color: globalStyles.currentTheme.text
                  }}>
                    📁 General Resources
                  </h2>

                  <div style={{ marginBottom: '2rem' }}>
                    <label style={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      padding: '0.75rem 1.5rem',
                      background: globalStyles.currentTheme.primary,
                      color: 'white',
                      borderRadius: '12px',
                      cursor: resourceUploadLoading ? 'not-allowed' : 'pointer',
                      fontSize: '1rem',
                      fontWeight: 500,
                      transition: 'all 0.3s ease'
                    }}>
                      <FiUpload size={20} />
                      {resourceUploadLoading ? 'Uploading...' : 'Upload Resource'}
                      <input
                        type="file"
                        accept=".pdf,.doc,.docx,.txt"
                        onChange={handleResourceUpload}
                        disabled={resourceUploadLoading}
                        style={{ display: 'none' }}
                      />
                    </label>
                  </div>

                  <div>
                    <h3 style={{
                      margin: '0 0 1rem 0',
                      fontSize: '1.2rem',
                      fontWeight: 600,
                      color: globalStyles.currentTheme.text
                    }}>
                      Your Uploaded Resources
                    </h3>
                    {userResources.length === 0 ? (
                      <div style={{
                        textAlign: 'center',
                        padding: '3rem',
                        background: globalStyles.currentTheme.secondary,
                        borderRadius: '12px',
                        border: `1px solid ${globalStyles.currentTheme.border}`
                      }}>
                        <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📄</div>
                        <h3 style={{
                          margin: '0 0 0.5rem 0',
                          color: globalStyles.currentTheme.text
                        }}>
                          No resources uploaded yet
                        </h3>
                        <p style={{
                          margin: 0,
                          color: globalStyles.currentTheme.textLight
                        }}>
                          Upload your study materials, notes, and documents to get started
                        </p>
                      </div>
                    ) : (
                      <div style={{
                        display: 'grid',
                        gap: '1rem'
                      }}>
                        {userResources.map((file, idx) => {
                          const { data: urlData } = supabase.storage.from('resources').getPublicUrl(`${user.id}/${file.name}`);
                          return (
                            <div key={idx} style={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              alignItems: 'center',
                              padding: '1rem',
                              background: globalStyles.currentTheme.secondary,
                              borderRadius: '12px',
                              border: `1px solid ${globalStyles.currentTheme.border}`
                            }}>
                              <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '1rem'
                              }}>
                                <div style={{
                                  width: '40px',
                                  height: '40px',
                                  borderRadius: '8px',
                                  background: globalStyles.currentTheme.primary,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  color: 'white',
                                  fontSize: '1.2rem'
                                }}>
                                  📄
                                </div>
                                <div>
                                  <div style={{
                                    fontWeight: 600,
                                    color: globalStyles.currentTheme.text,
                                    marginBottom: '0.25rem'
                                  }}>
                                    {file.name}
                                  </div>
                                  <div style={{
                                    fontSize: '0.8rem',
                                    color: globalStyles.currentTheme.textLight
                                  }}>
                                    Uploaded resource
                                  </div>
                                </div>
                              </div>
                              <a
                                href={urlData.publicUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: '0.5rem',
                                  padding: '0.5rem 1rem',
                                  background: globalStyles.currentTheme.primary,
                                  color: 'white',
                                  textDecoration: 'none',
                                  borderRadius: '8px',
                                  fontSize: '0.9rem',
                                  fontWeight: 500,
                                  transition: 'all 0.3s ease'
                                }}
                              >
                                <FiExternalLink size={16} />
                                Open
                              </a>
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Academics Tab Content */}
              {resourcesTab === 'academics' && (
                <div style={{
                  display: 'grid',
                  gap: '2rem'
                }}>
                  {/* Academic Subjects */}
                  <div style={{
                    background: globalStyles.currentTheme.surface,
                    borderRadius: '16px',
                    padding: '2rem',
                    border: `1px solid ${globalStyles.currentTheme.border}`,
                    boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`
                  }}>
                    <h2 style={{
                      margin: '0 0 1.5rem 0',
                      fontSize: '1.5rem',
                      fontWeight: 600,
                      color: globalStyles.currentTheme.text,
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem'
                    }}>
                      🎓 Academic Study Materials
                    </h2>

                    <div style={{
                      display: 'grid',
                      gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                      gap: '1.5rem'
                    }}>
                      {[
                        {
                          subject: 'Mathematics',
                          icon: '📐',
                          color: '#DC2626',
                          topics: ['Calculus', 'Linear Algebra', 'Statistics', 'Discrete Math'],
                          resources: 15
                        },
                        {
                          subject: 'Computer Science',
                          icon: '💻',
                          color: '#7C3AED',
                          topics: ['Data Structures', 'Algorithms', 'Database Systems', 'Software Engineering'],
                          resources: 23
                        },
                        {
                          subject: 'Physics',
                          icon: '⚛️',
                          color: '#059669',
                          topics: ['Mechanics', 'Thermodynamics', 'Electromagnetism', 'Quantum Physics'],
                          resources: 18
                        },
                        {
                          subject: 'Chemistry',
                          icon: '🧪',
                          color: '#EA580C',
                          topics: ['Organic Chemistry', 'Inorganic Chemistry', 'Physical Chemistry', 'Biochemistry'],
                          resources: 12
                        }
                      ].map((subject, index) => (
                        <div key={index} style={{
                          background: globalStyles.currentTheme.secondary,
                          borderRadius: '12px',
                          padding: '1.5rem',
                          border: `1px solid ${globalStyles.currentTheme.border}`,
                          cursor: 'pointer',
                          transition: 'all 0.3s ease'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.transform = 'translateY(-4px)';
                          e.currentTarget.style.boxShadow = `0 8px 25px ${globalStyles.currentTheme.shadow}`;
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.transform = 'translateY(0)';
                          e.currentTarget.style.boxShadow = 'none';
                        }}>
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '1rem',
                            marginBottom: '1rem'
                          }}>
                            <div style={{
                              width: '50px',
                              height: '50px',
                              borderRadius: '12px',
                              background: subject.color,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              fontSize: '1.5rem'
                            }}>
                              {subject.icon}
                            </div>
                            <div>
                              <h3 style={{
                                margin: 0,
                                fontSize: '1.2rem',
                                fontWeight: 600,
                                color: globalStyles.currentTheme.text
                              }}>
                                {subject.subject}
                              </h3>
                              <p style={{
                                margin: 0,
                                fontSize: '0.9rem',
                                color: globalStyles.currentTheme.textLight
                              }}>
                                {subject.resources} resources available
                              </p>
                            </div>
                          </div>

                          <div style={{
                            display: 'flex',
                            flexWrap: 'wrap',
                            gap: '0.5rem',
                            marginBottom: '1rem'
                          }}>
                            {subject.topics.map((topic, topicIndex) => (
                              <span key={topicIndex} style={{
                                padding: '0.25rem 0.5rem',
                                background: subject.color + '15',
                                color: subject.color,
                                borderRadius: '6px',
                                fontSize: '0.8rem',
                                fontWeight: 500
                              }}>
                                {topic}
                              </span>
                            ))}
                          </div>

                          <button style={{
                            width: '100%',
                            padding: '0.75rem',
                            background: subject.color,
                            color: 'white',
                            border: 'none',
                            borderRadius: '8px',
                            fontSize: '0.9rem',
                            fontWeight: 500,
                            cursor: 'pointer',
                            transition: 'all 0.3s ease'
                          }}>
                            Browse Materials
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Exam Preparation */}
                  <div style={{
                    background: globalStyles.currentTheme.surface,
                    borderRadius: '16px',
                    padding: '2rem',
                    border: `1px solid ${globalStyles.currentTheme.border}`,
                    boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`
                  }}>
                    <h2 style={{
                      margin: '0 0 1.5rem 0',
                      fontSize: '1.5rem',
                      fontWeight: 600,
                      color: globalStyles.currentTheme.text,
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem'
                    }}>
                      📝 Exam Preparation Resources
                    </h2>

                    <div style={{
                      display: 'grid',
                      gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                      gap: '1rem'
                    }}>
                      {[
                        { name: 'Previous Year Papers', icon: '📄', count: '50+ papers' },
                        { name: 'Sample Questions', icon: '❓', count: '500+ questions' },
                        { name: 'Study Guides', icon: '📖', count: '25+ guides' },
                        { name: 'Reference Books', icon: '📚', count: '100+ books' },
                        { name: 'Video Lectures', icon: '🎥', count: '200+ videos' },
                        { name: 'Practice Tests', icon: '✅', count: '30+ tests' }
                      ].map((resource, index) => (
                        <div key={index} style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '1rem',
                          padding: '1rem',
                          background: globalStyles.currentTheme.secondary,
                          borderRadius: '12px',
                          border: `1px solid ${globalStyles.currentTheme.border}`,
                          cursor: 'pointer',
                          transition: 'all 0.3s ease'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.background = globalStyles.currentTheme.primary + '10';
                          e.currentTarget.style.borderColor = globalStyles.currentTheme.primary;
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.background = globalStyles.currentTheme.secondary;
                          e.currentTarget.style.borderColor = globalStyles.currentTheme.border;
                        }}>
                          <div style={{
                            width: '40px',
                            height: '40px',
                            borderRadius: '8px',
                            background: globalStyles.currentTheme.primary,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: '1.2rem'
                          }}>
                            {resource.icon}
                          </div>
                          <div>
                            <div style={{
                              fontWeight: 600,
                              color: globalStyles.currentTheme.text,
                              marginBottom: '0.25rem'
                            }}>
                              {resource.name}
                            </div>
                            <div style={{
                              fontSize: '0.8rem',
                              color: globalStyles.currentTheme.textLight
                            }}>
                              {resource.count}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Study Tools Tab Content */}
              {resourcesTab === 'tools' && (
                <div style={{
                  background: globalStyles.currentTheme.surface,
                  borderRadius: '16px',
                  padding: '2rem',
                  border: `1px solid ${globalStyles.currentTheme.border}`,
                  boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`
                }}>
                  <h2 style={{
                    margin: '0 0 1.5rem 0',
                    fontSize: '1.5rem',
                    fontWeight: 600,
                    color: globalStyles.currentTheme.text,
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem'
                  }}>
                    🛠️ Study Tools & Utilities
                  </h2>

                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
                    gap: '1.5rem'
                  }}>
                    {[
                      {
                        name: 'Scientific Calculator',
                        icon: '🧮',
                        description: 'Advanced calculator for complex calculations',
                        color: '#DC2626'
                      },
                      {
                        name: 'Formula Reference',
                        icon: '📐',
                        description: 'Quick access to mathematical and scientific formulas',
                        color: '#7C3AED'
                      },
                      {
                        name: 'Unit Converter',
                        icon: '⚖️',
                        description: 'Convert between different units of measurement',
                        color: '#059669'
                      },
                      {
                        name: 'Study Timer',
                        icon: '⏰',
                        description: 'Pomodoro timer for effective study sessions',
                        color: '#EA580C'
                      },
                      {
                        name: 'Note Taking',
                        icon: '📝',
                        description: 'Digital notepad for quick notes and ideas',
                        color: '#0284C7'
                      },
                      {
                        name: 'Progress Tracker',
                        icon: '📊',
                        description: 'Track your study progress and goals',
                        color: '#7C2D12'
                      }
                    ].map((tool, index) => (
                      <div key={index} style={{
                        background: globalStyles.currentTheme.secondary,
                        borderRadius: '12px',
                        padding: '1.5rem',
                        border: `1px solid ${globalStyles.currentTheme.border}`,
                        cursor: 'pointer',
                        transition: 'all 0.3s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'translateY(-4px)';
                        e.currentTarget.style.boxShadow = `0 8px 25px ${globalStyles.currentTheme.shadow}`;
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'translateY(0)';
                        e.currentTarget.style.boxShadow = 'none';
                      }}>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '1rem',
                          marginBottom: '1rem'
                        }}>
                          <div style={{
                            width: '50px',
                            height: '50px',
                            borderRadius: '12px',
                            background: tool.color,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: '1.5rem'
                          }}>
                            {tool.icon}
                          </div>
                          <div>
                            <h3 style={{
                              margin: 0,
                              fontSize: '1.1rem',
                              fontWeight: 600,
                              color: globalStyles.currentTheme.text
                            }}>
                              {tool.name}
                            </h3>
                          </div>
                        </div>

                        <p style={{
                          margin: '0 0 1rem 0',
                          fontSize: '0.9rem',
                          color: globalStyles.currentTheme.textLight,
                          lineHeight: 1.4
                        }}>
                          {tool.description}
                        </p>

                        <button style={{
                          width: '100%',
                          padding: '0.75rem',
                          background: tool.color,
                          color: 'white',
                          border: 'none',
                          borderRadius: '8px',
                          fontSize: '0.9rem',
                          fontWeight: 500,
                          cursor: 'pointer',
                          transition: 'all 0.3s ease'
                        }}>
                          Launch Tool
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
        {activeTab === "academics" && <Exams />}
        {activeTab === "faq" && <Faq />}
        {activeTab === "admin" && user?.email === ADMIN_EMAIL && (
          <div style={{ padding: '24px' }}>
            <div style={getStyle('card')}>
              <h2 style={{
                marginTop: 0,
                color: '#333'
              }}>Admin Panel</h2>
              <div style={{
                display: 'flex',
                gap: '16px',
                marginBottom: '24px'
              }}>
                <button
                  style={{
                    ...getStyle('buttonPrimary'),
                    background: adminTab === 'users' ?
                      globalStyles.currentTheme.primary : 'transparent',
                    color: adminTab === 'users' ?
                      'white' : '#333',
                    border: '1px solid #ddd'
                  }}
                  onClick={() => setAdminTab('users')}
                >
                  Users
                </button>
                <button
                  style={{
                    ...getStyle('buttonPrimary'),
                    background: adminTab === 'resources' ?
                      globalStyles.currentTheme.primary : 'transparent',
                    color: adminTab === 'resources' ?
                      'white' : '#333',
                    border: '1px solid #ddd'
                  }}
                  onClick={() => setAdminTab('resources')}
                >
                  Resources
                </button>
              </div>

              {adminTab === 'users' && (
                <div>
                  <h3 style={{
                    marginBottom: '16px',
                    color: '#333'
                  }}>All Users</h3>
                  <div style={{
                    backgroundColor: '#f5f5f5',
                    border: '1px solid #e0e0e0',
                    borderRadius: '8px',
                    padding: '16px'
                  }}>
                    {allUsers.map((user, idx) => (
                      <div key={idx} style={{
                        padding: '12px',
                        borderBottom: '1px solid #eee',
                        color: '#333'
                      }}>
                        {user.email}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {adminTab === 'resources' && (
                <div>
                  <h3 style={{
                    marginBottom: '16px',
                    color: '#333'
                  }}>All Resources</h3>
                  <p style={{
                    opacity: 0.7,
                    color: '#666'
                  }}>Resource management coming soon</p>
                </div>
              )}
            </div>
          </div>
        )}
      </main>

      {/* Notification */}
      {notification && (
        <div style={{
          ...getStyle('notification'),
          backgroundColor: notification.type === 'error' ? '#f44336' :
                         notification.type === 'success' ? '#4caf50' : '#2196f3',
          color: 'white',
          border: 'none'
        }}>
          {notification.msg}
        </div>
      )}

      {/* Enhanced Global styles */}
      <style>{`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        @keyframes slideIn {
          from { transform: translateX(100%); }
          to { transform: translateX(0); }
        }
        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.5; }
        }
        @keyframes bounce {
          0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
          40%, 43% { transform: translate3d(0,-8px,0); }
          70% { transform: translate3d(0,-4px,0); }
          90% { transform: translate3d(0,-2px,0); }
        }
        @keyframes glow {
          0%, 100% { box-shadow: 0 0 5px rgba(99, 102, 241, 0.3); }
          50% { box-shadow: 0 0 20px rgba(99, 102, 241, 0.6); }
        }
        @keyframes shimmer {
          0% { background-position: -200px 0; }
          100% { background-position: calc(200px + 100%) 0; }
        }

        /* Optimized Tab Navigation Styles */
        .tab-navigation-container {
          position: relative;
          overflow: hidden;
          contain: layout style paint;
        }

        .tab-button {
          position: relative;
          transition: color 0.15s ease;
          will-change: color;
          z-index: 20;
        }

        .tab-button:hover:not(.active) {
          color: var(--primary-color);
        }

        .tab-button.active {
          color: white;
        }

        .static-background-bar {
          z-index: 1;
          pointer-events: none;
        }

        /* Simplified Responsive Styles */
        @media (max-width: 640px) {
          .tab-button {
            font-size: 0.9rem;
            padding: 0.75rem 0.5rem;
          }
        }

        /* Focus styles for accessibility */
        .tab-button:focus-visible {
          outline: 2px solid var(--primary-color);
          outline-offset: 2px;
        }

        /* Touch device optimizations */
        @media (hover: none) and (pointer: coarse) {
          .tab-button:hover {
            color: inherit;
          }

          .tab-button:active {
            opacity: 0.8;
          }
        }

        /* Enhanced hover effects */
        .company-card:hover {
          animation: bounce 0.6s ease;
        }

        .favorite-btn:hover {
          animation: pulse 0.5s ease;
        }

        /* Smooth transitions for all interactive elements */
        button, input, select {
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        button:hover {
          transform: translateY(-1px);
        }

        * {
          box-sizing: border-box;
        }
        body {
          margin: 0;
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
      `}</style>
    </div>
  );
};
export default EduAIChatBot;