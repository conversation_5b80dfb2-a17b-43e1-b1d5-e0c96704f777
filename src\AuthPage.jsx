import React, { useState, useEffect } from 'react';
import { initializeApp } from 'firebase/app';
import { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword } from 'firebase/auth';
import { getFirestore, doc, setDoc } from 'firebase/firestore';
import emailjs from 'emailjs-com';
import styles from './styles';

// Firebase Config
const firebaseConfig = {
  apiKey: "AIzaSyDmRAxMJvEKmel3JpUF1dXbBNccQJibcGo",
  authDomain: "exploit-f41eb.firebaseapp.com",
  projectId: "exploit-f41eb",
  storageBucket: "exploit-f41eb.firebasestorage.app",
  messagingSenderId: "849491574761",
  appId: "1:849491574761:web:22ddb4ea62063d7a709578",
  measurementId: "G-R0SWYQKXCV"
};

const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

// EmailJS Config
const EMAILJS_SERVICE_ID = 'service_sthdzci';
const EMAILJS_TEMPLATE_ID = 'template_8mnpzqq';
const EMAILJS_PUBLIC_KEY = 'jEfQd05ItbAthM3QN';

const AuthPage = ({ onAuthSuccess }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [otp, setOtp] = useState('');
  const [generatedOtp, setGeneratedOtp] = useState('');
  const [isOtpSent, setIsOtpSent] = useState(false);
  const [profilePic, setProfilePic] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSignupMode, setIsSignupMode] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [particles, setParticles] = useState([]);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isFormHovered, setIsFormHovered] = useState(false);
  const [showCelebration, setShowCelebration] = useState(false);

  // Create floating particles effect
  useEffect(() => {
    const createParticles = () => {
      const newParticles = [];
      for (let i = 0; i < 15; i++) {
        newParticles.push({
          id: i,
          x: Math.random() * 100,
          y: Math.random() * 100,
          size: Math.random() * 4 + 2,
          speed: Math.random() * 2 + 1,
          opacity: Math.random() * 0.5 + 0.2,
        });
      }
      setParticles(newParticles);
    };
    createParticles();

    // Mouse tracking for interactive effects
    const handleMouseMove = (e) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const generateOtp = () => {
    const otp = Math.floor(100000 + Math.random() * 900000); // Generate a 6-digit OTP
    setGeneratedOtp(otp);
    return otp;
  };

  const sendOtpEmail = (email, otp) => {
    const templateParams = {
      to_email: email,
      otp: otp
    };

    emailjs.send(EMAILJS_SERVICE_ID, EMAILJS_TEMPLATE_ID, templateParams, EMAILJS_PUBLIC_KEY)
      .then((response) => {
        console.log('OTP sent!', response.status, response.text);
      }, (err) => {
        console.error('OTP sending failed:', err);
      });
  };

  const handleProfilePicChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setProfilePic(reader.result); // Store image in base64
      };
      reader.readAsDataURL(file); // Convert file to base64
    }
  };

  const handleSignup = async () => {
    if (!email || !password) {
      setError('Please fill in all fields');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const otp = generateOtp();
      sendOtpEmail(email, otp);
      setIsOtpSent(true);

      // Save user data to Firestore
      const userRef = doc(db, "users", userCredential.user.uid);
      await setDoc(userRef, {
        email: email,
        uid: userCredential.user.uid,
        createdAt: new Date(),
        dp: profilePic,
      });

    } catch (error) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleOtpVerification = () => {
    if (otp === generatedOtp.toString()) {
      setShowCelebration(true);
      setTimeout(() => {
        setIsOtpSent(false);
        setOtp('');
        setIsSignupMode(false);
        setError('');
        setShowCelebration(false);
        alert('🎉 OTP Verified! You can now login with your credentials.');
      }, 2000);
    } else {
      setError('Invalid OTP. Please try again.');
    }
  };

  const handleLogin = async () => {
    if (!email || !password) {
      setError('Please fill in all fields');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      onAuthSuccess();
    } catch (error) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{
      ...styles.authPageContainer,
      background: `
        linear-gradient(135deg, ${styles.currentTheme.primary}15 0%, ${styles.currentTheme.accent}10 25%, ${styles.currentTheme.primaryLight}20 50%, ${styles.currentTheme.secondary}30 75%, ${styles.currentTheme.primary}10 100%),
        radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, ${styles.currentTheme.primary}20 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, ${styles.currentTheme.accent}20 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, ${styles.currentTheme.primaryLight}15 0%, transparent 50%)
      `,
    }}>
      {/* Animated Background */}
      <div style={{
        ...styles.authBackgroundAnimation,
        transform: `translate(${mousePosition.x * 0.02}px, ${mousePosition.y * 0.02}px)`,
      }}></div>

      {/* Floating Particles */}
      {particles.map((particle) => (
        <div
          key={particle.id}
          style={{
            position: 'absolute',
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            background: styles.currentTheme.primary,
            borderRadius: '50%',
            opacity: particle.opacity,
            animation: `floatParticle${(particle.id % 2) + 1} ${12 + particle.speed * 3}s infinite linear`,
            zIndex: 1,
            pointerEvents: 'none',
          }}
        />
      ))}

      {/* Celebration Effect */}
      {showCelebration && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          pointerEvents: 'none',
          zIndex: 1000,
        }}>
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              style={{
                position: 'absolute',
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                fontSize: '2rem',
                animation: `confetti ${2 + Math.random() * 2}s ease-out forwards`,
                animationDelay: `${Math.random() * 0.5}s`,
              }}
            >
              {['🎉', '✨', '🎊', '🌟', '💫'][Math.floor(Math.random() * 5)]}
            </div>
          ))}
        </div>
      )}

      {/* Main Form Container */}
      <div
        style={{
          ...styles.authFormContainer,
          transform: isFormHovered
            ? `translateY(-8px) scale(1.02) rotateX(${mousePosition.y * 0.01}deg) rotateY(${mousePosition.x * 0.01}deg)`
            : 'translateY(0) scale(1)',
          boxShadow: isFormHovered
            ? `
              0 35px 60px -12px ${styles.currentTheme.shadow},
              0 20px 30px -8px ${styles.currentTheme.shadow},
              inset 0 1px 0 rgba(255, 255, 255, 0.3),
              0 0 0 1px rgba(255, 255, 255, 0.2)
            `
            : styles.authFormContainer.boxShadow,
        }}
        onMouseEnter={() => setIsFormHovered(true)}
        onMouseLeave={() => setIsFormHovered(false)}
      >
        <h1 style={styles.authFormTitle}>
          {isSignupMode ? '✨ Create Account' : '🎯 Welcome Back'}
        </h1>
        <p style={styles.authFormSubtitle}>
          {isSignupMode
            ? '🚀 Join our learning platform and start your amazing journey'
            : '💫 Sign in to continue your learning adventure'
          }
        </p>

        {/* Progress Indicator */}
        {isSignupMode && !isOtpSent && (
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            marginBottom: '2rem',
            gap: '0.5rem',
          }}>
            <div style={{
              width: '12px',
              height: '12px',
              borderRadius: '50%',
              background: email && password ? styles.currentTheme.primary : styles.currentTheme.border,
              transition: 'all 0.3s ease',
              animation: email && password ? 'pulse 2s infinite' : 'none',
            }}></div>
            <div style={{
              width: '12px',
              height: '12px',
              borderRadius: '50%',
              background: profilePic ? styles.currentTheme.primary : styles.currentTheme.border,
              transition: 'all 0.3s ease',
              animation: profilePic ? 'pulse 2s infinite' : 'none',
            }}></div>
            <div style={{
              width: '12px',
              height: '12px',
              borderRadius: '50%',
              background: styles.currentTheme.border,
              transition: 'all 0.3s ease',
            }}></div>
          </div>
        )}

        {!isOtpSent ? (
          <>
            {/* Email Input */}
            <div style={styles.authInputGroup}>
              <div style={{ position: 'relative' }}>
                <span style={{
                  position: 'absolute',
                  left: '1.25rem',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  fontSize: '1.2rem',
                  color: styles.currentTheme.textLight,
                  zIndex: 1,
                }}>
                  📧
                </span>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email address"
                  style={{
                    ...styles.authInput,
                    paddingLeft: '3.5rem',
                  }}
                  disabled={isLoading}
                />
              </div>
            </div>

            {/* Password Input */}
            <div style={styles.authInputGroup}>
              <div style={{ position: 'relative' }}>
                <span style={{
                  position: 'absolute',
                  left: '1.25rem',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  fontSize: '1.2rem',
                  color: styles.currentTheme.textLight,
                  zIndex: 1,
                }}>
                  🔒
                </span>
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your password"
                  style={{
                    ...styles.authInput,
                    paddingLeft: '3.5rem',
                    paddingRight: '3.5rem',
                  }}
                  disabled={isLoading}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  style={{
                    position: 'absolute',
                    right: '1.25rem',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    background: 'none',
                    border: 'none',
                    fontSize: '1.2rem',
                    cursor: 'pointer',
                    color: styles.currentTheme.textLight,
                    zIndex: 1,
                  }}
                  disabled={isLoading}
                >
                  {showPassword ? '🙈' : '👁️'}
                </button>
              </div>
            </div>

            {/* Profile Picture Upload (only for signup) */}
            {isSignupMode && (
              <div style={styles.authInputGroup}>
                <div style={{ textAlign: 'center' }}>
                  <label style={{
                    display: 'block',
                    padding: '1.5rem',
                    border: `2px dashed ${styles.currentTheme.primary}`,
                    borderRadius: '16px',
                    background: 'rgba(255, 255, 255, 0.5)',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    ':hover': {
                      background: 'rgba(255, 255, 255, 0.8)',
                      transform: 'translateY(-2px)',
                    }
                  }}>
                    <span style={{ fontSize: '2rem', display: 'block', marginBottom: '0.5rem' }}>
                      📸
                    </span>
                    <span style={{ color: styles.currentTheme.textLight, fontSize: '0.9rem' }}>
                      {profilePic ? 'Change Profile Picture' : 'Upload Profile Picture'}
                    </span>
                    <input
                      type="file"
                      onChange={handleProfilePicChange}
                      accept="image/*"
                      style={{ display: 'none' }}
                      disabled={isLoading}
                    />
                  </label>
                  {profilePic && (
                    <div style={{ marginTop: '1rem', position: 'relative', display: 'inline-block' }}>
                      <img
                        src={profilePic}
                        alt="Profile Preview"
                        style={{
                          ...styles.authProfilePreview,
                          animation: 'fadeIn 0.5s ease',
                        }}
                      />
                      <div style={{
                        position: 'absolute',
                        top: '-5px',
                        right: '-5px',
                        background: styles.currentTheme.primary,
                        color: 'white',
                        borderRadius: '50%',
                        width: '24px',
                        height: '24px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '0.8rem',
                        animation: 'pulse 2s infinite',
                      }}>
                        ✓
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div style={styles.authButtonGroup}>
              {isSignupMode ? (
                <>
                  <button
                    onClick={handleSignup}
                    disabled={isLoading}
                    style={styles.authButtonPrimary}
                  >
                    {isLoading && <div style={styles.authLoadingSpinner}></div>}
                    {isLoading ? '🚀 Creating Magic...' : '✨ Create Account'}
                  </button>
                  <button
                    onClick={() => setIsSignupMode(false)}
                    disabled={isLoading}
                    style={styles.authButtonSecondary}
                  >
                    🔑 Login Instead
                  </button>
                </>
              ) : (
                <>
                  <button
                    onClick={handleLogin}
                    disabled={isLoading}
                    style={styles.authButtonPrimary}
                  >
                    {isLoading && <div style={styles.authLoadingSpinner}></div>}
                    {isLoading ? '🎯 Signing In...' : '🚀 Sign In'}
                  </button>
                  <button
                    onClick={() => setIsSignupMode(true)}
                    disabled={isLoading}
                    style={styles.authButtonSecondary}
                  >
                    ✨ Create Account
                  </button>
                </>
              )}
            </div>
          </>
        ) : (
          /* OTP Verification Section */
          <div style={styles.authOtpContainer}>
            <div style={{ textAlign: 'center', marginBottom: '1.5rem' }}>
              <div style={{
                fontSize: '3rem',
                marginBottom: '1rem',
                animation: 'pulse 2s infinite',
              }}>
                📧
              </div>
              <h3 style={styles.authOtpTitle}>🔐 Verify Your Email</h3>
              <p style={{ color: styles.currentTheme.textLight, marginBottom: '1rem' }}>
                ✨ We've sent a magical 6-digit code to<br />
                <strong style={{ color: styles.currentTheme.primary }}>{email}</strong>
              </p>
            </div>
            <div style={{ position: 'relative' }}>
              <span style={{
                position: 'absolute',
                left: '1rem',
                top: '50%',
                transform: 'translateY(-50%)',
                fontSize: '1.2rem',
                color: styles.currentTheme.textLight,
                zIndex: 1,
              }}>
                🔢
              </span>
              <input
                type="text"
                value={otp}
                onChange={(e) => setOtp(e.target.value)}
                placeholder="Enter 6-digit code"
                maxLength="6"
                style={{
                  ...styles.authOtpInput,
                  paddingLeft: '3rem',
                }}
              />
            </div>
            <button
              onClick={handleOtpVerification}
              style={{
                ...styles.authOtpButton,
                background: styles.currentTheme.gradient,
                animation: otp.length === 6 ? 'buttonPulse 2s infinite' : 'none',
              }}
            >
              🎯 Verify & Continue
            </button>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div style={{
            ...styles.authErrorMessage,
            animation: 'slideIn 0.3s ease',
          }}>
            <span style={{ marginRight: '0.5rem' }}>⚠️</span>
            {error}
          </div>
        )}

        {/* Success Indicators */}
        {email && password && !error && (
          <div style={{
            textAlign: 'center',
            marginTop: '1rem',
            color: styles.currentTheme.primary,
            fontSize: '0.9rem',
            animation: 'fadeIn 0.5s ease',
          }}>
            ✅ Ready to {isSignupMode ? 'create your account' : 'sign in'}!
          </div>
        )}
      </div>
    </div>
  );
};

export default AuthPage;