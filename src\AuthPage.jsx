import React, { useState } from 'react';
import { initializeApp } from 'firebase/app';
import { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword } from 'firebase/auth';
import { getFirestore, doc, setDoc } from 'firebase/firestore';
import emailjs from 'emailjs-com';
import styles from './styles';

// Firebase Config
const firebaseConfig = {
  apiKey: "AIzaSyDmRAxMJvEKmel3JpUF1dXbBNccQJibcGo",
  authDomain: "exploit-f41eb.firebaseapp.com",
  projectId: "exploit-f41eb",
  storageBucket: "exploit-f41eb.firebasestorage.app",
  messagingSenderId: "849491574761",
  appId: "1:849491574761:web:22ddb4ea62063d7a709578",
  measurementId: "G-R0SWYQKXCV"
};

const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

// EmailJS Config
const EMAILJS_SERVICE_ID = 'service_sthdzci';
const EMAILJS_TEMPLATE_ID = 'template_8mnpzqq';
const EMAILJS_PUBLIC_KEY = 'jEfQd05ItbAthM3QN';

const AuthPage = ({ onAuthSuccess }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [otp, setOtp] = useState('');
  const [generatedOtp, setGeneratedOtp] = useState('');
  const [isOtpSent, setIsOtpSent] = useState(false);
  const [profilePic, setProfilePic] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSignupMode, setIsSignupMode] = useState(true);

  const generateOtp = () => {
    const otp = Math.floor(100000 + Math.random() * 900000); // Generate a 6-digit OTP
    setGeneratedOtp(otp);
    return otp;
  };

  const sendOtpEmail = (email, otp) => {
    const templateParams = {
      to_email: email,
      otp: otp
    };

    emailjs.send(EMAILJS_SERVICE_ID, EMAILJS_TEMPLATE_ID, templateParams, EMAILJS_PUBLIC_KEY)
      .then((response) => {
        console.log('OTP sent!', response.status, response.text);
      }, (err) => {
        console.error('OTP sending failed:', err);
      });
  };

  const handleProfilePicChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setProfilePic(reader.result); // Store image in base64
      };
      reader.readAsDataURL(file); // Convert file to base64
    }
  };

  const handleSignup = async () => {
    if (!email || !password) {
      setError('Please fill in all fields');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const otp = generateOtp();
      sendOtpEmail(email, otp);
      setIsOtpSent(true);

      // Save user data to Firestore
      const userRef = doc(db, "users", userCredential.user.uid);
      await setDoc(userRef, {
        email: email,
        uid: userCredential.user.uid,
        createdAt: new Date(),
        dp: profilePic,
      });

    } catch (error) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleOtpVerification = () => {
    if (otp === generatedOtp.toString()) {
      setIsOtpSent(false);
      setOtp('');
      setIsSignupMode(false);
      setError('');
      alert('OTP Verified! You can now login with your credentials.');
    } else {
      setError('Invalid OTP. Please try again.');
    }
  };

  const handleLogin = async () => {
    if (!email || !password) {
      setError('Please fill in all fields');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      onAuthSuccess();
    } catch (error) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={styles.authPageContainer}>
      {/* Animated Background */}
      <div style={styles.authBackgroundAnimation}></div>

      {/* Main Form Container */}
      <div style={styles.authFormContainer}>
        <h1 style={styles.authFormTitle}>
          {isSignupMode ? 'Create Account' : 'Welcome Back'}
        </h1>
        <p style={styles.authFormSubtitle}>
          {isSignupMode
            ? 'Join our learning platform and start your journey'
            : 'Sign in to continue your learning journey'
          }
        </p>

        {!isOtpSent ? (
          <>
            {/* Email Input */}
            <div style={styles.authInputGroup}>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
                style={styles.authInput}
                disabled={isLoading}
              />
            </div>

            {/* Password Input */}
            <div style={styles.authInputGroup}>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your password"
                style={styles.authInput}
                disabled={isLoading}
              />
            </div>

            {/* Profile Picture Upload (only for signup) */}
            {isSignupMode && (
              <div style={styles.authInputGroup}>
                <input
                  type="file"
                  onChange={handleProfilePicChange}
                  accept="image/*"
                  style={styles.authFileInput}
                  disabled={isLoading}
                />
                {profilePic && (
                  <img
                    src={profilePic}
                    alt="Profile Preview"
                    style={styles.authProfilePreview}
                  />
                )}
              </div>
            )}

            {/* Action Buttons */}
            <div style={styles.authButtonGroup}>
              {isSignupMode ? (
                <>
                  <button
                    onClick={handleSignup}
                    disabled={isLoading}
                    style={styles.authButtonPrimary}
                  >
                    {isLoading && <div style={styles.authLoadingSpinner}></div>}
                    {isLoading ? 'Creating Account...' : 'Sign Up'}
                  </button>
                  <button
                    onClick={() => setIsSignupMode(false)}
                    disabled={isLoading}
                    style={styles.authButtonSecondary}
                  >
                    Login Instead
                  </button>
                </>
              ) : (
                <>
                  <button
                    onClick={handleLogin}
                    disabled={isLoading}
                    style={styles.authButtonPrimary}
                  >
                    {isLoading && <div style={styles.authLoadingSpinner}></div>}
                    {isLoading ? 'Signing In...' : 'Sign In'}
                  </button>
                  <button
                    onClick={() => setIsSignupMode(true)}
                    disabled={isLoading}
                    style={styles.authButtonSecondary}
                  >
                    Create Account
                  </button>
                </>
              )}
            </div>
          </>
        ) : (
          /* OTP Verification Section */
          <div style={styles.authOtpContainer}>
            <h3 style={styles.authOtpTitle}>Verify Your Email</h3>
            <p style={{ color: styles.currentTheme.textLight, marginBottom: '1rem' }}>
              We've sent a 6-digit code to {email}
            </p>
            <input
              type="text"
              value={otp}
              onChange={(e) => setOtp(e.target.value)}
              placeholder="Enter OTP"
              maxLength="6"
              style={styles.authOtpInput}
            />
            <button
              onClick={handleOtpVerification}
              style={styles.authOtpButton}
            >
              Verify OTP
            </button>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div style={styles.authErrorMessage}>
            {error}
          </div>
        )}
      </div>
    </div>
  );
};

export default AuthPage;