// styles.js - Centralized Theme System

// Color Themes Configuration
const themes = {
  mustardGreen: {
    primary: '#8B7355',
    primaryDark: '#6B5B47',
    primaryLight: '#A0956B',
    secondary: '#F5F3F0',
    accent: '#9A8B73',
    background: '#f0f4f8',
    surface: '#ffffff',
    text: '#2d3748',
    textLight: '#718096',
    textDark: '#1a202c',
    border: '#e2e8f0',
    shadow: 'rgba(139, 115, 85, 0.1)',
    shadowDark: 'rgba(139, 115, 85, 0.3)',
    gradient: 'linear-gradient(135deg, #8B7355 0%, #A0956B 100%)',
    gradientReverse: 'linear-gradient(135deg, #A0956B 0%, #8B7355 100%)',
    gradientLight: 'linear-gradient(135deg, #F5F3F0 0%, #E8E2D5 100%)',
  },
  redTheme: {
    primary: '#DC2626',
    primaryDark: '#B91C1C',
    primaryLight: '#EF4444',
    secondary: '#FEE2E2',
    accent: '#F87171',
    background: '#FEF2F2',
    surface: '#ffffff',
    text: '#2d3748',
    textLight: '#718096',
    textDark: '#1a202c',
    border: '#e2e8f0',
    shadow: 'rgba(220, 38, 38, 0.1)',
    shadowDark: 'rgba(220, 38, 38, 0.3)',
    gradient: 'linear-gradient(135deg, #DC2626 0%, #EF4444 100%)',
    gradientReverse: 'linear-gradient(135deg, #EF4444 0%, #DC2626 100%)',
    gradientLight: 'linear-gradient(135deg, #FEE2E2 0%, #FECACA 100%)',
  },
  softRedGradient: {
    primary: '#FFB6B6',
    primaryDark: '#FF99CC',
    primaryLight: '#FFC5C5',
    secondary: '#FFF0F0',
    accent: '#FFE0E0',
    background: '#FFF5F5',
    surface: '#ffffff',
    text: '#2d3748',
    textLight: '#718096',
    textDark: '#1a202c',
    border: '#e2e8f0',
    shadow: 'rgba(255, 182, 182, 0.1)',
    shadowDark: 'rgba(255, 182, 182, 0.3)',
    gradient: 'linear-gradient(135deg, #FFC5C5 0%, #FFB6B6 100%)',
    gradientReverse: 'linear-gradient(135deg, #FFB6B6 0%, #FF99CC 100%)',
    gradientLight: 'linear-gradient(135deg, #FFF0F0 0%, #FFE0E0 100%)',
  },
  purpleTheme: {
    primary: '#7C3AED',
    primaryDark: '#5B21B6',
    primaryLight: '#8B5CF6',
    secondary: '#F3E8FF',
    accent: '#A78BFA',
    background: '#FEFBFF',
    surface: '#ffffff',
    text: '#2d3748',
    textLight: '#718096',
    textDark: '#1a202c',
    border: '#e2e8f0',
    shadow: 'rgba(124, 58, 237, 0.1)',
    shadowDark: 'rgba(124, 58, 237, 0.3)',
    gradient: 'linear-gradient(135deg, #7C3AED 0%, #8B5CF6 100%)',
    gradientReverse: 'linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%)',
    gradientLight: 'linear-gradient(135deg, #F3E8FF 0%, #E9D5FF 100%)',
  },
  darkMode: {
    primary: '#8B7355',
    primaryDark: '#6B5B47',
    primaryLight: '#A0956B',
    secondary: '#252525',
    accent: '#333333',
    background: '#121212',
    surface: '#1e1e1e',
    text: '#e0e0e0',
    textLight: '#a0a0a0',
    textDark: '#ffffff',
    border: '#333333',
    shadow: 'rgba(0, 0, 0, 0.3)',
    shadowDark: 'rgba(0, 0, 0, 0.5)',
    gradient: 'linear-gradient(135deg, #8B7355 0%, #A0956B 100%)',
    gradientReverse: 'linear-gradient(135deg, #A0956B 0%, #8B7355 100%)',
    gradientLight: 'linear-gradient(135deg, #252525 0%, #333333 100%)',
  }
};

// Current theme selector - can be changed to switch themes
const currentTheme = themes.purpleTheme; // Change this to switch themes globally

// Helper function to get theme colors
const getTheme = (themeName = 'purpleTheme') => {
  return themes[themeName] || themes.purpleTheme;
};

// Helper function to switch themes globally
const switchTheme = (themeName) => {
  if (themes[themeName]) {
    // Update currentTheme reference
    Object.assign(currentTheme, themes[themeName]);
    return true;
  }
  return false;
};

// Helper function to get all available theme names
const getAvailableThemes = () => {
  return Object.keys(themes);
};

const styles = {
  // Theme configuration
  themes,
  currentTheme,
  getTheme,
  switchTheme,
  getAvailableThemes,

  // Base styles
  background: {
    minHeight: "100vh",
    backgroundColor: currentTheme.background,
    display: "flex",
    flexDirection: "column",
    fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, sans-serif",
    position: "relative",
    overflowX: "hidden",
  },
    animatedBackground: {
      position: "fixed",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: "linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)",
      zIndex: 0,
      "::before": {
        content: '""',
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundImage: `
          radial-gradient(circle at 10% 20%, rgba(255, 94, 94, 0.1) 0%, transparent 20%),
          radial-gradient(circle at 90% 30%, rgba(94, 163, 255, 0.1) 0%, transparent 25%),
          radial-gradient(circle at 50% 80%, rgba(255, 215, 94, 0.1) 0%, transparent 20%)
        `,
        animation: "moveBackground 20s infinite alternate",
      },
    },
    navbar: {
      width: "100%",
      background: currentTheme.gradient,
      boxShadow: `0 4px 6px ${currentTheme.shadow}`,
      padding: "0.75rem 2rem",
      position: "sticky",
      top: 0,
      zIndex: 1000,
      backdropFilter: "blur(8px)",
      borderBottom: `1px solid ${currentTheme.border}`,
    },
    navContainer: {
      width: "100%",
      maxWidth: "1400px",
      margin: "0 auto",
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
    },
    menuButton: {
      background: "transparent",
      border: "none",
      cursor: "pointer",
      color: "#4a5568",
      padding: "0.5rem",
      borderRadius: "8px",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      transition: "all 0.2s ease",
      ":hover": {
        background: "rgba(0, 0, 0, 0.05)",
        color: "#3182ce",
      },
      "@media (min-width: 768px)": {
        display: "none",
      },
    },
    centerTitleContainer: {
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      justifyContent: "center",
      flex: 1,
      textAlign: "center",
    },
    mainTitle: {
      fontSize: "1.25rem",
      fontWeight: "bold",
      background: currentTheme.gradient,
      WebkitBackgroundClip: "text",
      WebkitTextFillColor: "transparent",
      letterSpacing: "0.5px",
      textTransform: "uppercase",
    },
    subTitle: {
      fontSize: "0.75rem",
      color: currentTheme.textLight,
      marginTop: "0.25rem",
      fontWeight: 500,
      letterSpacing: "0.5px",
    },
    rightLogoContainer: {
      display: "flex",
      alignItems: "center",
    },
    logoImage: {
      width: "40px",
      height: "40px",
      borderRadius: "50%",
      objectFit: "cover",
      border: `2px solid ${currentTheme.primary}`,
      boxShadow: `0 2px 4px ${currentTheme.shadow}`,
    },
    sidebar: {
      position: "fixed",
      top: 0,
      left: 0,
      width: "280px",
      height: "100vh",
      backgroundColor: currentTheme.surface,
      boxShadow: `4px 0 15px ${currentTheme.shadow}`,
      zIndex: 1100,
      transition: "transform 0.3s ease-in-out",
      display: "flex",
      flexDirection: "column",
      overflowY: "auto",
    },
    sidebarHeader: {
      padding: "1.5rem 1.5rem 1rem",
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      borderBottom: `1px solid ${currentTheme.border}`,
    },
    sidebarTitle: {
      fontSize: "1.25rem",
      fontWeight: "600",
      color: currentTheme.text,
    },
    closeSidebarButton: {
      background: "transparent",
      border: "none",
      cursor: "pointer",
      color: "#4a5568",
      padding: "0.25rem",
      borderRadius: "4px",
      transition: "all 0.2s ease",
      ":hover": {
        background: "rgba(0, 0, 0, 0.05)",
        color: "#3182ce",
      },
    },
    sidebarContent: {
      flex: 1,
      padding: "1rem 0",
      overflowY: "auto",
    },
    sidebarItemGroup: {
      marginBottom: "0.5rem",
    },
    sidebarItem: {
      display: "flex",
      alignItems: "center",
      padding: "0.75rem 1.5rem",
      cursor: "pointer",
      transition: "all 0.2s ease",
      position: "relative",
      ":hover": {
        background: `${currentTheme.shadow}`,
        color: currentTheme.primary,
      },
    },
    sidebarItemActive: {
      background: `${currentTheme.shadow}`,
      color: currentTheme.primary,
      fontWeight: "500",
    },
    sidebarIcon: {
      marginRight: "1rem",
      fontSize: "1.1rem",
      display: "flex",
      alignItems: "center",
    },
    sidebarItemText: {
      flex: 1,
    },
    sidebarExpandIcon: {
      transition: "transform 0.2s ease",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      padding: "0.25rem",
      borderRadius: "4px",
      ":hover": {
        background: "rgba(0, 0, 0, 0.05)",
      },
    },
    subItemsContainer: {
      paddingLeft: "2.5rem",
      overflow: "hidden",
      transition: "all 0.3s ease",
    },
    subItem: {
      padding: "0.65rem 1.5rem",
      fontSize: "0.9rem",
      color: currentTheme.textLight,
      cursor: "pointer",
      transition: "all 0.2s ease",
      ":hover": {
        background: `${currentTheme.shadow}`,
        color: currentTheme.primary,
      },
    },
    sidebarFooter: {
      padding: "1.5rem",
      borderTop: "1px solid rgba(0, 0, 0, 0.05)",
      textAlign: "center",
    },
    sidebarFooterText: {
      fontSize: "0.8rem",
      color: "#718096",
    },
    overlay: {
      position: "fixed",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      zIndex: 1090,
      "@media (min-width: 768px)": {
        display: "none",
      },
    },
    mainContainer: {
      width: "100%",
      maxWidth: "1400px",
      margin: "2rem auto",
      padding: "0 2rem",
      flex: 1,
      display: "flex",
      justifyContent: "center",
      alignItems: "flex-start",
      position: "relative",
      zIndex: 1,
      "@media (max-width: 768px)": {
        margin: "1rem auto",
        padding: "0 1rem",
      },
    },
    resumeContainer: {
      width: "100%",
      maxWidth: "800px",
      backgroundColor: currentTheme.surface,
      borderRadius: "16px",
      overflow: "hidden",
      boxShadow: `0 10px 25px ${currentTheme.shadow}`,
      position: "relative",
      border: `1px solid ${currentTheme.border}`,
      display: "flex",
      flexDirection: "column",
      height: "calc(100vh - 120px)",
      "@media (max-width: 768px)": {
          height: "calc(100vh - 100px)",
          borderRadius: "12px",
      },
      "@media (max-width: 480px)": {
          height: "calc(100vh - 80px)",
          borderRadius: "8px",
      }
  },
  gridOverlay: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundImage: `
        linear-gradient(rgba(139, 115, 85, 0.03) 1px, transparent 1px),
        linear-gradient(90deg, rgba(139, 115, 85, 0.03) 1px, transparent 1px)
      `,
      backgroundSize: "20px 20px",
      pointerEvents: "none",
    },
    header: {
      background: currentTheme.gradient,
      color: "white",
      padding: "1.75rem 2rem",
      textAlign: "center",
      position: "relative",
      overflow: "hidden",
      "@media (max-width: 768px)": {
          padding: "1.5rem 1.5rem",
      },
      "@media (max-width: 480px)": {
          padding: "1.25rem 1rem",
      },
      "::before": {
          content: '""',
          position: "absolute",
          top: "-50%",
          left: "-50%",
          right: "-50%",
          bottom: "-50%",
          background: `
              radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)
          `,
          transform: "rotate(30deg)",
          animation: "shine 3s infinite",
      }
  },
  title: {
      margin: 0,
      fontSize: "1.75rem",
      fontWeight: 700,
      position: "relative",
      textShadow: "0 2px 4px rgba(0,0,0,0.1)",
    },
    subtitle: {
      marginTop: "0.75rem",
      fontSize: "1rem",
      opacity: 0.9,
      position: "relative",
      fontWeight: 400,
    },
    chatContainer: {
      height: "calc(100vh - 200px)",
      maxHeight: "800px",
      minHeight: "400px",
      overflowY: "auto",
      padding: "1.5rem",
      background: currentTheme.surface,
      position: "relative",
      display: "flex",
      flexDirection: "column",
      width: "100%",
      boxSizing: "border-box",
      "@media (max-width: 1024px)": {
        height: "calc(100vh - 160px)",
        padding: "1rem",
      },
      "@media (max-width: 768px)": {
        height: "calc(100vh - 140px)",
        padding: "0.75rem",
        minHeight: "300px",
      },
      "@media (max-width: 480px)": {
        height: "calc(100vh - 120px)",
        padding: "0.5rem",
        minHeight: "180px",
      }
    },
    chatMessages: {
      display: "flex",
      flexDirection: "column",
      gap: "1rem",
      position: "relative",
      flex: 1,
      overflowY: "auto",
      paddingRight: "0.5rem",
      scrollBehavior: "smooth",
      width: "100%",
      boxSizing: "border-box",
      "@media (max-width: 768px)": {
        gap: "0.5rem",
        paddingRight: "0.25rem",
      },
      "@media (max-width: 480px)": {
        gap: "0.25rem",
        paddingRight: 0,
      }
    },
    message: {
      padding: "1rem 1.25rem",
      borderRadius: "12px",
      maxWidth: "85%",
      width: "fit-content",
      boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
      lineHeight: 1.6,
      position: "relative",
      transition: "all 0.3s ease",
      fontSize: "1rem",
      animation: "fadeIn 0.3s ease-out",
      wordWrap: "break-word",
      "@media (max-width: 768px)": {
          maxWidth: "90%",
          padding: "0.875rem 1rem",
      },
      "@media (max-width: 480px)": {
          maxWidth: "95%",
          padding: "0.75rem 0.875rem",
          fontSize: "0.95rem",
      }
    },
    welcomeMessage: {
      background: currentTheme.secondary,
      padding: "1.5rem",
      borderRadius: "12px",
      textAlign: "center",
      margin: "0.5rem 0 1.5rem",
      border: `1px dashed ${currentTheme.primary}`,
      backdropFilter: "blur(5px)",
      animation: "fadeIn 0.8s ease-out",
    },
    welcomeTitle: {
      fontSize: "1.25rem",
      fontWeight: 600,
      color: currentTheme.primary,
      marginBottom: "0.75rem",
    },
    welcomeText: {
      fontSize: "1rem",
      color: currentTheme.textLight,
      lineHeight: 1.6,
    },
    userMessage: {
      alignSelf: "flex-end",
      background: currentTheme.gradient,
      color: "white",
      borderBottomRightRadius: "4px",
    },
    botMessage: {
      alignSelf: "flex-start",
      background: currentTheme.secondary,
      color: currentTheme.text,
      borderBottomLeftRadius: "4px",
      border: `1px solid ${currentTheme.border}`,
      backdropFilter: "blur(5px)",
    },
    messageRole: {
      fontSize: "0.75rem",
      fontWeight: 600,
      marginBottom: "0.5rem",
      opacity: 0.8,
      textTransform: "uppercase",
      letterSpacing: "0.5px",
    },
    messageContent: {
      fontSize: "1rem",
      whiteSpace: "pre-wrap",
    },
    inputContainer: {
      display: "flex",
      padding: "1.25rem",
      background: currentTheme.secondary,
      borderTop: `1px solid ${currentTheme.border}`,
      position: "relative",
      backdropFilter: "blur(5px)",
      transition: "all 0.3s ease",
      transform: "translateY(0)",
      maxHeight: "120px",
      width: "100%",
      boxSizing: "border-box",
      "&.focused": {
        background: currentTheme.surface,
        boxShadow: `0 -4px 10px ${currentTheme.shadow}`,
      },
      "@media (max-width: 1024px)": {
        padding: "1rem",
        maxHeight: "100px",
      },
      "@media (max-width: 768px)": {
        padding: "0.75rem",
        flexDirection: "column",
        gap: "0.75rem",
        maxHeight: "90px",
      },
      "@media (max-width: 480px)": {
        padding: "0.5rem",
        flexDirection: "column",
        gap: "0.5rem",
        maxHeight: "70px",
      }
    },
    input: {
      flex: 1,
      padding: "1rem 1.5rem",
      borderRadius: "8px",
      border: `1px solid ${currentTheme.border}`,
      outline: "none",
      fontSize: "1rem",
      transition: "all 0.3s ease",
      background: currentTheme.surface,
      color: currentTheme.text,
      boxShadow: `0 1px 2px ${currentTheme.shadow}`,
      "&:focus": {
        borderColor: currentTheme.primary,
        boxShadow: `0 0 0 3px ${currentTheme.shadow}`,
        background: currentTheme.surface,
      },
      "&::placeholder": {
        color: currentTheme.textLight,
        transition: "opacity 0.3s ease",
      },
      "&:focus::placeholder": {
        opacity: 0.7,
      },
      "@media (max-width: 768px)": {
        padding: "0.875rem 1.25rem",
        fontSize: "0.95rem",
      },
      "@media (max-width: 480px)": {
        padding: "0.75rem 1rem",
        fontSize: "0.9rem",
        width: "100%",
      }
    },
    sendButton: {
      background: currentTheme.gradient,
      color: "white",
      border: "none",
      borderRadius: "8px",
      padding: "0 1.75rem",
      marginLeft: "1rem",
      cursor: "pointer",
      fontWeight: 600,
      fontSize: "1rem",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      transition: "all 0.3s ease",
      position: "relative",
      overflow: "hidden",
      boxShadow: `0 2px 5px ${currentTheme.shadowDark}`,
      minWidth: "80px",
      height: "42px",
      "&:hover:not(:disabled)": {
        transform: "translateY(-2px)",
        boxShadow: `0 4px 8px ${currentTheme.shadowDark}`,
      },
      "&:disabled": {
        background: currentTheme.textLight,
        cursor: "not-allowed",
        transform: "none",
        boxShadow: "none",
        opacity: 0.7,
      },
      "@media (max-width: 768px)": {
        padding: "0 1.5rem",
        fontSize: "0.95rem",
        height: "40px",
        marginLeft: "0.75rem",
      },
      "@media (max-width: 480px)": {
        marginLeft: 0,
        width: "100%",
        height: "38px",
        transform: "translateY(0) !important",
        opacity: "1 !important",
      }
    },
    sendButtonLoading: {
      width: "24px",
      height: "24px",
      border: "3px solid rgba(255,255,255,0.3)",
      borderTopColor: "white",
      borderRadius: "50%",
      animation: "spin 1s linear infinite",
    },
    loadingContainer: {
      display: "flex",
      justifyContent: "center",
      padding: "1.5rem",
    },
    loadingDots: {
      display: "flex",
      gap: "0.75rem",
      alignItems: "center",
    },
    loadingDot: {
      width: "12px",
      height: "12px",
      borderRadius: "50%",
      background: currentTheme.primary,
      animation: "bounce 1.4s infinite ease-in-out",
      ":nth-child(1)": {
        animationDelay: "0s",
      },
      ":nth-child(2)": {
        animationDelay: "0.2s",
      },
      ":nth-child(3)": {
        animationDelay: "0.4s",
      },
    },
    dsaContainer: {
      width: "100%",
      backgroundColor: "white",
      borderRadius: "16px",
      overflow: "hidden",
      boxShadow: "0 10px 25px rgba(0, 0, 0, 0.08)",
      position: "relative",
      border: "1px solid rgba(0, 0, 0, 0.05)",
    },
    dsaHeader: {
      padding: "1.75rem 2rem",
      background: "linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%)",
      borderBottom: "1px solid rgba(0, 0, 0, 0.05)",
      textAlign: "center",
    },
    dsaTitle: {
      margin: 0,
      fontSize: "1.75rem",
      fontWeight: 700,
      color: "#2d3748",
    },
    dsaSubtitle: {
      marginTop: "0.75rem",
      fontSize: "1rem",
      color: "#4a5568",
      fontWeight: 400,
    },
    searchBox: {
      position: "relative",
      maxWidth: "600px",
      margin: "1.5rem auto 0",
      display: "flex",
      alignItems: "center",
    },
    searchIcon: {
      position: "absolute",
      left: "1rem",
      top: "50%",
      transform: "translateY(-50%)",
      color: "#718096",
    },
    searchInput: {
      width: "100%",
      padding: "1rem 1rem 1rem 3rem",
      borderRadius: "8px",
      border: "1px solid #e2e8f0",
      outline: "none",
      fontSize: "1rem",
      transition: "all 0.2s ease",
      background: "white",
      color: "#2d3748",
      boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
      ":focus": {
        borderColor: "#8B7355",
        boxShadow: "0 0 0 3px rgba(139, 115, 85, 0.1)",
      },
    },
    clearSearchButton: {
      position: "absolute",
      right: "1rem",
      top: "50%",
      transform: "translateY(-50%)",
      background: "transparent",
      border: "none",
      cursor: "pointer",
      color: "#718096",
      padding: "0.25rem",
      borderRadius: "50%",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      transition: "all 0.2s ease",
      ":hover": {
        background: "rgba(0, 0, 0, 0.05)",
      },
    },
    companiesGrid: {
      display: "grid",
      gridTemplateColumns: "repeat(auto-fill, minmax(200px, 1fr))",
      gap: "1rem",
      padding: "2rem",
      background: "white",
    },
    companyCard: {
      background: currentTheme.gradient,
      border: `1px solid ${currentTheme.border}`,
      borderRadius: "12px",
      padding: "1.25rem",
      cursor: "pointer",
      transition: "all 0.3s ease",
      position: "relative",
      overflow: "hidden",
      boxShadow: `0 2px 4px ${currentTheme.shadow}`,
      ":hover": {
        transform: "translateY(-5px)",
        boxShadow: `0 8px 16px ${currentTheme.shadow}`,
        borderColor: currentTheme.primary,
      },
    },
    companyInitial: {
      width: "40px",
      height: "40px",
      borderRadius: "50%",
      background: currentTheme.gradientReverse,
      color: "white",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      fontWeight: "bold",
      fontSize: "1.2rem",
      marginBottom: "1rem",
    },
    companyName: {
      fontSize: "1rem",
      fontWeight: "600",
      color: currentTheme.text,
      transition: "all 0.3s ease",
    },
    companyHoverEffect: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: "linear-gradient(135deg, rgba(139, 115, 85, 0.1) 0%, rgba(160, 149, 107, 0.1) 100%)",
      opacity: 0,
      transition: "opacity 0.3s ease",
      ":hover": {
        opacity: 1,
      },
    },
    noResults: {
      textAlign: "center",
      padding: "3rem",
      gridColumn: "1 / -1",
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      justifyContent: "center",
    },
    noResultsIcon: {
      marginBottom: "1rem",
      color: "#a0aec0",
    },
    noResultsText: {
      fontSize: "1.1rem",
      color: "#4a5568",
      marginBottom: "1.5rem",
    },
    clearSearchButtonLarge: {
      background: "linear-gradient(135deg, #8B7355 0%, #6B5B47 100%)",
      color: "white",
      border: "none",
      borderRadius: "8px",
      padding: "0.75rem 1.5rem",
      cursor: "pointer",
      fontWeight: 600,
      fontSize: "1rem",
      transition: "all 0.2s ease",
      boxShadow: "0 2px 5px rgba(139, 115, 85, 0.3)",
      ":hover": {
        transform: "translateY(-2px)",
        boxShadow: "0 4px 8px rgba(139, 115, 85, 0.4)",
      },
    },
    quizzesContainer: {
      width: "100%",
      backgroundColor: "white",
      borderRadius: "16px",
      overflow: "hidden",
      boxShadow: "0 10px 25px rgba(0, 0, 0, 0.08)",
      position: "relative",
      border: "1px solid rgba(0, 0, 0, 0.05)",
    },
    quizzesHeader: {
      padding: "1.75rem 2rem",
      background: "linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%)",
      borderBottom: "1px solid rgba(0, 0, 0, 0.05)",
      textAlign: "center",
    },
    quizzesTitle: {
      margin: 0,
      fontSize: "1.75rem",
      fontWeight: 700,
      color: "#2d3748",
    },
    quizzesSubtitle: {
      marginTop: "0.75rem",
      fontSize: "1rem",
      color: "#4a5568",
      fontWeight: 400,
    },
    quizCards: {
      display: "grid",
      gridTemplateColumns: "repeat(auto-fill, minmax(280px, 1fr))",
      gap: "1.5rem",
      padding: "2rem",
      background: "white",
    },
    quizCard: {
      background: "linear-gradient(135deg, #8B7355 0%, #A0956B 100%)", // mustard green gradient
      border: "1px solid #e2e8f0",
      borderRadius: "12px",
      padding: "1.5rem",
      cursor: "pointer",
      transition: "all 0.3s ease",
      position: "relative",
      overflow: "hidden",
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      boxShadow: "0 2px 4px rgba(0, 0, 0, 0.03)",
      ":hover": {
        transform: "translateY(-5px)",
        boxShadow: "0 8px 16px rgba(0, 0, 0, 0.1)",
        borderColor: "#8B7355",
      },
    },
    quizCardContent: {
      flex: 1,
    },
    quizCardTitle: {
      fontSize: "1.1rem",
      fontWeight: "600",
      color: "#2d3748",
      marginBottom: "0.5rem",
    },
    quizCardDescription: {
      fontSize: "0.9rem",
      color: "#718096",
      lineHeight: 1.5,
    },
    quizCardArrow: {
      color: "#8B7355",
      marginLeft: "1rem",
    },
    quizCardHover: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: "linear-gradient(135deg, rgba(139, 115, 85, 0.1) 0%, rgba(160, 149, 107, 0.1) 100%)",
      opacity: 0,
      transition: "opacity 0.3s ease",
      ":hover": {
        opacity: 1,
      },
    },
    examsContainer: {
      width: "100%",
      backgroundColor: "white",
      borderRadius: "16px",
      overflow: "hidden",
      boxShadow: "0 10px 25px rgba(0, 0, 0, 0.08)",
      position: "relative",
      border: "1px solid rgba(0, 0, 0, 0.05)",
      padding: "2rem",
      textAlign: "center",
    },
    examsTitle: {
      margin: 0,
      fontSize: "1.75rem",
      fontWeight: 700,
      color: "#2d3748",
    },
    examsSubtitle: {
      marginTop: "0.75rem",
      fontSize: "1rem",
      color: "#4a5568",
      fontWeight: 400,
      marginBottom: "2rem",
    },
    aptitudeContainer: {
      width: "100%",
      backgroundColor: "white",
      borderRadius: "16px",
      overflow: "hidden",
      boxShadow: "0 10px 25px rgba(0, 0, 0, 0.08)",
      position: "relative",
      border: "1px solid rgba(0, 0, 0, 0.05)",
      padding: "2rem",
      textAlign: "center",
    },
    aptitudeTitle: {
      margin: 0,
      fontSize: "1.75rem",
      fontWeight: 700,
      color: "#2d3748",
    },
    aptitudeSubtitle: {
      marginTop: "0.75rem",
      fontSize: "1rem",
      color: "#4a5568",
      fontWeight: 400,
      marginBottom: "2rem",
    },
    faqContainer: {
      width: "100%",
      backgroundColor: "white",
      borderRadius: "16px",
      overflow: "hidden",
      boxShadow: "0 10px 25px rgba(0, 0, 0, 0.08)",
      position: "relative",
      border: "1px solid rgba(0, 0, 0, 0.05)",
      padding: "2rem",
      textAlign: "center",
    },
    faqTitle: {
      margin: 0,
      fontSize: "1.75rem",
      fontWeight: 700,
      color: "#2d3748",
    },
    faqSubtitle: {
      marginTop: "0.75rem",
      fontSize: "1rem",
      color: "#4a5568",
      fontWeight: 400,
      marginBottom: "2rem",
    },
    comingSoon: {
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      minHeight: "300px",
    },
    comingSoonContent: {
      maxWidth: "400px",
      textAlign: "center",
    },
    comingSoonIcon: {
      fontSize: "3rem",
      marginBottom: "1rem",
      color: "#FFB6B6",
    },
    comingSoonText: {
      fontSize: "1.5rem",
      fontWeight: 600,
      color: "#2d3748",
      marginBottom: "0.5rem",
    },
    comingSoonDescription: {
      fontSize: "1rem",
      color: "#718096",
      lineHeight: 1.6,
    },
    "@keyframes fadeIn": {
      from: { opacity: 0, transform: "translateY(10px)" },
      to: { opacity: 1, transform: "translateY(0)" },
    },
    "@keyframes spin": {
      to: { transform: "rotate(360deg)" },
    },
    "@keyframes bounce": {
      "0%, 80%, 100%": { transform: "scale(0.6)" },
      "40%": { transform: "scale(1)" },
    },
    "@keyframes moveBackground": {
      "0%": { transform: "translate(0, 0)" },
      "100%": { transform: "translate(50px, 50px)" },
    },
    "@keyframes shine": {
      "0%": { transform: "rotate(30deg) translate(-30%, -30%)" },
      "100%": { transform: "rotate(30deg) translate(30%, 30%)" },
    },
    examButtonsGrid: {
      display: "grid",
      gridTemplateColumns: "repeat(auto-fit, minmax(280px, 1fr))",
      gap: "1.5rem",
      padding: "1.5rem 0",
      "@media (max-width: 768px)": {
        gridTemplateColumns: "1fr",
        padding: "1rem 0",
      }
    },
    examButton: {
      background: "white",
      borderRadius: "12px",
      padding: "1.5rem",
      border: "1px solid #e2e8f0",
      transition: "all 0.3s ease",
      display: "flex",
      flexDirection: "column",
      gap: "1rem",
      cursor: "pointer",
      boxShadow: "0 2px 4px rgba(0, 0, 0, 0.05)",
      "&:hover": {
        transform: "translateY(-4px)",
        boxShadow: "0 8px 16px rgba(0, 0, 0, 0.1)",
        borderColor: "#FFB6B6",
      }
    },
    examIconContainer: {
      width: "48px",
      height: "48px",
      borderRadius: "12px",
      background: "linear-gradient(135deg, #FFC5C5 0%, #FFB6B6 100%)",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      marginBottom: "0.5rem",
    },
    examIcon: {
      fontSize: "24px",
    },
    examContent: {
      flex: 1,
    },
    examTitle: {
      fontSize: "1.25rem",
      fontWeight: "600",
      color: "#2d3748",
      marginBottom: "0.5rem",
    },
    examDescription: {
      fontSize: "0.9rem",
      color: "#718096",
      lineHeight: "1.5",
    },
    resourcesList: {
      display: "flex",
      flexDirection: "column",
      gap: "0.5rem",
      marginTop: "1rem",
    },
    resourceLink: {
      color: "#FFB6B6",
      textDecoration: "none",
      fontSize: "0.9rem",
      padding: "0.5rem",
      borderRadius: "6px",
      background: "#FFF0F0",
      transition: "all 0.2s ease",
      "&:hover": {
        background: "#FFE0E0",
        color: "#FF99CC",
      }
    },
    comingSoonBadge: {
      display: "inline-block",
      padding: "0.5rem 1rem",
      borderRadius: "20px",
      background: "#edf2f7",
      color: "#718096",
      fontSize: "0.85rem",
      fontWeight: "500",
      marginTop: "0.5rem",
    },
    selectedExamContainer: {
      width: "100%",
      padding: "1.5rem",
    },
    examHeader: {
      marginBottom: "2rem",
      display: "flex",
      flexDirection: "column",
      gap: "1rem",
    },
    backButton: {
      background: "none",
      border: "none",
      color: "#FFB6B6",
      cursor: "pointer",
      fontSize: "1rem",
      padding: "0.75rem 0",
      display: "flex",
      alignItems: "center",
      gap: "0.5rem",
      transition: "all 0.2s ease",
      marginBottom: "1rem",
      "&:hover": {
        color: "#FF99CC",
        transform: "translateX(-4px)",
      }
    },
    selectedExamTitle: {
      fontSize: "2rem",
      fontWeight: "700",
      color: "#2d3748",
      display: "flex",
      alignItems: "center",
      gap: "0.75rem",
    },
    resourceTypeFilter: {
      display: "flex",
      gap: "0.75rem",
      flexWrap: "wrap",
      marginBottom: "2rem",
      padding: "1rem",
      background: "#f7fafc",
      borderRadius: "12px",
    },
    filterButton: {
      background: "white",
      border: "1px solid #e2e8f0",
      borderRadius: "8px",
      padding: "0.5rem 1rem",
      cursor: "pointer",
      transition: "all 0.2s ease",
      fontSize: "0.9rem",
      display: "flex",
      alignItems: "center",
      gap: "0.5rem",
      "&:hover": {
        background: "#FFF0F0",
        borderColor: "#FFB6B6",
      }
    },
    filterButtonActive: {
      background: "#FFB6B6",
      color: "white",
      borderColor: "#FFB6B6",
      "&:hover": {
        background: "#FF99CC",
      }
    },
    resourcesGrid: {
      display: "grid",
      gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
      gap: "2rem",
    },
    resourceCategory: {
      background: "white",
      borderRadius: "12px",
      padding: "1.5rem",
      border: "1px solid #e2e8f0",
      boxShadow: "0 2px 4px rgba(0, 0, 0, 0.05)",
    },
    categoryTitle: {
      fontSize: "1.25rem",
      fontWeight: "600",
      color: "#2d3748",
      marginBottom: "1rem",
      display: "flex",
      alignItems: "center",
      gap: "0.5rem",
    },
    resourceCard: {
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      padding: "1rem",
      background: "#f7fafc",
      borderRadius: "8px",
      marginBottom: "0.75rem",
      textDecoration: "none",
      color: "inherit",
      transition: "all 0.2s ease",
      "&:hover": {
        background: "#FFF0F0",
        transform: "translateY(-2px)",
      }
    },
    resourceInfo: {
      flex: 1,
    },
    resourceTitle: {
      fontSize: "1rem",
      fontWeight: "500",
      color: "#2d3748",
      marginBottom: "0.25rem",
    },
    resourceDescription: {
      fontSize: "0.875rem",
      color: "#718096",
      marginBottom: "0.5rem",
    },
    resourceYear: {
      fontSize: "0.75rem",
      color: "#4a5568",
      background: "#edf2f7",
      padding: "0.25rem 0.5rem",
      borderRadius: "4px",
    },
    downloadIcon: {
      fontSize: "1.25rem",
      color: "#FFB6B6",
      marginLeft: "1rem",
    },
    categoryIcon: {
      fontSize: "1.25rem",
      marginRight: "0.5rem",
    },
    categoryPreview: {
      display: "flex",
      gap: "0.5rem",
      marginTop: "1rem",
      color: "#718096",
    },
    gateContainer: {
      width: "100%",
      padding: "1.5rem",
    },
    gateHeader: {
      marginBottom: "2rem",
    },
    gateStats: {
      display: "flex",
      gap: "1.5rem",
      marginBottom: "2rem",
      "@media (max-width: 768px)": {
        flexDirection: "column",
        gap: "1rem",
      }
    },
    statCard: {
      flex: 1,
      background: "white",
      padding: "1.5rem",
      borderRadius: "12px",
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      gap: "0.5rem",
      boxShadow: "0 2px 4px rgba(0, 0, 0, 0.05)",
      transition: "transform 0.2s ease",
      "&:hover": {
        transform: "translateY(-4px)",
      }
    },
    statIcon: {
      fontSize: "2rem",
    },
    statValue: {
      fontSize: "1.5rem",
      fontWeight: "bold",
      color: "#2d3748",
    },
    statLabel: {
      fontSize: "0.9rem",
      color: "#718096",
    },
    gateFilters: {
      display: "flex",
      gap: "1rem",
      marginBottom: "2rem",
      "@media (max-width: 768px)": {
        flexDirection: "column",
      }
    },
    gateSelect: {
      padding: "0.75rem 1rem",
      borderRadius: "8px",
      border: "1px solid #e2e8f0",
      background: "white",
      fontSize: "0.95rem",
      color: "#2d3748",
      cursor: "pointer",
      transition: "all 0.2s ease",
      "&:focus": {
        outline: "none",
        borderColor: "#FFB6B6",
        boxShadow: "0 0 0 3px rgba(255, 182, 182, 0.1)",
      }
    },
    gateContent: {
      display: "flex",
      gap: "2rem",
      "@media (max-width: 1024px)": {
        flexDirection: "column",
      }
    },
    gateSidebar: {
      width: "300px",
      flexShrink: 0,
      "@media (max-width: 1024px)": {
        width: "100%",
      }
    },
    sidebarSection: {
      background: "white",
      borderRadius: "12px",
      padding: "1.5rem",
      marginBottom: "1.5rem",
      boxShadow: "0 2px 4px rgba(0, 0, 0, 0.05)",
    },
    sidebarTitleGate: {
      fontSize: "1.1rem",
      fontWeight: "600",
      color: currentTheme.text,
      marginBottom: "1rem",
    },
    quickLinks: {
      display: "flex",
      flexDirection: "column",
      gap: "0.75rem",
    },
    quickLink: {
      color: "#FFB6B6",
      textDecoration: "none",
      fontSize: "0.95rem",
      display: "flex",
      alignItems: "center",
      gap: "0.5rem",
      padding: "0.5rem",
      borderRadius: "6px",
      transition: "all 0.2s ease",
      "&:hover": {
        background: "#FFF0F0",
      }
    },
    datesList: {
      display: "flex",
      flexDirection: "column",
      gap: "1rem",
    },
    dateItem: {
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      padding: "0.75rem",
      background: "#f7fafc",
      borderRadius: "8px",
    },
    dateLabel: {
      fontSize: "0.9rem",
      color: "#4a5568",
    },
    dateValue: {
      fontSize: "0.9rem",
      fontWeight: "500",
      color: "#2d3748",
    },
    gateMainContent: {
      flex: 1,
    },
    subjectsGrid: {
      display: "grid",
      gridTemplateColumns: "repeat(auto-fill, minmax(250px, 1fr))",
      gap: "1.5rem",
    },
    subjectCard: {
      background: "white",
      borderRadius: "12px",
      padding: "1.5rem",
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      gap: "1rem",
      cursor: "pointer",
      transition: "all 0.3s ease",
      boxShadow: "0 2px 4px rgba(0, 0, 0, 0.05)",
      "&:hover": {
        transform: "translateY(-4px)",
        boxShadow: "0 8px 16px rgba(0, 0, 0, 0.1)",
      }
    },
    subjectIcon: {
      fontSize: "2.5rem",
    },
    subjectName: {
      fontSize: "1.1rem",
      fontWeight: "600",
      color: "#2d3748",
      textAlign: "center",
    },
    subjectTopics: {
      fontSize: "0.9rem",
      color: "#718096",
    },
    subjectContent: {
      background: "white",
      borderRadius: "12px",
      padding: "2rem",
      boxShadow: "0 2px 4px rgba(0, 0, 0, 0.05)",
    },
    subjectTitle: {
      fontSize: "1.5rem",
      fontWeight: "700",
      color: "#2d3748",
      marginBottom: "2rem",
    },
    topicsList: {
      display: "grid",
      gridTemplateColumns: "repeat(auto-fill, minmax(300px, 1fr))",
      gap: "1.5rem",
    },
    topicCard: {
      background: "#f7fafc",
      borderRadius: "10px",
      padding: "1.25rem",
      transition: "all 0.2s ease",
      "&:hover": {
        background: "#FFF0F0",
      }
    },
    topicTitle: {
      fontSize: "1rem",
      fontWeight: "500",
      color: "#2d3748",
      marginBottom: "1rem",
    },
    topicResources: {
      display: "flex",
      gap: "0.75rem",
    },
    resourceButton: {
      background: "white",
      border: "1px solid #e2e8f0",
      borderRadius: "6px",
      padding: "0.5rem 0.75rem",
      fontSize: "0.9rem",
      color: "#4a5568",
      cursor: "pointer",
      transition: "all 0.2s ease",
      "&:hover": {
        background: "#FFB6B6",
        color: "white",
        borderColor: "#FFB6B6",
      }
    },
    leftLogoContainer: {
      display: "flex",
      alignItems: "center",
      marginRight: "1rem",
    },
    eduaiLogoImage: {
      width: "40px",
      height: "40px",
      borderRadius: "50%",
      objectFit: "cover",
      border: "2px solid #FF99CC",
      boxShadow: "0 2px 4px rgba(255, 153, 204, 0.2)",
    },
    userAvatar: {
      width: '40px', height: '40px', borderRadius: '50%', background: '#f0f4f8', display: 'flex', alignItems: 'center', justifyContent: 'center', fontWeight: 700, fontSize: '1.1rem', marginRight: '0.75rem', border: '2px solid #FF99CC', boxShadow: '0 2px 4px rgba(255,153,204,0.1)'
    },
    userAvatarLarge: {
      width: '56px', height: '56px', borderRadius: '50%', background: '#f0f4f8', display: 'flex', alignItems: 'center', justifyContent: 'center', fontWeight: 700, fontSize: '2rem', border: '2px solid #FF99CC', boxShadow: '0 2px 4px rgba(255,153,204,0.1)'
    },

    // Authentication Page Styles
    authPageContainer: {
      minHeight: '100vh',
      background: `
        linear-gradient(135deg, ${currentTheme.primary}15 0%, ${currentTheme.accent}10 25%, ${currentTheme.primaryLight}20 50%, ${currentTheme.secondary}30 75%, ${currentTheme.primary}10 100%),
        radial-gradient(circle at 20% 80%, ${currentTheme.primary}25 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, ${currentTheme.accent}20 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, ${currentTheme.primaryLight}15 0%, transparent 50%)
      `,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '2rem',
      position: 'relative',
      overflow: 'hidden',
      fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, sans-serif",
      animation: 'gradientShift 8s ease-in-out infinite alternate',
      '@media (max-width: 768px)': {
        padding: '1rem',
      },
      '@media (max-width: 480px)': {
        padding: '0.5rem',
      }
    },

    authBackgroundAnimation: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: `
        radial-gradient(circle at 20% 80%, ${currentTheme.primary}30 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, ${currentTheme.accent}25 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, ${currentTheme.primaryLight}20 0%, transparent 50%)
      `,
      animation: 'authFloat 8s ease-in-out infinite alternate',
      zIndex: 1,
    },

    authFloatingParticles: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      pointerEvents: 'none',
      zIndex: 1,
      '&::before': {
        content: '""',
        position: 'absolute',
        width: '6px',
        height: '6px',
        background: currentTheme.primary,
        borderRadius: '50%',
        top: '20%',
        left: '10%',
        animation: 'floatParticle1 12s infinite linear',
        opacity: 0.6,
      },
      '&::after': {
        content: '""',
        position: 'absolute',
        width: '4px',
        height: '4px',
        background: currentTheme.accent,
        borderRadius: '50%',
        top: '60%',
        right: '15%',
        animation: 'floatParticle2 15s infinite linear',
        opacity: 0.4,
      }
    },

    authFormContainer: {
      background: 'rgba(255, 255, 255, 0.98)',
      backdropFilter: 'blur(25px)',
      borderRadius: '28px',
      padding: '3rem',
      width: '100%',
      maxWidth: '500px',
      boxShadow: `
        0 25px 50px -12px ${currentTheme.shadow},
        0 15px 25px -8px ${currentTheme.shadow},
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1)
      `,
      border: `1px solid rgba(255, 255, 255, 0.3)`,
      position: 'relative',
      zIndex: 2,
      transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
      animation: 'formSlideIn 0.8s cubic-bezier(0.4, 0, 0.2, 1)',
      overflow: 'hidden',
      ':hover': {
        transform: 'translateY(-5px)',
        boxShadow: `
          0 35px 60px -12px ${currentTheme.shadow},
          0 20px 30px -8px ${currentTheme.shadow},
          inset 0 1px 0 rgba(255, 255, 255, 0.2),
          0 0 0 1px rgba(255, 255, 255, 0.15)
        `,
      },
      '::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: '2px',
        background: currentTheme.gradient,
        borderRadius: '28px 28px 0 0',
      },
      '::after': {
        content: '""',
        position: 'absolute',
        top: '-50%',
        left: '-50%',
        width: '200%',
        height: '200%',
        background: `
          radial-gradient(circle, ${currentTheme.primary}05 0%, transparent 70%)
        `,
        animation: 'shimmer 3s ease-in-out infinite',
        pointerEvents: 'none',
      },
      '@media (max-width: 768px)': {
        padding: '2.5rem',
        borderRadius: '24px',
        maxWidth: '450px',
      },
      '@media (max-width: 480px)': {
        padding: '2rem',
        borderRadius: '20px',
        maxWidth: '100%',
      }
    },

    authFormTitle: {
      fontSize: '3rem',
      fontWeight: '800',
      textAlign: 'center',
      marginBottom: '0.75rem',
      background: `linear-gradient(135deg, ${currentTheme.primary} 0%, ${currentTheme.primaryLight} 50%, ${currentTheme.accent} 100%)`,
      WebkitBackgroundClip: 'text',
      WebkitTextFillColor: 'transparent',
      backgroundClip: 'text',
      backgroundSize: '200% 200%',
      animation: 'gradientText 4s ease-in-out infinite',
      letterSpacing: '-0.03em',
      lineHeight: '1.1',
      position: 'relative',
      zIndex: 1,
      textShadow: `0 0 30px ${currentTheme.primary}20`,
      '@media (max-width: 768px)': {
        fontSize: '2.5rem',
      },
      '@media (max-width: 480px)': {
        fontSize: '2rem',
      }
    },

    authFormSubtitle: {
      fontSize: '1rem',
      color: currentTheme.textLight,
      textAlign: 'center',
      marginBottom: '2.5rem',
      fontWeight: '400',
      '@media (max-width: 480px)': {
        fontSize: '0.9rem',
        marginBottom: '2rem',
      }
    },

    authInputGroup: {
      marginBottom: '1.5rem',
      position: 'relative',
    },

    authInput: {
      width: '100%',
      padding: '1.25rem 1.5rem',
      fontSize: '1.1rem',
      border: `2px solid ${currentTheme.border}`,
      borderRadius: '16px',
      background: 'rgba(255, 255, 255, 0.9)',
      transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
      outline: 'none',
      fontFamily: 'inherit',
      boxSizing: 'border-box',
      position: 'relative',
      backdropFilter: 'blur(10px)',
      '::placeholder': {
        color: currentTheme.textLight,
        opacity: 0.6,
        transition: 'all 0.3s ease',
      },
      ':focus': {
        borderColor: currentTheme.primary,
        background: 'rgba(255, 255, 255, 1)',
        boxShadow: `
          0 0 0 4px ${currentTheme.primary}15,
          0 8px 25px ${currentTheme.shadow},
          inset 0 1px 0 rgba(255, 255, 255, 0.2)
        `,
        transform: 'translateY(-3px) scale(1.02)',
      },
      ':focus::placeholder': {
        opacity: 0.4,
        transform: 'translateY(-2px)',
      },
      ':hover': {
        borderColor: currentTheme.primaryLight,
        transform: 'translateY(-1px)',
        boxShadow: `0 4px 12px ${currentTheme.shadow}`,
      },
      '@media (max-width: 480px)': {
        padding: '1rem 1.25rem',
        fontSize: '1rem',
      }
    },

    authFileInput: {
      width: '100%',
      padding: '1rem',
      fontSize: '0.95rem',
      border: `2px dashed ${currentTheme.border}`,
      borderRadius: '12px',
      background: 'rgba(255, 255, 255, 0.5)',
      transition: 'all 0.3s ease',
      cursor: 'pointer',
      textAlign: 'center',
      boxSizing: 'border-box',
      ':hover': {
        borderColor: currentTheme.primary,
        background: 'rgba(255, 255, 255, 0.8)',
      },
      ':focus': {
        outline: 'none',
        borderColor: currentTheme.primary,
        boxShadow: `0 0 0 3px ${currentTheme.primary}20`,
      }
    },

    authProfilePreview: {
      width: '80px',
      height: '80px',
      borderRadius: '50%',
      objectFit: 'cover',
      border: `3px solid ${currentTheme.primary}`,
      margin: '1rem auto',
      display: 'block',
      boxShadow: `0 4px 12px ${currentTheme.shadow}`,
    },

    authButtonGroup: {
      display: 'flex',
      gap: '1rem',
      marginTop: '2rem',
      '@media (max-width: 480px)': {
        flexDirection: 'column',
        gap: '0.75rem',
      }
    },

    authButtonPrimary: {
      flex: 1,
      padding: '1.25rem 2.5rem',
      fontSize: '1.1rem',
      fontWeight: '700',
      border: 'none',
      borderRadius: '16px',
      background: currentTheme.gradient,
      color: 'white',
      cursor: 'pointer',
      transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
      position: 'relative',
      overflow: 'hidden',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: '0.75rem',
      textTransform: 'uppercase',
      letterSpacing: '0.5px',
      boxShadow: `0 4px 15px ${currentTheme.shadow}`,
      '::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: '-100%',
        width: '100%',
        height: '100%',
        background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
        transition: 'left 0.5s',
      },
      ':hover': {
        transform: 'translateY(-4px) scale(1.02)',
        boxShadow: `
          0 15px 35px ${currentTheme.shadow},
          0 5px 15px ${currentTheme.shadow}
        `,
        filter: 'brightness(1.1)',
      },
      ':hover::before': {
        left: '100%',
      },
      ':active': {
        transform: 'translateY(-2px) scale(1.01)',
      },
      ':disabled': {
        opacity: 0.6,
        cursor: 'not-allowed',
        transform: 'none',
        filter: 'none',
      },
      '@media (max-width: 480px)': {
        padding: '1rem 2rem',
        fontSize: '1rem',
      }
    },

    authButtonSecondary: {
      flex: 1,
      padding: '1rem 2rem',
      fontSize: '1rem',
      fontWeight: '600',
      border: `2px solid ${currentTheme.primary}`,
      borderRadius: '12px',
      background: 'transparent',
      color: currentTheme.primary,
      cursor: 'pointer',
      transition: 'all 0.3s ease',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: '0.5rem',
      ':hover': {
        background: currentTheme.primary,
        color: 'white',
        transform: 'translateY(-2px)',
        boxShadow: `0 8px 25px ${currentTheme.shadow}`,
      },
      ':active': {
        transform: 'translateY(0)',
      },
      ':disabled': {
        opacity: 0.6,
        cursor: 'not-allowed',
        transform: 'none',
      },
      '@media (max-width: 480px)': {
        padding: '0.875rem 1.5rem',
      }
    },

    authOtpContainer: {
      marginTop: '2rem',
      padding: '1.5rem',
      background: `${currentTheme.secondary}80`,
      borderRadius: '16px',
      border: `1px solid ${currentTheme.border}`,
      textAlign: 'center',
    },

    authOtpTitle: {
      fontSize: '1.25rem',
      fontWeight: '600',
      color: currentTheme.text,
      marginBottom: '1rem',
    },

    authOtpInput: {
      width: '100%',
      maxWidth: '200px',
      padding: '1rem',
      fontSize: '1.25rem',
      textAlign: 'center',
      border: `2px solid ${currentTheme.border}`,
      borderRadius: '12px',
      background: 'white',
      marginBottom: '1rem',
      letterSpacing: '0.5rem',
      fontWeight: '600',
      boxSizing: 'border-box',
      ':focus': {
        outline: 'none',
        borderColor: currentTheme.primary,
        boxShadow: `0 0 0 3px ${currentTheme.primary}20`,
      }
    },

    authOtpButton: {
      padding: '0.75rem 2rem',
      fontSize: '0.95rem',
      fontWeight: '600',
      border: 'none',
      borderRadius: '10px',
      background: currentTheme.gradient,
      color: 'white',
      cursor: 'pointer',
      transition: 'all 0.3s ease',
      ':hover': {
        transform: 'translateY(-1px)',
        boxShadow: `0 4px 12px ${currentTheme.shadow}`,
      }
    },

    authErrorMessage: {
      background: '#FEE2E2',
      color: '#DC2626',
      padding: '1rem',
      borderRadius: '12px',
      marginTop: '1rem',
      fontSize: '0.9rem',
      fontWeight: '500',
      textAlign: 'center',
      border: '1px solid #FECACA',
    },

    authLoadingSpinner: {
      width: '20px',
      height: '20px',
      border: '2px solid rgba(255, 255, 255, 0.3)',
      borderTop: '2px solid white',
      borderRadius: '50%',
      animation: 'spin 1s linear infinite',
      marginRight: '0.5rem',
    },

    // EduAIChatBot specific styles
    appContainer: {
      minHeight: '100vh',
      backgroundColor: currentTheme.background,
      color: currentTheme.text,
      transition: 'all 0.3s ease'
    },
    navbarFixed: {
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      height: '64px',
      background: currentTheme.gradient,
      boxShadow: `0 2px 10px ${currentTheme.shadow}`,
      display: 'flex',
      alignItems: 'center',
      padding: '0 24px',
      zIndex: 1000,
      borderBottom: `1px solid ${currentTheme.border}`
    },
    sidebarFixed: {
      position: 'fixed',
      top: '64px',
      left: 0,
      bottom: 0,
      width: '280px',
      backgroundColor: currentTheme.surface,
      transform: 'translateX(-100%)',
      transition: 'transform 0.3s ease',
      zIndex: 900,
      boxShadow: `2px 0 10px ${currentTheme.shadow}`,
      overflowY: 'auto'
    },
    sidebarOpen: {
      transform: 'translateX(0)'
    },
    sidebarItemEdu: {
      padding: '12px 16px',
      display: 'flex',
      alignItems: 'center',
      cursor: 'pointer',
      borderRadius: '8px',
      margin: '4px 8px',
      transition: 'all 0.2s ease',
      color: currentTheme.text,
      background: currentTheme.gradientLight,
      '&:hover': {
        background: currentTheme.gradient,
        color: 'white'
      }
    },
    sidebarItemActiveEdu: {
      background: currentTheme.gradient,
      color: 'white',
      fontWeight: 500
    },
    mainContentEdu: {
      paddingTop: '80px',
      paddingBottom: '40px',
      transition: 'margin-left 0.3s ease',
      minHeight: 'calc(100vh - 120px)'
    },
    mainContentWithSidebar: {
      marginLeft: '280px'
    },
    cardEdu: {
      backgroundColor: currentTheme.surface,
      borderRadius: '12px',
      boxShadow: `0 4px 6px ${currentTheme.shadow}`,
      padding: '24px',
      marginBottom: '24px',
      transition: 'all 0.3s ease'
    },
    buttonPrimary: {
      background: currentTheme.gradient,
      color: '#fff',
      border: 'none',
      borderRadius: '8px',
      padding: '12px 24px',
      fontWeight: 500,
      fontSize: '16px',
      cursor: 'pointer',
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: '8px',
      transition: 'all 0.2s ease',
      '&:hover': {
        transform: 'translateY(-2px)',
        boxShadow: `0 4px 12px ${currentTheme.shadowDark}`
      },
      '&:disabled': {
        opacity: 0.7,
        cursor: 'not-allowed'
      }
    },
    inputField: {
      width: '100%',
      padding: '14px 16px',
      borderRadius: '8px',
      border: `1.5px solid ${currentTheme.primary}`,
      fontSize: '16px',
      backgroundColor: currentTheme.secondary,
      color: currentTheme.primary,
      transition: 'all 0.3s ease',
      '&:focus': {
        outline: 'none',
        borderColor: currentTheme.primary,
        boxShadow: `0 0 0 2px ${currentTheme.shadow}`
      }
    },
    chatBubbleUser: {
      backgroundColor: currentTheme.primary,
      color: '#fff',
      padding: '12px 16px',
      borderRadius: '18px 18px 4px 18px',
      maxWidth: '80%',
      marginLeft: 'auto',
      marginBottom: '8px',
      boxShadow: `0 2px 4px ${currentTheme.shadow}`
    },
    chatBubbleBot: {
      backgroundColor: currentTheme.secondary,
      color: currentTheme.text,
      padding: '12px 16px',
      borderRadius: '18px 18px 18px 4px',
      maxWidth: '80%',
      marginRight: 'auto',
      marginBottom: '8px',
      boxShadow: `0 2px 4px ${currentTheme.shadow}`
    },
    companyCardEdu: {
      background: currentTheme.gradient,
      borderRadius: '12px',
      padding: '20px',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      cursor: 'pointer',
      transition: 'all 0.3s ease',
      boxShadow: `0 4px 6px ${currentTheme.shadow}`,
      '&:hover': {
        transform: 'translateY(-4px)',
        boxShadow: `0 8px 15px ${currentTheme.shadowDark}`,
        background: currentTheme.gradientReverse
      }
    },
    quizCardEdu: {
      backgroundColor: currentTheme.surface,
      borderRadius: '12px',
      padding: '20px',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      cursor: 'pointer',
      transition: 'all 0.3s ease',
      boxShadow: `0 4px 6px ${currentTheme.shadow}`,
      '&:hover': {
        transform: 'translateY(-4px)',
        boxShadow: `0 8px 15px ${currentTheme.shadowDark}`,
        backgroundColor: currentTheme.secondary
      }
    },
    notification: {
      position: 'fixed',
      bottom: '24px',
      right: '24px',
      padding: '12px 24px',
      borderRadius: '8px',
      boxShadow: `0 4px 12px ${currentTheme.shadowDark}`,
      zIndex: 2000,
      animation: 'slideIn 0.3s ease-out',
      color: '#fff'
    },

    // Academic Dashboard specific styles
    academicBackground: {
      minHeight: '100vh',
      background: currentTheme.gradientLight,
      padding: '2rem 1rem'
    },
    academicHeader: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: '2rem',
      flexWrap: 'wrap',
      gap: '1rem'
    },
    academicTitle: {
      fontSize: '2.5rem',
      fontWeight: 'bold',
      color: currentTheme.primary,
      margin: 0,
      marginBottom: '0.5rem'
    },
    academicSubtitle: {
      color: currentTheme.textLight,
      margin: 0
    },
    academicButton: {
      background: currentTheme.primary,
      color: 'white',
      padding: '0.75rem 1.5rem',
      borderRadius: '0.5rem',
      border: 'none',
      cursor: 'pointer',
      display: 'flex',
      alignItems: 'center',
      gap: '0.5rem',
      fontSize: '1rem',
      transition: 'all 0.3s ease',
      '&:hover': {
        background: currentTheme.primaryDark
      }
    },
    academicButtonSecondary: {
      background: 'white',
      color: currentTheme.primary,
      border: `2px solid ${currentTheme.primary}`,
      padding: '0.75rem 1.5rem',
      borderRadius: '0.5rem',
      cursor: 'pointer',
      display: 'flex',
      alignItems: 'center',
      gap: '0.5rem',
      fontSize: '1rem',
      transition: 'all 0.3s ease',
      '&:hover': {
        background: currentTheme.secondary
      }
    },
    academicStatsGrid: {
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
      gap: '1.5rem',
      marginBottom: '2rem'
    },
    academicStatCard: {
      background: 'white',
      borderRadius: '1rem',
      boxShadow: `0 4px 6px ${currentTheme.shadow}`,
      padding: '1.5rem',
      display: 'flex',
      alignItems: 'center',
      transition: 'transform 0.3s ease',
      cursor: 'pointer',
      '&:hover': {
        transform: 'translateY(-5px)'
      }
    },
    academicStatIcon: {
      padding: '0.75rem',
      borderRadius: '50%',
      marginRight: '1rem'
    },
    academicMainGrid: {
      display: 'grid',
      gridTemplateColumns: '2fr 1fr',
      gap: '2rem',
      '@media (max-width: 1024px)': {
        gridTemplateColumns: '1fr'
      }
    },
    academicCoursesSection: {
      background: 'white',
      borderRadius: '1rem',
      boxShadow: `0 4px 6px ${currentTheme.shadow}`,
      overflow: 'hidden'
    },
    academicSectionHeader: {
      background: currentTheme.primary,
      color: 'white',
      padding: '1.5rem',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      flexWrap: 'wrap',
      gap: '1rem'
    },
    academicSectionTitle: {
      fontSize: '1.25rem',
      fontWeight: 'bold',
      margin: 0
    },
    academicFilterButton: {
      background: 'white',
      color: currentTheme.primary,
      padding: '0.5rem 1rem',
      borderRadius: '0.375rem',
      border: 'none',
      fontSize: '0.875rem',
      cursor: 'pointer',
      transition: 'all 0.3s ease'
    },
    academicFilterButtonActive: {
      background: currentTheme.primaryDark,
      color: 'white'
    },
    academicCourseCard: {
      border: `1px solid ${currentTheme.border}`,
      borderRadius: '0.5rem',
      padding: '1rem',
      transition: 'all 0.3s ease',
      cursor: 'pointer',
      '&:hover': {
        borderColor: currentTheme.primary,
        transform: 'translateY(-2px)'
      }
    },

    // Academics component styles
    academicsContainer: {
      width: '100%',
      maxWidth: '1200px',
      margin: '0 auto',
      padding: '2rem 1rem'
    },
    academicsHeaderSection: {
      textAlign: 'center',
      marginBottom: '2rem'
    },
    academicsMainTitle: {
      fontSize: '2rem',
      fontWeight: '700',
      color: currentTheme.text,
      marginBottom: '0.5rem'
    },
    academicsMainSubtitle: {
      fontSize: '1.1rem',
      color: currentTheme.textLight,
      maxWidth: '600px',
      margin: '0 auto'
    },
    academicsGrid: {
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
      gap: '1.5rem',
      marginBottom: '3rem'
    },
    academicCardBasic: {
      background: currentTheme.surface,
      borderRadius: '12px',
      padding: '1.5rem',
      boxShadow: `0 2px 8px ${currentTheme.shadow}`,
      transition: 'transform 0.2s, box-shadow 0.2s',
      cursor: 'pointer',
      position: 'relative',
      overflow: 'hidden',
      '&:hover': {
        transform: 'translateY(-4px)',
        boxShadow: `0 4px 12px ${currentTheme.shadowDark}`
      }
    },
    academicIcon: {
      fontSize: '2rem',
      marginBottom: '1rem'
    },
    academicCardTitleBasic: {
      fontSize: '1.25rem',
      fontWeight: '600',
      color: currentTheme.text,
      marginBottom: '0.5rem'
    },
    academicCardDescriptionBasic: {
      fontSize: '1rem',
      color: currentTheme.textLight,
      lineHeight: '1.5'
    },
    subjectSection: {
      marginTop: '2rem'
    },
    subjectTitleMain: {
      fontSize: '1.5rem',
      fontWeight: '600',
      color: currentTheme.text,
      marginBottom: '1.5rem',
      textAlign: 'center'
    },
    subjectGrid: {
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',
      gap: '1rem'
    },
    subjectCardBasic: {
      background: currentTheme.surface,
      borderRadius: '8px',
      padding: '1.25rem 1rem',
      textAlign: 'center',
      fontWeight: '500',
      color: currentTheme.textLight,
      boxShadow: `0 1px 3px ${currentTheme.shadow}`,
      transition: 'all 0.2s',
      '&:hover': {
        background: currentTheme.primary,
        color: '#fff',
        transform: 'translateY(-2px)'
      }
    }
};

// CSS Animations and Keyframes
const authAnimations = `
  @keyframes authFloat {
    0% {
      transform: translateY(0px) rotate(0deg);
      opacity: 0.7;
    }
    50% {
      transform: translateY(-15px) rotate(3deg);
      opacity: 1;
    }
    100% {
      transform: translateY(0px) rotate(0deg);
      opacity: 0.7;
    }
  }

  @keyframes gradientShift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  @keyframes gradientText {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  @keyframes formSlideIn {
    0% {
      opacity: 0;
      transform: translateY(30px) scale(0.95);
    }
    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes shimmer {
    0% {
      transform: translateX(-100%) translateY(-100%) rotate(30deg);
    }
    100% {
      transform: translateX(100%) translateY(100%) rotate(30deg);
    }
  }

  @keyframes floatParticle1 {
    0% {
      transform: translateY(100vh) translateX(0px) rotate(0deg);
      opacity: 0;
    }
    10% {
      opacity: 1;
    }
    90% {
      opacity: 1;
    }
    100% {
      transform: translateY(-100px) translateX(100px) rotate(360deg);
      opacity: 0;
    }
  }

  @keyframes floatParticle2 {
    0% {
      transform: translateY(100vh) translateX(0px) rotate(0deg);
      opacity: 0;
    }
    15% {
      opacity: 0.8;
    }
    85% {
      opacity: 0.8;
    }
    100% {
      transform: translateY(-100px) translateX(-150px) rotate(-360deg);
      opacity: 0;
    }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  @keyframes buttonPulse {
    0% {
      box-shadow: 0 0 0 0 rgba(124, 58, 237, 0.7);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(124, 58, 237, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(124, 58, 237, 0);
    }
  }

  @keyframes typewriter {
    from {
      width: 0;
    }
    to {
      width: 100%;
    }
  }

  @keyframes confetti {
    0% {
      transform: translateY(0) rotate(0deg);
      opacity: 1;
    }
    100% {
      transform: translateY(100vh) rotate(720deg);
      opacity: 0;
    }
  }

  @keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
      transform: translate3d(0,0,0);
    }
    40%, 43% {
      transform: translate3d(0, -30px, 0);
    }
    70% {
      transform: translate3d(0, -15px, 0);
    }
    90% {
      transform: translate3d(0, -4px, 0);
    }
  }
`;

// Inject animations into the document head
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = authAnimations;
  document.head.appendChild(styleElement);
}

export default styles;