{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\quiz\\\\aich (4)\\\\aich (3)\\\\aich(5)\\\\src\\\\EduAIChatBot.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport { getDoc, doc } from 'firebase/firestore';\nimport Faq from './Faq';\nimport Exams from \"./Exams\";\nimport Coding from \"./Coding\";\nimport { auth, db } from './firebaseConfig';\nimport axios from \"axios\";\nimport { sidebarItems } from './sidebarItems';\nimport { onAuthStateChanged } from 'firebase/auth';\nimport globalStyles from './styles.js';\nimport { FiMenu, FiX, FiChevronDown, FiChevronRight, FiFileText, FiCode, FiHelpCircle, FiAward, FiBook, FiUser, FiShield, FiSearch, FiUpload, FiLogIn, FiLogOut, FiBriefcase, FiBarChart2, FiLayers, FiCheckCircle, FiExternalLink, FiHeart, FiClock, FiRefreshCw } from \"react-icons/fi\";\nimport { createClient } from '@supabase/supabase-js';\nimport { Bar } from 'react-chartjs-2';\nimport { Chart, BarElement, CategoryScale, LinearScale, Tooltip, Legend } from 'chart.js';\nimport ReactMarkdown from 'react-markdown';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nChart.register(BarElement, CategoryScale, LinearScale, Tooltip, Legend);\n\n// Enhanced sidebar items with icons\nconst updatedSidebarItems = sidebarItems.map(item => {\n  const iconMap = {\n    \"resume\": /*#__PURE__*/_jsxDEV(FiFileText, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 15\n    }, this),\n    \"dsa\": /*#__PURE__*/_jsxDEV(FiCode, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 12\n    }, this),\n    \"coding\": /*#__PURE__*/_jsxDEV(FiLayers, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 15\n    }, this),\n    \"resources\": /*#__PURE__*/_jsxDEV(FiBriefcase, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 18\n    }, this),\n    \"quizzes\": /*#__PURE__*/_jsxDEV(FiCheckCircle, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 16\n    }, this),\n    \"aptitude\": /*#__PURE__*/_jsxDEV(FiBarChart2, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 17\n    }, this),\n    \"academics\": /*#__PURE__*/_jsxDEV(FiBook, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 18\n    }, this),\n    \"faq\": /*#__PURE__*/_jsxDEV(FiHelpCircle, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 12\n    }, this),\n    \"admin\": /*#__PURE__*/_jsxDEV(FiShield, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 14\n    }, this)\n  };\n  return {\n    ...item,\n    icon: iconMap[item.tab] || iconMap[item.title.toLowerCase()] || /*#__PURE__*/_jsxDEV(FiAward, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 69\n    }, this)\n  };\n});\nconst EduAIChatBot = () => {\n  _s();\n  // State declarations\n  const [input, setInput] = useState(\"\");\n  const [messages, setMessages] = useState([]);\n  const [userId, setUserId] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [knowledge, setKnowledge] = useState(\"\");\n  const [activeTab, setActiveTab] = useState(\"dashboard\");\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [expandedMenus, setExpandedMenus] = useState({});\n  const [user, setUser] = useState(null);\n  const [resumeUploadLoading, setResumeUploadLoading] = useState(false);\n  const [resumeUrl, setResumeUrl] = useState(null);\n  const [resourceUploadLoading, setResourceUploadLoading] = useState(false);\n  const [userResources] = useState([]);\n  const ADMIN_EMAIL = '<EMAIL>';\n  const [allUsers] = useState([]);\n  const [adminTab, setAdminTab] = useState('users');\n  const [notification, setNotification] = useState(null);\n  const [activityLog, setActivityLog] = useState([]);\n  const [resourcesTab, setResourcesTab] = useState('general'); // New state for resources tab\n  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1024); // Responsive state\n\n  // Optimized tab positions for smooth sliding\n  const getTabPosition = tabId => {\n    const tabIndex = ['general', 'academics', 'tools'].indexOf(tabId);\n    return `${tabIndex * 33.333}%`;\n  };\n  const chatEndRef = useRef(null);\n\n  // Optimized window resize handler with throttling\n  useEffect(() => {\n    let timeoutId = null;\n    const handleResize = () => {\n      if (timeoutId) clearTimeout(timeoutId);\n      timeoutId = setTimeout(() => {\n        setWindowWidth(window.innerWidth);\n      }, 100); // Throttle to 100ms\n    };\n    window.addEventListener('resize', handleResize, {\n      passive: true\n    });\n    return () => {\n      window.removeEventListener('resize', handleResize);\n      if (timeoutId) clearTimeout(timeoutId);\n    };\n  }, []);\n\n  // Enhanced DSA section states\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [sortBy, setSortBy] = useState('name');\n  const [favoriteCompanies, setFavoriteCompanies] = useState([]);\n  const [recentCompanies, setRecentCompanies] = useState([]);\n  const [showRevertButton, setShowRevertButton] = useState(true);\n\n  // API configurations\n  const API_KEY = \"AIzaSyC6kHWto78QdqHz7Uu9RzEXb443ZO7tG5M\";\n  const SUPABASE_URL = 'https://gziaptswfepiveyylven.supabase.co';\n  const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imd6aWFwdHN3ZmVwaXZleXlsdmVuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU2NzczNTYsImV4cCI6MjA2MTI1MzM1Nn0.wmqXZGffrox8E_PuCwbzh4xJEffsvFmZCVcF6WFAX6Q';\n  const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);\n\n  // Company categories for enhanced DSA section\n  const companyCategories = {\n    'FAANG': ['Facebook', 'Apple', 'Amazon', 'Netflix', 'Google'],\n    'Big Tech': ['Microsoft', 'Adobe', 'Salesforce', 'Oracle', 'IBM', 'Intel', 'Nvidia'],\n    'Startups': ['Stripe', 'Airbnb', 'Uber', 'Lyft', 'DoorDash', 'Instacart', 'Coinbase'],\n    'Finance': ['Goldman Sachs', 'JPMorgan', 'Morgan Stanley', 'BlackRock', 'Citadel', 'Two Sigma'],\n    'Consulting': ['McKinsey', 'BCG', 'Bain', 'Deloitte', 'Accenture', 'PwC'],\n    'E-commerce': ['Amazon', 'eBay', 'Shopify', 'Etsy', 'Wayfair', 'Booking.com'],\n    'Gaming': ['Blizzard', 'Epic Games', 'Riot Games', 'Unity', 'Roblox'],\n    'Indian': ['TCS', 'Infosys', 'Wipro', 'HCL', 'Flipkart', 'Paytm', 'Zomato', 'Swiggy']\n  };\n\n  // Complete list of companies\n  const companies = [\"Accenture\", \"Accolite\", \"Adobe\", \"Affirm\", \"Agoda\", \"Airbnb\", \"Airtel\", \"Akamar\", \"Akuna Capital\", \"Alibaba\", \"Altimetrik\", \"Amazon\", \"AMD\", \"Amdocs\", \"American Express\", \"Anduril\", \"Apple\", \"Arista Networks\", \"Arcesium\", \"Atlassian\", \"Attentive\", \"athenahealth\", \"Autodesk\", \"Avito\", \"Baidu\", \"Barclays\", \"BitGo\", \"BlackRock\", \"Blizzard\", \"Block\", \"Bloomberg\", \"BNY Mellon\", \"Boft\", \"Booking.com\", \"Bos\", \"BP\", \"ByteDance\", \"Cadence\", \"Capgemini\", \"Capital One\", \"CARS24\", \"carwale\", \"Cashfree\", \"Chewy\", \"Cisco\", \"Citadel\", \"Citrix\", \"Cloudera\", \"Cloudflare\", \"Cognizant\", \"Coinbase\", \"Commvault\", \"Confluent\", \"Coupang\", \"Coursera\", \"CrowdStrike\", \"Cruise\", \"Curefit\", \"Databricks\", \"Datadog\", \"DE Shaw\", \"Deloitte\", \"Dell\", \"Deliveroo\", \"Derantior\", \"Deutsche Bank\", \"Devflev\", \"Directi\", \"Disney\", \"Docusign\", \"DoorDash\", \"Dream11\", \"Dropbox\", \"DRW\", \"Dunzo\", \"eBay\", \"EPAM Systems\", \"Epic Systems\", \"Expedia\", \"FactSet\", \"Flexport\", \"Flipkart\", \"Freshworks\", \"GE Healthcare\", \"Geico\", \"Goldman Sachs\", \"Google\", \"Grab\", \"Grammarly\", \"Graviton\", \"Groww\", \"GSN Games\", \"Hashedin\", \"HCL\", \"HPE\", \"Hubspot\", \"Hudson River Trading\", \"Huawei\", \"IBM\", \"IMC\", \"Indeed\", \"Infosys\", \"InMobi\", \"Intel\", \"Intuit\", \"JPMorgan\", \"Jane Street\", \"Josh Technology\", \"Jump Trading\", \"Juspay\", \"Karat\", \"KLA\", \"LinkedIn\", \"LiveRamp\", \"Lowe's\", \"Lucid\", \"Lyft\", \"MakeMyTrip\", \"Mastercard\", \"MathWorks\", \"Media.net\", \"Meesho\", \"Mercari\", \"Meta\", \"Microsoft\", \"Millennium\", \"Mitsogo\", \"Moloco\", \"MongoDB\", \"Morgan Stanley\", \"Moveworks\", \"Myntra\", \"Nagarro\", \"NetApp\", \"Netflix\", \"Nextdoor\", \"Nielsen\", \"Nike\", \"Niantic\", \"Nordstrom\", \"Nutanix\", \"Nvidia\", \"Okta\", \"OKX\", \"OpenAI\", \"OpenText\", \"Oracle\", \"Otter.ai\", \"Oyo\", \"Ozon\", \"Palantir Technologies\", \"Palo Alto Networks\", \"PayPal\", \"Paytm\", \"Persistent Systems\", \"PhonePe\", \"Pinterest\", \"Pocket Gems\", \"Point72\", \"Pure Storage\", \"Qualcomm\", \"Quora\", \"Rakuten\", \"Razorpay\", \"RBC\", \"Reddit\", \"Revolut\", \"Robinhood\", \"Roblox\", \"Rubrik\", \"Salesforce\", \"Samsung\", \"SAP\", \"ServiceNow\", \"Shopify\", \"Siemens\", \"Sigmoid\", \"SIG\", \"Snowflake\", \"Snap\", \"Sofi\", \"Splunk\", \"Spotify\", \"Sprinklr\", \"Squarepoint Capital\", \"Stripe\", \"Swiggy\", \"TCS\", \"Tekion\", \"Tencent\", \"Tesla\", \"ThoughtSpot\", \"ThoughtWorks\", \"TikTok\", \"Tinkoff\", \"Trilogy\", \"Turing\", \"Turo\", \"Twilio\", \"Twitch\", \"Two Sigma\", \"Uber\", \"UiPath\", \"UKG\", \"Veeva Systems\", \"Verily\", \"Verkada\", \"Virtu Financial\", \"Visa\", \"VK\", \"VMware\", \"Walmart Labs\", \"WarnerMedia\", \"Wayfair\", \"Wells Fargo\", \"Wipro\", \"Wix\", \"Workday\", \"X\", \"Yahoo\", \"Yandex\", \"Yelp\", \"Zalando\", \"Zenefits\", \"Zepto\", \"Zeta\", \"Zillow\", \"Zoho\", \"Zomato\", \"ZScaler\", \"Zopsmart\"];\n\n  // Quiz buttons data\n  const quizButtons = [{\n    title: \"OP and CN Quiz\",\n    description: \"Test your knowledge of Operating System and Computer Networks\",\n    link: \"https://opcn.netlify.app\"\n  }, {\n    title: \"OOPs and DBMS Quiz\",\n    description: \"Challenge yourself with oops and dbms\",\n    link: \"https://oopsanddbms.netlify.app/\"\n  }, {\n    title: \"System Design Quiz\",\n    description: \"Test your system design knowledge\",\n    link: \"https://system-design041.netlify.app\"\n  }, {\n    title: \"Quantitative Aptitude and Reasoning Quiz\",\n    description: \"Practice common quant and reasoning questions\",\n    link: \"https://quantandreasoning.netlify.app\"\n  }, {\n    title: \"Cloud & DevOps Quiz\",\n    description: \"Test your knowledge of Cloud and DevOps concepts\",\n    link: \"https://cloud-devops.netlify.app\"\n  }, {\n    title: \"DSA Quiz\",\n    description: \"Data Structures and Algorithms quiz\",\n    link: \"https://dsa041.netlify.app\"\n  }, {\n    title: \"Operating System & Computer Networks Quiz\",\n    description: \"Quiz on OS and Computer Networks\",\n    link: \"https://opcn.netlify.app\"\n  }, {\n    title: \"Web Development Quiz\",\n    description: \"Quiz on Web Development topics\",\n    link: \"https://web-dev041.netlify.app\"\n  }];\n\n  // Use centralized styles\n  const styles = {\n    ...globalStyles,\n    appContainer: {\n      ...globalStyles.appContainer,\n      backgroundColor: globalStyles.currentTheme.background,\n      color: globalStyles.currentTheme.text\n    },\n    navbar: {\n      ...globalStyles.navbarFixed,\n      borderBottom: `1px solid ${globalStyles.currentTheme.border}`\n    },\n    sidebar: {\n      ...globalStyles.sidebarFixed,\n      backgroundColor: globalStyles.currentTheme.surface,\n      borderRight: `1px solid ${globalStyles.currentTheme.border}`,\n      transform: sidebarOpen ? 'translateX(0)' : 'translateX(-100%)'\n    },\n    sidebarItem: {\n      ...globalStyles.sidebarItemEdu,\n      color: globalStyles.currentTheme.text,\n      background: globalStyles.currentTheme.surface,\n      border: `1px solid ${globalStyles.currentTheme.border}`,\n      transition: 'all 0.3s ease',\n      '&:hover': {\n        background: globalStyles.currentTheme.primary,\n        color: 'white'\n      }\n    },\n    sidebarItemActive: {\n      ...globalStyles.sidebarItemActiveEdu,\n      color: 'white',\n      background: globalStyles.currentTheme.primary,\n      border: `1px solid ${globalStyles.currentTheme.primary}`\n    },\n    mainContent: {\n      ...globalStyles.mainContentEdu,\n      marginLeft: sidebarOpen ? '280px' : '0',\n      backgroundColor: globalStyles.currentTheme.background,\n      color: globalStyles.currentTheme.text,\n      minHeight: '100vh'\n    },\n    card: {\n      ...globalStyles.cardEdu,\n      backgroundColor: globalStyles.currentTheme.surface,\n      color: globalStyles.currentTheme.text,\n      border: `1px solid ${globalStyles.currentTheme.border}`,\n      boxShadow: `0 4px 6px ${globalStyles.currentTheme.shadow}`\n    },\n    buttonPrimary: {\n      ...globalStyles.buttonPrimary\n    },\n    inputField: {\n      ...globalStyles.inputField,\n      backgroundColor: '#fff',\n      color: '#333',\n      border: '1px solid #ddd',\n      '&:focus': {\n        borderColor: globalStyles.currentTheme.primary,\n        outline: 'none',\n        boxShadow: `0 0 0 2px ${globalStyles.currentTheme.primary}20`\n      }\n    },\n    chatBubbleUser: {\n      ...globalStyles.chatBubbleUser,\n      backgroundColor: globalStyles.currentTheme.primary,\n      color: 'white'\n    },\n    chatBubbleBot: {\n      ...globalStyles.chatBubbleBot,\n      backgroundColor: globalStyles.currentTheme.secondary,\n      color: globalStyles.currentTheme.text,\n      border: '1px solid transparent'\n    },\n    companyCard: {\n      ...globalStyles.companyCardEdu\n    },\n    quizCard: {\n      ...globalStyles.quizCardEdu,\n      backgroundColor: globalStyles.currentTheme.surface\n    },\n    notification: {\n      ...globalStyles.notification\n    }\n  };\n\n  // Helper function to apply styles with hover states\n  const getStyle = (styleName, hover = false) => {\n    const baseStyle = styles[styleName];\n    if (typeof baseStyle === 'function') return baseStyle();\n    if (hover && baseStyle['&:hover']) {\n      return {\n        ...baseStyle,\n        ...baseStyle['&:hover']\n      };\n    }\n    return baseStyle;\n  };\n\n  // Fetch user profile\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, user => {\n      if (user) {\n        setUserId(user.uid);\n      } else {\n        console.log('User is not authenticated');\n        setLoading(false);\n      }\n    });\n    return () => unsubscribe();\n  }, []);\n  useEffect(() => {\n    if (userId) {\n      const fetchUserProfile = async () => {\n        const userRef = doc(db, \"users\", userId);\n        const userDoc = await getDoc(userRef);\n        if (userDoc.exists()) {\n          const userData = userDoc.data();\n          // Profile pic functionality can be added later\n          console.log(\"User data loaded:\", userData.dp);\n        } else {\n          console.log(\"No such user!\");\n        }\n        setLoading(false);\n      };\n      fetchUserProfile();\n    }\n  }, [userId]);\n\n  // Fetch training data\n  useEffect(() => {\n    fetch(\"/training-data.txt\").then(res => res.text()).then(data => setKnowledge(data)).catch(err => console.error(\"Failed to load training data:\", err));\n  }, []);\n\n  // Supabase auth state\n  useEffect(() => {\n    supabase.auth.getSession().then(({\n      data: {\n        session\n      }\n    }) => {\n      setUser((session === null || session === void 0 ? void 0 : session.user) || null);\n    });\n    const {\n      data: listener\n    } = supabase.auth.onAuthStateChange((_event, session) => {\n      setUser((session === null || session === void 0 ? void 0 : session.user) || null);\n    });\n    return () => {\n      listener === null || listener === void 0 ? void 0 : listener.subscription.unsubscribe();\n    };\n  }, [supabase.auth]);\n\n  // Handle resume upload\n  const handleResumeUpload = async e => {\n    const file = e.target.files[0];\n    if (!file || !user) return;\n    setResumeUploadLoading(true);\n    const filePath = `${user.id}/${file.name}`;\n    const {\n      error\n    } = await supabase.storage.from('resumes').upload(filePath, file, {\n      upsert: true\n    });\n    if (!error) {\n      const {\n        data: urlData\n      } = supabase.storage.from('resumes').getPublicUrl(filePath);\n      setResumeUrl(urlData.publicUrl);\n      showNotification('Resume uploaded successfully!', 'success');\n      logActivity('Uploaded a resume');\n    } else {\n      showNotification('Resume upload failed.', 'error');\n    }\n    setResumeUploadLoading(false);\n  };\n\n  // Handle resource upload\n  const handleResourceUpload = async e => {\n    const file = e.target.files[0];\n    if (!file || !user) return;\n    setResourceUploadLoading(true);\n    const filePath = `${user.id}/${file.name}`;\n    const {\n      error\n    } = await supabase.storage.from('resources').upload(filePath, file, {\n      upsert: true\n    });\n    if (!error) {\n      showNotification('Resource uploaded!', 'success');\n      logActivity(`Uploaded resource: ${file.name}`);\n    } else {\n      showNotification('Resource upload failed.', 'error');\n    }\n    setResourceUploadLoading(false);\n  };\n\n  // Enhanced company click handler\n  const handleCompanyClick = company => {\n    // Add to recent companies\n    setRecentCompanies(prev => {\n      const filtered = prev.filter(c => c !== company);\n      return [company, ...filtered].slice(0, 5); // Keep only 5 recent\n    });\n    logActivity(`Viewed ${company} DSA questions`);\n    if (company.toLowerCase() === 'microsoft') {\n      window.location.href = '/company-dsa/Microsoft_questions.html';\n      return;\n    }\n    const formattedCompany = company.replace(/\\s+/g, '');\n    window.location.href = `/company-dsa/${formattedCompany}.html`;\n  };\n\n  // Toggle favorite company\n  const toggleFavorite = (company, e) => {\n    e.stopPropagation(); // Prevent company click\n    setFavoriteCompanies(prev => {\n      if (prev.includes(company)) {\n        return prev.filter(c => c !== company);\n      } else {\n        return [...prev, company];\n      }\n    });\n  };\n\n  // Revert header color changes\n  const revertHeaderChanges = () => {\n    setShowRevertButton(false);\n    showNotification('Header text color reverted to theme default!', 'success');\n\n    // Actually revert the header text color by updating the DOM\n    const eduNovaElement = document.querySelector('[data-header-title]');\n    const subtitleElement = document.querySelector('[data-header-subtitle]');\n    if (eduNovaElement) {\n      eduNovaElement.style.color = '#333';\n    }\n    if (subtitleElement) {\n      subtitleElement.style.color = '#333';\n    }\n  };\n\n  // Get filtered companies based on category and search\n  const getFilteredCompanies = () => {\n    let filtered = companies;\n\n    // Filter by category\n    if (selectedCategory !== 'all') {\n      const categoryCompanies = companyCategories[selectedCategory] || [];\n      filtered = filtered.filter(company => categoryCompanies.some(catCompany => company.toLowerCase().includes(catCompany.toLowerCase()) || catCompany.toLowerCase().includes(company.toLowerCase())));\n    }\n\n    // Filter by search term\n    filtered = filtered.filter(company => company.toLowerCase().includes(searchTerm.toLowerCase()));\n\n    // Sort companies\n    if (sortBy === 'name') {\n      filtered.sort();\n    } else if (sortBy === 'favorites') {\n      filtered.sort((a, b) => {\n        const aFav = favoriteCompanies.includes(a);\n        const bFav = favoriteCompanies.includes(b);\n        if (aFav && !bFav) return -1;\n        if (!aFav && bFav) return 1;\n        return a.localeCompare(b);\n      });\n    }\n    return filtered;\n  };\n\n  // Open quiz link\n  const openQuizLink = url => {\n    window.open(url, \"_blank\");\n  };\n\n  // Send message to chatbot\n  const sendMessage = async () => {\n    if (!input.trim()) return;\n    const userMessage = {\n      role: \"user\",\n      content: input\n    };\n    setMessages(prev => [...prev, userMessage]);\n    setInput(\"\");\n    setLoading(true);\n    try {\n      var _res$data$candidates, _res$data$candidates$, _res$data$candidates$2, _res$data$candidates$3, _res$data$candidates$4;\n      const prompt = `You are a resume assistant. Help users improve their resumes, provide suggestions, and answer career-related questions. Use the following knowledge if it helps. If it's not relevant, use your own intelligence.\\n\\nKnowledge:\\n${knowledge}\\n\\nQuestion: ${input}`;\n      const res = await axios.post(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`, {\n        contents: [{\n          parts: [{\n            text: prompt\n          }]\n        }]\n      }, {\n        headers: {\n          \"Content-Type\": \"application/json\"\n        }\n      });\n      const botReply = ((_res$data$candidates = res.data.candidates) === null || _res$data$candidates === void 0 ? void 0 : (_res$data$candidates$ = _res$data$candidates[0]) === null || _res$data$candidates$ === void 0 ? void 0 : (_res$data$candidates$2 = _res$data$candidates$.content) === null || _res$data$candidates$2 === void 0 ? void 0 : (_res$data$candidates$3 = _res$data$candidates$2.parts) === null || _res$data$candidates$3 === void 0 ? void 0 : (_res$data$candidates$4 = _res$data$candidates$3[0]) === null || _res$data$candidates$4 === void 0 ? void 0 : _res$data$candidates$4.text) || \"⚠ No response received.\";\n      const botMessage = {\n        role: \"bot\",\n        content: botReply\n      };\n      setMessages(prev => [...prev, botMessage]);\n    } catch (error) {\n      console.error(\"Gemini API Error:\", error);\n      setMessages(prev => [...prev, {\n        role: \"bot\",\n        content: \"❌ Error: \" + error.message\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Authentication functionality can be added later if needed\n\n  // Handle logout\n  const handleLogout = async () => {\n    await supabase.auth.signOut();\n  };\n\n  // Show notification\n  const showNotification = (msg, type = 'info') => {\n    setNotification({\n      msg,\n      type\n    });\n    setTimeout(() => setNotification(null), 3000);\n  };\n\n  // Log activity\n  const logActivity = msg => {\n    setActivityLog(log => [{\n      type: 'activity',\n      date: new Date().toISOString(),\n      msg\n    }, ...log.slice(0, 19)]);\n  };\n\n  // Toggle menu\n  const toggleMenu = menu => {\n    setExpandedMenus(prev => ({\n      ...prev,\n      [menu]: !prev[menu]\n    }));\n  };\n\n  // Auto-scroll chat\n  useEffect(() => {\n    if (chatEndRef.current) chatEndRef.current.scrollIntoView({\n      behavior: 'smooth'\n    });\n  }, [messages, loading]);\n\n  // Chart data\n  const getLast7Days = () => {\n    const days = [];\n    for (let i = 6; i >= 0; i--) {\n      const d = new Date();\n      d.setDate(d.getDate() - i);\n      days.push(d.toLocaleDateString());\n    }\n    return days;\n  };\n  const chartLabels = getLast7Days();\n  const chartData = {\n    labels: chartLabels,\n    datasets: [{\n      label: 'Resource Uploads',\n      data: chartLabels.map(day => activityLog.filter(a => a.type === 'activity' && a.msg.startsWith('Uploaded resource') && new Date(a.date).toLocaleDateString() === day).length),\n      backgroundColor: '#3182ce'\n    }, {\n      label: 'Coding Practice',\n      data: chartLabels.map(day => activityLog.filter(a => a.type === 'activity' && a.msg === 'Clicked coding practice link' && new Date(a.date).toLocaleDateString() === day).length),\n      backgroundColor: '#805ad5'\n    }]\n  };\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'top'\n      },\n      tooltip: {\n        enabled: true\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          stepSize: 1\n        }\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: getStyle('appContainer'),\n    children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n      style: getStyle('navbar'),\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          background: 'none',\n          border: 'none',\n          color: 'white',\n          // Always white since navbar has gradient background\n          marginRight: '20px',\n          cursor: 'pointer',\n          padding: '8px',\n          borderRadius: '4px',\n          transition: 'all 0.2s ease'\n        },\n        onClick: () => setSidebarOpen(!sidebarOpen),\n        onMouseEnter: e => e.target.style.background = 'rgba(255, 255, 255, 0.1)',\n        onMouseLeave: e => e.target.style.background = 'none',\n        children: sidebarOpen ? /*#__PURE__*/_jsxDEV(FiX, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 616,\n          columnNumber: 26\n        }, this) : /*#__PURE__*/_jsxDEV(FiMenu, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 616,\n          columnNumber: 46\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 601,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: require('./eduai-logo.jpg'),\n          alt: \"EduAI Logo\",\n          style: {\n            height: '36px',\n            marginRight: '12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 620,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            \"data-header-title\": true,\n            style: {\n              fontWeight: 600,\n              fontSize: '18px',\n              color: 'white'\n            },\n            children: \"EDU NOVA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 626,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            \"data-header-subtitle\": true,\n            style: {\n              fontSize: '12px',\n              opacity: 0.8,\n              color: 'white'\n            },\n            children: \"AI POWERED LEARNING SYSTEM\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 625,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 619,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '16px'\n        },\n        children: user ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '40px',\n              height: '40px',\n              borderRadius: '50%',\n              background: 'rgba(255, 255, 255, 0.2)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              fontWeight: 600,\n              cursor: 'pointer',\n              color: 'white',\n              backdropFilter: 'blur(10px)'\n            },\n            children: user.email === ADMIN_EMAIL ? /*#__PURE__*/_jsxDEV(FiShield, {\n              size: 20,\n              color: \"#4caf50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 657,\n              columnNumber: 47\n            }, this) : user.email[0].toUpperCase()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 644,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              ...getStyle('buttonPrimary'),\n              background: 'rgba(255, 255, 255, 0.2)',\n              color: 'white',\n              border: '1px solid rgba(255, 255, 255, 0.3)',\n              backdropFilter: 'blur(10px)'\n            },\n            onClick: handleLogout,\n            children: [/*#__PURE__*/_jsxDEV(FiLogOut, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 669,\n              columnNumber: 17\n            }, this), \" Logout\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 659,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(\"button\", {\n          style: {\n            ...getStyle('buttonPrimary'),\n            background: 'rgba(255, 255, 255, 0.2)',\n            color: 'white',\n            border: '1px solid rgba(255, 255, 255, 0.3)',\n            backdropFilter: 'blur(10px)'\n          },\n          onClick: () => {\n            console.log('Login functionality to be implemented');\n          },\n          children: [/*#__PURE__*/_jsxDEV(FiLogIn, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 683,\n            columnNumber: 15\n          }, this), \" Login\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 673,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 641,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 600,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n      style: getStyle('sidebar'),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '16px'\n        },\n        children: updatedSidebarItems.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              ...getStyle('sidebarItem'),\n              ...(activeTab === item.tab ? getStyle('sidebarItemActive') : {}),\n              cursor: 'pointer',\n              transition: 'all 0.3s ease'\n            },\n            onClick: () => {\n              setActiveTab(item.tab);\n              setSidebarOpen(false);\n            },\n            onMouseEnter: e => {\n              if (activeTab !== item.tab) {\n                e.target.style.background = 'rgba(0, 0, 0, 0.05)';\n                e.target.style.transform = 'translateX(4px)';\n                e.target.style.borderLeft = `3px solid ${globalStyles.currentTheme.primary}`;\n              }\n            },\n            onMouseLeave: e => {\n              if (activeTab !== item.tab) {\n                e.target.style.background = globalStyles.currentTheme.surface;\n                e.target.style.transform = 'translateX(0)';\n                e.target.style.borderLeft = '3px solid transparent';\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginRight: '12px'\n              },\n              children: item.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 720,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                flex: 1\n              },\n              children: item.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 721,\n              columnNumber: 17\n            }, this), item.subItems.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              onClick: e => {\n                e.stopPropagation();\n                toggleMenu(item.title);\n              },\n              children: expandedMenus[item.title] ? /*#__PURE__*/_jsxDEV(FiChevronDown, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 727,\n                columnNumber: 50\n              }, this) : /*#__PURE__*/_jsxDEV(FiChevronRight, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 727,\n                columnNumber: 70\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 723,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 694,\n            columnNumber: 15\n          }, this), item.subItems.length > 0 && expandedMenus[item.title] && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginLeft: '32px'\n            },\n            children: item.subItems.map((subItem, subIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...getStyle('sidebarItem'),\n                padding: '8px 16px 8px 32px',\n                fontSize: '14px',\n                opacity: 0.9\n              },\n              onClick: () => {\n                setActiveTab(item.tab);\n                setSidebarOpen(false);\n              },\n              onMouseEnter: e => {\n                e.target.style.background = 'rgba(0, 0, 0, 0.03)';\n                e.target.style.paddingLeft = '36px';\n                e.target.style.opacity = '1';\n              },\n              onMouseLeave: e => {\n                e.target.style.background = globalStyles.currentTheme.surface;\n                e.target.style.paddingLeft = '32px';\n                e.target.style.opacity = '0.9';\n              },\n              children: subItem.title\n            }, subIndex, false, {\n              fileName: _jsxFileName,\n              lineNumber: 735,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 733,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 693,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 691,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 690,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      style: getStyle('mainContent'),\n      children: [sidebarOpen && window.innerWidth < 768 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'fixed',\n          top: '64px',\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0,0,0,0.5)',\n          zIndex: 800\n        },\n        onClick: () => setSidebarOpen(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 772,\n        columnNumber: 11\n      }, this), activeTab === \"dashboard\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '2rem 1rem',\n          background: globalStyles.currentTheme.gradientLight,\n          minHeight: '100vh',\n          position: 'relative',\n          overflow: 'hidden'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundImage: `\n                radial-gradient(circle at 20% 30%, ${globalStyles.currentTheme.primary}08 0%, transparent 50%),\n                radial-gradient(circle at 80% 70%, ${globalStyles.currentTheme.primary}05 0%, transparent 50%),\n                radial-gradient(circle at 40% 80%, ${globalStyles.currentTheme.primary}03 0%, transparent 50%)\n              `,\n            zIndex: 0\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 796,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundImage: `\n                linear-gradient(${globalStyles.currentTheme.border} 1px, transparent 1px),\n                linear-gradient(90deg, ${globalStyles.currentTheme.border} 1px, transparent 1px)\n              `,\n            backgroundSize: '50px 50px',\n            opacity: 0.3,\n            zIndex: 0\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 811,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            maxWidth: '1400px',\n            margin: '0 auto',\n            position: 'relative',\n            zIndex: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: globalStyles.currentTheme.surface,\n              backdropFilter: 'blur(20px)',\n              borderRadius: '20px',\n              padding: '3rem',\n              marginBottom: '2rem',\n              position: 'relative',\n              overflow: 'hidden',\n              border: `1px solid ${globalStyles.currentTheme.border}`,\n              boxShadow: `0 20px 40px ${globalStyles.currentTheme.shadow}`\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                height: '4px',\n                background: globalStyles.currentTheme.gradient\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 841,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'auto 1fr auto',\n                gap: '2rem',\n                alignItems: 'center',\n                position: 'relative',\n                zIndex: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '100px',\n                  height: '100px',\n                  borderRadius: '20px',\n                  background: globalStyles.currentTheme.gradient,\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  fontSize: '2.5rem',\n                  fontWeight: 'bold',\n                  color: 'white',\n                  boxShadow: `0 10px 30px ${globalStyles.currentTheme.shadow}`,\n                  border: `3px solid ${globalStyles.currentTheme.surface}`\n                },\n                children: user ? user.email[0].toUpperCase() : '👤'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 859,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '2.5rem',\n                    fontWeight: 700,\n                    marginBottom: '0.5rem',\n                    color: globalStyles.currentTheme.text,\n                    lineHeight: 1.2\n                  },\n                  children: [\"Welcome back, \", user ? user.email.split('@')[0] : 'User', \"! \\uD83D\\uDC4B\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 878,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '1.1rem',\n                    color: globalStyles.currentTheme.textLight,\n                    fontWeight: 400,\n                    marginBottom: '1.5rem'\n                  },\n                  children: \"Ready to continue your learning journey? Let's achieve your goals together.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 888,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    gap: '1rem',\n                    flexWrap: 'wrap'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      background: globalStyles.currentTheme.primary + '15',\n                      color: globalStyles.currentTheme.primary,\n                      padding: '0.5rem 1rem',\n                      borderRadius: '12px',\n                      fontSize: '0.9rem',\n                      fontWeight: 600,\n                      border: `1px solid ${globalStyles.currentTheme.primary}30`\n                    },\n                    children: \"\\uD83D\\uDCC8 Active Learner\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 904,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      background: globalStyles.currentTheme.primary + '15',\n                      color: globalStyles.currentTheme.primary,\n                      padding: '0.5rem 1rem',\n                      borderRadius: '12px',\n                      fontSize: '0.9rem',\n                      fontWeight: 600,\n                      border: `1px solid ${globalStyles.currentTheme.primary}30`\n                    },\n                    children: \"\\uD83C\\uDFAF Goal Oriented\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 915,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 899,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 877,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'center',\n                  padding: '1rem',\n                  background: globalStyles.currentTheme.secondary,\n                  borderRadius: '16px',\n                  border: `1px solid ${globalStyles.currentTheme.border}`\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '2rem',\n                    fontWeight: 700,\n                    color: globalStyles.currentTheme.primary,\n                    marginBottom: '0.5rem'\n                  },\n                  children: new Date().toLocaleDateString('en-US', {\n                    day: 'numeric'\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 937,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '0.9rem',\n                    color: globalStyles.currentTheme.textLight,\n                    fontWeight: 500\n                  },\n                  children: new Date().toLocaleDateString('en-US', {\n                    month: 'short',\n                    year: 'numeric'\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 945,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 930,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 850,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 829,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',\n              gap: '1.5rem',\n              marginBottom: '2rem'\n            },\n            children: [{\n              title: 'Learning Progress',\n              value: '78',\n              unit: '%',\n              icon: '📈',\n              trend: '+12%',\n              description: 'This month',\n              color: globalStyles.currentTheme.primary\n            }, {\n              title: 'Study Hours',\n              value: '47',\n              unit: 'hrs',\n              icon: '⏱️',\n              trend: '+8 hrs',\n              description: 'This week',\n              color: '#10B981'\n            }, {\n              title: 'Completed Tasks',\n              value: '23',\n              unit: 'tasks',\n              icon: '✅',\n              trend: '+5',\n              description: 'This week',\n              color: '#3B82F6'\n            }, {\n              title: 'Skill Rating',\n              value: '4.8',\n              unit: '/5.0',\n              icon: '⭐',\n              trend: '+0.3',\n              description: 'Overall',\n              color: '#F59E0B'\n            }].map((stat, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: globalStyles.currentTheme.surface,\n                borderRadius: '16px',\n                padding: '1.5rem',\n                position: 'relative',\n                overflow: 'hidden',\n                border: `1px solid ${globalStyles.currentTheme.border}`,\n                cursor: 'pointer',\n                transition: 'all 0.3s ease',\n                boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'translateY(-4px)';\n                e.currentTarget.style.boxShadow = `0 8px 25px ${globalStyles.currentTheme.shadow}`;\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'translateY(0)';\n                e.currentTarget.style.boxShadow = `0 4px 12px ${globalStyles.currentTheme.shadow}`;\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  right: 0,\n                  height: '3px',\n                  background: stat.color\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1022,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'relative',\n                  zIndex: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    marginBottom: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.9rem',\n                      fontWeight: 600,\n                      color: globalStyles.currentTheme.textLight,\n                      textTransform: 'uppercase',\n                      letterSpacing: '0.5px'\n                    },\n                    children: stat.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1039,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '1.5rem',\n                      opacity: 0.8\n                    },\n                    children: stat.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1048,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1033,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'baseline',\n                    gap: '0.5rem',\n                    marginBottom: '0.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '2.5rem',\n                      fontWeight: 700,\n                      color: globalStyles.currentTheme.text,\n                      lineHeight: 1\n                    },\n                    children: stat.value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1063,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '1rem',\n                      fontWeight: 500,\n                      color: globalStyles.currentTheme.textLight\n                    },\n                    children: stat.unit\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1071,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1057,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.8rem',\n                      color: globalStyles.currentTheme.textLight\n                    },\n                    children: stat.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1086,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.8rem',\n                      fontWeight: 600,\n                      color: stat.color,\n                      background: stat.color + '15',\n                      padding: '0.25rem 0.5rem',\n                      borderRadius: '8px'\n                    },\n                    children: stat.trend\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1092,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1081,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1031,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1001,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 957,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: globalStyles.currentTheme.surface,\n              borderRadius: '20px',\n              padding: '2rem',\n              marginBottom: '2rem',\n              border: `1px solid ${globalStyles.currentTheme.border}`,\n              boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n              position: 'relative',\n              overflow: 'hidden'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                height: '4px',\n                background: globalStyles.currentTheme.gradient\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1120,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              style: {\n                margin: '0 0 2rem 0',\n                fontSize: '1.5rem',\n                fontWeight: 600,\n                color: globalStyles.currentTheme.text,\n                textAlign: 'center'\n              },\n              children: \"\\uD83D\\uDE80 Quick Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1129,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                gap: '1.5rem'\n              },\n              children: [{\n                icon: '📊',\n                title: 'Take Assessment',\n                subtitle: 'Skill Evaluation',\n                desc: 'Evaluate your current skill level',\n                action: () => setActiveTab('quizzes'),\n                color: globalStyles.currentTheme.primary\n              }, {\n                icon: '💻',\n                title: 'Practice Coding',\n                subtitle: 'DSA Problems',\n                desc: 'Solve data structures & algorithms',\n                action: () => setActiveTab('dsa'),\n                color: '#3B82F6'\n              }, {\n                icon: '📄',\n                title: 'Resume Review',\n                subtitle: 'Career Enhancement',\n                desc: 'Get professional resume feedback',\n                action: () => setActiveTab('resume'),\n                color: '#10B981'\n              }, {\n                icon: '📚',\n                title: 'Study Resources',\n                subtitle: 'Learning Materials',\n                desc: 'Access curated study materials',\n                action: () => setActiveTab('resources'),\n                color: '#F59E0B'\n              }].map((action, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: action.action,\n                style: {\n                  background: globalStyles.currentTheme.surface,\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  cursor: 'pointer',\n                  transition: 'all 0.3s ease',\n                  border: `1px solid ${globalStyles.currentTheme.border}`,\n                  position: 'relative',\n                  overflow: 'hidden',\n                  boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.transform = 'translateY(-4px)';\n                  e.currentTarget.style.boxShadow = `0 8px 25px ${globalStyles.currentTheme.shadow}`;\n                  e.currentTarget.style.borderColor = action.color;\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = `0 4px 12px ${globalStyles.currentTheme.shadow}`;\n                  e.currentTarget.style.borderColor = globalStyles.currentTheme.border;\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    height: '3px',\n                    background: action.color\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1203,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'relative',\n                    zIndex: 1,\n                    textAlign: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '2.5rem',\n                      marginBottom: '1rem',\n                      opacity: 0.8\n                    },\n                    children: action.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1213,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '1.1rem',\n                      fontWeight: 600,\n                      color: globalStyles.currentTheme.text,\n                      marginBottom: '0.5rem'\n                    },\n                    children: action.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1221,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.9rem',\n                      color: action.color,\n                      fontWeight: 500,\n                      marginBottom: '0.75rem'\n                    },\n                    children: action.subtitle\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1230,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.85rem',\n                      color: globalStyles.currentTheme.textLight,\n                      lineHeight: 1.4\n                    },\n                    children: action.desc\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1239,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1212,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1178,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1139,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1109,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',\n              gap: '2rem',\n              marginBottom: '2rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: globalStyles.currentTheme.surface,\n                borderRadius: '16px',\n                padding: '1.5rem',\n                boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                border: `1px solid ${globalStyles.currentTheme.border}`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 1.5rem 0',\n                  fontSize: '1.2rem',\n                  fontWeight: 600,\n                  color: globalStyles.currentTheme.text,\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                },\n                children: \"\\uD83D\\uDCCA Weekly Progress\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1267,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '1rem'\n                },\n                children: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    marginBottom: '0.75rem',\n                    padding: '0.5rem',\n                    borderRadius: '8px',\n                    background: index < 5 ? globalStyles.currentTheme.secondary : 'transparent'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontSize: '0.9rem',\n                      fontWeight: 500,\n                      color: globalStyles.currentTheme.text,\n                      minWidth: '40px'\n                    },\n                    children: day\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1291,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1,\n                      height: '8px',\n                      background: globalStyles.currentTheme.border,\n                      borderRadius: '4px',\n                      margin: '0 1rem',\n                      overflow: 'hidden'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        height: '100%',\n                        width: `${Math.random() * 80 + 20}%`,\n                        background: index < 5 ? globalStyles.currentTheme.primary : globalStyles.currentTheme.border,\n                        borderRadius: '4px',\n                        transition: 'width 1s ease'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1307,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1299,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontSize: '0.8rem',\n                      color: globalStyles.currentTheme.textLight,\n                      minWidth: '40px',\n                      textAlign: 'right'\n                    },\n                    children: index < 5 ? `${Math.floor(Math.random() * 3 + 1)}h` : '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1315,\n                    columnNumber: 25\n                  }, this)]\n                }, day, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1282,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1280,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1260,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: globalStyles.currentTheme.surface,\n                borderRadius: '16px',\n                padding: '1.5rem',\n                boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                border: `1px solid ${globalStyles.currentTheme.border}`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 1.5rem 0',\n                  fontSize: '1.2rem',\n                  fontWeight: 600,\n                  color: globalStyles.currentTheme.text,\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                },\n                children: \"\\uD83C\\uDFAF Goals & Achievements\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1336,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: '1rem'\n                },\n                children: [{\n                  title: 'Complete 5 DSA Problems',\n                  progress: 80,\n                  status: 'In Progress'\n                }, {\n                  title: 'Finish Resume Review',\n                  progress: 100,\n                  status: 'Completed'\n                }, {\n                  title: 'Take 3 Practice Tests',\n                  progress: 33,\n                  status: 'In Progress'\n                }].map((goal, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    padding: '1rem',\n                    borderRadius: '12px',\n                    background: globalStyles.currentTheme.secondary,\n                    border: `1px solid ${globalStyles.currentTheme.border}`\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      alignItems: 'center',\n                      marginBottom: '0.5rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        fontSize: '0.9rem',\n                        fontWeight: 500,\n                        color: globalStyles.currentTheme.text\n                      },\n                      children: goal.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1366,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        fontSize: '0.8rem',\n                        fontWeight: 600,\n                        color: goal.progress === 100 ? '#10B981' : globalStyles.currentTheme.primary,\n                        background: goal.progress === 100 ? '#10B98115' : globalStyles.currentTheme.primary + '15',\n                        padding: '0.25rem 0.5rem',\n                        borderRadius: '6px'\n                      },\n                      children: goal.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1373,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1360,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      height: '6px',\n                      background: globalStyles.currentTheme.border,\n                      borderRadius: '3px',\n                      overflow: 'hidden'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        height: '100%',\n                        width: `${goal.progress}%`,\n                        background: goal.progress === 100 ? '#10B981' : globalStyles.currentTheme.primary,\n                        borderRadius: '3px',\n                        transition: 'width 1s ease'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1390,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1384,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1354,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1348,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1329,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1253,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: '1fr 1fr',\n              gap: '2rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: globalStyles.currentTheme.surface,\n                borderRadius: '16px',\n                padding: '1.5rem',\n                boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                border: `1px solid ${globalStyles.currentTheme.border}`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 1.5rem 0',\n                  fontSize: '1.3rem',\n                  fontWeight: 600,\n                  color: globalStyles.currentTheme.text\n                },\n                children: \"\\uD83D\\uDCC8 Recent Activity\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1419,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  maxHeight: '250px',\n                  overflowY: 'auto'\n                },\n                children: activityLog.slice(0, 5).map((log, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '1rem',\n                    padding: '0.75rem',\n                    borderRadius: '8px',\n                    marginBottom: '0.5rem',\n                    background: index % 2 === 0 ? globalStyles.currentTheme.secondary : 'transparent',\n                    transition: 'all 0.3s ease'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '10px',\n                      height: '10px',\n                      borderRadius: '50%',\n                      background: log.type === 'login' ? '#4ECDC4' : '#45B7D1',\n                      flexShrink: 0\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1443,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.9rem',\n                        fontWeight: 500,\n                        color: globalStyles.currentTheme.text,\n                        marginBottom: '0.2rem'\n                      },\n                      children: log.msg\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1451,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.8rem',\n                        color: globalStyles.currentTheme.textLight\n                      },\n                      children: new Date(log.date).toLocaleString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1459,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1450,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1433,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1428,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1412,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: globalStyles.currentTheme.surface,\n                borderRadius: '16px',\n                padding: '1.5rem',\n                boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                border: `1px solid ${globalStyles.currentTheme.border}`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 1.5rem 0',\n                  fontSize: '1.3rem',\n                  fontWeight: 600,\n                  color: globalStyles.currentTheme.text\n                },\n                children: \"\\uD83D\\uDCC4 Resume Management\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1479,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: '1rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.75rem',\n                    padding: '1rem',\n                    borderRadius: '12px',\n                    background: globalStyles.currentTheme.secondary,\n                    border: `2px dashed ${globalStyles.currentTheme.border}`,\n                    cursor: resumeUploadLoading ? 'not-allowed' : 'pointer',\n                    transition: 'all 0.3s ease'\n                  },\n                  onMouseEnter: e => {\n                    if (!resumeUploadLoading) {\n                      e.currentTarget.style.borderColor = globalStyles.currentTheme.primary;\n                      e.currentTarget.style.background = globalStyles.currentTheme.primary + '10';\n                    }\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.borderColor = globalStyles.currentTheme.border;\n                    e.currentTarget.style.background = globalStyles.currentTheme.secondary;\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FiUpload, {\n                    size: 20,\n                    color: globalStyles.currentTheme.primary\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1510,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontWeight: 600,\n                        color: globalStyles.currentTheme.text,\n                        marginBottom: '0.2rem'\n                      },\n                      children: resumeUploadLoading ? 'Uploading...' : 'Upload Resume'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1512,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.8rem',\n                        color: globalStyles.currentTheme.textLight\n                      },\n                      children: \"PDF files only\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1519,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1511,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"file\",\n                    accept: \"application/pdf\",\n                    onChange: handleResumeUpload,\n                    disabled: resumeUploadLoading,\n                    style: {\n                      display: 'none'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1526,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1489,\n                  columnNumber: 21\n                }, this), resumeUrl && /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: resumeUrl,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.75rem',\n                    padding: '1rem',\n                    borderRadius: '12px',\n                    background: '#4ECDC4',\n                    color: 'white',\n                    textDecoration: 'none',\n                    fontWeight: 600,\n                    transition: 'all 0.3s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.background = '#3DBDB6';\n                    e.currentTarget.style.transform = 'translateY(-2px)';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.background = '#4ECDC4';\n                    e.currentTarget.style.transform = 'translateY(0)';\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FiFileText, {\n                    size: 20\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1561,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"View Resume\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1562,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1536,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1488,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1472,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1405,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: globalStyles.currentTheme.surface,\n              borderRadius: '16px',\n              padding: '2rem',\n              marginTop: '2rem',\n              border: `1px solid ${globalStyles.currentTheme.border}`,\n              boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                gap: '2rem',\n                marginBottom: '1.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  style: {\n                    margin: '0 0 1rem 0',\n                    fontSize: '1rem',\n                    fontWeight: 600,\n                    color: globalStyles.currentTheme.text\n                  },\n                  children: \"\\uD83D\\uDCC8 Your Progress\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1587,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    flexDirection: 'column',\n                    gap: '0.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.9rem',\n                      color: globalStyles.currentTheme.textLight\n                    },\n                    children: [\"Total Study Time: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      style: {\n                        color: globalStyles.currentTheme.primary\n                      },\n                      children: \"47 hours\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1604,\n                      columnNumber: 43\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1600,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.9rem',\n                      color: globalStyles.currentTheme.textLight\n                    },\n                    children: [\"Completed Tasks: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      style: {\n                        color: globalStyles.currentTheme.primary\n                      },\n                      children: \"23\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1610,\n                      columnNumber: 42\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1606,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1595,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1586,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  style: {\n                    margin: '0 0 1rem 0',\n                    fontSize: '1rem',\n                    fontWeight: 600,\n                    color: globalStyles.currentTheme.text\n                  },\n                  children: \"\\uD83D\\uDD17 Quick Links\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1617,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    flexDirection: 'column',\n                    gap: '0.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setActiveTab('academics'),\n                    style: {\n                      background: 'none',\n                      border: 'none',\n                      color: globalStyles.currentTheme.primary,\n                      fontSize: '0.9rem',\n                      cursor: 'pointer',\n                      textAlign: 'left',\n                      padding: '0.25rem 0'\n                    },\n                    children: \"\\uD83D\\uDCDA Academic Resources\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1630,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setActiveTab('coding'),\n                    style: {\n                      background: 'none',\n                      border: 'none',\n                      color: globalStyles.currentTheme.primary,\n                      fontSize: '0.9rem',\n                      cursor: 'pointer',\n                      textAlign: 'left',\n                      padding: '0.25rem 0'\n                    },\n                    children: \"\\uD83D\\uDCBB Coding Practice\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1644,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1625,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1616,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  style: {\n                    margin: '0 0 1rem 0',\n                    fontSize: '1rem',\n                    fontWeight: 600,\n                    color: globalStyles.currentTheme.text\n                  },\n                  children: \"\\uD83D\\uDCA1 Daily Inspiration\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1663,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '0.9rem',\n                    color: globalStyles.currentTheme.textLight,\n                    fontStyle: 'italic',\n                    lineHeight: 1.4\n                  },\n                  children: \"\\\"Success is not final, failure is not fatal: it is the courage to continue that counts.\\\"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1671,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1662,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1579,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                borderTop: `1px solid ${globalStyles.currentTheme.border}`,\n                paddingTop: '1rem',\n                fontSize: '0.8rem',\n                color: globalStyles.currentTheme.textLight\n              },\n              children: \"Keep learning, keep growing! \\uD83D\\uDE80 Your journey to success starts here.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1683,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1570,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 826,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 788,\n        columnNumber: 11\n      }, this), activeTab === \"resume\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px',\n          maxWidth: '1200px',\n          margin: '0 auto'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getStyle('card'),\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginTop: 0,\n              color: '#333'\n            },\n            children: \"Career Assistant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1700,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              opacity: 0.8,\n              marginBottom: '24px',\n              color: '#666'\n            },\n            children: \"Get personalized resume advice and career guidance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1704,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              height: '50vh',\n              overflowY: 'auto',\n              marginBottom: '24px',\n              padding: '16px',\n              backgroundColor: '#f5f5f5',\n              border: '1px solid #e0e0e0',\n              borderRadius: '8px'\n            },\n            children: [messages.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                height: '100%',\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                justifyContent: 'center',\n                textAlign: 'center',\n                opacity: 0.7\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '48px',\n                  marginBottom: '16px'\n                },\n                children: \"\\uD83D\\uDCAC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1733,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: 0,\n                  color: '#333'\n                },\n                children: \"Start a conversation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1734,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#666'\n                },\n                children: \"Ask about resumes, interviews, or career advice\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1738,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1724,\n              columnNumber: 19\n            }, this) : messages.map((msg, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...(msg.role === 'user' ? getStyle('chatBubbleUser') : getStyle('chatBubbleBot')),\n                animation: 'fadeIn 0.3s ease'\n              },\n              children: msg.role === 'bot' ? /*#__PURE__*/_jsxDEV(ReactMarkdown, {\n                children: msg.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1752,\n                columnNumber: 25\n              }, this) : msg.content\n            }, idx, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1744,\n              columnNumber: 21\n            }, this)), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: getStyle('chatBubbleBot'),\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '8px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '10px',\n                    height: '10px',\n                    borderRadius: '50%',\n                    backgroundColor: '#1976d2',\n                    animation: 'pulse 1.4s infinite ease-in-out'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1762,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '10px',\n                    height: '10px',\n                    borderRadius: '50%',\n                    backgroundColor: '#1976d2',\n                    animation: 'pulse 1.4s infinite ease-in-out',\n                    animationDelay: '0.2s'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1769,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '10px',\n                    height: '10px',\n                    borderRadius: '50%',\n                    backgroundColor: '#1976d2',\n                    animation: 'pulse 1.4s infinite ease-in-out',\n                    animationDelay: '0.4s'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1777,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1761,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1760,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: chatEndRef\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1788,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1713,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            style: {\n              display: 'flex',\n              gap: '12px'\n            },\n            onSubmit: e => {\n              e.preventDefault();\n              sendMessage();\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Type your message...\",\n              style: getStyle('inputField'),\n              value: input,\n              onChange: e => setInput(e.target.value),\n              disabled: loading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1799,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              style: {\n                ...getStyle('buttonPrimary'),\n                minWidth: '100px'\n              },\n              disabled: loading || !input.trim(),\n              children: loading ? 'Sending...' : 'Send'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1807,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1792,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1699,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1698,\n        columnNumber: 11\n      }, this), activeTab === \"dsa\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getStyle('card'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: '16px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                style: {\n                  marginTop: 0,\n                  marginBottom: '8px'\n                },\n                children: \"\\uD83D\\uDE80 Company Wise DSA Questions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1829,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  opacity: 0.8,\n                  margin: 0\n                },\n                children: \"Explore DSA questions from top companies with enhanced filtering and favorites\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1830,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1828,\n              columnNumber: 17\n            }, this), showRevertButton && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: revertHeaderChanges,\n              style: {\n                ...getStyle('buttonPrimary'),\n                background: '#ff6b6b',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '8px',\n                fontSize: '14px',\n                padding: '8px 16px',\n                border: 'none',\n                borderRadius: '8px',\n                color: 'white',\n                cursor: 'pointer',\n                transition: 'all 0.3s ease'\n              },\n              onMouseEnter: e => e.target.style.background = '#ff5252',\n              onMouseLeave: e => e.target.style.background = '#ff6b6b',\n              children: [/*#__PURE__*/_jsxDEV(FiRefreshCw, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1854,\n                columnNumber: 21\n              }, this), \"Revert Header Color\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1835,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1827,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '8px',\n              marginBottom: '20px',\n              flexWrap: 'wrap',\n              borderBottom: '1px solid #eee',\n              paddingBottom: '16px'\n            },\n            children: ['all', ...Object.keys(companyCategories)].map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectedCategory(category),\n              style: {\n                padding: '8px 16px',\n                borderRadius: '20px',\n                border: selectedCategory === category ? 'none' : '1px solid #ddd',\n                background: selectedCategory === category ? globalStyles.currentTheme.primary : 'transparent',\n                color: selectedCategory === category ? 'white' : '#666',\n                cursor: 'pointer',\n                fontSize: '14px',\n                fontWeight: selectedCategory === category ? 600 : 400,\n                transition: 'all 0.3s ease',\n                textTransform: 'capitalize'\n              },\n              onMouseEnter: e => {\n                if (selectedCategory !== category) {\n                  e.target.style.background = '#f5f5f5';\n                }\n              },\n              onMouseLeave: e => {\n                if (selectedCategory !== category) {\n                  e.target.style.background = 'transparent';\n                }\n              },\n              children: category === 'all' ? '🌟 All' : `${category === 'FAANG' ? '🔥' : category === 'Big Tech' ? '💻' : category === 'Startups' ? '🚀' : category === 'Finance' ? '💰' : category === 'Indian' ? '🇮🇳' : '🏢'} ${category}`\n            }, category, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1870,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1861,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '16px',\n              marginBottom: '24px',\n              flexWrap: 'wrap',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'relative',\n                flex: 1,\n                minWidth: '300px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  left: '16px',\n                  top: '50%',\n                  transform: 'translateY(-50%)',\n                  color: '#666'\n                },\n                children: /*#__PURE__*/_jsxDEV(FiSearch, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1924,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1917,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search companies...\",\n                style: {\n                  ...getStyle('inputField'),\n                  paddingLeft: '48px',\n                  width: '100%'\n                },\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1926,\n                columnNumber: 19\n              }, this), searchTerm && /*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  position: 'absolute',\n                  right: '16px',\n                  top: '50%',\n                  transform: 'translateY(-50%)',\n                  background: 'none',\n                  border: 'none',\n                  color: '#666',\n                  cursor: 'pointer'\n                },\n                onClick: () => setSearchTerm(\"\"),\n                children: /*#__PURE__*/_jsxDEV(FiX, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1951,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1938,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1916,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: sortBy,\n              onChange: e => setSortBy(e.target.value),\n              style: {\n                ...getStyle('inputField'),\n                width: 'auto',\n                minWidth: '150px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"name\",\n                children: \"\\uD83D\\uDCDD Sort by Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1966,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"favorites\",\n                children: \"\\u2B50 Favorites First\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1967,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1957,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1908,\n            columnNumber: 15\n          }, this), recentCompanies.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '24px',\n              padding: '16px',\n              borderRadius: '12px',\n              background: '#f8f9fa',\n              border: '1px solid #e9ecef'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '8px',\n                fontSize: '16px',\n                marginBottom: '12px',\n                color: '#333',\n                margin: '0 0 12px 0'\n              },\n              children: [/*#__PURE__*/_jsxDEV(FiClock, {\n                color: \"#666\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1989,\n                columnNumber: 21\n              }, this), \" Recently Viewed\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1980,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: '8px',\n                flexWrap: 'wrap'\n              },\n              children: recentCompanies.map(company => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleCompanyClick(company),\n                style: {\n                  padding: '6px 12px',\n                  borderRadius: '16px',\n                  border: `1px solid ${globalStyles.currentTheme.primary}`,\n                  background: 'transparent',\n                  color: globalStyles.currentTheme.primary,\n                  cursor: 'pointer',\n                  fontSize: '12px',\n                  transition: 'all 0.3s ease'\n                },\n                onMouseEnter: e => {\n                  e.target.style.background = globalStyles.currentTheme.primary;\n                  e.target.style.color = 'white';\n                },\n                onMouseLeave: e => {\n                  e.target.style.background = 'transparent';\n                  e.target.style.color = globalStyles.currentTheme.primary;\n                },\n                children: company\n              }, company, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1997,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1991,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1973,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fill, minmax(220px, 1fr))',\n              gap: '16px',\n              marginTop: '24px'\n            },\n            children: getFilteredCompanies().map((company, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...getStyle('companyCard'),\n                position: 'relative',\n                transform: favoriteCompanies.includes(company) ? 'scale(1.02)' : 'scale(1)',\n                border: favoriteCompanies.includes(company) ? `2px solid ${globalStyles.currentTheme.primary}` : `1px solid ${globalStyles.currentTheme.border}`,\n                background: globalStyles.currentTheme.surface,\n                color: globalStyles.currentTheme.text,\n                animation: `fadeIn 0.3s ease ${index * 0.1}s both`,\n                boxShadow: `0 4px 6px ${globalStyles.currentTheme.shadow}`\n              },\n              onClick: () => handleCompanyClick(company),\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: e => toggleFavorite(company, e),\n                style: {\n                  position: 'absolute',\n                  top: '8px',\n                  right: '8px',\n                  background: 'none',\n                  border: 'none',\n                  cursor: 'pointer',\n                  color: favoriteCompanies.includes(company) ? '#ff6b6b' : '#ccc',\n                  transition: 'all 0.3s ease',\n                  fontSize: '18px'\n                },\n                children: /*#__PURE__*/_jsxDEV(FiHeart, {\n                  fill: favoriteCompanies.includes(company) ? 'currentColor' : 'none'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2065,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2051,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '56px',\n                  height: '56px',\n                  borderRadius: '50%',\n                  background: `linear-gradient(135deg, ${globalStyles.currentTheme.primary}, ${globalStyles.currentTheme.primaryDark})`,\n                  color: 'white',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  fontSize: '24px',\n                  fontWeight: 700,\n                  marginBottom: '12px',\n                  boxShadow: `0 4px 8px ${globalStyles.currentTheme.shadow}`\n                },\n                children: company.charAt(0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2069,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: 600,\n                  textAlign: 'center',\n                  fontSize: '14px',\n                  marginBottom: '8px'\n                },\n                children: company\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2087,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  fontSize: '12px',\n                  opacity: 0.7,\n                  marginTop: '8px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"\\uD83D\\uDCCA \", Math.floor(Math.random() * 50) + 10, \" Questions\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2104,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"\\u2B50 \", (Math.random() * 2 + 3).toFixed(1)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2105,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2097,\n                columnNumber: 21\n              }, this), Object.entries(companyCategories).map(([category, companies]) => {\n                if (companies.some(c => c.toLowerCase() === company.toLowerCase())) {\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      position: 'absolute',\n                      top: '8px',\n                      left: '8px',\n                      background: globalStyles.currentTheme.primary,\n                      color: 'white',\n                      padding: '2px 6px',\n                      borderRadius: '8px',\n                      fontSize: '10px',\n                      fontWeight: 600\n                    },\n                    children: category\n                  }, category, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2112,\n                    columnNumber: 27\n                  }, this);\n                }\n                return null;\n              })]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2034,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2027,\n            columnNumber: 15\n          }, this), getFilteredCompanies().length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '40px',\n              opacity: 0.7,\n              color: '#666'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '48px',\n                marginBottom: '16px'\n              },\n              children: \"\\uD83D\\uDD0D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2144,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                color: '#333'\n              },\n              children: \"No companies found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2145,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#666'\n              },\n              children: \"Try adjusting your search or category filter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2146,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2138,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1825,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1824,\n        columnNumber: 11\n      }, this), activeTab === \"quizzes\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getStyle('card'),\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginTop: 0\n            },\n            children: \"Career Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2157,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              opacity: 0.8,\n              marginBottom: '24px'\n            },\n            children: \"Test your knowledge with our career-focused quizzes!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2158,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',\n              gap: '16px'\n            },\n            children: quizButtons.map((quiz, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: getStyle('quizCard'),\n              onClick: () => openQuizLink(quiz.link),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  style: {\n                    margin: '0 0 8px 0'\n                  },\n                  children: quiz.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2174,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '14px',\n                    opacity: 0.8\n                  },\n                  children: quiz.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2175,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2173,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#1976d2'\n                },\n                children: /*#__PURE__*/_jsxDEV(FiExternalLink, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2184,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2183,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2168,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2162,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2156,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2155,\n        columnNumber: 11\n      }, this), activeTab === \"coding\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Coding, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2196,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2195,\n        columnNumber: 11\n      }, this), activeTab === \"resources\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '2rem 1rem',\n          background: globalStyles.currentTheme.gradientLight,\n          minHeight: '100vh'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            maxWidth: '1400px',\n            margin: '0 auto'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: globalStyles.currentTheme.surface,\n              borderRadius: '20px',\n              padding: '2rem',\n              marginBottom: '2rem',\n              border: `1px solid ${globalStyles.currentTheme.border}`,\n              boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n              position: 'relative'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                height: '4px',\n                background: globalStyles.currentTheme.gradient\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2216,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              style: {\n                margin: '0 0 1rem 0',\n                fontSize: '2.5rem',\n                fontWeight: 700,\n                color: globalStyles.currentTheme.text,\n                textAlign: 'center'\n              },\n              children: \"\\uD83D\\uDCDA Study Resources & Materials\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2225,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: 0,\n                fontSize: '1.1rem',\n                color: globalStyles.currentTheme.textLight,\n                textAlign: 'center'\n              },\n              children: \"Access comprehensive study materials, academic resources, and learning tools\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2234,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2207,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: globalStyles.currentTheme.surface,\n              borderRadius: '16px',\n              padding: '1.5rem',\n              marginBottom: '2rem',\n              border: `1px solid ${globalStyles.currentTheme.border}`,\n              boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              id: \"tab-navigation-container\",\n              className: \"tab-navigation-container\",\n              style: {\n                position: 'relative',\n                display: 'flex',\n                justifyContent: 'center',\n                alignItems: 'center',\n                background: globalStyles.currentTheme.secondary,\n                borderRadius: '12px',\n                padding: '0.5rem',\n                overflow: 'hidden'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"static-background-bar\",\n                style: {\n                  position: 'absolute',\n                  top: '0.5rem',\n                  bottom: '0.5rem',\n                  left: '0.5rem',\n                  right: '0.5rem',\n                  background: globalStyles.currentTheme.primary + '20',\n                  borderRadius: '8px',\n                  zIndex: 1,\n                  pointerEvents: 'none' // Allow clicks to pass through to buttons\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2269,\n                columnNumber: 19\n              }, this), [{\n                id: 'general',\n                label: '📁 General Resources',\n                icon: '📁',\n                shortLabel: 'General'\n              }, {\n                id: 'academics',\n                label: '🎓 Academic Materials',\n                icon: '🎓',\n                shortLabel: 'Academics'\n              }, {\n                id: 'tools',\n                label: '🛠️ Study Tools',\n                icon: '🛠️',\n                shortLabel: 'Tools'\n              }].map((tab, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `tab-button ${resourcesTab === tab.id ? 'active' : ''}`,\n                onClick: () => setResourcesTab(tab.id),\n                onMouseEnter: e => {\n                  // Simplified hover effect\n                  if (resourcesTab !== tab.id) {\n                    e.target.style.color = globalStyles.currentTheme.primary;\n                  }\n                },\n                onMouseLeave: e => {\n                  // Reset hover effect\n                  if (resourcesTab !== tab.id) {\n                    e.target.style.color = globalStyles.currentTheme.text;\n                  }\n                },\n                onFocus: e => {\n                  // Keyboard navigation support\n                  e.target.style.outline = `2px solid ${globalStyles.currentTheme.primary}`;\n                  e.target.style.outlineOffset = '2px';\n                },\n                onBlur: e => {\n                  // Remove focus outline\n                  e.target.style.outline = 'none';\n                },\n                \"aria-label\": `Switch to ${tab.label} tab`,\n                \"aria-selected\": resourcesTab === tab.id,\n                role: \"tab\",\n                tabIndex: 0,\n                style: {\n                  flex: 1,\n                  position: 'relative',\n                  zIndex: 20,\n                  padding: '0.875rem 1rem',\n                  minHeight: '44px',\n                  // Touch target requirement\n                  border: 'none',\n                  background: 'transparent',\n                  color: resourcesTab === tab.id ? 'white' : globalStyles.currentTheme.text,\n                  cursor: 'pointer',\n                  fontSize: '1rem',\n                  fontWeight: resourcesTab === tab.id ? 600 : 500,\n                  borderRadius: '8px',\n                  transition: 'all 0.3s ease',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  gap: '0.5rem',\n                  textAlign: 'center',\n                  whiteSpace: 'nowrap',\n                  userSelect: 'none',\n                  WebkitTapHighlightColor: 'transparent' // Remove tap highlight on mobile\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: tab.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2346,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    marginLeft: '0.5rem'\n                  },\n                  children: windowWidth < 640 ? tab.shortLabel : tab.label.replace(tab.icon + ' ', '')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2347,\n                  columnNumber: 23\n                }, this)]\n              }, tab.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2290,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2254,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                marginTop: '1rem',\n                padding: '0.5rem',\n                fontSize: '0.9rem',\n                color: globalStyles.currentTheme.textLight,\n                fontStyle: 'italic'\n              },\n              children: [resourcesTab === 'general' && 'Upload and manage your personal study materials and documents', resourcesTab === 'academics' && 'Access comprehensive academic resources organized by subject', resourcesTab === 'tools' && 'Utilize powerful study tools and utilities to enhance your learning']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2355,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2245,\n            columnNumber: 15\n          }, this), resourcesTab === 'general' && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: globalStyles.currentTheme.surface,\n              borderRadius: '16px',\n              padding: '2rem',\n              border: `1px solid ${globalStyles.currentTheme.border}`,\n              boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              style: {\n                margin: '0 0 1.5rem 0',\n                fontSize: '1.5rem',\n                fontWeight: 600,\n                color: globalStyles.currentTheme.text\n              },\n              children: \"\\uD83D\\uDCC1 General Resources\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2378,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '2rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'inline-flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  padding: '0.75rem 1.5rem',\n                  background: globalStyles.currentTheme.primary,\n                  color: 'white',\n                  borderRadius: '12px',\n                  cursor: resourceUploadLoading ? 'not-allowed' : 'pointer',\n                  fontSize: '1rem',\n                  fontWeight: 500,\n                  transition: 'all 0.3s ease'\n                },\n                children: [/*#__PURE__*/_jsxDEV(FiUpload, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2401,\n                  columnNumber: 23\n                }, this), resourceUploadLoading ? 'Uploading...' : 'Upload Resource', /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  accept: \".pdf,.doc,.docx,.txt\",\n                  onChange: handleResourceUpload,\n                  disabled: resourceUploadLoading,\n                  style: {\n                    display: 'none'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2403,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2388,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2387,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 1rem 0',\n                  fontSize: '1.2rem',\n                  fontWeight: 600,\n                  color: globalStyles.currentTheme.text\n                },\n                children: \"Your Uploaded Resources\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2414,\n                columnNumber: 21\n              }, this), userResources.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'center',\n                  padding: '3rem',\n                  background: globalStyles.currentTheme.secondary,\n                  borderRadius: '12px',\n                  border: `1px solid ${globalStyles.currentTheme.border}`\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '3rem',\n                    marginBottom: '1rem'\n                  },\n                  children: \"\\uD83D\\uDCC4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2430,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  style: {\n                    margin: '0 0 0.5rem 0',\n                    color: globalStyles.currentTheme.text\n                  },\n                  children: \"No resources uploaded yet\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2431,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    color: globalStyles.currentTheme.textLight\n                  },\n                  children: \"Upload your study materials, notes, and documents to get started\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2437,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2423,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'grid',\n                  gap: '1rem'\n                },\n                children: userResources.map((file, idx) => {\n                  const {\n                    data: urlData\n                  } = supabase.storage.from('resources').getPublicUrl(`${user.id}/${file.name}`);\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      alignItems: 'center',\n                      padding: '1rem',\n                      background: globalStyles.currentTheme.secondary,\n                      borderRadius: '12px',\n                      border: `1px solid ${globalStyles.currentTheme.border}`\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '40px',\n                          height: '40px',\n                          borderRadius: '8px',\n                          background: globalStyles.currentTheme.primary,\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          color: 'white',\n                          fontSize: '1.2rem'\n                        },\n                        children: \"\\uD83D\\uDCC4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2466,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            fontWeight: 600,\n                            color: globalStyles.currentTheme.text,\n                            marginBottom: '0.25rem'\n                          },\n                          children: file.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2480,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            fontSize: '0.8rem',\n                            color: globalStyles.currentTheme.textLight\n                          },\n                          children: \"Uploaded resource\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2487,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2479,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2461,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: urlData.publicUrl,\n                      target: \"_blank\",\n                      rel: \"noopener noreferrer\",\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.5rem',\n                        padding: '0.5rem 1rem',\n                        background: globalStyles.currentTheme.primary,\n                        color: 'white',\n                        textDecoration: 'none',\n                        borderRadius: '8px',\n                        fontSize: '0.9rem',\n                        fontWeight: 500,\n                        transition: 'all 0.3s ease'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(FiExternalLink, {\n                        size: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2513,\n                        columnNumber: 33\n                      }, this), \"Open\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2495,\n                      columnNumber: 31\n                    }, this)]\n                  }, idx, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2452,\n                    columnNumber: 29\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2445,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2413,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2371,\n            columnNumber: 17\n          }, this), resourcesTab === 'academics' && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gap: '2rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: globalStyles.currentTheme.surface,\n                borderRadius: '16px',\n                padding: '2rem',\n                border: `1px solid ${globalStyles.currentTheme.border}`,\n                boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                style: {\n                  margin: '0 0 1.5rem 0',\n                  fontSize: '1.5rem',\n                  fontWeight: 600,\n                  color: globalStyles.currentTheme.text,\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                },\n                children: \"\\uD83C\\uDF93 Academic Study Materials\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2539,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'grid',\n                  gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n                  gap: '1.5rem'\n                },\n                children: [{\n                  subject: 'Mathematics',\n                  icon: '📐',\n                  color: '#DC2626',\n                  topics: ['Calculus', 'Linear Algebra', 'Statistics', 'Discrete Math'],\n                  resources: 15\n                }, {\n                  subject: 'Computer Science',\n                  icon: '💻',\n                  color: '#7C3AED',\n                  topics: ['Data Structures', 'Algorithms', 'Database Systems', 'Software Engineering'],\n                  resources: 23\n                }, {\n                  subject: 'Physics',\n                  icon: '⚛️',\n                  color: '#059669',\n                  topics: ['Mechanics', 'Thermodynamics', 'Electromagnetism', 'Quantum Physics'],\n                  resources: 18\n                }, {\n                  subject: 'Chemistry',\n                  icon: '🧪',\n                  color: '#EA580C',\n                  topics: ['Organic Chemistry', 'Inorganic Chemistry', 'Physical Chemistry', 'Biochemistry'],\n                  resources: 12\n                }].map((subject, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    background: globalStyles.currentTheme.secondary,\n                    borderRadius: '12px',\n                    padding: '1.5rem',\n                    border: `1px solid ${globalStyles.currentTheme.border}`,\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.transform = 'translateY(-4px)';\n                    e.currentTarget.style.boxShadow = `0 8px 25px ${globalStyles.currentTheme.shadow}`;\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.transform = 'translateY(0)';\n                    e.currentTarget.style.boxShadow = 'none';\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '1rem',\n                      marginBottom: '1rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        width: '50px',\n                        height: '50px',\n                        borderRadius: '12px',\n                        background: subject.color,\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        fontSize: '1.5rem'\n                      },\n                      children: subject.icon\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2608,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        style: {\n                          margin: 0,\n                          fontSize: '1.2rem',\n                          fontWeight: 600,\n                          color: globalStyles.currentTheme.text\n                        },\n                        children: subject.subject\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2621,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        style: {\n                          margin: 0,\n                          fontSize: '0.9rem',\n                          color: globalStyles.currentTheme.textLight\n                        },\n                        children: [subject.resources, \" resources available\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2629,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2620,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2602,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      flexWrap: 'wrap',\n                      gap: '0.5rem',\n                      marginBottom: '1rem'\n                    },\n                    children: subject.topics.map((topic, topicIndex) => /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        padding: '0.25rem 0.5rem',\n                        background: subject.color + '15',\n                        color: subject.color,\n                        borderRadius: '6px',\n                        fontSize: '0.8rem',\n                        fontWeight: 500\n                      },\n                      children: topic\n                    }, topicIndex, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2646,\n                      columnNumber: 31\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2639,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    style: {\n                      width: '100%',\n                      padding: '0.75rem',\n                      background: subject.color,\n                      color: 'white',\n                      border: 'none',\n                      borderRadius: '8px',\n                      fontSize: '0.9rem',\n                      fontWeight: 500,\n                      cursor: 'pointer',\n                      transition: 'all 0.3s ease'\n                    },\n                    children: \"Browse Materials\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2659,\n                    columnNumber: 27\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2586,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2551,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2532,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: globalStyles.currentTheme.surface,\n                borderRadius: '16px',\n                padding: '2rem',\n                border: `1px solid ${globalStyles.currentTheme.border}`,\n                boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                style: {\n                  margin: '0 0 1.5rem 0',\n                  fontSize: '1.5rem',\n                  fontWeight: 600,\n                  color: globalStyles.currentTheme.text,\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                },\n                children: \"\\uD83D\\uDCDD Exam Preparation Resources\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2686,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'grid',\n                  gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                  gap: '1rem'\n                },\n                children: [{\n                  name: 'Previous Year Papers',\n                  icon: '📄',\n                  count: '50+ papers'\n                }, {\n                  name: 'Sample Questions',\n                  icon: '❓',\n                  count: '500+ questions'\n                }, {\n                  name: 'Study Guides',\n                  icon: '📖',\n                  count: '25+ guides'\n                }, {\n                  name: 'Reference Books',\n                  icon: '📚',\n                  count: '100+ books'\n                }, {\n                  name: 'Video Lectures',\n                  icon: '🎥',\n                  count: '200+ videos'\n                }, {\n                  name: 'Practice Tests',\n                  icon: '✅',\n                  count: '30+ tests'\n                }].map((resource, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '1rem',\n                    padding: '1rem',\n                    background: globalStyles.currentTheme.secondary,\n                    borderRadius: '12px',\n                    border: `1px solid ${globalStyles.currentTheme.border}`,\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.background = globalStyles.currentTheme.primary + '10';\n                    e.currentTarget.style.borderColor = globalStyles.currentTheme.primary;\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.background = globalStyles.currentTheme.secondary;\n                    e.currentTarget.style.borderColor = globalStyles.currentTheme.border;\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '40px',\n                      height: '40px',\n                      borderRadius: '8px',\n                      background: globalStyles.currentTheme.primary,\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      fontSize: '1.2rem'\n                    },\n                    children: resource.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2730,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontWeight: 600,\n                        color: globalStyles.currentTheme.text,\n                        marginBottom: '0.25rem'\n                      },\n                      children: resource.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2743,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.8rem',\n                        color: globalStyles.currentTheme.textLight\n                      },\n                      children: resource.count\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2750,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2742,\n                    columnNumber: 27\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2711,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2698,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2679,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2527,\n            columnNumber: 17\n          }, this), resourcesTab === 'tools' && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: globalStyles.currentTheme.surface,\n              borderRadius: '16px',\n              padding: '2rem',\n              border: `1px solid ${globalStyles.currentTheme.border}`,\n              boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              style: {\n                margin: '0 0 1.5rem 0',\n                fontSize: '1.5rem',\n                fontWeight: 600,\n                color: globalStyles.currentTheme.text,\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              },\n              children: \"\\uD83D\\uDEE0\\uFE0F Study Tools & Utilities\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2773,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',\n                gap: '1.5rem'\n              },\n              children: [{\n                name: 'Scientific Calculator',\n                icon: '🧮',\n                description: 'Advanced calculator for complex calculations',\n                color: '#DC2626'\n              }, {\n                name: 'Formula Reference',\n                icon: '📐',\n                description: 'Quick access to mathematical and scientific formulas',\n                color: '#7C3AED'\n              }, {\n                name: 'Unit Converter',\n                icon: '⚖️',\n                description: 'Convert between different units of measurement',\n                color: '#059669'\n              }, {\n                name: 'Study Timer',\n                icon: '⏰',\n                description: 'Pomodoro timer for effective study sessions',\n                color: '#EA580C'\n              }, {\n                name: 'Note Taking',\n                icon: '📝',\n                description: 'Digital notepad for quick notes and ideas',\n                color: '#0284C7'\n              }, {\n                name: 'Progress Tracker',\n                icon: '📊',\n                description: 'Track your study progress and goals',\n                color: '#7C2D12'\n              }].map((tool, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: globalStyles.currentTheme.secondary,\n                  borderRadius: '12px',\n                  padding: '1.5rem',\n                  border: `1px solid ${globalStyles.currentTheme.border}`,\n                  cursor: 'pointer',\n                  transition: 'all 0.3s ease'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.transform = 'translateY(-4px)';\n                  e.currentTarget.style.boxShadow = `0 8px 25px ${globalStyles.currentTheme.shadow}`;\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = 'none';\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '1rem',\n                    marginBottom: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '50px',\n                      height: '50px',\n                      borderRadius: '12px',\n                      background: tool.color,\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      fontSize: '1.5rem'\n                    },\n                    children: tool.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2850,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                      style: {\n                        margin: 0,\n                        fontSize: '1.1rem',\n                        fontWeight: 600,\n                        color: globalStyles.currentTheme.text\n                      },\n                      children: tool.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2863,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2862,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2844,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: '0 0 1rem 0',\n                    fontSize: '0.9rem',\n                    color: globalStyles.currentTheme.textLight,\n                    lineHeight: 1.4\n                  },\n                  children: tool.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2874,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    background: tool.color,\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: '8px',\n                    fontSize: '0.9rem',\n                    fontWeight: 500,\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease'\n                  },\n                  children: \"Launch Tool\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2883,\n                  columnNumber: 25\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2828,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2785,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2766,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2205,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2200,\n        columnNumber: 11\n      }, this), activeTab === \"academics\" && /*#__PURE__*/_jsxDEV(Exams, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2905,\n        columnNumber: 39\n      }, this), activeTab === \"faq\" && /*#__PURE__*/_jsxDEV(Faq, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2906,\n        columnNumber: 33\n      }, this), activeTab === \"admin\" && (user === null || user === void 0 ? void 0 : user.email) === ADMIN_EMAIL && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getStyle('card'),\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginTop: 0,\n              color: '#333'\n            },\n            children: \"Admin Panel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2910,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '16px',\n              marginBottom: '24px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                ...getStyle('buttonPrimary'),\n                background: adminTab === 'users' ? globalStyles.currentTheme.primary : 'transparent',\n                color: adminTab === 'users' ? 'white' : '#333',\n                border: '1px solid #ddd'\n              },\n              onClick: () => setAdminTab('users'),\n              children: \"Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2919,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                ...getStyle('buttonPrimary'),\n                background: adminTab === 'resources' ? globalStyles.currentTheme.primary : 'transparent',\n                color: adminTab === 'resources' ? 'white' : '#333',\n                border: '1px solid #ddd'\n              },\n              onClick: () => setAdminTab('resources'),\n              children: \"Resources\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2932,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2914,\n            columnNumber: 15\n          }, this), adminTab === 'users' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                marginBottom: '16px',\n                color: '#333'\n              },\n              children: \"All Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2949,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                backgroundColor: '#f5f5f5',\n                border: '1px solid #e0e0e0',\n                borderRadius: '8px',\n                padding: '16px'\n              },\n              children: allUsers.map((user, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '12px',\n                  borderBottom: '1px solid #eee',\n                  color: '#333'\n                },\n                children: user.email\n              }, idx, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2960,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2953,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2948,\n            columnNumber: 17\n          }, this), adminTab === 'resources' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                marginBottom: '16px',\n                color: '#333'\n              },\n              children: \"All Resources\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2974,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                opacity: 0.7,\n                color: '#666'\n              },\n              children: \"Resource management coming soon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2978,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2973,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2909,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2908,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 769,\n      columnNumber: 7\n    }, this), notification && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...getStyle('notification'),\n        backgroundColor: notification.type === 'error' ? '#f44336' : notification.type === 'success' ? '#4caf50' : '#2196f3',\n        color: 'white',\n        border: 'none'\n      },\n      children: notification.msg\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2991,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes fadeIn {\n          from { opacity: 0; transform: translateY(10px); }\n          to { opacity: 1; transform: translateY(0); }\n        }\n        @keyframes slideIn {\n          from { transform: translateX(100%); }\n          to { transform: translateX(0); }\n        }\n        @keyframes pulse {\n          0%, 100% { opacity: 1; }\n          50% { opacity: 0.5; }\n        }\n        @keyframes bounce {\n          0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }\n          40%, 43% { transform: translate3d(0,-8px,0); }\n          70% { transform: translate3d(0,-4px,0); }\n          90% { transform: translate3d(0,-2px,0); }\n        }\n        @keyframes glow {\n          0%, 100% { box-shadow: 0 0 5px rgba(99, 102, 241, 0.3); }\n          50% { box-shadow: 0 0 20px rgba(99, 102, 241, 0.6); }\n        }\n        @keyframes shimmer {\n          0% { background-position: -200px 0; }\n          100% { background-position: calc(200px + 100%) 0; }\n        }\n\n        /* Optimized Tab Navigation Styles */\n        .tab-navigation-container {\n          position: relative;\n          overflow: hidden;\n          contain: layout style paint;\n        }\n\n        .tab-button {\n          position: relative;\n          transition: color 0.15s ease;\n          will-change: color;\n          z-index: 20;\n        }\n\n        .tab-button:hover:not(.active) {\n          color: var(--primary-color);\n        }\n\n        .tab-button.active {\n          color: white;\n        }\n\n        .sliding-indicator {\n          transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n          will-change: transform;\n          contain: layout style paint;\n          z-index: 10;\n          pointer-events: none;\n        }\n\n        /* Simplified Responsive Styles */\n        @media (max-width: 640px) {\n          .tab-button {\n            font-size: 0.9rem;\n            padding: 0.75rem 0.5rem;\n          }\n        }\n\n        /* Focus styles for accessibility */\n        .tab-button:focus-visible {\n          outline: 2px solid var(--primary-color);\n          outline-offset: 2px;\n        }\n\n        /* Touch device optimizations */\n        @media (hover: none) and (pointer: coarse) {\n          .tab-button:hover {\n            color: inherit;\n          }\n\n          .tab-button:active {\n            opacity: 0.8;\n          }\n        }\n\n        /* Enhanced hover effects */\n        .company-card:hover {\n          animation: bounce 0.6s ease;\n        }\n\n        .favorite-btn:hover {\n          animation: pulse 0.5s ease;\n        }\n\n        /* Smooth transitions for all interactive elements */\n        button, input, select {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        button:hover {\n          transform: translateY(-1px);\n        }\n\n        * {\n          box-sizing: border-box;\n        }\n        body {\n          margin: 0;\n          font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 3003,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 598,\n    columnNumber: 5\n  }, this);\n};\n_s(EduAIChatBot, \"4N/NG6NnJtEDIEZuim389AjoX/o=\");\n_c = EduAIChatBot;\nexport default EduAIChatBot;\nvar _c;\n$RefreshReg$(_c, \"EduAIChatBot\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "getDoc", "doc", "Faq", "<PERSON><PERSON>", "Coding", "auth", "db", "axios", "sidebarItems", "onAuthStateChanged", "globalStyles", "FiMenu", "FiX", "FiChevronDown", "FiChevronRight", "FiFileText", "FiCode", "FiHelpCircle", "FiAward", "FiBook", "FiUser", "FiShield", "FiSearch", "FiUpload", "FiLogIn", "FiLogOut", "FiBriefcase", "FiBarChart2", "FiLayers", "FiCheckCircle", "FiExternalLink", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "FiRefreshCw", "createClient", "Bar", "Chart", "BarElement", "CategoryScale", "LinearScale", "<PERSON><PERSON><PERSON>", "Legend", "ReactMarkdown", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "register", "updatedSidebarItems", "map", "item", "iconMap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "tab", "title", "toLowerCase", "EduAIChatBot", "_s", "input", "setInput", "messages", "setMessages", "userId", "setUserId", "loading", "setLoading", "knowledge", "setKnowledge", "activeTab", "setActiveTab", "searchTerm", "setSearchTerm", "sidebarOpen", "setSidebarOpen", "expandedMenus", "setExpandedMenus", "user", "setUser", "resumeUploadLoading", "setResumeUploadLoading", "resumeUrl", "setResumeUrl", "resourceUploadLoading", "setResourceUploadLoading", "userResources", "ADMIN_EMAIL", "allUsers", "adminTab", "setAdminTab", "notification", "setNotification", "activityLog", "setActivityLog", "resourcesTab", "setResourcesTab", "windowWidth", "setW<PERSON>owWidth", "window", "innerWidth", "getTabPosition", "tabId", "tabIndex", "indexOf", "chatEndRef", "timeoutId", "handleResize", "clearTimeout", "setTimeout", "addEventListener", "passive", "removeEventListener", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "sortBy", "setSortBy", "favoriteCompanies", "setFavoriteCompanies", "recentCompanies", "setRecentCompanies", "showRevertButton", "setShowRevertButton", "API_KEY", "SUPABASE_URL", "SUPABASE_ANON_KEY", "supabase", "companyCategories", "companies", "quizButtons", "description", "link", "styles", "appContainer", "backgroundColor", "currentTheme", "background", "color", "text", "navbar", "navbarFixed", "borderBottom", "border", "sidebar", "sidebarFixed", "surface", "borderRight", "transform", "sidebarItem", "sidebarItemEdu", "transition", "primary", "sidebarItemActive", "sidebarItemActiveEdu", "mainContent", "mainContentEdu", "marginLeft", "minHeight", "card", "cardEdu", "boxShadow", "shadow", "buttonPrimary", "inputField", "borderColor", "outline", "chatBubbleUser", "chatBubbleBot", "secondary", "companyCard", "companyCardEdu", "quizCard", "quizCardEdu", "getStyle", "styleName", "hover", "baseStyle", "unsubscribe", "uid", "console", "log", "fetchUserProfile", "userRef", "userDoc", "exists", "userData", "data", "dp", "fetch", "then", "res", "catch", "err", "error", "getSession", "session", "listener", "onAuthStateChange", "_event", "subscription", "handleResumeUpload", "e", "file", "target", "files", "filePath", "id", "name", "storage", "from", "upload", "upsert", "urlData", "getPublicUrl", "publicUrl", "showNotification", "logActivity", "handleResourceUpload", "handleCompanyClick", "company", "prev", "filtered", "filter", "c", "slice", "location", "href", "formattedCompany", "replace", "toggleFavorite", "stopPropagation", "includes", "revertHeaderChanges", "eduNovaElement", "document", "querySelector", "subtitleElement", "style", "getFilteredCompanies", "categoryCompanies", "some", "catCompany", "sort", "a", "b", "aFav", "bFav", "localeCompare", "openQuizLink", "url", "open", "sendMessage", "trim", "userMessage", "role", "content", "_res$data$candidates", "_res$data$candidates$", "_res$data$candidates$2", "_res$data$candidates$3", "_res$data$candidates$4", "prompt", "post", "contents", "parts", "headers", "botReply", "candidates", "botMessage", "message", "handleLogout", "signOut", "msg", "type", "date", "Date", "toISOString", "toggleMenu", "menu", "current", "scrollIntoView", "behavior", "getLast7Days", "days", "i", "d", "setDate", "getDate", "push", "toLocaleDateString", "chartLabels", "chartData", "labels", "datasets", "label", "day", "startsWith", "length", "chartOptions", "responsive", "plugins", "legend", "position", "tooltip", "enabled", "scales", "y", "beginAtZero", "ticks", "stepSize", "children", "marginRight", "cursor", "padding", "borderRadius", "onClick", "onMouseEnter", "onMouseLeave", "size", "flex", "display", "alignItems", "src", "require", "alt", "height", "fontWeight", "fontSize", "opacity", "gap", "width", "justifyContent", "<PERSON><PERSON>ilter", "email", "toUpperCase", "index", "borderLeft", "subItems", "subItem", "subIndex", "paddingLeft", "top", "left", "right", "bottom", "zIndex", "gradientLight", "overflow", "backgroundImage", "backgroundSize", "max<PERSON><PERSON><PERSON>", "margin", "marginBottom", "gradient", "gridTemplateColumns", "lineHeight", "split", "textLight", "flexWrap", "textAlign", "month", "year", "value", "unit", "trend", "stat", "currentTarget", "textTransform", "letterSpacing", "subtitle", "desc", "action", "min<PERSON><PERSON><PERSON>", "Math", "random", "floor", "flexDirection", "progress", "status", "goal", "maxHeight", "overflowY", "flexShrink", "toLocaleString", "accept", "onChange", "disabled", "rel", "textDecoration", "marginTop", "fontStyle", "borderTop", "paddingTop", "idx", "animation", "animationDelay", "ref", "onSubmit", "preventDefault", "placeholder", "paddingBottom", "Object", "keys", "category", "fill", "primaryDark", "char<PERSON>t", "toFixed", "entries", "quiz", "className", "pointerEvents", "<PERSON><PERSON><PERSON><PERSON>", "onFocus", "outlineOffset", "onBlur", "whiteSpace", "userSelect", "WebkitTapHighlightColor", "subject", "topics", "resources", "topic", "topicIndex", "count", "resource", "tool", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(5)/src/EduAIChatBot.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\nimport { getDoc, doc } from 'firebase/firestore';\nimport Faq from './Faq';\nimport Exams from \"./Exams\";\nimport Coding from \"./Coding\";\nimport { auth, db } from './firebaseConfig';\nimport axios from \"axios\";\nimport { sidebarItems } from './sidebarItems';\nimport { onAuthStateChanged } from 'firebase/auth';\nimport globalStyles from './styles.js';\nimport {\n  FiMenu, FiX, FiChevronDown, FiChevronRight, FiFileText,\n  FiCode, FiHelpCircle, FiAward, FiBook, FiUser, FiShield,\n  FiSearch, FiUpload, FiLogIn, FiLogOut,\n  FiBriefcase, FiBarChart2, FiLayers, FiCheckCircle, FiExternalLink,\n  FiHeart, FiClock, FiRefreshCw\n} from \"react-icons/fi\";\nimport { createClient } from '@supabase/supabase-js';\nimport { Bar } from 'react-chartjs-2';\nimport { Chart, BarElement, CategoryScale, LinearScale, Tooltip, Legend } from 'chart.js';\nimport ReactMarkdown from 'react-markdown';\n\nChart.register(BarElement, CategoryScale, LinearScale, Tooltip, Legend);\n\n// Enhanced sidebar items with icons\nconst updatedSidebarItems = sidebarItems.map(item => {\n  const iconMap = {\n    \"resume\": <FiFileText />,\n    \"dsa\": <FiCode />,\n    \"coding\": <FiLayers />,\n    \"resources\": <FiBriefcase />,\n    \"quizzes\": <FiCheckCircle />,\n    \"aptitude\": <FiBarChart2 />,\n    \"academics\": <FiBook />,\n    \"faq\": <FiHelpCircle />,\n    \"admin\": <FiShield />\n  };\n\n  return {\n    ...item,\n    icon: iconMap[item.tab] || iconMap[item.title.toLowerCase()] || <FiAward />\n  };\n});\n\nconst EduAIChatBot = () => {\n  // State declarations\n  const [input, setInput] = useState(\"\");\n  const [messages, setMessages] = useState([]);\n  const [userId, setUserId] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [knowledge, setKnowledge] = useState(\"\");\n  const [activeTab, setActiveTab] = useState(\"dashboard\");\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [expandedMenus, setExpandedMenus] = useState({});\n  const [user, setUser] = useState(null);\n\n  const [resumeUploadLoading, setResumeUploadLoading] = useState(false);\n  const [resumeUrl, setResumeUrl] = useState(null);\n  const [resourceUploadLoading, setResourceUploadLoading] = useState(false);\n  const [userResources] = useState([]);\n  const ADMIN_EMAIL = '<EMAIL>';\n  const [allUsers] = useState([]);\n  const [adminTab, setAdminTab] = useState('users');\n  const [notification, setNotification] = useState(null);\n  const [activityLog, setActivityLog] = useState([]);\n  const [resourcesTab, setResourcesTab] = useState('general'); // New state for resources tab\n  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1024); // Responsive state\n\n  // Optimized tab positions for smooth sliding\n  const getTabPosition = (tabId) => {\n    const tabIndex = ['general', 'academics', 'tools'].indexOf(tabId);\n    return `${tabIndex * 33.333}%`;\n  };\n  const chatEndRef = useRef(null);\n\n  // Optimized window resize handler with throttling\n  useEffect(() => {\n    let timeoutId = null;\n    const handleResize = () => {\n      if (timeoutId) clearTimeout(timeoutId);\n      timeoutId = setTimeout(() => {\n        setWindowWidth(window.innerWidth);\n      }, 100); // Throttle to 100ms\n    };\n\n    window.addEventListener('resize', handleResize, { passive: true });\n    return () => {\n      window.removeEventListener('resize', handleResize);\n      if (timeoutId) clearTimeout(timeoutId);\n    };\n  }, []);\n\n  // Enhanced DSA section states\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [sortBy, setSortBy] = useState('name');\n  const [favoriteCompanies, setFavoriteCompanies] = useState([]);\n  const [recentCompanies, setRecentCompanies] = useState([]);\n  const [showRevertButton, setShowRevertButton] = useState(true);\n\n  // API configurations\n  const API_KEY = \"AIzaSyC6kHWto78QdqHz7Uu9RzEXb443ZO7tG5M\";\n  const SUPABASE_URL = 'https://gziaptswfepiveyylven.supabase.co';\n  const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imd6aWFwdHN3ZmVwaXZleXlsdmVuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU2NzczNTYsImV4cCI6MjA2MTI1MzM1Nn0.wmqXZGffrox8E_PuCwbzh4xJEffsvFmZCVcF6WFAX6Q';\n  const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);\n\n  // Company categories for enhanced DSA section\n  const companyCategories = {\n    'FAANG': ['Facebook', 'Apple', 'Amazon', 'Netflix', 'Google'],\n    'Big Tech': ['Microsoft', 'Adobe', 'Salesforce', 'Oracle', 'IBM', 'Intel', 'Nvidia'],\n    'Startups': ['Stripe', 'Airbnb', 'Uber', 'Lyft', 'DoorDash', 'Instacart', 'Coinbase'],\n    'Finance': ['Goldman Sachs', 'JPMorgan', 'Morgan Stanley', 'BlackRock', 'Citadel', 'Two Sigma'],\n    'Consulting': ['McKinsey', 'BCG', 'Bain', 'Deloitte', 'Accenture', 'PwC'],\n    'E-commerce': ['Amazon', 'eBay', 'Shopify', 'Etsy', 'Wayfair', 'Booking.com'],\n    'Gaming': ['Blizzard', 'Epic Games', 'Riot Games', 'Unity', 'Roblox'],\n    'Indian': ['TCS', 'Infosys', 'Wipro', 'HCL', 'Flipkart', 'Paytm', 'Zomato', 'Swiggy']\n  };\n\n  // Complete list of companies\n  const companies = [\n     \"Accenture\", \"Accolite\", \"Adobe\", \"Affirm\", \"Agoda\", \"Airbnb\", \"Airtel\",\n    \"Akamar\", \"Akuna Capital\", \"Alibaba\", \"Altimetrik\", \"Amazon\", \"AMD\",\n    \"Amdocs\", \"American Express\", \"Anduril\", \"Apple\", \"Arista Networks\",\n    \"Arcesium\", \"Atlassian\", \"Attentive\", \"athenahealth\", \"Autodesk\",\n    \"Avito\", \"Baidu\", \"Barclays\", \"BitGo\", \"BlackRock\", \"Blizzard\",\n    \"Block\", \"Bloomberg\", \"BNY Mellon\", \"Boft\", \"Booking.com\", \"Bos\",\n    \"BP\", \"ByteDance\", \"Cadence\", \"Capgemini\", \"Capital One\", \"CARS24\",\n    \"carwale\", \"Cashfree\", \"Chewy\", \"Cisco\", \"Citadel\", \"Citrix\",\n    \"Cloudera\", \"Cloudflare\", \"Cognizant\", \"Coinbase\", \"Commvault\",\n    \"Confluent\", \"Coupang\", \"Coursera\", \"CrowdStrike\", \"Cruise\",\n    \"Curefit\", \"Databricks\", \"Datadog\", \"DE Shaw\", \"Deloitte\", \"Dell\",\n    \"Deliveroo\", \"Derantior\", \"Deutsche Bank\", \"Devflev\", \"Directi\",\n    \"Disney\", \"Docusign\", \"DoorDash\", \"Dream11\", \"Dropbox\", \"DRW\",\n    \"Dunzo\", \"eBay\", \"EPAM Systems\", \"Epic Systems\", \"Expedia\",\n    \"FactSet\", \"Flexport\", \"Flipkart\", \"Freshworks\", \"GE Healthcare\",\n    \"Geico\", \"Goldman Sachs\", \"Google\", \"Grab\", \"Grammarly\", \"Graviton\",\n    \"Groww\", \"GSN Games\", \"Hashedin\", \"HCL\", \"HPE\", \"Hubspot\",\n    \"Hudson River Trading\", \"Huawei\", \"IBM\", \"IMC\", \"Indeed\", \"Infosys\",\n    \"InMobi\", \"Intel\", \"Intuit\", \"JPMorgan\", \"Jane Street\",\n    \"Josh Technology\", \"Jump Trading\", \"Juspay\", \"Karat\", \"KLA\",\n    \"LinkedIn\", \"LiveRamp\", \"Lowe's\", \"Lucid\", \"Lyft\", \"MakeMyTrip\",\n    \"Mastercard\", \"MathWorks\", \"Media.net\", \"Meesho\", \"Mercari\", \"Meta\",\n    \"Microsoft\", \"Millennium\", \"Mitsogo\", \"Moloco\", \"MongoDB\",\n    \"Morgan Stanley\", \"Moveworks\", \"Myntra\", \"Nagarro\", \"NetApp\",\n    \"Netflix\", \"Nextdoor\", \"Nielsen\", \"Nike\", \"Niantic\", \"Nordstrom\",\n    \"Nutanix\", \"Nvidia\", \"Okta\", \"OKX\", \"OpenAI\", \"OpenText\", \"Oracle\",\n    \"Otter.ai\", \"Oyo\", \"Ozon\", \"Palantir Technologies\", \"Palo Alto Networks\",\n    \"PayPal\", \"Paytm\", \"Persistent Systems\", \"PhonePe\", \"Pinterest\",\n    \"Pocket Gems\", \"Point72\", \"Pure Storage\", \"Qualcomm\", \"Quora\",\n    \"Rakuten\", \"Razorpay\", \"RBC\", \"Reddit\", \"Revolut\", \"Robinhood\",\n    \"Roblox\", \"Rubrik\", \"Salesforce\", \"Samsung\", \"SAP\", \"ServiceNow\",\n    \"Shopify\", \"Siemens\", \"Sigmoid\", \"SIG\", \"Snowflake\", \"Snap\", \"Sofi\",\n    \"Splunk\", \"Spotify\", \"Sprinklr\", \"Squarepoint Capital\", \"Stripe\",\n    \"Swiggy\", \"TCS\", \"Tekion\", \"Tencent\", \"Tesla\", \"ThoughtSpot\",\n    \"ThoughtWorks\", \"TikTok\", \"Tinkoff\", \"Trilogy\", \"Turing\", \"Turo\",\n    \"Twilio\", \"Twitch\", \"Two Sigma\", \"Uber\", \"UiPath\", \"UKG\",\n    \"Veeva Systems\", \"Verily\", \"Verkada\", \"Virtu Financial\", \"Visa\",\n    \"VK\", \"VMware\", \"Walmart Labs\", \"WarnerMedia\", \"Wayfair\",\n    \"Wells Fargo\", \"Wipro\", \"Wix\", \"Workday\", \"X\", \"Yahoo\", \"Yandex\",\n    \"Yelp\", \"Zalando\", \"Zenefits\", \"Zepto\", \"Zeta\", \"Zillow\", \"Zoho\",\n    \"Zomato\", \"ZScaler\", \"Zopsmart\"\n  ];\n\n  // Quiz buttons data\n  const quizButtons = [\n    {\n      title: \"OP and CN Quiz\",\n      description: \"Test your knowledge of Operating System and Computer Networks\",\n      link: \"https://opcn.netlify.app\",\n    },\n    {\n      title: \"OOPs and DBMS Quiz\",\n      description: \"Challenge yourself with oops and dbms\",\n      link: \"https://oopsanddbms.netlify.app/\",\n    },\n    {\n      title: \"System Design Quiz\",\n      description: \"Test your system design knowledge\",\n      link: \"https://system-design041.netlify.app\",\n    },\n    {\n      title: \"Quantitative Aptitude and Reasoning Quiz\",\n      description: \"Practice common quant and reasoning questions\",\n      link: \"https://quantandreasoning.netlify.app\",\n    },\n    {\n      title: \"Cloud & DevOps Quiz\",\n      description: \"Test your knowledge of Cloud and DevOps concepts\",\n      link: \"https://cloud-devops.netlify.app\",\n    },\n    {\n      title: \"DSA Quiz\",\n      description: \"Data Structures and Algorithms quiz\",\n      link: \"https://dsa041.netlify.app\",\n    },\n    {\n      title: \"Operating System & Computer Networks Quiz\",\n      description: \"Quiz on OS and Computer Networks\",\n      link: \"https://opcn.netlify.app\",\n    },\n     {\n      title: \"Web Development Quiz\",\n      description: \"Quiz on Web Development topics\",\n      link: \"https://web-dev041.netlify.app\",\n\n    },\n  ];\n\n  // Use centralized styles\n  const styles = {\n    ...globalStyles,\n    appContainer: {\n      ...globalStyles.appContainer,\n      backgroundColor: globalStyles.currentTheme.background,\n      color: globalStyles.currentTheme.text,\n    },\n    navbar: {\n      ...globalStyles.navbarFixed,\n      borderBottom: `1px solid ${globalStyles.currentTheme.border}`\n    },\n    sidebar: {\n      ...globalStyles.sidebarFixed,\n      backgroundColor: globalStyles.currentTheme.surface,\n      borderRight: `1px solid ${globalStyles.currentTheme.border}`,\n      transform: sidebarOpen ? 'translateX(0)' : 'translateX(-100%)',\n    },\n    sidebarItem: {\n      ...globalStyles.sidebarItemEdu,\n      color: globalStyles.currentTheme.text,\n      background: globalStyles.currentTheme.surface,\n      border: `1px solid ${globalStyles.currentTheme.border}`,\n      transition: 'all 0.3s ease',\n      '&:hover': {\n        background: globalStyles.currentTheme.primary,\n        color: 'white'\n      }\n    },\n    sidebarItemActive: {\n      ...globalStyles.sidebarItemActiveEdu,\n      color: 'white',\n      background: globalStyles.currentTheme.primary,\n      border: `1px solid ${globalStyles.currentTheme.primary}`,\n    },\n    mainContent: {\n      ...globalStyles.mainContentEdu,\n      marginLeft: sidebarOpen ? '280px' : '0',\n      backgroundColor: globalStyles.currentTheme.background,\n      color: globalStyles.currentTheme.text,\n      minHeight: '100vh'\n    },\n    card: {\n      ...globalStyles.cardEdu,\n      backgroundColor: globalStyles.currentTheme.surface,\n      color: globalStyles.currentTheme.text,\n      border: `1px solid ${globalStyles.currentTheme.border}`,\n      boxShadow: `0 4px 6px ${globalStyles.currentTheme.shadow}`\n    },\n    buttonPrimary: {\n      ...globalStyles.buttonPrimary,\n    },\n    inputField: {\n      ...globalStyles.inputField,\n      backgroundColor: '#fff',\n      color: '#333',\n      border: '1px solid #ddd',\n      '&:focus': {\n        borderColor: globalStyles.currentTheme.primary,\n        outline: 'none',\n        boxShadow: `0 0 0 2px ${globalStyles.currentTheme.primary}20`\n      }\n    },\n    chatBubbleUser: {\n      ...globalStyles.chatBubbleUser,\n      backgroundColor: globalStyles.currentTheme.primary,\n      color: 'white'\n    },\n    chatBubbleBot: {\n      ...globalStyles.chatBubbleBot,\n      backgroundColor: globalStyles.currentTheme.secondary,\n      color: globalStyles.currentTheme.text,\n      border: '1px solid transparent'\n    },\n    companyCard: {\n      ...globalStyles.companyCardEdu,\n    },\n    quizCard: {\n      ...globalStyles.quizCardEdu,\n      backgroundColor: globalStyles.currentTheme.surface,\n    },\n    notification: {\n      ...globalStyles.notification,\n    }\n  };\n\n  // Helper function to apply styles with hover states\n  const getStyle = (styleName, hover = false) => {\n    const baseStyle = styles[styleName];\n    if (typeof baseStyle === 'function') return baseStyle();\n    if (hover && baseStyle['&:hover']) {\n      return { ...baseStyle, ...baseStyle['&:hover'] };\n    }\n    return baseStyle;\n  };\n\n  // Fetch user profile\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, (user) => {\n      if (user) {\n        setUserId(user.uid);\n      } else {\n        console.log('User is not authenticated');\n        setLoading(false);\n      }\n    });\n    return () => unsubscribe();\n  }, []);\n\n  useEffect(() => {\n    if (userId) {\n      const fetchUserProfile = async () => {\n        const userRef = doc(db, \"users\", userId);\n        const userDoc = await getDoc(userRef);\n\n        if (userDoc.exists()) {\n          const userData = userDoc.data();\n          // Profile pic functionality can be added later\n          console.log(\"User data loaded:\", userData.dp);\n        } else {\n          console.log(\"No such user!\");\n        }\n        setLoading(false);\n      };\n      fetchUserProfile();\n    }\n  }, [userId]);\n\n  // Fetch training data\n  useEffect(() => {\n    fetch(\"/training-data.txt\")\n      .then((res) => res.text())\n      .then((data) => setKnowledge(data))\n      .catch((err) => console.error(\"Failed to load training data:\", err));\n  }, []);\n\n  // Supabase auth state\n  useEffect(() => {\n    supabase.auth.getSession().then(({ data: { session } }) => {\n      setUser(session?.user || null);\n    });\n    const { data: listener } = supabase.auth.onAuthStateChange((_event, session) => {\n      setUser(session?.user || null);\n    });\n    return () => {\n      listener?.subscription.unsubscribe();\n    };\n  }, [supabase.auth]);\n\n  // Handle resume upload\n  const handleResumeUpload = async (e) => {\n    const file = e.target.files[0];\n    if (!file || !user) return;\n    setResumeUploadLoading(true);\n    const filePath = `${user.id}/${file.name}`;\n    const { error } = await supabase.storage.from('resumes').upload(filePath, file, { upsert: true });\n    if (!error) {\n      const { data: urlData } = supabase.storage.from('resumes').getPublicUrl(filePath);\n      setResumeUrl(urlData.publicUrl);\n      showNotification('Resume uploaded successfully!', 'success');\n      logActivity('Uploaded a resume');\n    } else {\n      showNotification('Resume upload failed.', 'error');\n    }\n    setResumeUploadLoading(false);\n  };\n\n  // Handle resource upload\n  const handleResourceUpload = async (e) => {\n    const file = e.target.files[0];\n    if (!file || !user) return;\n    setResourceUploadLoading(true);\n    const filePath = `${user.id}/${file.name}`;\n    const { error } = await supabase.storage.from('resources').upload(filePath, file, { upsert: true });\n    if (!error) {\n      showNotification('Resource uploaded!', 'success');\n      logActivity(`Uploaded resource: ${file.name}`);\n    } else {\n      showNotification('Resource upload failed.', 'error');\n    }\n    setResourceUploadLoading(false);\n  };\n\n  // Enhanced company click handler\n  const handleCompanyClick = (company) => {\n    // Add to recent companies\n    setRecentCompanies(prev => {\n      const filtered = prev.filter(c => c !== company);\n      return [company, ...filtered].slice(0, 5); // Keep only 5 recent\n    });\n\n    logActivity(`Viewed ${company} DSA questions`);\n\n    if (company.toLowerCase() === 'microsoft') {\n      window.location.href = '/company-dsa/Microsoft_questions.html';\n      return;\n    }\n    const formattedCompany = company.replace(/\\s+/g, '');\n    window.location.href = `/company-dsa/${formattedCompany}.html`;\n  };\n\n  // Toggle favorite company\n  const toggleFavorite = (company, e) => {\n    e.stopPropagation(); // Prevent company click\n    setFavoriteCompanies(prev => {\n      if (prev.includes(company)) {\n        return prev.filter(c => c !== company);\n      } else {\n        return [...prev, company];\n      }\n    });\n  };\n\n  // Revert header color changes\n  const revertHeaderChanges = () => {\n    setShowRevertButton(false);\n    showNotification('Header text color reverted to theme default!', 'success');\n\n    // Actually revert the header text color by updating the DOM\n    const eduNovaElement = document.querySelector('[data-header-title]');\n    const subtitleElement = document.querySelector('[data-header-subtitle]');\n\n    if (eduNovaElement) {\n      eduNovaElement.style.color = '#333';\n    }\n    if (subtitleElement) {\n      subtitleElement.style.color = '#333';\n    }\n  };\n\n  // Get filtered companies based on category and search\n  const getFilteredCompanies = () => {\n    let filtered = companies;\n\n    // Filter by category\n    if (selectedCategory !== 'all') {\n      const categoryCompanies = companyCategories[selectedCategory] || [];\n      filtered = filtered.filter(company =>\n        categoryCompanies.some(catCompany =>\n          company.toLowerCase().includes(catCompany.toLowerCase()) ||\n          catCompany.toLowerCase().includes(company.toLowerCase())\n        )\n      );\n    }\n\n    // Filter by search term\n    filtered = filtered.filter(company =>\n      company.toLowerCase().includes(searchTerm.toLowerCase())\n    );\n\n    // Sort companies\n    if (sortBy === 'name') {\n      filtered.sort();\n    } else if (sortBy === 'favorites') {\n      filtered.sort((a, b) => {\n        const aFav = favoriteCompanies.includes(a);\n        const bFav = favoriteCompanies.includes(b);\n        if (aFav && !bFav) return -1;\n        if (!aFav && bFav) return 1;\n        return a.localeCompare(b);\n      });\n    }\n\n    return filtered;\n  };\n\n  // Open quiz link\n  const openQuizLink = (url) => {\n    window.open(url, \"_blank\");\n  };\n\n  // Send message to chatbot\n  const sendMessage = async () => {\n    if (!input.trim()) return;\n\n    const userMessage = { role: \"user\", content: input };\n    setMessages((prev) => [...prev, userMessage]);\n    setInput(\"\");\n    setLoading(true);\n\n    try {\n      const prompt = `You are a resume assistant. Help users improve their resumes, provide suggestions, and answer career-related questions. Use the following knowledge if it helps. If it's not relevant, use your own intelligence.\\n\\nKnowledge:\\n${knowledge}\\n\\nQuestion: ${input}`;\n\n      const res = await axios.post(\n        `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`,\n        {\n          contents: [\n            {\n              parts: [{ text: prompt }],\n            },\n          ],\n        },\n        {\n          headers: {\n            \"Content-Type\": \"application/json\",\n          },\n        }\n      );\n\n      const botReply =\n        res.data.candidates?.[0]?.content?.parts?.[0]?.text ||\n        \"⚠ No response received.\";\n      const botMessage = { role: \"bot\", content: botReply };\n      setMessages((prev) => [...prev, botMessage]);\n    } catch (error) {\n      console.error(\"Gemini API Error:\", error);\n      setMessages((prev) => [\n        ...prev,\n        { role: \"bot\", content: \"❌ Error: \" + error.message },\n      ]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Authentication functionality can be added later if needed\n\n  // Handle logout\n  const handleLogout = async () => {\n    await supabase.auth.signOut();\n  };\n\n  // Show notification\n  const showNotification = (msg, type = 'info') => {\n    setNotification({ msg, type });\n    setTimeout(() => setNotification(null), 3000);\n  };\n\n  // Log activity\n  const logActivity = (msg) => {\n    setActivityLog(log => [\n      { type: 'activity', date: new Date().toISOString(), msg },\n      ...log.slice(0, 19)\n    ]);\n  };\n\n  // Toggle menu\n  const toggleMenu = (menu) => {\n    setExpandedMenus(prev => ({\n      ...prev,\n      [menu]: !prev[menu]\n    }));\n  };\n\n  // Auto-scroll chat\n  useEffect(() => {\n    if (chatEndRef.current) chatEndRef.current.scrollIntoView({ behavior: 'smooth' });\n  }, [messages, loading]);\n\n  // Chart data\n  const getLast7Days = () => {\n    const days = [];\n    for (let i = 6; i >= 0; i--) {\n      const d = new Date();\n      d.setDate(d.getDate() - i);\n      days.push(d.toLocaleDateString());\n    }\n    return days;\n  };\n\n  const chartLabels = getLast7Days();\n  const chartData = {\n    labels: chartLabels,\n    datasets: [\n      {\n        label: 'Resource Uploads',\n        data: chartLabels.map(day => activityLog.filter(a => a.type === 'activity' && a.msg.startsWith('Uploaded resource') && new Date(a.date).toLocaleDateString() === day).length),\n        backgroundColor: '#3182ce',\n      },\n      {\n        label: 'Coding Practice',\n        data: chartLabels.map(day => activityLog.filter(a => a.type === 'activity' && a.msg === 'Clicked coding practice link' && new Date(a.date).toLocaleDateString() === day).length),\n        backgroundColor: '#805ad5',\n      },\n    ],\n  };\n\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: { position: 'top' },\n      tooltip: { enabled: true },\n    },\n    scales: {\n      y: { beginAtZero: true, ticks: { stepSize: 1 } },\n    },\n  };\n\n  return (\n    <div style={getStyle('appContainer')}>\n      {/* Top Navigation Bar */}\n      <nav style={getStyle('navbar')}>\n        <button\n          style={{\n            background: 'none',\n            border: 'none',\n            color: 'white', // Always white since navbar has gradient background\n            marginRight: '20px',\n            cursor: 'pointer',\n            padding: '8px',\n            borderRadius: '4px',\n            transition: 'all 0.2s ease'\n          }}\n          onClick={() => setSidebarOpen(!sidebarOpen)}\n          onMouseEnter={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.1)'}\n          onMouseLeave={(e) => e.target.style.background = 'none'}\n        >\n          {sidebarOpen ? <FiX size={24} /> : <FiMenu size={24} />}\n        </button>\n\n        <div style={{ flex: 1, display: 'flex', alignItems: 'center' }}>\n          <img\n            src={require('./eduai-logo.jpg')}\n            alt=\"EduAI Logo\"\n            style={{ height: '36px', marginRight: '12px' }}\n          />\n          <div>\n            <div\n              data-header-title\n              style={{ fontWeight: 600, fontSize: '18px', color: 'white' }}\n            >\n              EDU NOVA\n            </div>\n            <div\n              data-header-subtitle\n              style={{ fontSize: '12px', opacity: 0.8, color: 'white' }}\n            >\n              AI POWERED LEARNING SYSTEM\n            </div>\n          </div>\n        </div>\n\n        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>\n          {user ? (\n            <>\n              <div style={{\n                width: '40px',\n                height: '40px',\n                borderRadius: '50%',\n                background: 'rgba(255, 255, 255, 0.2)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontWeight: 600,\n                cursor: 'pointer',\n                color: 'white',\n                backdropFilter: 'blur(10px)'\n              }}>\n                {user.email === ADMIN_EMAIL ? <FiShield size={20} color=\"#4caf50\" /> : user.email[0].toUpperCase()}\n              </div>\n              <button\n                style={{\n                  ...getStyle('buttonPrimary'),\n                  background: 'rgba(255, 255, 255, 0.2)',\n                  color: 'white',\n                  border: '1px solid rgba(255, 255, 255, 0.3)',\n                  backdropFilter: 'blur(10px)'\n                }}\n                onClick={handleLogout}\n              >\n                <FiLogOut /> Logout\n              </button>\n            </>\n          ) : (\n            <button\n              style={{\n                ...getStyle('buttonPrimary'),\n                background: 'rgba(255, 255, 255, 0.2)',\n                color: 'white',\n                border: '1px solid rgba(255, 255, 255, 0.3)',\n                backdropFilter: 'blur(10px)'\n              }}\n              onClick={() => { console.log('Login functionality to be implemented'); }}\n            >\n              <FiLogIn /> Login\n            </button>\n          )}\n        </div>\n      </nav>\n\n      {/* Sidebar */}\n      <aside style={getStyle('sidebar')}>\n        <div style={{ padding: '16px' }}>\n          {updatedSidebarItems.map((item, index) => (\n            <div key={index}>\n              <div\n                style={{\n                  ...getStyle('sidebarItem'),\n                  ...(activeTab === item.tab ? getStyle('sidebarItemActive') : {}),\n                  cursor: 'pointer',\n                  transition: 'all 0.3s ease'\n                }}\n                onClick={() => {\n                  setActiveTab(item.tab);\n                  setSidebarOpen(false);\n                }}\n                onMouseEnter={(e) => {\n                  if (activeTab !== item.tab) {\n                    e.target.style.background = 'rgba(0, 0, 0, 0.05)';\n                    e.target.style.transform = 'translateX(4px)';\n                    e.target.style.borderLeft = `3px solid ${globalStyles.currentTheme.primary}`;\n                  }\n                }}\n                onMouseLeave={(e) => {\n                  if (activeTab !== item.tab) {\n                    e.target.style.background = globalStyles.currentTheme.surface;\n                    e.target.style.transform = 'translateX(0)';\n                    e.target.style.borderLeft = '3px solid transparent';\n                  }\n                }}\n              >\n                <div style={{ marginRight: '12px' }}>{item.icon}</div>\n                <span style={{ flex: 1 }}>{item.title}</span>\n                {item.subItems.length > 0 && (\n                  <div onClick={(e) => {\n                    e.stopPropagation();\n                    toggleMenu(item.title);\n                  }}>\n                    {expandedMenus[item.title] ? <FiChevronDown /> : <FiChevronRight />}\n                  </div>\n                )}\n              </div>\n\n              {item.subItems.length > 0 && expandedMenus[item.title] && (\n                <div style={{ marginLeft: '32px' }}>\n                  {item.subItems.map((subItem, subIndex) => (\n                    <div\n                      key={subIndex}\n                      style={{\n                        ...getStyle('sidebarItem'),\n                        padding: '8px 16px 8px 32px',\n                        fontSize: '14px',\n                        opacity: 0.9\n                      }}\n                      onClick={() => {\n                        setActiveTab(item.tab);\n                        setSidebarOpen(false);\n                      }}\n                      onMouseEnter={(e) => {\n                        e.target.style.background = 'rgba(0, 0, 0, 0.03)';\n                        e.target.style.paddingLeft = '36px';\n                        e.target.style.opacity = '1';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.target.style.background = globalStyles.currentTheme.surface;\n                        e.target.style.paddingLeft = '32px';\n                        e.target.style.opacity = '0.9';\n                      }}\n                    >\n                      {subItem.title}\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n      </aside>\n\n      {/* Main Content */}\n      <main style={getStyle('mainContent')}>\n        {/* Overlay when sidebar is open on mobile */}\n        {sidebarOpen && window.innerWidth < 768 && (\n          <div\n            style={{\n              position: 'fixed',\n              top: '64px',\n              left: 0,\n              right: 0,\n              bottom: 0,\n              backgroundColor: 'rgba(0,0,0,0.5)',\n              zIndex: 800\n            }}\n            onClick={() => setSidebarOpen(false)}\n          />\n        )}\n\n        {/* Dashboard Content */}\n        {activeTab === \"dashboard\" && (\n          <div style={{\n            padding: '2rem 1rem',\n            background: globalStyles.currentTheme.gradientLight,\n            minHeight: '100vh',\n            position: 'relative',\n            overflow: 'hidden'\n          }}>\n            {/* Professional Background Pattern */}\n            <div style={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              backgroundImage: `\n                radial-gradient(circle at 20% 30%, ${globalStyles.currentTheme.primary}08 0%, transparent 50%),\n                radial-gradient(circle at 80% 70%, ${globalStyles.currentTheme.primary}05 0%, transparent 50%),\n                radial-gradient(circle at 40% 80%, ${globalStyles.currentTheme.primary}03 0%, transparent 50%)\n              `,\n              zIndex: 0\n            }} />\n\n            {/* Subtle Grid Pattern */}\n            <div style={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              backgroundImage: `\n                linear-gradient(${globalStyles.currentTheme.border} 1px, transparent 1px),\n                linear-gradient(90deg, ${globalStyles.currentTheme.border} 1px, transparent 1px)\n              `,\n              backgroundSize: '50px 50px',\n              opacity: 0.3,\n              zIndex: 0\n            }} />\n\n            <div style={{ maxWidth: '1400px', margin: '0 auto', position: 'relative', zIndex: 1 }}>\n\n              {/* Professional Hero Section */}\n              <div style={{\n                background: globalStyles.currentTheme.surface,\n                backdropFilter: 'blur(20px)',\n                borderRadius: '20px',\n                padding: '3rem',\n                marginBottom: '2rem',\n                position: 'relative',\n                overflow: 'hidden',\n                border: `1px solid ${globalStyles.currentTheme.border}`,\n                boxShadow: `0 20px 40px ${globalStyles.currentTheme.shadow}`\n              }}>\n                {/* Professional accent line */}\n                <div style={{\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  right: 0,\n                  height: '4px',\n                  background: globalStyles.currentTheme.gradient\n                }} />\n\n                <div style={{\n                  display: 'grid',\n                  gridTemplateColumns: 'auto 1fr auto',\n                  gap: '2rem',\n                  alignItems: 'center',\n                  position: 'relative',\n                  zIndex: 1\n                }}>\n                  {/* Professional Avatar */}\n                  <div style={{\n                    width: '100px',\n                    height: '100px',\n                    borderRadius: '20px',\n                    background: globalStyles.currentTheme.gradient,\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    fontSize: '2.5rem',\n                    fontWeight: 'bold',\n                    color: 'white',\n                    boxShadow: `0 10px 30px ${globalStyles.currentTheme.shadow}`,\n                    border: `3px solid ${globalStyles.currentTheme.surface}`\n                  }}>\n                    {user ? user.email[0].toUpperCase() : '👤'}\n                  </div>\n\n                  {/* Welcome Content */}\n                  <div>\n                    <h1 style={{\n                      margin: 0,\n                      fontSize: '2.5rem',\n                      fontWeight: 700,\n                      marginBottom: '0.5rem',\n                      color: globalStyles.currentTheme.text,\n                      lineHeight: 1.2\n                    }}>\n                      Welcome back, {user ? user.email.split('@')[0] : 'User'}! 👋\n                    </h1>\n                    <p style={{\n                      margin: 0,\n                      fontSize: '1.1rem',\n                      color: globalStyles.currentTheme.textLight,\n                      fontWeight: 400,\n                      marginBottom: '1.5rem'\n                    }}>\n                      Ready to continue your learning journey? Let's achieve your goals together.\n                    </p>\n\n                    {/* Professional Status Badges */}\n                    <div style={{\n                      display: 'flex',\n                      gap: '1rem',\n                      flexWrap: 'wrap'\n                    }}>\n                      <div style={{\n                        background: globalStyles.currentTheme.primary + '15',\n                        color: globalStyles.currentTheme.primary,\n                        padding: '0.5rem 1rem',\n                        borderRadius: '12px',\n                        fontSize: '0.9rem',\n                        fontWeight: 600,\n                        border: `1px solid ${globalStyles.currentTheme.primary}30`\n                      }}>\n                        📈 Active Learner\n                      </div>\n                      <div style={{\n                        background: globalStyles.currentTheme.primary + '15',\n                        color: globalStyles.currentTheme.primary,\n                        padding: '0.5rem 1rem',\n                        borderRadius: '12px',\n                        fontSize: '0.9rem',\n                        fontWeight: 600,\n                        border: `1px solid ${globalStyles.currentTheme.primary}30`\n                      }}>\n                        🎯 Goal Oriented\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Quick Stats */}\n                  <div style={{\n                    textAlign: 'center',\n                    padding: '1rem',\n                    background: globalStyles.currentTheme.secondary,\n                    borderRadius: '16px',\n                    border: `1px solid ${globalStyles.currentTheme.border}`\n                  }}>\n                    <div style={{\n                      fontSize: '2rem',\n                      fontWeight: 700,\n                      color: globalStyles.currentTheme.primary,\n                      marginBottom: '0.5rem'\n                    }}>\n                      {new Date().toLocaleDateString('en-US', { day: 'numeric' })}\n                    </div>\n                    <div style={{\n                      fontSize: '0.9rem',\n                      color: globalStyles.currentTheme.textLight,\n                      fontWeight: 500\n                    }}>\n                      {new Date().toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Professional Analytics Cards */}\n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',\n                gap: '1.5rem',\n                marginBottom: '2rem'\n              }}>\n                {[\n                  {\n                    title: 'Learning Progress',\n                    value: '78',\n                    unit: '%',\n                    icon: '📈',\n                    trend: '+12%',\n                    description: 'This month',\n                    color: globalStyles.currentTheme.primary\n                  },\n                  {\n                    title: 'Study Hours',\n                    value: '47',\n                    unit: 'hrs',\n                    icon: '⏱️',\n                    trend: '+8 hrs',\n                    description: 'This week',\n                    color: '#10B981'\n                  },\n                  {\n                    title: 'Completed Tasks',\n                    value: '23',\n                    unit: 'tasks',\n                    icon: '✅',\n                    trend: '+5',\n                    description: 'This week',\n                    color: '#3B82F6'\n                  },\n                  {\n                    title: 'Skill Rating',\n                    value: '4.8',\n                    unit: '/5.0',\n                    icon: '⭐',\n                    trend: '+0.3',\n                    description: 'Overall',\n                    color: '#F59E0B'\n                  }\n                ].map((stat, index) => (\n                  <div key={index} style={{\n                    background: globalStyles.currentTheme.surface,\n                    borderRadius: '16px',\n                    padding: '1.5rem',\n                    position: 'relative',\n                    overflow: 'hidden',\n                    border: `1px solid ${globalStyles.currentTheme.border}`,\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n                  }}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.transform = 'translateY(-4px)';\n                    e.currentTarget.style.boxShadow = `0 8px 25px ${globalStyles.currentTheme.shadow}`;\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.transform = 'translateY(0)';\n                    e.currentTarget.style.boxShadow = `0 4px 12px ${globalStyles.currentTheme.shadow}`;\n                  }}>\n\n                    {/* Subtle accent line */}\n                    <div style={{\n                      position: 'absolute',\n                      top: 0,\n                      left: 0,\n                      right: 0,\n                      height: '3px',\n                      background: stat.color\n                    }} />\n\n                    <div style={{ position: 'relative', zIndex: 1 }}>\n                      {/* Header */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        marginBottom: '1rem'\n                      }}>\n                        <div style={{\n                          fontSize: '0.9rem',\n                          fontWeight: 600,\n                          color: globalStyles.currentTheme.textLight,\n                          textTransform: 'uppercase',\n                          letterSpacing: '0.5px'\n                        }}>\n                          {stat.title}\n                        </div>\n                        <div style={{\n                          fontSize: '1.5rem',\n                          opacity: 0.8\n                        }}>\n                          {stat.icon}\n                        </div>\n                      </div>\n\n                      {/* Main Value */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'baseline',\n                        gap: '0.5rem',\n                        marginBottom: '0.5rem'\n                      }}>\n                        <div style={{\n                          fontSize: '2.5rem',\n                          fontWeight: 700,\n                          color: globalStyles.currentTheme.text,\n                          lineHeight: 1\n                        }}>\n                          {stat.value}\n                        </div>\n                        <div style={{\n                          fontSize: '1rem',\n                          fontWeight: 500,\n                          color: globalStyles.currentTheme.textLight\n                        }}>\n                          {stat.unit}\n                        </div>\n                      </div>\n\n                      {/* Trend and Description */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between'\n                      }}>\n                        <div style={{\n                          fontSize: '0.8rem',\n                          color: globalStyles.currentTheme.textLight\n                        }}>\n                          {stat.description}\n                        </div>\n                        <div style={{\n                          fontSize: '0.8rem',\n                          fontWeight: 600,\n                          color: stat.color,\n                          background: stat.color + '15',\n                          padding: '0.25rem 0.5rem',\n                          borderRadius: '8px'\n                        }}>\n                          {stat.trend}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n\n              {/* Professional Quick Actions */}\n              <div style={{\n                background: globalStyles.currentTheme.surface,\n                borderRadius: '20px',\n                padding: '2rem',\n                marginBottom: '2rem',\n                border: `1px solid ${globalStyles.currentTheme.border}`,\n                boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                position: 'relative',\n                overflow: 'hidden'\n              }}>\n                {/* Professional accent line */}\n                <div style={{\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  right: 0,\n                  height: '4px',\n                  background: globalStyles.currentTheme.gradient\n                }} />\n\n                <h2 style={{\n                  margin: '0 0 2rem 0',\n                  fontSize: '1.5rem',\n                  fontWeight: 600,\n                  color: globalStyles.currentTheme.text,\n                  textAlign: 'center'\n                }}>\n                  🚀 Quick Actions\n                </h2>\n\n                <div style={{\n                  display: 'grid',\n                  gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                  gap: '1.5rem'\n                }}>\n                  {[\n                    {\n                      icon: '📊',\n                      title: 'Take Assessment',\n                      subtitle: 'Skill Evaluation',\n                      desc: 'Evaluate your current skill level',\n                      action: () => setActiveTab('quizzes'),\n                      color: globalStyles.currentTheme.primary\n                    },\n                    {\n                      icon: '💻',\n                      title: 'Practice Coding',\n                      subtitle: 'DSA Problems',\n                      desc: 'Solve data structures & algorithms',\n                      action: () => setActiveTab('dsa'),\n                      color: '#3B82F6'\n                    },\n                    {\n                      icon: '📄',\n                      title: 'Resume Review',\n                      subtitle: 'Career Enhancement',\n                      desc: 'Get professional resume feedback',\n                      action: () => setActiveTab('resume'),\n                      color: '#10B981'\n                    },\n                    {\n                      icon: '📚',\n                      title: 'Study Resources',\n                      subtitle: 'Learning Materials',\n                      desc: 'Access curated study materials',\n                      action: () => setActiveTab('resources'),\n                      color: '#F59E0B'\n                    }\n                  ].map((action, index) => (\n                    <div key={index}\n                      onClick={action.action}\n                      style={{\n                        background: globalStyles.currentTheme.surface,\n                        borderRadius: '16px',\n                        padding: '1.5rem',\n                        cursor: 'pointer',\n                        transition: 'all 0.3s ease',\n                        border: `1px solid ${globalStyles.currentTheme.border}`,\n                        position: 'relative',\n                        overflow: 'hidden',\n                        boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-4px)';\n                        e.currentTarget.style.boxShadow = `0 8px 25px ${globalStyles.currentTheme.shadow}`;\n                        e.currentTarget.style.borderColor = action.color;\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = `0 4px 12px ${globalStyles.currentTheme.shadow}`;\n                        e.currentTarget.style.borderColor = globalStyles.currentTheme.border;\n                      }}\n                    >\n                      {/* Subtle accent line */}\n                      <div style={{\n                        position: 'absolute',\n                        top: 0,\n                        left: 0,\n                        right: 0,\n                        height: '3px',\n                        background: action.color\n                      }} />\n\n                      <div style={{ position: 'relative', zIndex: 1, textAlign: 'center' }}>\n                        <div style={{\n                          fontSize: '2.5rem',\n                          marginBottom: '1rem',\n                          opacity: 0.8\n                        }}>\n                          {action.icon}\n                        </div>\n\n                        <div style={{\n                          fontSize: '1.1rem',\n                          fontWeight: 600,\n                          color: globalStyles.currentTheme.text,\n                          marginBottom: '0.5rem'\n                        }}>\n                          {action.title}\n                        </div>\n\n                        <div style={{\n                          fontSize: '0.9rem',\n                          color: action.color,\n                          fontWeight: 500,\n                          marginBottom: '0.75rem'\n                        }}>\n                          {action.subtitle}\n                        </div>\n\n                        <div style={{\n                          fontSize: '0.85rem',\n                          color: globalStyles.currentTheme.textLight,\n                          lineHeight: 1.4\n                        }}>\n                          {action.desc}\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* Professional Insights Section */}\n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',\n                gap: '2rem',\n                marginBottom: '2rem'\n              }}>\n                {/* Learning Progress Chart */}\n                <div style={{\n                  background: globalStyles.currentTheme.surface,\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                  border: `1px solid ${globalStyles.currentTheme.border}`\n                }}>\n                  <h3 style={{\n                    margin: '0 0 1.5rem 0',\n                    fontSize: '1.2rem',\n                    fontWeight: 600,\n                    color: globalStyles.currentTheme.text,\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem'\n                  }}>\n                    📊 Weekly Progress\n                  </h3>\n\n                  {/* Simple Progress Visualization */}\n                  <div style={{ marginBottom: '1rem' }}>\n                    {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, index) => (\n                      <div key={day} style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        marginBottom: '0.75rem',\n                        padding: '0.5rem',\n                        borderRadius: '8px',\n                        background: index < 5 ? globalStyles.currentTheme.secondary : 'transparent'\n                      }}>\n                        <span style={{\n                          fontSize: '0.9rem',\n                          fontWeight: 500,\n                          color: globalStyles.currentTheme.text,\n                          minWidth: '40px'\n                        }}>\n                          {day}\n                        </span>\n                        <div style={{\n                          flex: 1,\n                          height: '8px',\n                          background: globalStyles.currentTheme.border,\n                          borderRadius: '4px',\n                          margin: '0 1rem',\n                          overflow: 'hidden'\n                        }}>\n                          <div style={{\n                            height: '100%',\n                            width: `${Math.random() * 80 + 20}%`,\n                            background: index < 5 ? globalStyles.currentTheme.primary : globalStyles.currentTheme.border,\n                            borderRadius: '4px',\n                            transition: 'width 1s ease'\n                          }} />\n                        </div>\n                        <span style={{\n                          fontSize: '0.8rem',\n                          color: globalStyles.currentTheme.textLight,\n                          minWidth: '40px',\n                          textAlign: 'right'\n                        }}>\n                          {index < 5 ? `${Math.floor(Math.random() * 3 + 1)}h` : '-'}\n                        </span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Goals & Achievements */}\n                <div style={{\n                  background: globalStyles.currentTheme.surface,\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                  border: `1px solid ${globalStyles.currentTheme.border}`\n                }}>\n                  <h3 style={{\n                    margin: '0 0 1.5rem 0',\n                    fontSize: '1.2rem',\n                    fontWeight: 600,\n                    color: globalStyles.currentTheme.text,\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem'\n                  }}>\n                    🎯 Goals & Achievements\n                  </h3>\n\n                  <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n                    {[\n                      { title: 'Complete 5 DSA Problems', progress: 80, status: 'In Progress' },\n                      { title: 'Finish Resume Review', progress: 100, status: 'Completed' },\n                      { title: 'Take 3 Practice Tests', progress: 33, status: 'In Progress' }\n                    ].map((goal, index) => (\n                      <div key={index} style={{\n                        padding: '1rem',\n                        borderRadius: '12px',\n                        background: globalStyles.currentTheme.secondary,\n                        border: `1px solid ${globalStyles.currentTheme.border}`\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          justifyContent: 'space-between',\n                          alignItems: 'center',\n                          marginBottom: '0.5rem'\n                        }}>\n                          <span style={{\n                            fontSize: '0.9rem',\n                            fontWeight: 500,\n                            color: globalStyles.currentTheme.text\n                          }}>\n                            {goal.title}\n                          </span>\n                          <span style={{\n                            fontSize: '0.8rem',\n                            fontWeight: 600,\n                            color: goal.progress === 100 ? '#10B981' : globalStyles.currentTheme.primary,\n                            background: goal.progress === 100 ? '#10B98115' : globalStyles.currentTheme.primary + '15',\n                            padding: '0.25rem 0.5rem',\n                            borderRadius: '6px'\n                          }}>\n                            {goal.status}\n                          </span>\n                        </div>\n                        <div style={{\n                          height: '6px',\n                          background: globalStyles.currentTheme.border,\n                          borderRadius: '3px',\n                          overflow: 'hidden'\n                        }}>\n                          <div style={{\n                            height: '100%',\n                            width: `${goal.progress}%`,\n                            background: goal.progress === 100 ? '#10B981' : globalStyles.currentTheme.primary,\n                            borderRadius: '3px',\n                            transition: 'width 1s ease'\n                          }} />\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n\n              {/* Recent Activity & Resume Management */}\n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr',\n                gap: '2rem'\n              }}>\n\n                {/* Recent Activity */}\n                <div style={{\n                  background: globalStyles.currentTheme.surface,\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                  border: `1px solid ${globalStyles.currentTheme.border}`\n                }}>\n                  <h3 style={{\n                    margin: '0 0 1.5rem 0',\n                    fontSize: '1.3rem',\n                    fontWeight: 600,\n                    color: globalStyles.currentTheme.text\n                  }}>\n                    📈 Recent Activity\n                  </h3>\n\n                  <div style={{\n                    maxHeight: '250px',\n                    overflowY: 'auto'\n                  }}>\n                    {activityLog.slice(0, 5).map((log, index) => (\n                      <div key={index} style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem',\n                        padding: '0.75rem',\n                        borderRadius: '8px',\n                        marginBottom: '0.5rem',\n                        background: index % 2 === 0 ? globalStyles.currentTheme.secondary : 'transparent',\n                        transition: 'all 0.3s ease'\n                      }}>\n                        <div style={{\n                          width: '10px',\n                          height: '10px',\n                          borderRadius: '50%',\n                          background: log.type === 'login' ? '#4ECDC4' : '#45B7D1',\n                          flexShrink: 0\n                        }} />\n                        <div style={{ flex: 1 }}>\n                          <div style={{\n                            fontSize: '0.9rem',\n                            fontWeight: 500,\n                            color: globalStyles.currentTheme.text,\n                            marginBottom: '0.2rem'\n                          }}>\n                            {log.msg}\n                          </div>\n                          <div style={{\n                            fontSize: '0.8rem',\n                            color: globalStyles.currentTheme.textLight\n                          }}>\n                            {new Date(log.date).toLocaleString()}\n                          </div>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Resume Management */}\n                <div style={{\n                  background: globalStyles.currentTheme.surface,\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                  border: `1px solid ${globalStyles.currentTheme.border}`\n                }}>\n                  <h3 style={{\n                    margin: '0 0 1.5rem 0',\n                    fontSize: '1.3rem',\n                    fontWeight: 600,\n                    color: globalStyles.currentTheme.text\n                  }}>\n                    📄 Resume Management\n                  </h3>\n\n                  <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n                    <label style={{\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.75rem',\n                      padding: '1rem',\n                      borderRadius: '12px',\n                      background: globalStyles.currentTheme.secondary,\n                      border: `2px dashed ${globalStyles.currentTheme.border}`,\n                      cursor: resumeUploadLoading ? 'not-allowed' : 'pointer',\n                      transition: 'all 0.3s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      if (!resumeUploadLoading) {\n                        e.currentTarget.style.borderColor = globalStyles.currentTheme.primary;\n                        e.currentTarget.style.background = globalStyles.currentTheme.primary + '10';\n                      }\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.borderColor = globalStyles.currentTheme.border;\n                      e.currentTarget.style.background = globalStyles.currentTheme.secondary;\n                    }}>\n                      <FiUpload size={20} color={globalStyles.currentTheme.primary} />\n                      <div>\n                        <div style={{\n                          fontWeight: 600,\n                          color: globalStyles.currentTheme.text,\n                          marginBottom: '0.2rem'\n                        }}>\n                          {resumeUploadLoading ? 'Uploading...' : 'Upload Resume'}\n                        </div>\n                        <div style={{\n                          fontSize: '0.8rem',\n                          color: globalStyles.currentTheme.textLight\n                        }}>\n                          PDF files only\n                        </div>\n                      </div>\n                      <input\n                        type=\"file\"\n                        accept=\"application/pdf\"\n                        onChange={handleResumeUpload}\n                        disabled={resumeUploadLoading}\n                        style={{ display: 'none' }}\n                      />\n                    </label>\n\n                    {resumeUrl && (\n                      <a\n                        href={resumeUrl}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.75rem',\n                          padding: '1rem',\n                          borderRadius: '12px',\n                          background: '#4ECDC4',\n                          color: 'white',\n                          textDecoration: 'none',\n                          fontWeight: 600,\n                          transition: 'all 0.3s ease'\n                        }}\n                        onMouseEnter={(e) => {\n                          e.currentTarget.style.background = '#3DBDB6';\n                          e.currentTarget.style.transform = 'translateY(-2px)';\n                        }}\n                        onMouseLeave={(e) => {\n                          e.currentTarget.style.background = '#4ECDC4';\n                          e.currentTarget.style.transform = 'translateY(0)';\n                        }}\n                      >\n                        <FiFileText size={20} />\n                        <span>View Resume</span>\n                      </a>\n                    )}\n                  </div>\n                </div>\n              </div>\n\n              {/* Professional Footer Section */}\n              <div style={{\n                background: globalStyles.currentTheme.surface,\n                borderRadius: '16px',\n                padding: '2rem',\n                marginTop: '2rem',\n                border: `1px solid ${globalStyles.currentTheme.border}`,\n                boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                textAlign: 'center'\n              }}>\n                <div style={{\n                  display: 'grid',\n                  gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                  gap: '2rem',\n                  marginBottom: '1.5rem'\n                }}>\n                  {/* Quick Stats */}\n                  <div>\n                    <h4 style={{\n                      margin: '0 0 1rem 0',\n                      fontSize: '1rem',\n                      fontWeight: 600,\n                      color: globalStyles.currentTheme.text\n                    }}>\n                      📈 Your Progress\n                    </h4>\n                    <div style={{\n                      display: 'flex',\n                      flexDirection: 'column',\n                      gap: '0.5rem'\n                    }}>\n                      <div style={{\n                        fontSize: '0.9rem',\n                        color: globalStyles.currentTheme.textLight\n                      }}>\n                        Total Study Time: <strong style={{ color: globalStyles.currentTheme.primary }}>47 hours</strong>\n                      </div>\n                      <div style={{\n                        fontSize: '0.9rem',\n                        color: globalStyles.currentTheme.textLight\n                      }}>\n                        Completed Tasks: <strong style={{ color: globalStyles.currentTheme.primary }}>23</strong>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Quick Links */}\n                  <div>\n                    <h4 style={{\n                      margin: '0 0 1rem 0',\n                      fontSize: '1rem',\n                      fontWeight: 600,\n                      color: globalStyles.currentTheme.text\n                    }}>\n                      🔗 Quick Links\n                    </h4>\n                    <div style={{\n                      display: 'flex',\n                      flexDirection: 'column',\n                      gap: '0.5rem'\n                    }}>\n                      <button\n                        onClick={() => setActiveTab('academics')}\n                        style={{\n                          background: 'none',\n                          border: 'none',\n                          color: globalStyles.currentTheme.primary,\n                          fontSize: '0.9rem',\n                          cursor: 'pointer',\n                          textAlign: 'left',\n                          padding: '0.25rem 0'\n                        }}\n                      >\n                        📚 Academic Resources\n                      </button>\n                      <button\n                        onClick={() => setActiveTab('coding')}\n                        style={{\n                          background: 'none',\n                          border: 'none',\n                          color: globalStyles.currentTheme.primary,\n                          fontSize: '0.9rem',\n                          cursor: 'pointer',\n                          textAlign: 'left',\n                          padding: '0.25rem 0'\n                        }}\n                      >\n                        💻 Coding Practice\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Motivational Quote */}\n                  <div>\n                    <h4 style={{\n                      margin: '0 0 1rem 0',\n                      fontSize: '1rem',\n                      fontWeight: 600,\n                      color: globalStyles.currentTheme.text\n                    }}>\n                      💡 Daily Inspiration\n                    </h4>\n                    <div style={{\n                      fontSize: '0.9rem',\n                      color: globalStyles.currentTheme.textLight,\n                      fontStyle: 'italic',\n                      lineHeight: 1.4\n                    }}>\n                      \"Success is not final, failure is not fatal: it is the courage to continue that counts.\"\n                    </div>\n                  </div>\n                </div>\n\n                {/* Bottom Footer */}\n                <div style={{\n                  borderTop: `1px solid ${globalStyles.currentTheme.border}`,\n                  paddingTop: '1rem',\n                  fontSize: '0.8rem',\n                  color: globalStyles.currentTheme.textLight\n                }}>\n                  Keep learning, keep growing! 🚀 Your journey to success starts here.\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Chat Interface */}\n        {activeTab === \"resume\" && (\n          <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>\n            <div style={getStyle('card')}>\n              <h2 style={{\n                marginTop: 0,\n                color: '#333'\n              }}>Career Assistant</h2>\n              <p style={{\n                opacity: 0.8,\n                marginBottom: '24px',\n                color: '#666'\n              }}>\n                Get personalized resume advice and career guidance\n              </p>\n\n              {/* Chat messages */}\n              <div style={{\n                height: '50vh',\n                overflowY: 'auto',\n                marginBottom: '24px',\n                padding: '16px',\n                backgroundColor: '#f5f5f5',\n                border: '1px solid #e0e0e0',\n                borderRadius: '8px'\n              }}>\n\n                {messages.length === 0 ? (\n                  <div style={{\n                    height: '100%',\n                    display: 'flex',\n                    flexDirection: 'column',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    textAlign: 'center',\n                    opacity: 0.7\n                  }}>\n                    <div style={{ fontSize: '48px', marginBottom: '16px' }}>💬</div>\n                    <h3 style={{\n                      margin: 0,\n                      color: '#333'\n                    }}>Start a conversation</h3>\n                    <p style={{\n                      color: '#666'\n                    }}>Ask about resumes, interviews, or career advice</p>\n                  </div>\n                ) : (\n                  messages.map((msg, idx) => (\n                    <div\n                      key={idx}\n                      style={{\n                        ...(msg.role === 'user' ? getStyle('chatBubbleUser') : getStyle('chatBubbleBot')),\n                        animation: 'fadeIn 0.3s ease'\n                      }}\n                    >\n                      {msg.role === 'bot' ? (\n                        <ReactMarkdown>{msg.content}</ReactMarkdown>\n                      ) : (\n                        msg.content\n                      )}\n                    </div>\n                  ))\n                )}\n                {loading && (\n                  <div style={getStyle('chatBubbleBot')}>\n                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n                      <div style={{\n                        width: '10px',\n                        height: '10px',\n                        borderRadius: '50%',\n                        backgroundColor: '#1976d2',\n                        animation: 'pulse 1.4s infinite ease-in-out'\n                      }} />\n                      <div style={{\n                        width: '10px',\n                        height: '10px',\n                        borderRadius: '50%',\n                        backgroundColor: '#1976d2',\n                        animation: 'pulse 1.4s infinite ease-in-out',\n                        animationDelay: '0.2s'\n                      }} />\n                      <div style={{\n                        width: '10px',\n                        height: '10px',\n                        borderRadius: '50%',\n                        backgroundColor: '#1976d2',\n                        animation: 'pulse 1.4s infinite ease-in-out',\n                        animationDelay: '0.4s'\n                      }} />\n                    </div>\n                  </div>\n                )}\n                <div ref={chatEndRef} />\n              </div>\n\n              {/* Input area */}\n              <form\n                style={{ display: 'flex', gap: '12px' }}\n                onSubmit={e => {\n                  e.preventDefault();\n                  sendMessage();\n                }}\n              >\n                <input\n                  type=\"text\"\n                  placeholder=\"Type your message...\"\n                  style={getStyle('inputField')}\n                  value={input}\n                  onChange={e => setInput(e.target.value)}\n                  disabled={loading}\n                />\n                <button\n                  type=\"submit\"\n                  style={{\n                    ...getStyle('buttonPrimary'),\n                    minWidth: '100px'\n                  }}\n                  disabled={loading || !input.trim()}\n                >\n                  {loading ? 'Sending...' : 'Send'}\n                </button>\n              </form>\n            </div>\n          </div>\n        )}\n\n        {/* Enhanced DSA Company Questions */}\n        {activeTab === \"dsa\" && (\n          <div style={{ padding: '24px' }}>\n            <div style={getStyle('card')}>\n              {/* Header with revert button */}\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>\n                <div>\n                  <h2 style={{ marginTop: 0, marginBottom: '8px' }}>🚀 Company Wise DSA Questions</h2>\n                  <p style={{ opacity: 0.8, margin: 0 }}>\n                    Explore DSA questions from top companies with enhanced filtering and favorites\n                  </p>\n                </div>\n                {showRevertButton && (\n                  <button\n                    onClick={revertHeaderChanges}\n                    style={{\n                      ...getStyle('buttonPrimary'),\n                      background: '#ff6b6b',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '8px',\n                      fontSize: '14px',\n                      padding: '8px 16px',\n                      border: 'none',\n                      borderRadius: '8px',\n                      color: 'white',\n                      cursor: 'pointer',\n                      transition: 'all 0.3s ease'\n                    }}\n                    onMouseEnter={(e) => e.target.style.background = '#ff5252'}\n                    onMouseLeave={(e) => e.target.style.background = '#ff6b6b'}\n                  >\n                    <FiRefreshCw size={16} />\n                    Revert Header Color\n                  </button>\n                )}\n              </div>\n\n              {/* Category Tabs */}\n              <div style={{\n                display: 'flex',\n                gap: '8px',\n                marginBottom: '20px',\n                flexWrap: 'wrap',\n                borderBottom: '1px solid #eee',\n                paddingBottom: '16px'\n              }}>\n                {['all', ...Object.keys(companyCategories)].map(category => (\n                  <button\n                    key={category}\n                    onClick={() => setSelectedCategory(category)}\n                    style={{\n                      padding: '8px 16px',\n                      borderRadius: '20px',\n                      border: selectedCategory === category\n                        ? 'none'\n                        : '1px solid #ddd',\n                      background: selectedCategory === category\n                        ? globalStyles.currentTheme.primary\n                        : 'transparent',\n                      color: selectedCategory === category\n                        ? 'white'\n                        : '#666',\n                      cursor: 'pointer',\n                      fontSize: '14px',\n                      fontWeight: selectedCategory === category ? 600 : 400,\n                      transition: 'all 0.3s ease',\n                      textTransform: 'capitalize'\n                    }}\n                    onMouseEnter={(e) => {\n                      if (selectedCategory !== category) {\n                        e.target.style.background = '#f5f5f5';\n                      }\n                    }}\n                    onMouseLeave={(e) => {\n                      if (selectedCategory !== category) {\n                        e.target.style.background = 'transparent';\n                      }\n                    }}\n                  >\n                    {category === 'all' ? '🌟 All' : `${category === 'FAANG' ? '🔥' : category === 'Big Tech' ? '💻' : category === 'Startups' ? '🚀' : category === 'Finance' ? '💰' : category === 'Indian' ? '🇮🇳' : '🏢'} ${category}`}\n                  </button>\n                ))}\n              </div>\n\n              {/* Controls Row */}\n              <div style={{\n                display: 'flex',\n                gap: '16px',\n                marginBottom: '24px',\n                flexWrap: 'wrap',\n                alignItems: 'center'\n              }}>\n                {/* Search box */}\n                <div style={{ position: 'relative', flex: 1, minWidth: '300px' }}>\n                  <div style={{\n                    position: 'absolute',\n                    left: '16px',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    color: '#666'\n                  }}>\n                    <FiSearch size={20} />\n                  </div>\n                  <input\n                    type=\"text\"\n                    placeholder=\"Search companies...\"\n                    style={{\n                      ...getStyle('inputField'),\n                      paddingLeft: '48px',\n                      width: '100%'\n                    }}\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                  />\n                  {searchTerm && (\n                    <button\n                      style={{\n                        position: 'absolute',\n                        right: '16px',\n                        top: '50%',\n                        transform: 'translateY(-50%)',\n                        background: 'none',\n                        border: 'none',\n                        color: '#666',\n                        cursor: 'pointer'\n                      }}\n                      onClick={() => setSearchTerm(\"\")}\n                    >\n                      <FiX size={20} />\n                    </button>\n                  )}\n                </div>\n\n                {/* Sort dropdown */}\n                <select\n                  value={sortBy}\n                  onChange={(e) => setSortBy(e.target.value)}\n                  style={{\n                    ...getStyle('inputField'),\n                    width: 'auto',\n                    minWidth: '150px'\n                  }}\n                >\n                  <option value=\"name\">📝 Sort by Name</option>\n                  <option value=\"favorites\">⭐ Favorites First</option>\n                </select>\n              </div>\n\n              {/* Recent Companies */}\n              {recentCompanies.length > 0 && (\n                <div style={{\n                  marginBottom: '24px',\n                  padding: '16px',\n                  borderRadius: '12px',\n                  background: '#f8f9fa',\n                  border: '1px solid #e9ecef'\n                }}>\n                  <h3 style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '8px',\n                    fontSize: '16px',\n                    marginBottom: '12px',\n                    color: '#333',\n                    margin: '0 0 12px 0'\n                  }}>\n                    <FiClock color=\"#666\" /> Recently Viewed\n                  </h3>\n                  <div style={{\n                    display: 'flex',\n                    gap: '8px',\n                    flexWrap: 'wrap'\n                  }}>\n                    {recentCompanies.map(company => (\n                      <button\n                        key={company}\n                        onClick={() => handleCompanyClick(company)}\n                        style={{\n                          padding: '6px 12px',\n                          borderRadius: '16px',\n                          border: `1px solid ${globalStyles.currentTheme.primary}`,\n                          background: 'transparent',\n                          color: globalStyles.currentTheme.primary,\n                          cursor: 'pointer',\n                          fontSize: '12px',\n                          transition: 'all 0.3s ease'\n                        }}\n                        onMouseEnter={(e) => {\n                          e.target.style.background = globalStyles.currentTheme.primary;\n                          e.target.style.color = 'white';\n                        }}\n                        onMouseLeave={(e) => {\n                          e.target.style.background = 'transparent';\n                          e.target.style.color = globalStyles.currentTheme.primary;\n                        }}\n                      >\n                        {company}\n                      </button>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {/* Companies grid */}\n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fill, minmax(220px, 1fr))',\n                gap: '16px',\n                marginTop: '24px'\n              }}>\n                {getFilteredCompanies().map((company, index) => (\n                  <div\n                    key={index}\n                    style={{\n                      ...getStyle('companyCard'),\n                      position: 'relative',\n                      transform: favoriteCompanies.includes(company) ? 'scale(1.02)' : 'scale(1)',\n                      border: favoriteCompanies.includes(company)\n                        ? `2px solid ${globalStyles.currentTheme.primary}`\n                        : `1px solid ${globalStyles.currentTheme.border}`,\n                      background: globalStyles.currentTheme.surface,\n                      color: globalStyles.currentTheme.text,\n                      animation: `fadeIn 0.3s ease ${index * 0.1}s both`,\n                      boxShadow: `0 4px 6px ${globalStyles.currentTheme.shadow}`\n                    }}\n                    onClick={() => handleCompanyClick(company)}\n                  >\n                    {/* Favorite button */}\n                    <button\n                      onClick={(e) => toggleFavorite(company, e)}\n                      style={{\n                        position: 'absolute',\n                        top: '8px',\n                        right: '8px',\n                        background: 'none',\n                        border: 'none',\n                        cursor: 'pointer',\n                        color: favoriteCompanies.includes(company) ? '#ff6b6b' : '#ccc',\n                        transition: 'all 0.3s ease',\n                        fontSize: '18px'\n                      }}\n                    >\n                      <FiHeart fill={favoriteCompanies.includes(company) ? 'currentColor' : 'none'} />\n                    </button>\n\n                    {/* Company initial with gradient */}\n                    <div style={{\n                      width: '56px',\n                      height: '56px',\n                      borderRadius: '50%',\n                      background: `linear-gradient(135deg, ${globalStyles.currentTheme.primary}, ${globalStyles.currentTheme.primaryDark})`,\n                      color: 'white',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      fontSize: '24px',\n                      fontWeight: 700,\n                      marginBottom: '12px',\n                      boxShadow: `0 4px 8px ${globalStyles.currentTheme.shadow}`\n                    }}>\n                      {company.charAt(0)}\n                    </div>\n\n                    {/* Company name */}\n                    <div style={{\n                      fontWeight: 600,\n                      textAlign: 'center',\n                      fontSize: '14px',\n                      marginBottom: '8px'\n                    }}>\n                      {company}\n                    </div>\n\n                    {/* Mock stats */}\n                    <div style={{\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      fontSize: '12px',\n                      opacity: 0.7,\n                      marginTop: '8px'\n                    }}>\n                      <span>📊 {Math.floor(Math.random() * 50) + 10} Questions</span>\n                      <span>⭐ {(Math.random() * 2 + 3).toFixed(1)}</span>\n                    </div>\n\n                    {/* Category badge */}\n                    {Object.entries(companyCategories).map(([category, companies]) => {\n                      if (companies.some(c => c.toLowerCase() === company.toLowerCase())) {\n                        return (\n                          <div\n                            key={category}\n                            style={{\n                              position: 'absolute',\n                              top: '8px',\n                              left: '8px',\n                              background: globalStyles.currentTheme.primary,\n                              color: 'white',\n                              padding: '2px 6px',\n                              borderRadius: '8px',\n                              fontSize: '10px',\n                              fontWeight: 600\n                            }}\n                          >\n                            {category}\n                          </div>\n                        );\n                      }\n                      return null;\n                    })}\n                  </div>\n                ))}\n              </div>\n\n              {/* No results message */}\n              {getFilteredCompanies().length === 0 && (\n                <div style={{\n                  textAlign: 'center',\n                  padding: '40px',\n                  opacity: 0.7,\n                  color: '#666'\n                }}>\n                  <div style={{ fontSize: '48px', marginBottom: '16px' }}>🔍</div>\n                  <h3 style={{ color: '#333' }}>No companies found</h3>\n                  <p style={{ color: '#666' }}>Try adjusting your search or category filter</p>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Quizzes Section */}\n        {activeTab === \"quizzes\" && (\n          <div style={{ padding: '24px' }}>\n            <div style={getStyle('card')}>\n              <h2 style={{ marginTop: 0 }}>Career Quizzes</h2>\n              <p style={{ opacity: 0.8, marginBottom: '24px' }}>\n                Test your knowledge with our career-focused quizzes!\n              </p>\n\n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',\n                gap: '16px'\n              }}>\n                {quizButtons.map((quiz, index) => (\n                  <div\n                    key={index}\n                    style={getStyle('quizCard')}\n                    onClick={() => openQuizLink(quiz.link)}\n                  >\n                    <div>\n                      <h3 style={{ margin: '0 0 8px 0' }}>{quiz.title}</h3>\n                      <p style={{\n                        margin: 0,\n                        fontSize: '14px',\n                        opacity: 0.8\n                      }}>\n                        {quiz.description}\n                      </p>\n                    </div>\n                    <div style={{ color: '#1976d2' }}>\n                      <FiExternalLink size={20} />\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Other tabs */}\n        {activeTab === \"coding\" && (\n          <div style={{ padding: '24px' }}>\n            <Coding />\n          </div>\n        )}\n        {activeTab === \"resources\" && (\n          <div style={{\n            padding: '2rem 1rem',\n            background: globalStyles.currentTheme.gradientLight,\n            minHeight: '100vh'\n          }}>\n            <div style={{ maxWidth: '1400px', margin: '0 auto' }}>\n              {/* Header */}\n              <div style={{\n                background: globalStyles.currentTheme.surface,\n                borderRadius: '20px',\n                padding: '2rem',\n                marginBottom: '2rem',\n                border: `1px solid ${globalStyles.currentTheme.border}`,\n                boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                position: 'relative'\n              }}>\n                <div style={{\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  right: 0,\n                  height: '4px',\n                  background: globalStyles.currentTheme.gradient\n                }} />\n\n                <h1 style={{\n                  margin: '0 0 1rem 0',\n                  fontSize: '2.5rem',\n                  fontWeight: 700,\n                  color: globalStyles.currentTheme.text,\n                  textAlign: 'center'\n                }}>\n                  📚 Study Resources & Materials\n                </h1>\n                <p style={{\n                  margin: 0,\n                  fontSize: '1.1rem',\n                  color: globalStyles.currentTheme.textLight,\n                  textAlign: 'center'\n                }}>\n                  Access comprehensive study materials, academic resources, and learning tools\n                </p>\n              </div>\n\n              {/* Enhanced Tab Navigation with Sliding Underline */}\n              <div style={{\n                background: globalStyles.currentTheme.surface,\n                borderRadius: '16px',\n                padding: '1.5rem',\n                marginBottom: '2rem',\n                border: `1px solid ${globalStyles.currentTheme.border}`,\n                boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n              }}>\n                {/* Tab Container with Sliding Underline */}\n                <div\n                  id=\"tab-navigation-container\"\n                  className=\"tab-navigation-container\"\n                  style={{\n                    position: 'relative',\n                    display: 'flex',\n                    justifyContent: 'center',\n                    alignItems: 'center',\n                    background: globalStyles.currentTheme.secondary,\n                    borderRadius: '12px',\n                    padding: '0.5rem',\n                    overflow: 'hidden'\n                  }}\n                >\n                  {/* Fixed Static Background Bar */}\n                  <div\n                    className=\"static-background-bar\"\n                    style={{\n                      position: 'absolute',\n                      top: '0.5rem',\n                      bottom: '0.5rem',\n                      left: '0.5rem',\n                      right: '0.5rem',\n                      background: globalStyles.currentTheme.primary + '20',\n                      borderRadius: '8px',\n                      zIndex: 1,\n                      pointerEvents: 'none' // Allow clicks to pass through to buttons\n                    }}\n                  />\n\n                  {/* Tab Buttons */}\n                  {[\n                    { id: 'general', label: '📁 General Resources', icon: '📁', shortLabel: 'General' },\n                    { id: 'academics', label: '🎓 Academic Materials', icon: '🎓', shortLabel: 'Academics' },\n                    { id: 'tools', label: '🛠️ Study Tools', icon: '🛠️', shortLabel: 'Tools' }\n                  ].map((tab, index) => (\n                    <button\n                      key={tab.id}\n                      className={`tab-button ${resourcesTab === tab.id ? 'active' : ''}`}\n                      onClick={() => setResourcesTab(tab.id)}\n                      onMouseEnter={(e) => {\n                        // Simplified hover effect\n                        if (resourcesTab !== tab.id) {\n                          e.target.style.color = globalStyles.currentTheme.primary;\n                        }\n                      }}\n                      onMouseLeave={(e) => {\n                        // Reset hover effect\n                        if (resourcesTab !== tab.id) {\n                          e.target.style.color = globalStyles.currentTheme.text;\n                        }\n                      }}\n                      onFocus={(e) => {\n                        // Keyboard navigation support\n                        e.target.style.outline = `2px solid ${globalStyles.currentTheme.primary}`;\n                        e.target.style.outlineOffset = '2px';\n                      }}\n                      onBlur={(e) => {\n                        // Remove focus outline\n                        e.target.style.outline = 'none';\n                      }}\n                      aria-label={`Switch to ${tab.label} tab`}\n                      aria-selected={resourcesTab === tab.id}\n                      role=\"tab\"\n                      tabIndex={0}\n                      style={{\n                        flex: 1,\n                        position: 'relative',\n                        zIndex: 20,\n                        padding: '0.875rem 1rem',\n                        minHeight: '44px', // Touch target requirement\n                        border: 'none',\n                        background: 'transparent',\n                        color: resourcesTab === tab.id\n                          ? 'white'\n                          : globalStyles.currentTheme.text,\n                        cursor: 'pointer',\n                        fontSize: '1rem',\n                        fontWeight: resourcesTab === tab.id ? 600 : 500,\n                        borderRadius: '8px',\n                        transition: 'all 0.3s ease',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        gap: '0.5rem',\n                        textAlign: 'center',\n                        whiteSpace: 'nowrap',\n                        userSelect: 'none',\n                        WebkitTapHighlightColor: 'transparent' // Remove tap highlight on mobile\n                      }}\n                    >\n                      {/* Simplified responsive display */}\n                      <span>{tab.icon}</span>\n                      <span style={{ marginLeft: '0.5rem' }}>\n                        {windowWidth < 640 ? tab.shortLabel : tab.label.replace(tab.icon + ' ', '')}\n                      </span>\n                    </button>\n                  ))}\n                </div>\n\n                {/* Tab Description */}\n                <div style={{\n                  textAlign: 'center',\n                  marginTop: '1rem',\n                  padding: '0.5rem',\n                  fontSize: '0.9rem',\n                  color: globalStyles.currentTheme.textLight,\n                  fontStyle: 'italic'\n                }}>\n                  {resourcesTab === 'general' && 'Upload and manage your personal study materials and documents'}\n                  {resourcesTab === 'academics' && 'Access comprehensive academic resources organized by subject'}\n                  {resourcesTab === 'tools' && 'Utilize powerful study tools and utilities to enhance your learning'}\n                </div>\n              </div>\n\n              {/* Content based on selected tab */}\n              {resourcesTab === 'general' && (\n                <div style={{\n                  background: globalStyles.currentTheme.surface,\n                  borderRadius: '16px',\n                  padding: '2rem',\n                  border: `1px solid ${globalStyles.currentTheme.border}`,\n                  boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n                }}>\n                  <h2 style={{\n                    margin: '0 0 1.5rem 0',\n                    fontSize: '1.5rem',\n                    fontWeight: 600,\n                    color: globalStyles.currentTheme.text\n                  }}>\n                    📁 General Resources\n                  </h2>\n\n                  <div style={{ marginBottom: '2rem' }}>\n                    <label style={{\n                      display: 'inline-flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      padding: '0.75rem 1.5rem',\n                      background: globalStyles.currentTheme.primary,\n                      color: 'white',\n                      borderRadius: '12px',\n                      cursor: resourceUploadLoading ? 'not-allowed' : 'pointer',\n                      fontSize: '1rem',\n                      fontWeight: 500,\n                      transition: 'all 0.3s ease'\n                    }}>\n                      <FiUpload size={20} />\n                      {resourceUploadLoading ? 'Uploading...' : 'Upload Resource'}\n                      <input\n                        type=\"file\"\n                        accept=\".pdf,.doc,.docx,.txt\"\n                        onChange={handleResourceUpload}\n                        disabled={resourceUploadLoading}\n                        style={{ display: 'none' }}\n                      />\n                    </label>\n                  </div>\n\n                  <div>\n                    <h3 style={{\n                      margin: '0 0 1rem 0',\n                      fontSize: '1.2rem',\n                      fontWeight: 600,\n                      color: globalStyles.currentTheme.text\n                    }}>\n                      Your Uploaded Resources\n                    </h3>\n                    {userResources.length === 0 ? (\n                      <div style={{\n                        textAlign: 'center',\n                        padding: '3rem',\n                        background: globalStyles.currentTheme.secondary,\n                        borderRadius: '12px',\n                        border: `1px solid ${globalStyles.currentTheme.border}`\n                      }}>\n                        <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📄</div>\n                        <h3 style={{\n                          margin: '0 0 0.5rem 0',\n                          color: globalStyles.currentTheme.text\n                        }}>\n                          No resources uploaded yet\n                        </h3>\n                        <p style={{\n                          margin: 0,\n                          color: globalStyles.currentTheme.textLight\n                        }}>\n                          Upload your study materials, notes, and documents to get started\n                        </p>\n                      </div>\n                    ) : (\n                      <div style={{\n                        display: 'grid',\n                        gap: '1rem'\n                      }}>\n                        {userResources.map((file, idx) => {\n                          const { data: urlData } = supabase.storage.from('resources').getPublicUrl(`${user.id}/${file.name}`);\n                          return (\n                            <div key={idx} style={{\n                              display: 'flex',\n                              justifyContent: 'space-between',\n                              alignItems: 'center',\n                              padding: '1rem',\n                              background: globalStyles.currentTheme.secondary,\n                              borderRadius: '12px',\n                              border: `1px solid ${globalStyles.currentTheme.border}`\n                            }}>\n                              <div style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '1rem'\n                              }}>\n                                <div style={{\n                                  width: '40px',\n                                  height: '40px',\n                                  borderRadius: '8px',\n                                  background: globalStyles.currentTheme.primary,\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  justifyContent: 'center',\n                                  color: 'white',\n                                  fontSize: '1.2rem'\n                                }}>\n                                  📄\n                                </div>\n                                <div>\n                                  <div style={{\n                                    fontWeight: 600,\n                                    color: globalStyles.currentTheme.text,\n                                    marginBottom: '0.25rem'\n                                  }}>\n                                    {file.name}\n                                  </div>\n                                  <div style={{\n                                    fontSize: '0.8rem',\n                                    color: globalStyles.currentTheme.textLight\n                                  }}>\n                                    Uploaded resource\n                                  </div>\n                                </div>\n                              </div>\n                              <a\n                                href={urlData.publicUrl}\n                                target=\"_blank\"\n                                rel=\"noopener noreferrer\"\n                                style={{\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: '0.5rem',\n                                  padding: '0.5rem 1rem',\n                                  background: globalStyles.currentTheme.primary,\n                                  color: 'white',\n                                  textDecoration: 'none',\n                                  borderRadius: '8px',\n                                  fontSize: '0.9rem',\n                                  fontWeight: 500,\n                                  transition: 'all 0.3s ease'\n                                }}\n                              >\n                                <FiExternalLink size={16} />\n                                Open\n                              </a>\n                            </div>\n                          );\n                        })}\n                      </div>\n                    )}\n                  </div>\n                </div>\n              )}\n\n              {/* Academics Tab Content */}\n              {resourcesTab === 'academics' && (\n                <div style={{\n                  display: 'grid',\n                  gap: '2rem'\n                }}>\n                  {/* Academic Subjects */}\n                  <div style={{\n                    background: globalStyles.currentTheme.surface,\n                    borderRadius: '16px',\n                    padding: '2rem',\n                    border: `1px solid ${globalStyles.currentTheme.border}`,\n                    boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n                  }}>\n                    <h2 style={{\n                      margin: '0 0 1.5rem 0',\n                      fontSize: '1.5rem',\n                      fontWeight: 600,\n                      color: globalStyles.currentTheme.text,\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem'\n                    }}>\n                      🎓 Academic Study Materials\n                    </h2>\n\n                    <div style={{\n                      display: 'grid',\n                      gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n                      gap: '1.5rem'\n                    }}>\n                      {[\n                        {\n                          subject: 'Mathematics',\n                          icon: '📐',\n                          color: '#DC2626',\n                          topics: ['Calculus', 'Linear Algebra', 'Statistics', 'Discrete Math'],\n                          resources: 15\n                        },\n                        {\n                          subject: 'Computer Science',\n                          icon: '💻',\n                          color: '#7C3AED',\n                          topics: ['Data Structures', 'Algorithms', 'Database Systems', 'Software Engineering'],\n                          resources: 23\n                        },\n                        {\n                          subject: 'Physics',\n                          icon: '⚛️',\n                          color: '#059669',\n                          topics: ['Mechanics', 'Thermodynamics', 'Electromagnetism', 'Quantum Physics'],\n                          resources: 18\n                        },\n                        {\n                          subject: 'Chemistry',\n                          icon: '🧪',\n                          color: '#EA580C',\n                          topics: ['Organic Chemistry', 'Inorganic Chemistry', 'Physical Chemistry', 'Biochemistry'],\n                          resources: 12\n                        }\n                      ].map((subject, index) => (\n                        <div key={index} style={{\n                          background: globalStyles.currentTheme.secondary,\n                          borderRadius: '12px',\n                          padding: '1.5rem',\n                          border: `1px solid ${globalStyles.currentTheme.border}`,\n                          cursor: 'pointer',\n                          transition: 'all 0.3s ease'\n                        }}\n                        onMouseEnter={(e) => {\n                          e.currentTarget.style.transform = 'translateY(-4px)';\n                          e.currentTarget.style.boxShadow = `0 8px 25px ${globalStyles.currentTheme.shadow}`;\n                        }}\n                        onMouseLeave={(e) => {\n                          e.currentTarget.style.transform = 'translateY(0)';\n                          e.currentTarget.style.boxShadow = 'none';\n                        }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '1rem',\n                            marginBottom: '1rem'\n                          }}>\n                            <div style={{\n                              width: '50px',\n                              height: '50px',\n                              borderRadius: '12px',\n                              background: subject.color,\n                              display: 'flex',\n                              alignItems: 'center',\n                              justifyContent: 'center',\n                              fontSize: '1.5rem'\n                            }}>\n                              {subject.icon}\n                            </div>\n                            <div>\n                              <h3 style={{\n                                margin: 0,\n                                fontSize: '1.2rem',\n                                fontWeight: 600,\n                                color: globalStyles.currentTheme.text\n                              }}>\n                                {subject.subject}\n                              </h3>\n                              <p style={{\n                                margin: 0,\n                                fontSize: '0.9rem',\n                                color: globalStyles.currentTheme.textLight\n                              }}>\n                                {subject.resources} resources available\n                              </p>\n                            </div>\n                          </div>\n\n                          <div style={{\n                            display: 'flex',\n                            flexWrap: 'wrap',\n                            gap: '0.5rem',\n                            marginBottom: '1rem'\n                          }}>\n                            {subject.topics.map((topic, topicIndex) => (\n                              <span key={topicIndex} style={{\n                                padding: '0.25rem 0.5rem',\n                                background: subject.color + '15',\n                                color: subject.color,\n                                borderRadius: '6px',\n                                fontSize: '0.8rem',\n                                fontWeight: 500\n                              }}>\n                                {topic}\n                              </span>\n                            ))}\n                          </div>\n\n                          <button style={{\n                            width: '100%',\n                            padding: '0.75rem',\n                            background: subject.color,\n                            color: 'white',\n                            border: 'none',\n                            borderRadius: '8px',\n                            fontSize: '0.9rem',\n                            fontWeight: 500,\n                            cursor: 'pointer',\n                            transition: 'all 0.3s ease'\n                          }}>\n                            Browse Materials\n                          </button>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n\n                  {/* Exam Preparation */}\n                  <div style={{\n                    background: globalStyles.currentTheme.surface,\n                    borderRadius: '16px',\n                    padding: '2rem',\n                    border: `1px solid ${globalStyles.currentTheme.border}`,\n                    boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n                  }}>\n                    <h2 style={{\n                      margin: '0 0 1.5rem 0',\n                      fontSize: '1.5rem',\n                      fontWeight: 600,\n                      color: globalStyles.currentTheme.text,\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem'\n                    }}>\n                      📝 Exam Preparation Resources\n                    </h2>\n\n                    <div style={{\n                      display: 'grid',\n                      gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                      gap: '1rem'\n                    }}>\n                      {[\n                        { name: 'Previous Year Papers', icon: '📄', count: '50+ papers' },\n                        { name: 'Sample Questions', icon: '❓', count: '500+ questions' },\n                        { name: 'Study Guides', icon: '📖', count: '25+ guides' },\n                        { name: 'Reference Books', icon: '📚', count: '100+ books' },\n                        { name: 'Video Lectures', icon: '🎥', count: '200+ videos' },\n                        { name: 'Practice Tests', icon: '✅', count: '30+ tests' }\n                      ].map((resource, index) => (\n                        <div key={index} style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1rem',\n                          padding: '1rem',\n                          background: globalStyles.currentTheme.secondary,\n                          borderRadius: '12px',\n                          border: `1px solid ${globalStyles.currentTheme.border}`,\n                          cursor: 'pointer',\n                          transition: 'all 0.3s ease'\n                        }}\n                        onMouseEnter={(e) => {\n                          e.currentTarget.style.background = globalStyles.currentTheme.primary + '10';\n                          e.currentTarget.style.borderColor = globalStyles.currentTheme.primary;\n                        }}\n                        onMouseLeave={(e) => {\n                          e.currentTarget.style.background = globalStyles.currentTheme.secondary;\n                          e.currentTarget.style.borderColor = globalStyles.currentTheme.border;\n                        }}>\n                          <div style={{\n                            width: '40px',\n                            height: '40px',\n                            borderRadius: '8px',\n                            background: globalStyles.currentTheme.primary,\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            fontSize: '1.2rem'\n                          }}>\n                            {resource.icon}\n                          </div>\n                          <div>\n                            <div style={{\n                              fontWeight: 600,\n                              color: globalStyles.currentTheme.text,\n                              marginBottom: '0.25rem'\n                            }}>\n                              {resource.name}\n                            </div>\n                            <div style={{\n                              fontSize: '0.8rem',\n                              color: globalStyles.currentTheme.textLight\n                            }}>\n                              {resource.count}\n                            </div>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {/* Study Tools Tab Content */}\n              {resourcesTab === 'tools' && (\n                <div style={{\n                  background: globalStyles.currentTheme.surface,\n                  borderRadius: '16px',\n                  padding: '2rem',\n                  border: `1px solid ${globalStyles.currentTheme.border}`,\n                  boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n                }}>\n                  <h2 style={{\n                    margin: '0 0 1.5rem 0',\n                    fontSize: '1.5rem',\n                    fontWeight: 600,\n                    color: globalStyles.currentTheme.text,\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem'\n                  }}>\n                    🛠️ Study Tools & Utilities\n                  </h2>\n\n                  <div style={{\n                    display: 'grid',\n                    gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',\n                    gap: '1.5rem'\n                  }}>\n                    {[\n                      {\n                        name: 'Scientific Calculator',\n                        icon: '🧮',\n                        description: 'Advanced calculator for complex calculations',\n                        color: '#DC2626'\n                      },\n                      {\n                        name: 'Formula Reference',\n                        icon: '📐',\n                        description: 'Quick access to mathematical and scientific formulas',\n                        color: '#7C3AED'\n                      },\n                      {\n                        name: 'Unit Converter',\n                        icon: '⚖️',\n                        description: 'Convert between different units of measurement',\n                        color: '#059669'\n                      },\n                      {\n                        name: 'Study Timer',\n                        icon: '⏰',\n                        description: 'Pomodoro timer for effective study sessions',\n                        color: '#EA580C'\n                      },\n                      {\n                        name: 'Note Taking',\n                        icon: '📝',\n                        description: 'Digital notepad for quick notes and ideas',\n                        color: '#0284C7'\n                      },\n                      {\n                        name: 'Progress Tracker',\n                        icon: '📊',\n                        description: 'Track your study progress and goals',\n                        color: '#7C2D12'\n                      }\n                    ].map((tool, index) => (\n                      <div key={index} style={{\n                        background: globalStyles.currentTheme.secondary,\n                        borderRadius: '12px',\n                        padding: '1.5rem',\n                        border: `1px solid ${globalStyles.currentTheme.border}`,\n                        cursor: 'pointer',\n                        transition: 'all 0.3s ease'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-4px)';\n                        e.currentTarget.style.boxShadow = `0 8px 25px ${globalStyles.currentTheme.shadow}`;\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = 'none';\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1rem',\n                          marginBottom: '1rem'\n                        }}>\n                          <div style={{\n                            width: '50px',\n                            height: '50px',\n                            borderRadius: '12px',\n                            background: tool.color,\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            fontSize: '1.5rem'\n                          }}>\n                            {tool.icon}\n                          </div>\n                          <div>\n                            <h3 style={{\n                              margin: 0,\n                              fontSize: '1.1rem',\n                              fontWeight: 600,\n                              color: globalStyles.currentTheme.text\n                            }}>\n                              {tool.name}\n                            </h3>\n                          </div>\n                        </div>\n\n                        <p style={{\n                          margin: '0 0 1rem 0',\n                          fontSize: '0.9rem',\n                          color: globalStyles.currentTheme.textLight,\n                          lineHeight: 1.4\n                        }}>\n                          {tool.description}\n                        </p>\n\n                        <button style={{\n                          width: '100%',\n                          padding: '0.75rem',\n                          background: tool.color,\n                          color: 'white',\n                          border: 'none',\n                          borderRadius: '8px',\n                          fontSize: '0.9rem',\n                          fontWeight: 500,\n                          cursor: 'pointer',\n                          transition: 'all 0.3s ease'\n                        }}>\n                          Launch Tool\n                        </button>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n        {activeTab === \"academics\" && <Exams />}\n        {activeTab === \"faq\" && <Faq />}\n        {activeTab === \"admin\" && user?.email === ADMIN_EMAIL && (\n          <div style={{ padding: '24px' }}>\n            <div style={getStyle('card')}>\n              <h2 style={{\n                marginTop: 0,\n                color: '#333'\n              }}>Admin Panel</h2>\n              <div style={{\n                display: 'flex',\n                gap: '16px',\n                marginBottom: '24px'\n              }}>\n                <button\n                  style={{\n                    ...getStyle('buttonPrimary'),\n                    background: adminTab === 'users' ?\n                      globalStyles.currentTheme.primary : 'transparent',\n                    color: adminTab === 'users' ?\n                      'white' : '#333',\n                    border: '1px solid #ddd'\n                  }}\n                  onClick={() => setAdminTab('users')}\n                >\n                  Users\n                </button>\n                <button\n                  style={{\n                    ...getStyle('buttonPrimary'),\n                    background: adminTab === 'resources' ?\n                      globalStyles.currentTheme.primary : 'transparent',\n                    color: adminTab === 'resources' ?\n                      'white' : '#333',\n                    border: '1px solid #ddd'\n                  }}\n                  onClick={() => setAdminTab('resources')}\n                >\n                  Resources\n                </button>\n              </div>\n\n              {adminTab === 'users' && (\n                <div>\n                  <h3 style={{\n                    marginBottom: '16px',\n                    color: '#333'\n                  }}>All Users</h3>\n                  <div style={{\n                    backgroundColor: '#f5f5f5',\n                    border: '1px solid #e0e0e0',\n                    borderRadius: '8px',\n                    padding: '16px'\n                  }}>\n                    {allUsers.map((user, idx) => (\n                      <div key={idx} style={{\n                        padding: '12px',\n                        borderBottom: '1px solid #eee',\n                        color: '#333'\n                      }}>\n                        {user.email}\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {adminTab === 'resources' && (\n                <div>\n                  <h3 style={{\n                    marginBottom: '16px',\n                    color: '#333'\n                  }}>All Resources</h3>\n                  <p style={{\n                    opacity: 0.7,\n                    color: '#666'\n                  }}>Resource management coming soon</p>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </main>\n\n      {/* Notification */}\n      {notification && (\n        <div style={{\n          ...getStyle('notification'),\n          backgroundColor: notification.type === 'error' ? '#f44336' :\n                         notification.type === 'success' ? '#4caf50' : '#2196f3',\n          color: 'white',\n          border: 'none'\n        }}>\n          {notification.msg}\n        </div>\n      )}\n\n      {/* Enhanced Global styles */}\n      <style>{`\n        @keyframes fadeIn {\n          from { opacity: 0; transform: translateY(10px); }\n          to { opacity: 1; transform: translateY(0); }\n        }\n        @keyframes slideIn {\n          from { transform: translateX(100%); }\n          to { transform: translateX(0); }\n        }\n        @keyframes pulse {\n          0%, 100% { opacity: 1; }\n          50% { opacity: 0.5; }\n        }\n        @keyframes bounce {\n          0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }\n          40%, 43% { transform: translate3d(0,-8px,0); }\n          70% { transform: translate3d(0,-4px,0); }\n          90% { transform: translate3d(0,-2px,0); }\n        }\n        @keyframes glow {\n          0%, 100% { box-shadow: 0 0 5px rgba(99, 102, 241, 0.3); }\n          50% { box-shadow: 0 0 20px rgba(99, 102, 241, 0.6); }\n        }\n        @keyframes shimmer {\n          0% { background-position: -200px 0; }\n          100% { background-position: calc(200px + 100%) 0; }\n        }\n\n        /* Optimized Tab Navigation Styles */\n        .tab-navigation-container {\n          position: relative;\n          overflow: hidden;\n          contain: layout style paint;\n        }\n\n        .tab-button {\n          position: relative;\n          transition: color 0.15s ease;\n          will-change: color;\n          z-index: 20;\n        }\n\n        .tab-button:hover:not(.active) {\n          color: var(--primary-color);\n        }\n\n        .tab-button.active {\n          color: white;\n        }\n\n        .sliding-indicator {\n          transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n          will-change: transform;\n          contain: layout style paint;\n          z-index: 10;\n          pointer-events: none;\n        }\n\n        /* Simplified Responsive Styles */\n        @media (max-width: 640px) {\n          .tab-button {\n            font-size: 0.9rem;\n            padding: 0.75rem 0.5rem;\n          }\n        }\n\n        /* Focus styles for accessibility */\n        .tab-button:focus-visible {\n          outline: 2px solid var(--primary-color);\n          outline-offset: 2px;\n        }\n\n        /* Touch device optimizations */\n        @media (hover: none) and (pointer: coarse) {\n          .tab-button:hover {\n            color: inherit;\n          }\n\n          .tab-button:active {\n            opacity: 0.8;\n          }\n        }\n\n        /* Enhanced hover effects */\n        .company-card:hover {\n          animation: bounce 0.6s ease;\n        }\n\n        .favorite-btn:hover {\n          animation: pulse 0.5s ease;\n        }\n\n        /* Smooth transitions for all interactive elements */\n        button, input, select {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        button:hover {\n          transform: translateY(-1px);\n        }\n\n        * {\n          box-sizing: border-box;\n        }\n        body {\n          margin: 0;\n          font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n        }\n      `}</style>\n    </div>\n  );\n};\nexport default EduAIChatBot;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,GAAG,QAAQ,oBAAoB;AAChD,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,IAAI,EAAEC,EAAE,QAAQ,kBAAkB;AAC3C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,kBAAkB,QAAQ,eAAe;AAClD,OAAOC,YAAY,MAAM,aAAa;AACtC,SACEC,MAAM,EAAEC,GAAG,EAAEC,aAAa,EAAEC,cAAc,EAAEC,UAAU,EACtDC,MAAM,EAAEC,YAAY,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EACvDC,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,QAAQ,EACrCC,WAAW,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,cAAc,EACjEC,OAAO,EAAEC,OAAO,EAAEC,WAAW,QACxB,gBAAgB;AACvB,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,GAAG,QAAQ,iBAAiB;AACrC,SAASC,KAAK,EAAEC,UAAU,EAAEC,aAAa,EAAEC,WAAW,EAAEC,OAAO,EAAEC,MAAM,QAAQ,UAAU;AACzF,OAAOC,aAAa,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3CV,KAAK,CAACW,QAAQ,CAACV,UAAU,EAAEC,aAAa,EAAEC,WAAW,EAAEC,OAAO,EAAEC,MAAM,CAAC;;AAEvE;AACA,MAAMO,mBAAmB,GAAGxC,YAAY,CAACyC,GAAG,CAACC,IAAI,IAAI;EACnD,MAAMC,OAAO,GAAG;IACd,QAAQ,eAAEP,OAAA,CAAC7B,UAAU;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxB,KAAK,eAAEX,OAAA,CAAC5B,MAAM;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjB,QAAQ,eAAEX,OAAA,CAAChB,QAAQ;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtB,WAAW,eAAEX,OAAA,CAAClB,WAAW;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5B,SAAS,eAAEX,OAAA,CAACf,aAAa;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5B,UAAU,eAAEX,OAAA,CAACjB,WAAW;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3B,WAAW,eAAEX,OAAA,CAACzB,MAAM;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvB,KAAK,eAAEX,OAAA,CAAC3B,YAAY;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvB,OAAO,eAAEX,OAAA,CAACvB,QAAQ;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACtB,CAAC;EAED,OAAO;IACL,GAAGL,IAAI;IACPM,IAAI,EAAEL,OAAO,CAACD,IAAI,CAACO,GAAG,CAAC,IAAIN,OAAO,CAACD,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,iBAAIf,OAAA,CAAC1B,OAAO;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC5E,CAAC;AACH,CAAC,CAAC;AAEF,MAAMK,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB;EACA,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACmE,QAAQ,EAAEC,WAAW,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqE,MAAM,EAAEC,SAAS,CAAC,GAAGtE,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACuE,OAAO,EAAEC,UAAU,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyE,SAAS,EAAEC,YAAY,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC2E,SAAS,EAAEC,YAAY,CAAC,GAAG5E,QAAQ,CAAC,WAAW,CAAC;EACvD,MAAM,CAAC6E,UAAU,EAAEC,aAAa,CAAC,GAAG9E,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+E,WAAW,EAAEC,cAAc,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiF,aAAa,EAAEC,gBAAgB,CAAC,GAAGlF,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAACmF,IAAI,EAAEC,OAAO,CAAC,GAAGpF,QAAQ,CAAC,IAAI,CAAC;EAEtC,MAAM,CAACqF,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGtF,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACuF,SAAS,EAAEC,YAAY,CAAC,GAAGxF,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACyF,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG1F,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAAC2F,aAAa,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM4F,WAAW,GAAG,mBAAmB;EACvC,MAAM,CAACC,QAAQ,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;EAC/B,MAAM,CAAC8F,QAAQ,EAAEC,WAAW,CAAC,GAAG/F,QAAQ,CAAC,OAAO,CAAC;EACjD,MAAM,CAACgG,YAAY,EAAEC,eAAe,CAAC,GAAGjG,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACkG,WAAW,EAAEC,cAAc,CAAC,GAAGnG,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoG,YAAY,EAAEC,eAAe,CAAC,GAAGrG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACsG,WAAW,EAAEC,cAAc,CAAC,GAAGvG,QAAQ,CAAC,OAAOwG,MAAM,KAAK,WAAW,GAAGA,MAAM,CAACC,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC;;EAE1G;EACA,MAAMC,cAAc,GAAIC,KAAK,IAAK;IAChC,MAAMC,QAAQ,GAAG,CAAC,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC,CAACC,OAAO,CAACF,KAAK,CAAC;IACjE,OAAO,GAAGC,QAAQ,GAAG,MAAM,GAAG;EAChC,CAAC;EACD,MAAME,UAAU,GAAG5G,MAAM,CAAC,IAAI,CAAC;;EAE/B;EACAD,SAAS,CAAC,MAAM;IACd,IAAI8G,SAAS,GAAG,IAAI;IACpB,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAID,SAAS,EAAEE,YAAY,CAACF,SAAS,CAAC;MACtCA,SAAS,GAAGG,UAAU,CAAC,MAAM;QAC3BX,cAAc,CAACC,MAAM,CAACC,UAAU,CAAC;MACnC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX,CAAC;IAEDD,MAAM,CAACW,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,EAAE;MAAEI,OAAO,EAAE;IAAK,CAAC,CAAC;IAClE,OAAO,MAAM;MACXZ,MAAM,CAACa,mBAAmB,CAAC,QAAQ,EAAEL,YAAY,CAAC;MAClD,IAAID,SAAS,EAAEE,YAAY,CAACF,SAAS,CAAC;IACxC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM,CAACO,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvH,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACwH,MAAM,EAAEC,SAAS,CAAC,GAAGzH,QAAQ,CAAC,MAAM,CAAC;EAC5C,MAAM,CAAC0H,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3H,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC4H,eAAe,EAAEC,kBAAkB,CAAC,GAAG7H,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC8H,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/H,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAMgI,OAAO,GAAG,yCAAyC;EACzD,MAAMC,YAAY,GAAG,0CAA0C;EAC/D,MAAMC,iBAAiB,GAAG,kNAAkN;EAC5O,MAAMC,QAAQ,GAAG9F,YAAY,CAAC4F,YAAY,EAAEC,iBAAiB,CAAC;;EAE9D;EACA,MAAME,iBAAiB,GAAG;IACxB,OAAO,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC;IAC7D,UAAU,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC;IACpF,UAAU,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC;IACrF,SAAS,EAAE,CAAC,eAAe,EAAE,UAAU,EAAE,gBAAgB,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,CAAC;IAC/F,YAAY,EAAE,CAAC,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,KAAK,CAAC;IACzE,YAAY,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,CAAC;IAC7E,QAAQ,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC;IACrE,QAAQ,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ;EACtF,CAAC;;EAED;EACA,MAAMC,SAAS,GAAG,CACf,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EACxE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EACnE,QAAQ,EAAE,kBAAkB,EAAE,SAAS,EAAE,OAAO,EAAE,iBAAiB,EACnE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE,UAAU,EAChE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAC9D,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM,EAAE,aAAa,EAAE,KAAK,EAChE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,QAAQ,EAClE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAC5D,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAC9D,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,aAAa,EAAE,QAAQ,EAC3D,SAAS,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EACjE,WAAW,EAAE,WAAW,EAAE,eAAe,EAAE,SAAS,EAAE,SAAS,EAC/D,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAC7D,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,SAAS,EAC1D,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,EAChE,OAAO,EAAE,eAAe,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EACnE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EACzD,sBAAsB,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EACnE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,aAAa,EACtD,iBAAiB,EAAE,cAAc,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAC3D,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAC/D,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EACnE,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EACzD,gBAAgB,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAC5D,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAChE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAClE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,uBAAuB,EAAE,oBAAoB,EACxE,QAAQ,EAAE,OAAO,EAAE,oBAAoB,EAAE,SAAS,EAAE,WAAW,EAC/D,aAAa,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU,EAAE,OAAO,EAC7D,SAAS,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAC9D,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,KAAK,EAAE,YAAY,EAChE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,EACnE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,qBAAqB,EAAE,QAAQ,EAChE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,aAAa,EAC5D,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAChE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EACxD,eAAe,EAAE,QAAQ,EAAE,SAAS,EAAE,iBAAiB,EAAE,MAAM,EAC/D,IAAI,EAAE,QAAQ,EAAE,cAAc,EAAE,aAAa,EAAE,SAAS,EACxD,aAAa,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,QAAQ,EAChE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAChE,QAAQ,EAAE,SAAS,EAAE,UAAU,CAChC;;EAED;EACA,MAAMC,WAAW,GAAG,CAClB;IACEzE,KAAK,EAAE,gBAAgB;IACvB0E,WAAW,EAAE,+DAA+D;IAC5EC,IAAI,EAAE;EACR,CAAC,EACD;IACE3E,KAAK,EAAE,oBAAoB;IAC3B0E,WAAW,EAAE,uCAAuC;IACpDC,IAAI,EAAE;EACR,CAAC,EACD;IACE3E,KAAK,EAAE,oBAAoB;IAC3B0E,WAAW,EAAE,mCAAmC;IAChDC,IAAI,EAAE;EACR,CAAC,EACD;IACE3E,KAAK,EAAE,0CAA0C;IACjD0E,WAAW,EAAE,+CAA+C;IAC5DC,IAAI,EAAE;EACR,CAAC,EACD;IACE3E,KAAK,EAAE,qBAAqB;IAC5B0E,WAAW,EAAE,kDAAkD;IAC/DC,IAAI,EAAE;EACR,CAAC,EACD;IACE3E,KAAK,EAAE,UAAU;IACjB0E,WAAW,EAAE,qCAAqC;IAClDC,IAAI,EAAE;EACR,CAAC,EACD;IACE3E,KAAK,EAAE,2CAA2C;IAClD0E,WAAW,EAAE,kCAAkC;IAC/CC,IAAI,EAAE;EACR,CAAC,EACA;IACC3E,KAAK,EAAE,sBAAsB;IAC7B0E,WAAW,EAAE,gCAAgC;IAC7CC,IAAI,EAAE;EAER,CAAC,CACF;;EAED;EACA,MAAMC,MAAM,GAAG;IACb,GAAG5H,YAAY;IACf6H,YAAY,EAAE;MACZ,GAAG7H,YAAY,CAAC6H,YAAY;MAC5BC,eAAe,EAAE9H,YAAY,CAAC+H,YAAY,CAACC,UAAU;MACrDC,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG;IACnC,CAAC;IACDC,MAAM,EAAE;MACN,GAAGnI,YAAY,CAACoI,WAAW;MAC3BC,YAAY,EAAE,aAAarI,YAAY,CAAC+H,YAAY,CAACO,MAAM;IAC7D,CAAC;IACDC,OAAO,EAAE;MACP,GAAGvI,YAAY,CAACwI,YAAY;MAC5BV,eAAe,EAAE9H,YAAY,CAAC+H,YAAY,CAACU,OAAO;MAClDC,WAAW,EAAE,aAAa1I,YAAY,CAAC+H,YAAY,CAACO,MAAM,EAAE;MAC5DK,SAAS,EAAEzE,WAAW,GAAG,eAAe,GAAG;IAC7C,CAAC;IACD0E,WAAW,EAAE;MACX,GAAG5I,YAAY,CAAC6I,cAAc;MAC9BZ,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG,IAAI;MACrCF,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACU,OAAO;MAC7CH,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACO,MAAM,EAAE;MACvDQ,UAAU,EAAE,eAAe;MAC3B,SAAS,EAAE;QACTd,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACgB,OAAO;QAC7Cd,KAAK,EAAE;MACT;IACF,CAAC;IACDe,iBAAiB,EAAE;MACjB,GAAGhJ,YAAY,CAACiJ,oBAAoB;MACpChB,KAAK,EAAE,OAAO;MACdD,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACgB,OAAO;MAC7CT,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACgB,OAAO;IACxD,CAAC;IACDG,WAAW,EAAE;MACX,GAAGlJ,YAAY,CAACmJ,cAAc;MAC9BC,UAAU,EAAElF,WAAW,GAAG,OAAO,GAAG,GAAG;MACvC4D,eAAe,EAAE9H,YAAY,CAAC+H,YAAY,CAACC,UAAU;MACrDC,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG,IAAI;MACrCmB,SAAS,EAAE;IACb,CAAC;IACDC,IAAI,EAAE;MACJ,GAAGtJ,YAAY,CAACuJ,OAAO;MACvBzB,eAAe,EAAE9H,YAAY,CAAC+H,YAAY,CAACU,OAAO;MAClDR,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG,IAAI;MACrCI,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACO,MAAM,EAAE;MACvDkB,SAAS,EAAE,aAAaxJ,YAAY,CAAC+H,YAAY,CAAC0B,MAAM;IAC1D,CAAC;IACDC,aAAa,EAAE;MACb,GAAG1J,YAAY,CAAC0J;IAClB,CAAC;IACDC,UAAU,EAAE;MACV,GAAG3J,YAAY,CAAC2J,UAAU;MAC1B7B,eAAe,EAAE,MAAM;MACvBG,KAAK,EAAE,MAAM;MACbK,MAAM,EAAE,gBAAgB;MACxB,SAAS,EAAE;QACTsB,WAAW,EAAE5J,YAAY,CAAC+H,YAAY,CAACgB,OAAO;QAC9Cc,OAAO,EAAE,MAAM;QACfL,SAAS,EAAE,aAAaxJ,YAAY,CAAC+H,YAAY,CAACgB,OAAO;MAC3D;IACF,CAAC;IACDe,cAAc,EAAE;MACd,GAAG9J,YAAY,CAAC8J,cAAc;MAC9BhC,eAAe,EAAE9H,YAAY,CAAC+H,YAAY,CAACgB,OAAO;MAClDd,KAAK,EAAE;IACT,CAAC;IACD8B,aAAa,EAAE;MACb,GAAG/J,YAAY,CAAC+J,aAAa;MAC7BjC,eAAe,EAAE9H,YAAY,CAAC+H,YAAY,CAACiC,SAAS;MACpD/B,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG,IAAI;MACrCI,MAAM,EAAE;IACV,CAAC;IACD2B,WAAW,EAAE;MACX,GAAGjK,YAAY,CAACkK;IAClB,CAAC;IACDC,QAAQ,EAAE;MACR,GAAGnK,YAAY,CAACoK,WAAW;MAC3BtC,eAAe,EAAE9H,YAAY,CAAC+H,YAAY,CAACU;IAC7C,CAAC;IACDtD,YAAY,EAAE;MACZ,GAAGnF,YAAY,CAACmF;IAClB;EACF,CAAC;;EAED;EACA,MAAMkF,QAAQ,GAAGA,CAACC,SAAS,EAAEC,KAAK,GAAG,KAAK,KAAK;IAC7C,MAAMC,SAAS,GAAG5C,MAAM,CAAC0C,SAAS,CAAC;IACnC,IAAI,OAAOE,SAAS,KAAK,UAAU,EAAE,OAAOA,SAAS,CAAC,CAAC;IACvD,IAAID,KAAK,IAAIC,SAAS,CAAC,SAAS,CAAC,EAAE;MACjC,OAAO;QAAE,GAAGA,SAAS;QAAE,GAAGA,SAAS,CAAC,SAAS;MAAE,CAAC;IAClD;IACA,OAAOA,SAAS;EAClB,CAAC;;EAED;EACApL,SAAS,CAAC,MAAM;IACd,MAAMqL,WAAW,GAAG1K,kBAAkB,CAACJ,IAAI,EAAG2E,IAAI,IAAK;MACrD,IAAIA,IAAI,EAAE;QACRb,SAAS,CAACa,IAAI,CAACoG,GAAG,CAAC;MACrB,CAAC,MAAM;QACLC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxCjH,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC;IACF,OAAO,MAAM8G,WAAW,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;EAENrL,SAAS,CAAC,MAAM;IACd,IAAIoE,MAAM,EAAE;MACV,MAAMqH,gBAAgB,GAAG,MAAAA,CAAA,KAAY;QACnC,MAAMC,OAAO,GAAGvL,GAAG,CAACK,EAAE,EAAE,OAAO,EAAE4D,MAAM,CAAC;QACxC,MAAMuH,OAAO,GAAG,MAAMzL,MAAM,CAACwL,OAAO,CAAC;QAErC,IAAIC,OAAO,CAACC,MAAM,CAAC,CAAC,EAAE;UACpB,MAAMC,QAAQ,GAAGF,OAAO,CAACG,IAAI,CAAC,CAAC;UAC/B;UACAP,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEK,QAAQ,CAACE,EAAE,CAAC;QAC/C,CAAC,MAAM;UACLR,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;QAC9B;QACAjH,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC;MACDkH,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACrH,MAAM,CAAC,CAAC;;EAEZ;EACApE,SAAS,CAAC,MAAM;IACdgM,KAAK,CAAC,oBAAoB,CAAC,CACxBC,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACpD,IAAI,CAAC,CAAC,CAAC,CACzBmD,IAAI,CAAEH,IAAI,IAAKrH,YAAY,CAACqH,IAAI,CAAC,CAAC,CAClCK,KAAK,CAAEC,GAAG,IAAKb,OAAO,CAACc,KAAK,CAAC,+BAA+B,EAAED,GAAG,CAAC,CAAC;EACxE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApM,SAAS,CAAC,MAAM;IACdkI,QAAQ,CAAC3H,IAAI,CAAC+L,UAAU,CAAC,CAAC,CAACL,IAAI,CAAC,CAAC;MAAEH,IAAI,EAAE;QAAES;MAAQ;IAAE,CAAC,KAAK;MACzDpH,OAAO,CAAC,CAAAoH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAErH,IAAI,KAAI,IAAI,CAAC;IAChC,CAAC,CAAC;IACF,MAAM;MAAE4G,IAAI,EAAEU;IAAS,CAAC,GAAGtE,QAAQ,CAAC3H,IAAI,CAACkM,iBAAiB,CAAC,CAACC,MAAM,EAAEH,OAAO,KAAK;MAC9EpH,OAAO,CAAC,CAAAoH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAErH,IAAI,KAAI,IAAI,CAAC;IAChC,CAAC,CAAC;IACF,OAAO,MAAM;MACXsH,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEG,YAAY,CAACtB,WAAW,CAAC,CAAC;IACtC,CAAC;EACH,CAAC,EAAE,CAACnD,QAAQ,CAAC3H,IAAI,CAAC,CAAC;;EAEnB;EACA,MAAMqM,kBAAkB,GAAG,MAAOC,CAAC,IAAK;IACtC,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACF,IAAI,IAAI,CAAC5H,IAAI,EAAE;IACpBG,sBAAsB,CAAC,IAAI,CAAC;IAC5B,MAAM4H,QAAQ,GAAG,GAAG/H,IAAI,CAACgI,EAAE,IAAIJ,IAAI,CAACK,IAAI,EAAE;IAC1C,MAAM;MAAEd;IAAM,CAAC,GAAG,MAAMnE,QAAQ,CAACkF,OAAO,CAACC,IAAI,CAAC,SAAS,CAAC,CAACC,MAAM,CAACL,QAAQ,EAAEH,IAAI,EAAE;MAAES,MAAM,EAAE;IAAK,CAAC,CAAC;IACjG,IAAI,CAAClB,KAAK,EAAE;MACV,MAAM;QAAEP,IAAI,EAAE0B;MAAQ,CAAC,GAAGtF,QAAQ,CAACkF,OAAO,CAACC,IAAI,CAAC,SAAS,CAAC,CAACI,YAAY,CAACR,QAAQ,CAAC;MACjF1H,YAAY,CAACiI,OAAO,CAACE,SAAS,CAAC;MAC/BC,gBAAgB,CAAC,+BAA+B,EAAE,SAAS,CAAC;MAC5DC,WAAW,CAAC,mBAAmB,CAAC;IAClC,CAAC,MAAM;MACLD,gBAAgB,CAAC,uBAAuB,EAAE,OAAO,CAAC;IACpD;IACAtI,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMwI,oBAAoB,GAAG,MAAOhB,CAAC,IAAK;IACxC,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACF,IAAI,IAAI,CAAC5H,IAAI,EAAE;IACpBO,wBAAwB,CAAC,IAAI,CAAC;IAC9B,MAAMwH,QAAQ,GAAG,GAAG/H,IAAI,CAACgI,EAAE,IAAIJ,IAAI,CAACK,IAAI,EAAE;IAC1C,MAAM;MAAEd;IAAM,CAAC,GAAG,MAAMnE,QAAQ,CAACkF,OAAO,CAACC,IAAI,CAAC,WAAW,CAAC,CAACC,MAAM,CAACL,QAAQ,EAAEH,IAAI,EAAE;MAAES,MAAM,EAAE;IAAK,CAAC,CAAC;IACnG,IAAI,CAAClB,KAAK,EAAE;MACVsB,gBAAgB,CAAC,oBAAoB,EAAE,SAAS,CAAC;MACjDC,WAAW,CAAC,sBAAsBd,IAAI,CAACK,IAAI,EAAE,CAAC;IAChD,CAAC,MAAM;MACLQ,gBAAgB,CAAC,yBAAyB,EAAE,OAAO,CAAC;IACtD;IACAlI,wBAAwB,CAAC,KAAK,CAAC;EACjC,CAAC;;EAED;EACA,MAAMqI,kBAAkB,GAAIC,OAAO,IAAK;IACtC;IACAnG,kBAAkB,CAACoG,IAAI,IAAI;MACzB,MAAMC,QAAQ,GAAGD,IAAI,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKJ,OAAO,CAAC;MAChD,OAAO,CAACA,OAAO,EAAE,GAAGE,QAAQ,CAAC,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC;IAEFR,WAAW,CAAC,UAAUG,OAAO,gBAAgB,CAAC;IAE9C,IAAIA,OAAO,CAAClK,WAAW,CAAC,CAAC,KAAK,WAAW,EAAE;MACzC0C,MAAM,CAAC8H,QAAQ,CAACC,IAAI,GAAG,uCAAuC;MAC9D;IACF;IACA,MAAMC,gBAAgB,GAAGR,OAAO,CAACS,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;IACpDjI,MAAM,CAAC8H,QAAQ,CAACC,IAAI,GAAG,gBAAgBC,gBAAgB,OAAO;EAChE,CAAC;;EAED;EACA,MAAME,cAAc,GAAGA,CAACV,OAAO,EAAElB,CAAC,KAAK;IACrCA,CAAC,CAAC6B,eAAe,CAAC,CAAC,CAAC,CAAC;IACrBhH,oBAAoB,CAACsG,IAAI,IAAI;MAC3B,IAAIA,IAAI,CAACW,QAAQ,CAACZ,OAAO,CAAC,EAAE;QAC1B,OAAOC,IAAI,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKJ,OAAO,CAAC;MACxC,CAAC,MAAM;QACL,OAAO,CAAC,GAAGC,IAAI,EAAED,OAAO,CAAC;MAC3B;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMa,mBAAmB,GAAGA,CAAA,KAAM;IAChC9G,mBAAmB,CAAC,KAAK,CAAC;IAC1B6F,gBAAgB,CAAC,8CAA8C,EAAE,SAAS,CAAC;;IAE3E;IACA,MAAMkB,cAAc,GAAGC,QAAQ,CAACC,aAAa,CAAC,qBAAqB,CAAC;IACpE,MAAMC,eAAe,GAAGF,QAAQ,CAACC,aAAa,CAAC,wBAAwB,CAAC;IAExE,IAAIF,cAAc,EAAE;MAClBA,cAAc,CAACI,KAAK,CAACpG,KAAK,GAAG,MAAM;IACrC;IACA,IAAImG,eAAe,EAAE;MACnBA,eAAe,CAACC,KAAK,CAACpG,KAAK,GAAG,MAAM;IACtC;EACF,CAAC;;EAED;EACA,MAAMqG,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIjB,QAAQ,GAAG7F,SAAS;;IAExB;IACA,IAAIf,gBAAgB,KAAK,KAAK,EAAE;MAC9B,MAAM8H,iBAAiB,GAAGhH,iBAAiB,CAACd,gBAAgB,CAAC,IAAI,EAAE;MACnE4G,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACH,OAAO,IAChCoB,iBAAiB,CAACC,IAAI,CAACC,UAAU,IAC/BtB,OAAO,CAAClK,WAAW,CAAC,CAAC,CAAC8K,QAAQ,CAACU,UAAU,CAACxL,WAAW,CAAC,CAAC,CAAC,IACxDwL,UAAU,CAACxL,WAAW,CAAC,CAAC,CAAC8K,QAAQ,CAACZ,OAAO,CAAClK,WAAW,CAAC,CAAC,CACzD,CACF,CAAC;IACH;;IAEA;IACAoK,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACH,OAAO,IAChCA,OAAO,CAAClK,WAAW,CAAC,CAAC,CAAC8K,QAAQ,CAAC/J,UAAU,CAACf,WAAW,CAAC,CAAC,CACzD,CAAC;;IAED;IACA,IAAI0D,MAAM,KAAK,MAAM,EAAE;MACrB0G,QAAQ,CAACqB,IAAI,CAAC,CAAC;IACjB,CAAC,MAAM,IAAI/H,MAAM,KAAK,WAAW,EAAE;MACjC0G,QAAQ,CAACqB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QACtB,MAAMC,IAAI,GAAGhI,iBAAiB,CAACkH,QAAQ,CAACY,CAAC,CAAC;QAC1C,MAAMG,IAAI,GAAGjI,iBAAiB,CAACkH,QAAQ,CAACa,CAAC,CAAC;QAC1C,IAAIC,IAAI,IAAI,CAACC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC5B,IAAI,CAACD,IAAI,IAAIC,IAAI,EAAE,OAAO,CAAC;QAC3B,OAAOH,CAAC,CAACI,aAAa,CAACH,CAAC,CAAC;MAC3B,CAAC,CAAC;IACJ;IAEA,OAAOvB,QAAQ;EACjB,CAAC;;EAED;EACA,MAAM2B,YAAY,GAAIC,GAAG,IAAK;IAC5BtJ,MAAM,CAACuJ,IAAI,CAACD,GAAG,EAAE,QAAQ,CAAC;EAC5B,CAAC;;EAED;EACA,MAAME,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAAC/L,KAAK,CAACgM,IAAI,CAAC,CAAC,EAAE;IAEnB,MAAMC,WAAW,GAAG;MAAEC,IAAI,EAAE,MAAM;MAAEC,OAAO,EAAEnM;IAAM,CAAC;IACpDG,WAAW,CAAE6J,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEiC,WAAW,CAAC,CAAC;IAC7ChM,QAAQ,CAAC,EAAE,CAAC;IACZM,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MAAA,IAAA6L,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACF,MAAMC,MAAM,GAAG,oOAAoOjM,SAAS,iBAAiBR,KAAK,EAAE;MAEpR,MAAMkI,GAAG,GAAG,MAAMzL,KAAK,CAACiQ,IAAI,CAC1B,gGAAgG3I,OAAO,EAAE,EACzG;QACE4I,QAAQ,EAAE,CACR;UACEC,KAAK,EAAE,CAAC;YAAE9H,IAAI,EAAE2H;UAAO,CAAC;QAC1B,CAAC;MAEL,CAAC,EACD;QACEI,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CACF,CAAC;MAED,MAAMC,QAAQ,GACZ,EAAAV,oBAAA,GAAAlE,GAAG,CAACJ,IAAI,CAACiF,UAAU,cAAAX,oBAAA,wBAAAC,qBAAA,GAAnBD,oBAAA,CAAsB,CAAC,CAAC,cAAAC,qBAAA,wBAAAC,sBAAA,GAAxBD,qBAAA,CAA0BF,OAAO,cAAAG,sBAAA,wBAAAC,sBAAA,GAAjCD,sBAAA,CAAmCM,KAAK,cAAAL,sBAAA,wBAAAC,sBAAA,GAAxCD,sBAAA,CAA2C,CAAC,CAAC,cAAAC,sBAAA,uBAA7CA,sBAAA,CAA+C1H,IAAI,KACnD,yBAAyB;MAC3B,MAAMkI,UAAU,GAAG;QAAEd,IAAI,EAAE,KAAK;QAAEC,OAAO,EAAEW;MAAS,CAAC;MACrD3M,WAAW,CAAE6J,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEgD,UAAU,CAAC,CAAC;IAC9C,CAAC,CAAC,OAAO3E,KAAK,EAAE;MACdd,OAAO,CAACc,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzClI,WAAW,CAAE6J,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QAAEkC,IAAI,EAAE,KAAK;QAAEC,OAAO,EAAE,WAAW,GAAG9D,KAAK,CAAC4E;MAAQ,CAAC,CACtD,CAAC;IACJ,CAAC,SAAS;MACR1M,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;;EAEA;EACA,MAAM2M,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMhJ,QAAQ,CAAC3H,IAAI,CAAC4Q,OAAO,CAAC,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMxD,gBAAgB,GAAGA,CAACyD,GAAG,EAAEC,IAAI,GAAG,MAAM,KAAK;IAC/CrL,eAAe,CAAC;MAAEoL,GAAG;MAAEC;IAAK,CAAC,CAAC;IAC9BpK,UAAU,CAAC,MAAMjB,eAAe,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;EAC/C,CAAC;;EAED;EACA,MAAM4H,WAAW,GAAIwD,GAAG,IAAK;IAC3BlL,cAAc,CAACsF,GAAG,IAAI,CACpB;MAAE6F,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAAEJ;IAAI,CAAC,EACzD,GAAG5F,GAAG,CAAC4C,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CACpB,CAAC;EACJ,CAAC;;EAED;EACA,MAAMqD,UAAU,GAAIC,IAAI,IAAK;IAC3BzM,gBAAgB,CAAC+I,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP,CAAC0D,IAAI,GAAG,CAAC1D,IAAI,CAAC0D,IAAI;IACpB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA1R,SAAS,CAAC,MAAM;IACd,IAAI6G,UAAU,CAAC8K,OAAO,EAAE9K,UAAU,CAAC8K,OAAO,CAACC,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACnF,CAAC,EAAE,CAAC3N,QAAQ,EAAEI,OAAO,CAAC,CAAC;;EAEvB;EACA,MAAMwN,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,IAAI,GAAG,EAAE;IACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAMC,CAAC,GAAG,IAAIV,IAAI,CAAC,CAAC;MACpBU,CAAC,CAACC,OAAO,CAACD,CAAC,CAACE,OAAO,CAAC,CAAC,GAAGH,CAAC,CAAC;MAC1BD,IAAI,CAACK,IAAI,CAACH,CAAC,CAACI,kBAAkB,CAAC,CAAC,CAAC;IACnC;IACA,OAAON,IAAI;EACb,CAAC;EAED,MAAMO,WAAW,GAAGR,YAAY,CAAC,CAAC;EAClC,MAAMS,SAAS,GAAG;IAChBC,MAAM,EAAEF,WAAW;IACnBG,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,kBAAkB;MACzB5G,IAAI,EAAEwG,WAAW,CAACnP,GAAG,CAACwP,GAAG,IAAI1M,WAAW,CAACiI,MAAM,CAACqB,CAAC,IAAIA,CAAC,CAAC8B,IAAI,KAAK,UAAU,IAAI9B,CAAC,CAAC6B,GAAG,CAACwB,UAAU,CAAC,mBAAmB,CAAC,IAAI,IAAIrB,IAAI,CAAChC,CAAC,CAAC+B,IAAI,CAAC,CAACe,kBAAkB,CAAC,CAAC,KAAKM,GAAG,CAAC,CAACE,MAAM,CAAC;MAC7KnK,eAAe,EAAE;IACnB,CAAC,EACD;MACEgK,KAAK,EAAE,iBAAiB;MACxB5G,IAAI,EAAEwG,WAAW,CAACnP,GAAG,CAACwP,GAAG,IAAI1M,WAAW,CAACiI,MAAM,CAACqB,CAAC,IAAIA,CAAC,CAAC8B,IAAI,KAAK,UAAU,IAAI9B,CAAC,CAAC6B,GAAG,KAAK,8BAA8B,IAAI,IAAIG,IAAI,CAAChC,CAAC,CAAC+B,IAAI,CAAC,CAACe,kBAAkB,CAAC,CAAC,KAAKM,GAAG,CAAC,CAACE,MAAM,CAAC;MAChLnK,eAAe,EAAE;IACnB,CAAC;EAEL,CAAC;EAED,MAAMoK,YAAY,GAAG;IACnBC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE;MACPC,MAAM,EAAE;QAAEC,QAAQ,EAAE;MAAM,CAAC;MAC3BC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAK;IAC3B,CAAC;IACDC,MAAM,EAAE;MACNC,CAAC,EAAE;QAAEC,WAAW,EAAE,IAAI;QAAEC,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAE;MAAE;IACjD;EACF,CAAC;EAED,oBACE3Q,OAAA;IAAKmM,KAAK,EAAEhE,QAAQ,CAAC,cAAc,CAAE;IAAAyI,QAAA,gBAEnC5Q,OAAA;MAAKmM,KAAK,EAAEhE,QAAQ,CAAC,QAAQ,CAAE;MAAAyI,QAAA,gBAC7B5Q,OAAA;QACEmM,KAAK,EAAE;UACLrG,UAAU,EAAE,MAAM;UAClBM,MAAM,EAAE,MAAM;UACdL,KAAK,EAAE,OAAO;UAAE;UAChB8K,WAAW,EAAE,MAAM;UACnBC,MAAM,EAAE,SAAS;UACjBC,OAAO,EAAE,KAAK;UACdC,YAAY,EAAE,KAAK;UACnBpK,UAAU,EAAE;QACd,CAAE;QACFqK,OAAO,EAAEA,CAAA,KAAMhP,cAAc,CAAC,CAACD,WAAW,CAAE;QAC5CkP,YAAY,EAAGnH,CAAC,IAAKA,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACrG,UAAU,GAAG,0BAA2B;QAC5EqL,YAAY,EAAGpH,CAAC,IAAKA,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACrG,UAAU,GAAG,MAAO;QAAA8K,QAAA,EAEvD5O,WAAW,gBAAGhC,OAAA,CAAChC,GAAG;UAACoT,IAAI,EAAE;QAAG;UAAA5Q,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGX,OAAA,CAACjC,MAAM;UAACqT,IAAI,EAAE;QAAG;UAAA5Q,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eAETX,OAAA;QAAKmM,KAAK,EAAE;UAAEkF,IAAI,EAAE,CAAC;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAX,QAAA,gBAC7D5Q,OAAA;UACEwR,GAAG,EAAEC,OAAO,CAAC,kBAAkB,CAAE;UACjCC,GAAG,EAAC,YAAY;UAChBvF,KAAK,EAAE;YAAEwF,MAAM,EAAE,MAAM;YAAEd,WAAW,EAAE;UAAO;QAAE;UAAArQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACFX,OAAA;UAAA4Q,QAAA,gBACE5Q,OAAA;YACE,yBAAiB;YACjBmM,KAAK,EAAE;cAAEyF,UAAU,EAAE,GAAG;cAAEC,QAAQ,EAAE,MAAM;cAAE9L,KAAK,EAAE;YAAQ,CAAE;YAAA6K,QAAA,EAC9D;UAED;YAAApQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNX,OAAA;YACE,4BAAoB;YACpBmM,KAAK,EAAE;cAAE0F,QAAQ,EAAE,MAAM;cAAEC,OAAO,EAAE,GAAG;cAAE/L,KAAK,EAAE;YAAQ,CAAE;YAAA6K,QAAA,EAC3D;UAED;YAAApQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENX,OAAA;QAAKmM,KAAK,EAAE;UAAEmF,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEQ,GAAG,EAAE;QAAO,CAAE;QAAAnB,QAAA,EAChExO,IAAI,gBACHpC,OAAA,CAAAE,SAAA;UAAA0Q,QAAA,gBACE5Q,OAAA;YAAKmM,KAAK,EAAE;cACV6F,KAAK,EAAE,MAAM;cACbL,MAAM,EAAE,MAAM;cACdX,YAAY,EAAE,KAAK;cACnBlL,UAAU,EAAE,0BAA0B;cACtCwL,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBU,cAAc,EAAE,QAAQ;cACxBL,UAAU,EAAE,GAAG;cACfd,MAAM,EAAE,SAAS;cACjB/K,KAAK,EAAE,OAAO;cACdmM,cAAc,EAAE;YAClB,CAAE;YAAAtB,QAAA,EACCxO,IAAI,CAAC+P,KAAK,KAAKtP,WAAW,gBAAG7C,OAAA,CAACvB,QAAQ;cAAC2S,IAAI,EAAE,EAAG;cAACrL,KAAK,EAAC;YAAS;cAAAvF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAGyB,IAAI,CAAC+P,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;UAAC;YAAA5R,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC,eACNX,OAAA;YACEmM,KAAK,EAAE;cACL,GAAGhE,QAAQ,CAAC,eAAe,CAAC;cAC5BrC,UAAU,EAAE,0BAA0B;cACtCC,KAAK,EAAE,OAAO;cACdK,MAAM,EAAE,oCAAoC;cAC5C8L,cAAc,EAAE;YAClB,CAAE;YACFjB,OAAO,EAAE7C,YAAa;YAAAwC,QAAA,gBAEtB5Q,OAAA,CAACnB,QAAQ;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,WACd;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CAAC,gBAEHX,OAAA;UACEmM,KAAK,EAAE;YACL,GAAGhE,QAAQ,CAAC,eAAe,CAAC;YAC5BrC,UAAU,EAAE,0BAA0B;YACtCC,KAAK,EAAE,OAAO;YACdK,MAAM,EAAE,oCAAoC;YAC5C8L,cAAc,EAAE;UAClB,CAAE;UACFjB,OAAO,EAAEA,CAAA,KAAM;YAAExI,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;UAAE,CAAE;UAAAkI,QAAA,gBAEzE5Q,OAAA,CAACpB,OAAO;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNX,OAAA;MAAOmM,KAAK,EAAEhE,QAAQ,CAAC,SAAS,CAAE;MAAAyI,QAAA,eAChC5Q,OAAA;QAAKmM,KAAK,EAAE;UAAE4E,OAAO,EAAE;QAAO,CAAE;QAAAH,QAAA,EAC7BxQ,mBAAmB,CAACC,GAAG,CAAC,CAACC,IAAI,EAAE+R,KAAK,kBACnCrS,OAAA;UAAA4Q,QAAA,gBACE5Q,OAAA;YACEmM,KAAK,EAAE;cACL,GAAGhE,QAAQ,CAAC,aAAa,CAAC;cAC1B,IAAIvG,SAAS,KAAKtB,IAAI,CAACO,GAAG,GAAGsH,QAAQ,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC;cAChE2I,MAAM,EAAE,SAAS;cACjBlK,UAAU,EAAE;YACd,CAAE;YACFqK,OAAO,EAAEA,CAAA,KAAM;cACbpP,YAAY,CAACvB,IAAI,CAACO,GAAG,CAAC;cACtBoB,cAAc,CAAC,KAAK,CAAC;YACvB,CAAE;YACFiP,YAAY,EAAGnH,CAAC,IAAK;cACnB,IAAInI,SAAS,KAAKtB,IAAI,CAACO,GAAG,EAAE;gBAC1BkJ,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACrG,UAAU,GAAG,qBAAqB;gBACjDiE,CAAC,CAACE,MAAM,CAACkC,KAAK,CAAC1F,SAAS,GAAG,iBAAiB;gBAC5CsD,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACmG,UAAU,GAAG,aAAaxU,YAAY,CAAC+H,YAAY,CAACgB,OAAO,EAAE;cAC9E;YACF,CAAE;YACFsK,YAAY,EAAGpH,CAAC,IAAK;cACnB,IAAInI,SAAS,KAAKtB,IAAI,CAACO,GAAG,EAAE;gBAC1BkJ,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACrG,UAAU,GAAGhI,YAAY,CAAC+H,YAAY,CAACU,OAAO;gBAC7DwD,CAAC,CAACE,MAAM,CAACkC,KAAK,CAAC1F,SAAS,GAAG,eAAe;gBAC1CsD,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACmG,UAAU,GAAG,uBAAuB;cACrD;YACF,CAAE;YAAA1B,QAAA,gBAEF5Q,OAAA;cAAKmM,KAAK,EAAE;gBAAE0E,WAAW,EAAE;cAAO,CAAE;cAAAD,QAAA,EAAEtQ,IAAI,CAACM;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtDX,OAAA;cAAMmM,KAAK,EAAE;gBAAEkF,IAAI,EAAE;cAAE,CAAE;cAAAT,QAAA,EAAEtQ,IAAI,CAACQ;YAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC5CL,IAAI,CAACiS,QAAQ,CAACxC,MAAM,GAAG,CAAC,iBACvB/P,OAAA;cAAKiR,OAAO,EAAGlH,CAAC,IAAK;gBACnBA,CAAC,CAAC6B,eAAe,CAAC,CAAC;gBACnB+C,UAAU,CAACrO,IAAI,CAACQ,KAAK,CAAC;cACxB,CAAE;cAAA8P,QAAA,EACC1O,aAAa,CAAC5B,IAAI,CAACQ,KAAK,CAAC,gBAAGd,OAAA,CAAC/B,aAAa;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGX,OAAA,CAAC9B,cAAc;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAELL,IAAI,CAACiS,QAAQ,CAACxC,MAAM,GAAG,CAAC,IAAI7N,aAAa,CAAC5B,IAAI,CAACQ,KAAK,CAAC,iBACpDd,OAAA;YAAKmM,KAAK,EAAE;cAAEjF,UAAU,EAAE;YAAO,CAAE;YAAA0J,QAAA,EAChCtQ,IAAI,CAACiS,QAAQ,CAAClS,GAAG,CAAC,CAACmS,OAAO,EAAEC,QAAQ,kBACnCzS,OAAA;cAEEmM,KAAK,EAAE;gBACL,GAAGhE,QAAQ,CAAC,aAAa,CAAC;gBAC1B4I,OAAO,EAAE,mBAAmB;gBAC5Bc,QAAQ,EAAE,MAAM;gBAChBC,OAAO,EAAE;cACX,CAAE;cACFb,OAAO,EAAEA,CAAA,KAAM;gBACbpP,YAAY,CAACvB,IAAI,CAACO,GAAG,CAAC;gBACtBoB,cAAc,CAAC,KAAK,CAAC;cACvB,CAAE;cACFiP,YAAY,EAAGnH,CAAC,IAAK;gBACnBA,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACrG,UAAU,GAAG,qBAAqB;gBACjDiE,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACuG,WAAW,GAAG,MAAM;gBACnC3I,CAAC,CAACE,MAAM,CAACkC,KAAK,CAAC2F,OAAO,GAAG,GAAG;cAC9B,CAAE;cACFX,YAAY,EAAGpH,CAAC,IAAK;gBACnBA,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACrG,UAAU,GAAGhI,YAAY,CAAC+H,YAAY,CAACU,OAAO;gBAC7DwD,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACuG,WAAW,GAAG,MAAM;gBACnC3I,CAAC,CAACE,MAAM,CAACkC,KAAK,CAAC2F,OAAO,GAAG,KAAK;cAChC,CAAE;cAAAlB,QAAA,EAED4B,OAAO,CAAC1R;YAAK,GAtBT2R,QAAQ;cAAAjS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuBV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA,GArEO0R,KAAK;UAAA7R,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsEV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRX,OAAA;MAAMmM,KAAK,EAAEhE,QAAQ,CAAC,aAAa,CAAE;MAAAyI,QAAA,GAElC5O,WAAW,IAAIyB,MAAM,CAACC,UAAU,GAAG,GAAG,iBACrC1D,OAAA;QACEmM,KAAK,EAAE;UACLiE,QAAQ,EAAE,OAAO;UACjBuC,GAAG,EAAE,MAAM;UACXC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTlN,eAAe,EAAE,iBAAiB;UAClCmN,MAAM,EAAE;QACV,CAAE;QACF9B,OAAO,EAAEA,CAAA,KAAMhP,cAAc,CAAC,KAAK;MAAE;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CACF,EAGAiB,SAAS,KAAK,WAAW,iBACxB5B,OAAA;QAAKmM,KAAK,EAAE;UACV4E,OAAO,EAAE,WAAW;UACpBjL,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACmN,aAAa;UACnD7L,SAAS,EAAE,OAAO;UAClBiJ,QAAQ,EAAE,UAAU;UACpB6C,QAAQ,EAAE;QACZ,CAAE;QAAArC,QAAA,gBAEA5Q,OAAA;UAAKmM,KAAK,EAAE;YACViE,QAAQ,EAAE,UAAU;YACpBuC,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTI,eAAe,EAAE;AAC/B,qDAAqDpV,YAAY,CAAC+H,YAAY,CAACgB,OAAO;AACtF,qDAAqD/I,YAAY,CAAC+H,YAAY,CAACgB,OAAO;AACtF,qDAAqD/I,YAAY,CAAC+H,YAAY,CAACgB,OAAO;AACtF,eAAe;YACDkM,MAAM,EAAE;UACV;QAAE;UAAAvS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGLX,OAAA;UAAKmM,KAAK,EAAE;YACViE,QAAQ,EAAE,UAAU;YACpBuC,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTI,eAAe,EAAE;AAC/B,kCAAkCpV,YAAY,CAAC+H,YAAY,CAACO,MAAM;AAClE,yCAAyCtI,YAAY,CAAC+H,YAAY,CAACO,MAAM;AACzE,eAAe;YACD+M,cAAc,EAAE,WAAW;YAC3BrB,OAAO,EAAE,GAAG;YACZiB,MAAM,EAAE;UACV;QAAE;UAAAvS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAELX,OAAA;UAAKmM,KAAK,EAAE;YAAEiH,QAAQ,EAAE,QAAQ;YAAEC,MAAM,EAAE,QAAQ;YAAEjD,QAAQ,EAAE,UAAU;YAAE2C,MAAM,EAAE;UAAE,CAAE;UAAAnC,QAAA,gBAGpF5Q,OAAA;YAAKmM,KAAK,EAAE;cACVrG,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACU,OAAO;cAC7C2L,cAAc,EAAE,YAAY;cAC5BlB,YAAY,EAAE,MAAM;cACpBD,OAAO,EAAE,MAAM;cACfuC,YAAY,EAAE,MAAM;cACpBlD,QAAQ,EAAE,UAAU;cACpB6C,QAAQ,EAAE,QAAQ;cAClB7M,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACO,MAAM,EAAE;cACvDkB,SAAS,EAAE,eAAexJ,YAAY,CAAC+H,YAAY,CAAC0B,MAAM;YAC5D,CAAE;YAAAqJ,QAAA,gBAEA5Q,OAAA;cAAKmM,KAAK,EAAE;gBACViE,QAAQ,EAAE,UAAU;gBACpBuC,GAAG,EAAE,CAAC;gBACNC,IAAI,EAAE,CAAC;gBACPC,KAAK,EAAE,CAAC;gBACRlB,MAAM,EAAE,KAAK;gBACb7L,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAAC0N;cACxC;YAAE;cAAA/S,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAELX,OAAA;cAAKmM,KAAK,EAAE;gBACVmF,OAAO,EAAE,MAAM;gBACfkC,mBAAmB,EAAE,eAAe;gBACpCzB,GAAG,EAAE,MAAM;gBACXR,UAAU,EAAE,QAAQ;gBACpBnB,QAAQ,EAAE,UAAU;gBACpB2C,MAAM,EAAE;cACV,CAAE;cAAAnC,QAAA,gBAEA5Q,OAAA;gBAAKmM,KAAK,EAAE;kBACV6F,KAAK,EAAE,OAAO;kBACdL,MAAM,EAAE,OAAO;kBACfX,YAAY,EAAE,MAAM;kBACpBlL,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAAC0N,QAAQ;kBAC9CjC,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBU,cAAc,EAAE,QAAQ;kBACxBJ,QAAQ,EAAE,QAAQ;kBAClBD,UAAU,EAAE,MAAM;kBAClB7L,KAAK,EAAE,OAAO;kBACduB,SAAS,EAAE,eAAexJ,YAAY,CAAC+H,YAAY,CAAC0B,MAAM,EAAE;kBAC5DnB,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACU,OAAO;gBACxD,CAAE;gBAAAqK,QAAA,EACCxO,IAAI,GAAGA,IAAI,CAAC+P,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG;cAAI;gBAAA5R,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eAGNX,OAAA;gBAAA4Q,QAAA,gBACE5Q,OAAA;kBAAImM,KAAK,EAAE;oBACTkH,MAAM,EAAE,CAAC;oBACTxB,QAAQ,EAAE,QAAQ;oBAClBD,UAAU,EAAE,GAAG;oBACf0B,YAAY,EAAE,QAAQ;oBACtBvN,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG,IAAI;oBACrCyN,UAAU,EAAE;kBACd,CAAE;kBAAA7C,QAAA,GAAC,gBACa,EAACxO,IAAI,GAAGA,IAAI,CAAC+P,KAAK,CAACuB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,EAAC,gBAC1D;gBAAA;kBAAAlT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLX,OAAA;kBAAGmM,KAAK,EAAE;oBACRkH,MAAM,EAAE,CAAC;oBACTxB,QAAQ,EAAE,QAAQ;oBAClB9L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAAC8N,SAAS;oBAC1C/B,UAAU,EAAE,GAAG;oBACf0B,YAAY,EAAE;kBAChB,CAAE;kBAAA1C,QAAA,EAAC;gBAEH;kBAAApQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAGJX,OAAA;kBAAKmM,KAAK,EAAE;oBACVmF,OAAO,EAAE,MAAM;oBACfS,GAAG,EAAE,MAAM;oBACX6B,QAAQ,EAAE;kBACZ,CAAE;kBAAAhD,QAAA,gBACA5Q,OAAA;oBAAKmM,KAAK,EAAE;sBACVrG,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACgB,OAAO,GAAG,IAAI;sBACpDd,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACgB,OAAO;sBACxCkK,OAAO,EAAE,aAAa;sBACtBC,YAAY,EAAE,MAAM;sBACpBa,QAAQ,EAAE,QAAQ;sBAClBD,UAAU,EAAE,GAAG;sBACfxL,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACgB,OAAO;oBACxD,CAAE;oBAAA+J,QAAA,EAAC;kBAEH;oBAAApQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNX,OAAA;oBAAKmM,KAAK,EAAE;sBACVrG,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACgB,OAAO,GAAG,IAAI;sBACpDd,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACgB,OAAO;sBACxCkK,OAAO,EAAE,aAAa;sBACtBC,YAAY,EAAE,MAAM;sBACpBa,QAAQ,EAAE,QAAQ;sBAClBD,UAAU,EAAE,GAAG;sBACfxL,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACgB,OAAO;oBACxD,CAAE;oBAAA+J,QAAA,EAAC;kBAEH;oBAAApQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNX,OAAA;gBAAKmM,KAAK,EAAE;kBACV0H,SAAS,EAAE,QAAQ;kBACnB9C,OAAO,EAAE,MAAM;kBACfjL,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACiC,SAAS;kBAC/CkJ,YAAY,EAAE,MAAM;kBACpB5K,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACO,MAAM;gBACvD,CAAE;gBAAAwK,QAAA,gBACA5Q,OAAA;kBAAKmM,KAAK,EAAE;oBACV0F,QAAQ,EAAE,MAAM;oBAChBD,UAAU,EAAE,GAAG;oBACf7L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACgB,OAAO;oBACxCyM,YAAY,EAAE;kBAChB,CAAE;kBAAA1C,QAAA,EACC,IAAInC,IAAI,CAAC,CAAC,CAACc,kBAAkB,CAAC,OAAO,EAAE;oBAAEM,GAAG,EAAE;kBAAU,CAAC;gBAAC;kBAAArP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC,eACNX,OAAA;kBAAKmM,KAAK,EAAE;oBACV0F,QAAQ,EAAE,QAAQ;oBAClB9L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAAC8N,SAAS;oBAC1C/B,UAAU,EAAE;kBACd,CAAE;kBAAAhB,QAAA,EACC,IAAInC,IAAI,CAAC,CAAC,CAACc,kBAAkB,CAAC,OAAO,EAAE;oBAAEuE,KAAK,EAAE,OAAO;oBAAEC,IAAI,EAAE;kBAAU,CAAC;gBAAC;kBAAAvT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNX,OAAA;YAAKmM,KAAK,EAAE;cACVmF,OAAO,EAAE,MAAM;cACfkC,mBAAmB,EAAE,sCAAsC;cAC3DzB,GAAG,EAAE,QAAQ;cACbuB,YAAY,EAAE;YAChB,CAAE;YAAA1C,QAAA,EACC,CACC;cACE9P,KAAK,EAAE,mBAAmB;cAC1BkT,KAAK,EAAE,IAAI;cACXC,IAAI,EAAE,GAAG;cACTrT,IAAI,EAAE,IAAI;cACVsT,KAAK,EAAE,MAAM;cACb1O,WAAW,EAAE,YAAY;cACzBO,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACgB;YACnC,CAAC,EACD;cACE/F,KAAK,EAAE,aAAa;cACpBkT,KAAK,EAAE,IAAI;cACXC,IAAI,EAAE,KAAK;cACXrT,IAAI,EAAE,IAAI;cACVsT,KAAK,EAAE,QAAQ;cACf1O,WAAW,EAAE,WAAW;cACxBO,KAAK,EAAE;YACT,CAAC,EACD;cACEjF,KAAK,EAAE,iBAAiB;cACxBkT,KAAK,EAAE,IAAI;cACXC,IAAI,EAAE,OAAO;cACbrT,IAAI,EAAE,GAAG;cACTsT,KAAK,EAAE,IAAI;cACX1O,WAAW,EAAE,WAAW;cACxBO,KAAK,EAAE;YACT,CAAC,EACD;cACEjF,KAAK,EAAE,cAAc;cACrBkT,KAAK,EAAE,KAAK;cACZC,IAAI,EAAE,MAAM;cACZrT,IAAI,EAAE,GAAG;cACTsT,KAAK,EAAE,MAAM;cACb1O,WAAW,EAAE,SAAS;cACtBO,KAAK,EAAE;YACT,CAAC,CACF,CAAC1F,GAAG,CAAC,CAAC8T,IAAI,EAAE9B,KAAK,kBAChBrS,OAAA;cAAiBmM,KAAK,EAAE;gBACtBrG,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACU,OAAO;gBAC7CyK,YAAY,EAAE,MAAM;gBACpBD,OAAO,EAAE,QAAQ;gBACjBX,QAAQ,EAAE,UAAU;gBACpB6C,QAAQ,EAAE,QAAQ;gBAClB7M,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACO,MAAM,EAAE;gBACvD0K,MAAM,EAAE,SAAS;gBACjBlK,UAAU,EAAE,eAAe;gBAC3BU,SAAS,EAAE,cAAcxJ,YAAY,CAAC+H,YAAY,CAAC0B,MAAM;cAC3D,CAAE;cACF2J,YAAY,EAAGnH,CAAC,IAAK;gBACnBA,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAAC1F,SAAS,GAAG,kBAAkB;gBACpDsD,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAAC7E,SAAS,GAAG,cAAcxJ,YAAY,CAAC+H,YAAY,CAAC0B,MAAM,EAAE;cACpF,CAAE;cACF4J,YAAY,EAAGpH,CAAC,IAAK;gBACnBA,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAAC1F,SAAS,GAAG,eAAe;gBACjDsD,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAAC7E,SAAS,GAAG,cAAcxJ,YAAY,CAAC+H,YAAY,CAAC0B,MAAM,EAAE;cACpF,CAAE;cAAAqJ,QAAA,gBAGA5Q,OAAA;gBAAKmM,KAAK,EAAE;kBACViE,QAAQ,EAAE,UAAU;kBACpBuC,GAAG,EAAE,CAAC;kBACNC,IAAI,EAAE,CAAC;kBACPC,KAAK,EAAE,CAAC;kBACRlB,MAAM,EAAE,KAAK;kBACb7L,UAAU,EAAEqO,IAAI,CAACpO;gBACnB;cAAE;gBAAAvF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAELX,OAAA;gBAAKmM,KAAK,EAAE;kBAAEiE,QAAQ,EAAE,UAAU;kBAAE2C,MAAM,EAAE;gBAAE,CAAE;gBAAAnC,QAAA,gBAE9C5Q,OAAA;kBAAKmM,KAAK,EAAE;oBACVmF,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBU,cAAc,EAAE,eAAe;oBAC/BqB,YAAY,EAAE;kBAChB,CAAE;kBAAA1C,QAAA,gBACA5Q,OAAA;oBAAKmM,KAAK,EAAE;sBACV0F,QAAQ,EAAE,QAAQ;sBAClBD,UAAU,EAAE,GAAG;sBACf7L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAAC8N,SAAS;sBAC1CU,aAAa,EAAE,WAAW;sBAC1BC,aAAa,EAAE;oBACjB,CAAE;oBAAA1D,QAAA,EACCuD,IAAI,CAACrT;kBAAK;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,eACNX,OAAA;oBAAKmM,KAAK,EAAE;sBACV0F,QAAQ,EAAE,QAAQ;sBAClBC,OAAO,EAAE;oBACX,CAAE;oBAAAlB,QAAA,EACCuD,IAAI,CAACvT;kBAAI;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNX,OAAA;kBAAKmM,KAAK,EAAE;oBACVmF,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,UAAU;oBACtBQ,GAAG,EAAE,QAAQ;oBACbuB,YAAY,EAAE;kBAChB,CAAE;kBAAA1C,QAAA,gBACA5Q,OAAA;oBAAKmM,KAAK,EAAE;sBACV0F,QAAQ,EAAE,QAAQ;sBAClBD,UAAU,EAAE,GAAG;sBACf7L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG,IAAI;sBACrCyN,UAAU,EAAE;oBACd,CAAE;oBAAA7C,QAAA,EACCuD,IAAI,CAACH;kBAAK;oBAAAxT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,eACNX,OAAA;oBAAKmM,KAAK,EAAE;sBACV0F,QAAQ,EAAE,MAAM;sBAChBD,UAAU,EAAE,GAAG;sBACf7L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAAC8N;oBACnC,CAAE;oBAAA/C,QAAA,EACCuD,IAAI,CAACF;kBAAI;oBAAAzT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNX,OAAA;kBAAKmM,KAAK,EAAE;oBACVmF,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBU,cAAc,EAAE;kBAClB,CAAE;kBAAArB,QAAA,gBACA5Q,OAAA;oBAAKmM,KAAK,EAAE;sBACV0F,QAAQ,EAAE,QAAQ;sBAClB9L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAAC8N;oBACnC,CAAE;oBAAA/C,QAAA,EACCuD,IAAI,CAAC3O;kBAAW;oBAAAhF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC,eACNX,OAAA;oBAAKmM,KAAK,EAAE;sBACV0F,QAAQ,EAAE,QAAQ;sBAClBD,UAAU,EAAE,GAAG;sBACf7L,KAAK,EAAEoO,IAAI,CAACpO,KAAK;sBACjBD,UAAU,EAAEqO,IAAI,CAACpO,KAAK,GAAG,IAAI;sBAC7BgL,OAAO,EAAE,gBAAgB;sBACzBC,YAAY,EAAE;oBAChB,CAAE;oBAAAJ,QAAA,EACCuD,IAAI,CAACD;kBAAK;oBAAA1T,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GAtGE0R,KAAK;cAAA7R,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuGV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNX,OAAA;YAAKmM,KAAK,EAAE;cACVrG,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACU,OAAO;cAC7CyK,YAAY,EAAE,MAAM;cACpBD,OAAO,EAAE,MAAM;cACfuC,YAAY,EAAE,MAAM;cACpBlN,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACO,MAAM,EAAE;cACvDkB,SAAS,EAAE,cAAcxJ,YAAY,CAAC+H,YAAY,CAAC0B,MAAM,EAAE;cAC3D6I,QAAQ,EAAE,UAAU;cACpB6C,QAAQ,EAAE;YACZ,CAAE;YAAArC,QAAA,gBAEA5Q,OAAA;cAAKmM,KAAK,EAAE;gBACViE,QAAQ,EAAE,UAAU;gBACpBuC,GAAG,EAAE,CAAC;gBACNC,IAAI,EAAE,CAAC;gBACPC,KAAK,EAAE,CAAC;gBACRlB,MAAM,EAAE,KAAK;gBACb7L,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAAC0N;cACxC;YAAE;cAAA/S,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAELX,OAAA;cAAImM,KAAK,EAAE;gBACTkH,MAAM,EAAE,YAAY;gBACpBxB,QAAQ,EAAE,QAAQ;gBAClBD,UAAU,EAAE,GAAG;gBACf7L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG,IAAI;gBACrC6N,SAAS,EAAE;cACb,CAAE;cAAAjD,QAAA,EAAC;YAEH;cAAApQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAELX,OAAA;cAAKmM,KAAK,EAAE;gBACVmF,OAAO,EAAE,MAAM;gBACfkC,mBAAmB,EAAE,sCAAsC;gBAC3DzB,GAAG,EAAE;cACP,CAAE;cAAAnB,QAAA,EACC,CACC;gBACEhQ,IAAI,EAAE,IAAI;gBACVE,KAAK,EAAE,iBAAiB;gBACxByT,QAAQ,EAAE,kBAAkB;gBAC5BC,IAAI,EAAE,mCAAmC;gBACzCC,MAAM,EAAEA,CAAA,KAAM5S,YAAY,CAAC,SAAS,CAAC;gBACrCkE,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACgB;cACnC,CAAC,EACD;gBACEjG,IAAI,EAAE,IAAI;gBACVE,KAAK,EAAE,iBAAiB;gBACxByT,QAAQ,EAAE,cAAc;gBACxBC,IAAI,EAAE,oCAAoC;gBAC1CC,MAAM,EAAEA,CAAA,KAAM5S,YAAY,CAAC,KAAK,CAAC;gBACjCkE,KAAK,EAAE;cACT,CAAC,EACD;gBACEnF,IAAI,EAAE,IAAI;gBACVE,KAAK,EAAE,eAAe;gBACtByT,QAAQ,EAAE,oBAAoB;gBAC9BC,IAAI,EAAE,kCAAkC;gBACxCC,MAAM,EAAEA,CAAA,KAAM5S,YAAY,CAAC,QAAQ,CAAC;gBACpCkE,KAAK,EAAE;cACT,CAAC,EACD;gBACEnF,IAAI,EAAE,IAAI;gBACVE,KAAK,EAAE,iBAAiB;gBACxByT,QAAQ,EAAE,oBAAoB;gBAC9BC,IAAI,EAAE,gCAAgC;gBACtCC,MAAM,EAAEA,CAAA,KAAM5S,YAAY,CAAC,WAAW,CAAC;gBACvCkE,KAAK,EAAE;cACT,CAAC,CACF,CAAC1F,GAAG,CAAC,CAACoU,MAAM,EAAEpC,KAAK,kBAClBrS,OAAA;gBACEiR,OAAO,EAAEwD,MAAM,CAACA,MAAO;gBACvBtI,KAAK,EAAE;kBACLrG,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACU,OAAO;kBAC7CyK,YAAY,EAAE,MAAM;kBACpBD,OAAO,EAAE,QAAQ;kBACjBD,MAAM,EAAE,SAAS;kBACjBlK,UAAU,EAAE,eAAe;kBAC3BR,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACO,MAAM,EAAE;kBACvDgK,QAAQ,EAAE,UAAU;kBACpB6C,QAAQ,EAAE,QAAQ;kBAClB3L,SAAS,EAAE,cAAcxJ,YAAY,CAAC+H,YAAY,CAAC0B,MAAM;gBAC3D,CAAE;gBACF2J,YAAY,EAAGnH,CAAC,IAAK;kBACnBA,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAAC1F,SAAS,GAAG,kBAAkB;kBACpDsD,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAAC7E,SAAS,GAAG,cAAcxJ,YAAY,CAAC+H,YAAY,CAAC0B,MAAM,EAAE;kBAClFwC,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAACzE,WAAW,GAAG+M,MAAM,CAAC1O,KAAK;gBAClD,CAAE;gBACFoL,YAAY,EAAGpH,CAAC,IAAK;kBACnBA,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAAC1F,SAAS,GAAG,eAAe;kBACjDsD,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAAC7E,SAAS,GAAG,cAAcxJ,YAAY,CAAC+H,YAAY,CAAC0B,MAAM,EAAE;kBAClFwC,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAACzE,WAAW,GAAG5J,YAAY,CAAC+H,YAAY,CAACO,MAAM;gBACtE,CAAE;gBAAAwK,QAAA,gBAGF5Q,OAAA;kBAAKmM,KAAK,EAAE;oBACViE,QAAQ,EAAE,UAAU;oBACpBuC,GAAG,EAAE,CAAC;oBACNC,IAAI,EAAE,CAAC;oBACPC,KAAK,EAAE,CAAC;oBACRlB,MAAM,EAAE,KAAK;oBACb7L,UAAU,EAAE2O,MAAM,CAAC1O;kBACrB;gBAAE;kBAAAvF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAELX,OAAA;kBAAKmM,KAAK,EAAE;oBAAEiE,QAAQ,EAAE,UAAU;oBAAE2C,MAAM,EAAE,CAAC;oBAAEc,SAAS,EAAE;kBAAS,CAAE;kBAAAjD,QAAA,gBACnE5Q,OAAA;oBAAKmM,KAAK,EAAE;sBACV0F,QAAQ,EAAE,QAAQ;sBAClByB,YAAY,EAAE,MAAM;sBACpBxB,OAAO,EAAE;oBACX,CAAE;oBAAAlB,QAAA,EACC6D,MAAM,CAAC7T;kBAAI;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eAENX,OAAA;oBAAKmM,KAAK,EAAE;sBACV0F,QAAQ,EAAE,QAAQ;sBAClBD,UAAU,EAAE,GAAG;sBACf7L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG,IAAI;sBACrCsN,YAAY,EAAE;oBAChB,CAAE;oBAAA1C,QAAA,EACC6D,MAAM,CAAC3T;kBAAK;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eAENX,OAAA;oBAAKmM,KAAK,EAAE;sBACV0F,QAAQ,EAAE,QAAQ;sBAClB9L,KAAK,EAAE0O,MAAM,CAAC1O,KAAK;sBACnB6L,UAAU,EAAE,GAAG;sBACf0B,YAAY,EAAE;oBAChB,CAAE;oBAAA1C,QAAA,EACC6D,MAAM,CAACF;kBAAQ;oBAAA/T,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC,eAENX,OAAA;oBAAKmM,KAAK,EAAE;sBACV0F,QAAQ,EAAE,SAAS;sBACnB9L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAAC8N,SAAS;sBAC1CF,UAAU,EAAE;oBACd,CAAE;oBAAA7C,QAAA,EACC6D,MAAM,CAACD;kBAAI;oBAAAhU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GApEE0R,KAAK;gBAAA7R,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAqEV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNX,OAAA;YAAKmM,KAAK,EAAE;cACVmF,OAAO,EAAE,MAAM;cACfkC,mBAAmB,EAAE,sCAAsC;cAC3DzB,GAAG,EAAE,MAAM;cACXuB,YAAY,EAAE;YAChB,CAAE;YAAA1C,QAAA,gBAEA5Q,OAAA;cAAKmM,KAAK,EAAE;gBACVrG,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACU,OAAO;gBAC7CyK,YAAY,EAAE,MAAM;gBACpBD,OAAO,EAAE,QAAQ;gBACjBzJ,SAAS,EAAE,cAAcxJ,YAAY,CAAC+H,YAAY,CAAC0B,MAAM,EAAE;gBAC3DnB,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACO,MAAM;cACvD,CAAE;cAAAwK,QAAA,gBACA5Q,OAAA;gBAAImM,KAAK,EAAE;kBACTkH,MAAM,EAAE,cAAc;kBACtBxB,QAAQ,EAAE,QAAQ;kBAClBD,UAAU,EAAE,GAAG;kBACf7L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG,IAAI;kBACrCsL,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBQ,GAAG,EAAE;gBACP,CAAE;gBAAAnB,QAAA,EAAC;cAEH;gBAAApQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAGLX,OAAA;gBAAKmM,KAAK,EAAE;kBAAEmH,YAAY,EAAE;gBAAO,CAAE;gBAAA1C,QAAA,EAClC,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAACvQ,GAAG,CAAC,CAACwP,GAAG,EAAEwC,KAAK,kBAChErS,OAAA;kBAAemM,KAAK,EAAE;oBACpBmF,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBU,cAAc,EAAE,eAAe;oBAC/BqB,YAAY,EAAE,SAAS;oBACvBvC,OAAO,EAAE,QAAQ;oBACjBC,YAAY,EAAE,KAAK;oBACnBlL,UAAU,EAAEuM,KAAK,GAAG,CAAC,GAAGvU,YAAY,CAAC+H,YAAY,CAACiC,SAAS,GAAG;kBAChE,CAAE;kBAAA8I,QAAA,gBACA5Q,OAAA;oBAAMmM,KAAK,EAAE;sBACX0F,QAAQ,EAAE,QAAQ;sBAClBD,UAAU,EAAE,GAAG;sBACf7L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG,IAAI;sBACrC0O,QAAQ,EAAE;oBACZ,CAAE;oBAAA9D,QAAA,EACCf;kBAAG;oBAAArP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACPX,OAAA;oBAAKmM,KAAK,EAAE;sBACVkF,IAAI,EAAE,CAAC;sBACPM,MAAM,EAAE,KAAK;sBACb7L,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACO,MAAM;sBAC5C4K,YAAY,EAAE,KAAK;sBACnBqC,MAAM,EAAE,QAAQ;sBAChBJ,QAAQ,EAAE;oBACZ,CAAE;oBAAArC,QAAA,eACA5Q,OAAA;sBAAKmM,KAAK,EAAE;wBACVwF,MAAM,EAAE,MAAM;wBACdK,KAAK,EAAE,GAAG2C,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;wBACpC9O,UAAU,EAAEuM,KAAK,GAAG,CAAC,GAAGvU,YAAY,CAAC+H,YAAY,CAACgB,OAAO,GAAG/I,YAAY,CAAC+H,YAAY,CAACO,MAAM;wBAC5F4K,YAAY,EAAE,KAAK;wBACnBpK,UAAU,EAAE;sBACd;oBAAE;sBAAApG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACNX,OAAA;oBAAMmM,KAAK,EAAE;sBACX0F,QAAQ,EAAE,QAAQ;sBAClB9L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAAC8N,SAAS;sBAC1Ce,QAAQ,EAAE,MAAM;sBAChBb,SAAS,EAAE;oBACb,CAAE;oBAAAjD,QAAA,EACCyB,KAAK,GAAG,CAAC,GAAG,GAAGsC,IAAI,CAACE,KAAK,CAACF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;kBAAG;oBAAApU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC;gBAAA,GAxCCkP,GAAG;kBAAArP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyCR,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNX,OAAA;cAAKmM,KAAK,EAAE;gBACVrG,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACU,OAAO;gBAC7CyK,YAAY,EAAE,MAAM;gBACpBD,OAAO,EAAE,QAAQ;gBACjBzJ,SAAS,EAAE,cAAcxJ,YAAY,CAAC+H,YAAY,CAAC0B,MAAM,EAAE;gBAC3DnB,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACO,MAAM;cACvD,CAAE;cAAAwK,QAAA,gBACA5Q,OAAA;gBAAImM,KAAK,EAAE;kBACTkH,MAAM,EAAE,cAAc;kBACtBxB,QAAQ,EAAE,QAAQ;kBAClBD,UAAU,EAAE,GAAG;kBACf7L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG,IAAI;kBACrCsL,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBQ,GAAG,EAAE;gBACP,CAAE;gBAAAnB,QAAA,EAAC;cAEH;gBAAApQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAELX,OAAA;gBAAKmM,KAAK,EAAE;kBAAEmF,OAAO,EAAE,MAAM;kBAAEwD,aAAa,EAAE,QAAQ;kBAAE/C,GAAG,EAAE;gBAAO,CAAE;gBAAAnB,QAAA,EACnE,CACC;kBAAE9P,KAAK,EAAE,yBAAyB;kBAAEiU,QAAQ,EAAE,EAAE;kBAAEC,MAAM,EAAE;gBAAc,CAAC,EACzE;kBAAElU,KAAK,EAAE,sBAAsB;kBAAEiU,QAAQ,EAAE,GAAG;kBAAEC,MAAM,EAAE;gBAAY,CAAC,EACrE;kBAAElU,KAAK,EAAE,uBAAuB;kBAAEiU,QAAQ,EAAE,EAAE;kBAAEC,MAAM,EAAE;gBAAc,CAAC,CACxE,CAAC3U,GAAG,CAAC,CAAC4U,IAAI,EAAE5C,KAAK,kBAChBrS,OAAA;kBAAiBmM,KAAK,EAAE;oBACtB4E,OAAO,EAAE,MAAM;oBACfC,YAAY,EAAE,MAAM;oBACpBlL,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACiC,SAAS;oBAC/C1B,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACO,MAAM;kBACvD,CAAE;kBAAAwK,QAAA,gBACA5Q,OAAA;oBAAKmM,KAAK,EAAE;sBACVmF,OAAO,EAAE,MAAM;sBACfW,cAAc,EAAE,eAAe;sBAC/BV,UAAU,EAAE,QAAQ;sBACpB+B,YAAY,EAAE;oBAChB,CAAE;oBAAA1C,QAAA,gBACA5Q,OAAA;sBAAMmM,KAAK,EAAE;wBACX0F,QAAQ,EAAE,QAAQ;wBAClBD,UAAU,EAAE,GAAG;wBACf7L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG;sBACnC,CAAE;sBAAA4K,QAAA,EACCqE,IAAI,CAACnU;oBAAK;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC,eACPX,OAAA;sBAAMmM,KAAK,EAAE;wBACX0F,QAAQ,EAAE,QAAQ;wBAClBD,UAAU,EAAE,GAAG;wBACf7L,KAAK,EAAEkP,IAAI,CAACF,QAAQ,KAAK,GAAG,GAAG,SAAS,GAAGjX,YAAY,CAAC+H,YAAY,CAACgB,OAAO;wBAC5Ef,UAAU,EAAEmP,IAAI,CAACF,QAAQ,KAAK,GAAG,GAAG,WAAW,GAAGjX,YAAY,CAAC+H,YAAY,CAACgB,OAAO,GAAG,IAAI;wBAC1FkK,OAAO,EAAE,gBAAgB;wBACzBC,YAAY,EAAE;sBAChB,CAAE;sBAAAJ,QAAA,EACCqE,IAAI,CAACD;oBAAM;sBAAAxU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNX,OAAA;oBAAKmM,KAAK,EAAE;sBACVwF,MAAM,EAAE,KAAK;sBACb7L,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACO,MAAM;sBAC5C4K,YAAY,EAAE,KAAK;sBACnBiC,QAAQ,EAAE;oBACZ,CAAE;oBAAArC,QAAA,eACA5Q,OAAA;sBAAKmM,KAAK,EAAE;wBACVwF,MAAM,EAAE,MAAM;wBACdK,KAAK,EAAE,GAAGiD,IAAI,CAACF,QAAQ,GAAG;wBAC1BjP,UAAU,EAAEmP,IAAI,CAACF,QAAQ,KAAK,GAAG,GAAG,SAAS,GAAGjX,YAAY,CAAC+H,YAAY,CAACgB,OAAO;wBACjFmK,YAAY,EAAE,KAAK;wBACnBpK,UAAU,EAAE;sBACd;oBAAE;sBAAApG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA,GA3CE0R,KAAK;kBAAA7R,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA4CV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNX,OAAA;YAAKmM,KAAK,EAAE;cACVmF,OAAO,EAAE,MAAM;cACfkC,mBAAmB,EAAE,SAAS;cAC9BzB,GAAG,EAAE;YACP,CAAE;YAAAnB,QAAA,gBAGA5Q,OAAA;cAAKmM,KAAK,EAAE;gBACVrG,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACU,OAAO;gBAC7CyK,YAAY,EAAE,MAAM;gBACpBD,OAAO,EAAE,QAAQ;gBACjBzJ,SAAS,EAAE,cAAcxJ,YAAY,CAAC+H,YAAY,CAAC0B,MAAM,EAAE;gBAC3DnB,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACO,MAAM;cACvD,CAAE;cAAAwK,QAAA,gBACA5Q,OAAA;gBAAImM,KAAK,EAAE;kBACTkH,MAAM,EAAE,cAAc;kBACtBxB,QAAQ,EAAE,QAAQ;kBAClBD,UAAU,EAAE,GAAG;kBACf7L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG;gBACnC,CAAE;gBAAA4K,QAAA,EAAC;cAEH;gBAAApQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAELX,OAAA;gBAAKmM,KAAK,EAAE;kBACV+I,SAAS,EAAE,OAAO;kBAClBC,SAAS,EAAE;gBACb,CAAE;gBAAAvE,QAAA,EACCzN,WAAW,CAACmI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACjL,GAAG,CAAC,CAACqI,GAAG,EAAE2J,KAAK,kBACtCrS,OAAA;kBAAiBmM,KAAK,EAAE;oBACtBmF,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBQ,GAAG,EAAE,MAAM;oBACXhB,OAAO,EAAE,SAAS;oBAClBC,YAAY,EAAE,KAAK;oBACnBsC,YAAY,EAAE,QAAQ;oBACtBxN,UAAU,EAAEuM,KAAK,GAAG,CAAC,KAAK,CAAC,GAAGvU,YAAY,CAAC+H,YAAY,CAACiC,SAAS,GAAG,aAAa;oBACjFlB,UAAU,EAAE;kBACd,CAAE;kBAAAgK,QAAA,gBACA5Q,OAAA;oBAAKmM,KAAK,EAAE;sBACV6F,KAAK,EAAE,MAAM;sBACbL,MAAM,EAAE,MAAM;sBACdX,YAAY,EAAE,KAAK;sBACnBlL,UAAU,EAAE4C,GAAG,CAAC6F,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;sBACxD6G,UAAU,EAAE;oBACd;kBAAE;oBAAA5U,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACLX,OAAA;oBAAKmM,KAAK,EAAE;sBAAEkF,IAAI,EAAE;oBAAE,CAAE;oBAAAT,QAAA,gBACtB5Q,OAAA;sBAAKmM,KAAK,EAAE;wBACV0F,QAAQ,EAAE,QAAQ;wBAClBD,UAAU,EAAE,GAAG;wBACf7L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG,IAAI;wBACrCsN,YAAY,EAAE;sBAChB,CAAE;sBAAA1C,QAAA,EACClI,GAAG,CAAC4F;oBAAG;sBAAA9N,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACNX,OAAA;sBAAKmM,KAAK,EAAE;wBACV0F,QAAQ,EAAE,QAAQ;wBAClB9L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAAC8N;sBACnC,CAAE;sBAAA/C,QAAA,EACC,IAAInC,IAAI,CAAC/F,GAAG,CAAC8F,IAAI,CAAC,CAAC6G,cAAc,CAAC;oBAAC;sBAAA7U,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAhCE0R,KAAK;kBAAA7R,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiCV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNX,OAAA;cAAKmM,KAAK,EAAE;gBACVrG,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACU,OAAO;gBAC7CyK,YAAY,EAAE,MAAM;gBACpBD,OAAO,EAAE,QAAQ;gBACjBzJ,SAAS,EAAE,cAAcxJ,YAAY,CAAC+H,YAAY,CAAC0B,MAAM,EAAE;gBAC3DnB,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACO,MAAM;cACvD,CAAE;cAAAwK,QAAA,gBACA5Q,OAAA;gBAAImM,KAAK,EAAE;kBACTkH,MAAM,EAAE,cAAc;kBACtBxB,QAAQ,EAAE,QAAQ;kBAClBD,UAAU,EAAE,GAAG;kBACf7L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG;gBACnC,CAAE;gBAAA4K,QAAA,EAAC;cAEH;gBAAApQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAELX,OAAA;gBAAKmM,KAAK,EAAE;kBAAEmF,OAAO,EAAE,MAAM;kBAAEwD,aAAa,EAAE,QAAQ;kBAAE/C,GAAG,EAAE;gBAAO,CAAE;gBAAAnB,QAAA,gBACpE5Q,OAAA;kBAAOmM,KAAK,EAAE;oBACZmF,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBQ,GAAG,EAAE,SAAS;oBACdhB,OAAO,EAAE,MAAM;oBACfC,YAAY,EAAE,MAAM;oBACpBlL,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACiC,SAAS;oBAC/C1B,MAAM,EAAE,cAActI,YAAY,CAAC+H,YAAY,CAACO,MAAM,EAAE;oBACxD0K,MAAM,EAAExO,mBAAmB,GAAG,aAAa,GAAG,SAAS;oBACvDsE,UAAU,EAAE;kBACd,CAAE;kBACFsK,YAAY,EAAGnH,CAAC,IAAK;oBACnB,IAAI,CAACzH,mBAAmB,EAAE;sBACxByH,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAACzE,WAAW,GAAG5J,YAAY,CAAC+H,YAAY,CAACgB,OAAO;sBACrEkD,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAACrG,UAAU,GAAGhI,YAAY,CAAC+H,YAAY,CAACgB,OAAO,GAAG,IAAI;oBAC7E;kBACF,CAAE;kBACFsK,YAAY,EAAGpH,CAAC,IAAK;oBACnBA,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAACzE,WAAW,GAAG5J,YAAY,CAAC+H,YAAY,CAACO,MAAM;oBACpE2D,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAACrG,UAAU,GAAGhI,YAAY,CAAC+H,YAAY,CAACiC,SAAS;kBACxE,CAAE;kBAAA8I,QAAA,gBACA5Q,OAAA,CAACrB,QAAQ;oBAACyS,IAAI,EAAE,EAAG;oBAACrL,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACgB;kBAAQ;oBAAArG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAChEX,OAAA;oBAAA4Q,QAAA,gBACE5Q,OAAA;sBAAKmM,KAAK,EAAE;wBACVyF,UAAU,EAAE,GAAG;wBACf7L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG,IAAI;wBACrCsN,YAAY,EAAE;sBAChB,CAAE;sBAAA1C,QAAA,EACCtO,mBAAmB,GAAG,cAAc,GAAG;oBAAe;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD,CAAC,eACNX,OAAA;sBAAKmM,KAAK,EAAE;wBACV0F,QAAQ,EAAE,QAAQ;wBAClB9L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAAC8N;sBACnC,CAAE;sBAAA/C,QAAA,EAAC;oBAEH;sBAAApQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNX,OAAA;oBACEuO,IAAI,EAAC,MAAM;oBACX+G,MAAM,EAAC,iBAAiB;oBACxBC,QAAQ,EAAEzL,kBAAmB;oBAC7B0L,QAAQ,EAAElT,mBAAoB;oBAC9B6J,KAAK,EAAE;sBAAEmF,OAAO,EAAE;oBAAO;kBAAE;oBAAA9Q,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,EAEP6B,SAAS,iBACRxC,OAAA;kBACEwL,IAAI,EAAEhJ,SAAU;kBAChByH,MAAM,EAAC,QAAQ;kBACfwL,GAAG,EAAC,qBAAqB;kBACzBtJ,KAAK,EAAE;oBACLmF,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBQ,GAAG,EAAE,SAAS;oBACdhB,OAAO,EAAE,MAAM;oBACfC,YAAY,EAAE,MAAM;oBACpBlL,UAAU,EAAE,SAAS;oBACrBC,KAAK,EAAE,OAAO;oBACd2P,cAAc,EAAE,MAAM;oBACtB9D,UAAU,EAAE,GAAG;oBACfhL,UAAU,EAAE;kBACd,CAAE;kBACFsK,YAAY,EAAGnH,CAAC,IAAK;oBACnBA,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAACrG,UAAU,GAAG,SAAS;oBAC5CiE,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAAC1F,SAAS,GAAG,kBAAkB;kBACtD,CAAE;kBACF0K,YAAY,EAAGpH,CAAC,IAAK;oBACnBA,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAACrG,UAAU,GAAG,SAAS;oBAC5CiE,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAAC1F,SAAS,GAAG,eAAe;kBACnD,CAAE;kBAAAmK,QAAA,gBAEF5Q,OAAA,CAAC7B,UAAU;oBAACiT,IAAI,EAAE;kBAAG;oBAAA5Q,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACxBX,OAAA;oBAAA4Q,QAAA,EAAM;kBAAW;oBAAApQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CACJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNX,OAAA;YAAKmM,KAAK,EAAE;cACVrG,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACU,OAAO;cAC7CyK,YAAY,EAAE,MAAM;cACpBD,OAAO,EAAE,MAAM;cACf4E,SAAS,EAAE,MAAM;cACjBvP,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACO,MAAM,EAAE;cACvDkB,SAAS,EAAE,cAAcxJ,YAAY,CAAC+H,YAAY,CAAC0B,MAAM,EAAE;cAC3DsM,SAAS,EAAE;YACb,CAAE;YAAAjD,QAAA,gBACA5Q,OAAA;cAAKmM,KAAK,EAAE;gBACVmF,OAAO,EAAE,MAAM;gBACfkC,mBAAmB,EAAE,sCAAsC;gBAC3DzB,GAAG,EAAE,MAAM;gBACXuB,YAAY,EAAE;cAChB,CAAE;cAAA1C,QAAA,gBAEA5Q,OAAA;gBAAA4Q,QAAA,gBACE5Q,OAAA;kBAAImM,KAAK,EAAE;oBACTkH,MAAM,EAAE,YAAY;oBACpBxB,QAAQ,EAAE,MAAM;oBAChBD,UAAU,EAAE,GAAG;oBACf7L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG;kBACnC,CAAE;kBAAA4K,QAAA,EAAC;gBAEH;kBAAApQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLX,OAAA;kBAAKmM,KAAK,EAAE;oBACVmF,OAAO,EAAE,MAAM;oBACfwD,aAAa,EAAE,QAAQ;oBACvB/C,GAAG,EAAE;kBACP,CAAE;kBAAAnB,QAAA,gBACA5Q,OAAA;oBAAKmM,KAAK,EAAE;sBACV0F,QAAQ,EAAE,QAAQ;sBAClB9L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAAC8N;oBACnC,CAAE;oBAAA/C,QAAA,GAAC,oBACiB,eAAA5Q,OAAA;sBAAQmM,KAAK,EAAE;wBAAEpG,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACgB;sBAAQ,CAAE;sBAAA+J,QAAA,EAAC;oBAAQ;sBAAApQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7F,CAAC,eACNX,OAAA;oBAAKmM,KAAK,EAAE;sBACV0F,QAAQ,EAAE,QAAQ;sBAClB9L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAAC8N;oBACnC,CAAE;oBAAA/C,QAAA,GAAC,mBACgB,eAAA5Q,OAAA;sBAAQmM,KAAK,EAAE;wBAAEpG,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACgB;sBAAQ,CAAE;sBAAA+J,QAAA,EAAC;oBAAE;sBAAApQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNX,OAAA;gBAAA4Q,QAAA,gBACE5Q,OAAA;kBAAImM,KAAK,EAAE;oBACTkH,MAAM,EAAE,YAAY;oBACpBxB,QAAQ,EAAE,MAAM;oBAChBD,UAAU,EAAE,GAAG;oBACf7L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG;kBACnC,CAAE;kBAAA4K,QAAA,EAAC;gBAEH;kBAAApQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLX,OAAA;kBAAKmM,KAAK,EAAE;oBACVmF,OAAO,EAAE,MAAM;oBACfwD,aAAa,EAAE,QAAQ;oBACvB/C,GAAG,EAAE;kBACP,CAAE;kBAAAnB,QAAA,gBACA5Q,OAAA;oBACEiR,OAAO,EAAEA,CAAA,KAAMpP,YAAY,CAAC,WAAW,CAAE;oBACzCsK,KAAK,EAAE;sBACLrG,UAAU,EAAE,MAAM;sBAClBM,MAAM,EAAE,MAAM;sBACdL,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACgB,OAAO;sBACxCgL,QAAQ,EAAE,QAAQ;sBAClBf,MAAM,EAAE,SAAS;sBACjB+C,SAAS,EAAE,MAAM;sBACjB9C,OAAO,EAAE;oBACX,CAAE;oBAAAH,QAAA,EACH;kBAED;oBAAApQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTX,OAAA;oBACEiR,OAAO,EAAEA,CAAA,KAAMpP,YAAY,CAAC,QAAQ,CAAE;oBACtCsK,KAAK,EAAE;sBACLrG,UAAU,EAAE,MAAM;sBAClBM,MAAM,EAAE,MAAM;sBACdL,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACgB,OAAO;sBACxCgL,QAAQ,EAAE,QAAQ;sBAClBf,MAAM,EAAE,SAAS;sBACjB+C,SAAS,EAAE,MAAM;sBACjB9C,OAAO,EAAE;oBACX,CAAE;oBAAAH,QAAA,EACH;kBAED;oBAAApQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNX,OAAA;gBAAA4Q,QAAA,gBACE5Q,OAAA;kBAAImM,KAAK,EAAE;oBACTkH,MAAM,EAAE,YAAY;oBACpBxB,QAAQ,EAAE,MAAM;oBAChBD,UAAU,EAAE,GAAG;oBACf7L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG;kBACnC,CAAE;kBAAA4K,QAAA,EAAC;gBAEH;kBAAApQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLX,OAAA;kBAAKmM,KAAK,EAAE;oBACV0F,QAAQ,EAAE,QAAQ;oBAClB9L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAAC8N,SAAS;oBAC1CiC,SAAS,EAAE,QAAQ;oBACnBnC,UAAU,EAAE;kBACd,CAAE;kBAAA7C,QAAA,EAAC;gBAEH;kBAAApQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNX,OAAA;cAAKmM,KAAK,EAAE;gBACV0J,SAAS,EAAE,aAAa/X,YAAY,CAAC+H,YAAY,CAACO,MAAM,EAAE;gBAC1D0P,UAAU,EAAE,MAAM;gBAClBjE,QAAQ,EAAE,QAAQ;gBAClB9L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAAC8N;cACnC,CAAE;cAAA/C,QAAA,EAAC;YAEH;cAAApQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAiB,SAAS,KAAK,QAAQ,iBACrB5B,OAAA;QAAKmM,KAAK,EAAE;UAAE4E,OAAO,EAAE,MAAM;UAAEqC,QAAQ,EAAE,QAAQ;UAAEC,MAAM,EAAE;QAAS,CAAE;QAAAzC,QAAA,eACpE5Q,OAAA;UAAKmM,KAAK,EAAEhE,QAAQ,CAAC,MAAM,CAAE;UAAAyI,QAAA,gBAC3B5Q,OAAA;YAAImM,KAAK,EAAE;cACTwJ,SAAS,EAAE,CAAC;cACZ5P,KAAK,EAAE;YACT,CAAE;YAAA6K,QAAA,EAAC;UAAgB;YAAApQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBX,OAAA;YAAGmM,KAAK,EAAE;cACR2F,OAAO,EAAE,GAAG;cACZwB,YAAY,EAAE,MAAM;cACpBvN,KAAK,EAAE;YACT,CAAE;YAAA6K,QAAA,EAAC;UAEH;YAAApQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAGJX,OAAA;YAAKmM,KAAK,EAAE;cACVwF,MAAM,EAAE,MAAM;cACdwD,SAAS,EAAE,MAAM;cACjB7B,YAAY,EAAE,MAAM;cACpBvC,OAAO,EAAE,MAAM;cACfnL,eAAe,EAAE,SAAS;cAC1BQ,MAAM,EAAE,mBAAmB;cAC3B4K,YAAY,EAAE;YAChB,CAAE;YAAAJ,QAAA,GAECxP,QAAQ,CAAC2O,MAAM,KAAK,CAAC,gBACpB/P,OAAA;cAAKmM,KAAK,EAAE;gBACVwF,MAAM,EAAE,MAAM;gBACdL,OAAO,EAAE,MAAM;gBACfwD,aAAa,EAAE,QAAQ;gBACvBvD,UAAU,EAAE,QAAQ;gBACpBU,cAAc,EAAE,QAAQ;gBACxB4B,SAAS,EAAE,QAAQ;gBACnB/B,OAAO,EAAE;cACX,CAAE;cAAAlB,QAAA,gBACA5Q,OAAA;gBAAKmM,KAAK,EAAE;kBAAE0F,QAAQ,EAAE,MAAM;kBAAEyB,YAAY,EAAE;gBAAO,CAAE;gBAAA1C,QAAA,EAAC;cAAE;gBAAApQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChEX,OAAA;gBAAImM,KAAK,EAAE;kBACTkH,MAAM,EAAE,CAAC;kBACTtN,KAAK,EAAE;gBACT,CAAE;gBAAA6K,QAAA,EAAC;cAAoB;gBAAApQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5BX,OAAA;gBAAGmM,KAAK,EAAE;kBACRpG,KAAK,EAAE;gBACT,CAAE;gBAAA6K,QAAA,EAAC;cAA+C;gBAAApQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,GAENS,QAAQ,CAACf,GAAG,CAAC,CAACiO,GAAG,EAAEyH,GAAG,kBACpB/V,OAAA;cAEEmM,KAAK,EAAE;gBACL,IAAImC,GAAG,CAAClB,IAAI,KAAK,MAAM,GAAGjF,QAAQ,CAAC,gBAAgB,CAAC,GAAGA,QAAQ,CAAC,eAAe,CAAC,CAAC;gBACjF6N,SAAS,EAAE;cACb,CAAE;cAAApF,QAAA,EAEDtC,GAAG,CAAClB,IAAI,KAAK,KAAK,gBACjBpN,OAAA,CAACF,aAAa;gBAAA8Q,QAAA,EAAEtC,GAAG,CAACjB;cAAO;gBAAA7M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAgB,CAAC,GAE5C2N,GAAG,CAACjB;YACL,GAVI0I,GAAG;cAAAvV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWL,CACN,CACF,EACAa,OAAO,iBACNxB,OAAA;cAAKmM,KAAK,EAAEhE,QAAQ,CAAC,eAAe,CAAE;cAAAyI,QAAA,eACpC5Q,OAAA;gBAAKmM,KAAK,EAAE;kBAAEmF,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEQ,GAAG,EAAE;gBAAM,CAAE;gBAAAnB,QAAA,gBAChE5Q,OAAA;kBAAKmM,KAAK,EAAE;oBACV6F,KAAK,EAAE,MAAM;oBACbL,MAAM,EAAE,MAAM;oBACdX,YAAY,EAAE,KAAK;oBACnBpL,eAAe,EAAE,SAAS;oBAC1BoQ,SAAS,EAAE;kBACb;gBAAE;kBAAAxV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACLX,OAAA;kBAAKmM,KAAK,EAAE;oBACV6F,KAAK,EAAE,MAAM;oBACbL,MAAM,EAAE,MAAM;oBACdX,YAAY,EAAE,KAAK;oBACnBpL,eAAe,EAAE,SAAS;oBAC1BoQ,SAAS,EAAE,iCAAiC;oBAC5CC,cAAc,EAAE;kBAClB;gBAAE;kBAAAzV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACLX,OAAA;kBAAKmM,KAAK,EAAE;oBACV6F,KAAK,EAAE,MAAM;oBACbL,MAAM,EAAE,MAAM;oBACdX,YAAY,EAAE,KAAK;oBACnBpL,eAAe,EAAE,SAAS;oBAC1BoQ,SAAS,EAAE,iCAAiC;oBAC5CC,cAAc,EAAE;kBAClB;gBAAE;kBAAAzV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,eACDX,OAAA;cAAKkW,GAAG,EAAEnS;YAAW;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eAGNX,OAAA;YACEmM,KAAK,EAAE;cAAEmF,OAAO,EAAE,MAAM;cAAES,GAAG,EAAE;YAAO,CAAE;YACxCoE,QAAQ,EAAEpM,CAAC,IAAI;cACbA,CAAC,CAACqM,cAAc,CAAC,CAAC;cAClBnJ,WAAW,CAAC,CAAC;YACf,CAAE;YAAA2D,QAAA,gBAEF5Q,OAAA;cACEuO,IAAI,EAAC,MAAM;cACX8H,WAAW,EAAC,sBAAsB;cAClClK,KAAK,EAAEhE,QAAQ,CAAC,YAAY,CAAE;cAC9B6L,KAAK,EAAE9S,KAAM;cACbqU,QAAQ,EAAExL,CAAC,IAAI5I,QAAQ,CAAC4I,CAAC,CAACE,MAAM,CAAC+J,KAAK,CAAE;cACxCwB,QAAQ,EAAEhU;YAAQ;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACFX,OAAA;cACEuO,IAAI,EAAC,QAAQ;cACbpC,KAAK,EAAE;gBACL,GAAGhE,QAAQ,CAAC,eAAe,CAAC;gBAC5BuM,QAAQ,EAAE;cACZ,CAAE;cACFc,QAAQ,EAAEhU,OAAO,IAAI,CAACN,KAAK,CAACgM,IAAI,CAAC,CAAE;cAAA0D,QAAA,EAElCpP,OAAO,GAAG,YAAY,GAAG;YAAM;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAiB,SAAS,KAAK,KAAK,iBAClB5B,OAAA;QAAKmM,KAAK,EAAE;UAAE4E,OAAO,EAAE;QAAO,CAAE;QAAAH,QAAA,eAC9B5Q,OAAA;UAAKmM,KAAK,EAAEhE,QAAQ,CAAC,MAAM,CAAE;UAAAyI,QAAA,gBAE3B5Q,OAAA;YAAKmM,KAAK,EAAE;cAAEmF,OAAO,EAAE,MAAM;cAAEW,cAAc,EAAE,eAAe;cAAEV,UAAU,EAAE,QAAQ;cAAE+B,YAAY,EAAE;YAAO,CAAE;YAAA1C,QAAA,gBAC3G5Q,OAAA;cAAA4Q,QAAA,gBACE5Q,OAAA;gBAAImM,KAAK,EAAE;kBAAEwJ,SAAS,EAAE,CAAC;kBAAErC,YAAY,EAAE;gBAAM,CAAE;gBAAA1C,QAAA,EAAC;cAA6B;gBAAApQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpFX,OAAA;gBAAGmM,KAAK,EAAE;kBAAE2F,OAAO,EAAE,GAAG;kBAAEuB,MAAM,EAAE;gBAAE,CAAE;gBAAAzC,QAAA,EAAC;cAEvC;gBAAApQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,EACLoE,gBAAgB,iBACf/E,OAAA;cACEiR,OAAO,EAAEnF,mBAAoB;cAC7BK,KAAK,EAAE;gBACL,GAAGhE,QAAQ,CAAC,eAAe,CAAC;gBAC5BrC,UAAU,EAAE,SAAS;gBACrBwL,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBQ,GAAG,EAAE,KAAK;gBACVF,QAAQ,EAAE,MAAM;gBAChBd,OAAO,EAAE,UAAU;gBACnB3K,MAAM,EAAE,MAAM;gBACd4K,YAAY,EAAE,KAAK;gBACnBjL,KAAK,EAAE,OAAO;gBACd+K,MAAM,EAAE,SAAS;gBACjBlK,UAAU,EAAE;cACd,CAAE;cACFsK,YAAY,EAAGnH,CAAC,IAAKA,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACrG,UAAU,GAAG,SAAU;cAC3DqL,YAAY,EAAGpH,CAAC,IAAKA,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACrG,UAAU,GAAG,SAAU;cAAA8K,QAAA,gBAE3D5Q,OAAA,CAACX,WAAW;gBAAC+R,IAAI,EAAE;cAAG;gBAAA5Q,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,uBAE3B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNX,OAAA;YAAKmM,KAAK,EAAE;cACVmF,OAAO,EAAE,MAAM;cACfS,GAAG,EAAE,KAAK;cACVuB,YAAY,EAAE,MAAM;cACpBM,QAAQ,EAAE,MAAM;cAChBzN,YAAY,EAAE,gBAAgB;cAC9BmQ,aAAa,EAAE;YACjB,CAAE;YAAA1F,QAAA,EACC,CAAC,KAAK,EAAE,GAAG2F,MAAM,CAACC,IAAI,CAACnR,iBAAiB,CAAC,CAAC,CAAChF,GAAG,CAACoW,QAAQ,iBACtDzW,OAAA;cAEEiR,OAAO,EAAEA,CAAA,KAAMzM,mBAAmB,CAACiS,QAAQ,CAAE;cAC7CtK,KAAK,EAAE;gBACL4E,OAAO,EAAE,UAAU;gBACnBC,YAAY,EAAE,MAAM;gBACpB5K,MAAM,EAAE7B,gBAAgB,KAAKkS,QAAQ,GACjC,MAAM,GACN,gBAAgB;gBACpB3Q,UAAU,EAAEvB,gBAAgB,KAAKkS,QAAQ,GACrC3Y,YAAY,CAAC+H,YAAY,CAACgB,OAAO,GACjC,aAAa;gBACjBd,KAAK,EAAExB,gBAAgB,KAAKkS,QAAQ,GAChC,OAAO,GACP,MAAM;gBACV3F,MAAM,EAAE,SAAS;gBACjBe,QAAQ,EAAE,MAAM;gBAChBD,UAAU,EAAErN,gBAAgB,KAAKkS,QAAQ,GAAG,GAAG,GAAG,GAAG;gBACrD7P,UAAU,EAAE,eAAe;gBAC3ByN,aAAa,EAAE;cACjB,CAAE;cACFnD,YAAY,EAAGnH,CAAC,IAAK;gBACnB,IAAIxF,gBAAgB,KAAKkS,QAAQ,EAAE;kBACjC1M,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACrG,UAAU,GAAG,SAAS;gBACvC;cACF,CAAE;cACFqL,YAAY,EAAGpH,CAAC,IAAK;gBACnB,IAAIxF,gBAAgB,KAAKkS,QAAQ,EAAE;kBACjC1M,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACrG,UAAU,GAAG,aAAa;gBAC3C;cACF,CAAE;cAAA8K,QAAA,EAED6F,QAAQ,KAAK,KAAK,GAAG,QAAQ,GAAG,GAAGA,QAAQ,KAAK,OAAO,GAAG,IAAI,GAAGA,QAAQ,KAAK,UAAU,GAAG,IAAI,GAAGA,QAAQ,KAAK,UAAU,GAAG,IAAI,GAAGA,QAAQ,KAAK,SAAS,GAAG,IAAI,GAAGA,QAAQ,KAAK,QAAQ,GAAG,MAAM,GAAG,IAAI,IAAIA,QAAQ;YAAE,GA/BlNA,QAAQ;cAAAjW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgCP,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNX,OAAA;YAAKmM,KAAK,EAAE;cACVmF,OAAO,EAAE,MAAM;cACfS,GAAG,EAAE,MAAM;cACXuB,YAAY,EAAE,MAAM;cACpBM,QAAQ,EAAE,MAAM;cAChBrC,UAAU,EAAE;YACd,CAAE;YAAAX,QAAA,gBAEA5Q,OAAA;cAAKmM,KAAK,EAAE;gBAAEiE,QAAQ,EAAE,UAAU;gBAAEiB,IAAI,EAAE,CAAC;gBAAEqD,QAAQ,EAAE;cAAQ,CAAE;cAAA9D,QAAA,gBAC/D5Q,OAAA;gBAAKmM,KAAK,EAAE;kBACViE,QAAQ,EAAE,UAAU;kBACpBwC,IAAI,EAAE,MAAM;kBACZD,GAAG,EAAE,KAAK;kBACVlM,SAAS,EAAE,kBAAkB;kBAC7BV,KAAK,EAAE;gBACT,CAAE;gBAAA6K,QAAA,eACA5Q,OAAA,CAACtB,QAAQ;kBAAC0S,IAAI,EAAE;gBAAG;kBAAA5Q,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACNX,OAAA;gBACEuO,IAAI,EAAC,MAAM;gBACX8H,WAAW,EAAC,qBAAqB;gBACjClK,KAAK,EAAE;kBACL,GAAGhE,QAAQ,CAAC,YAAY,CAAC;kBACzBuK,WAAW,EAAE,MAAM;kBACnBV,KAAK,EAAE;gBACT,CAAE;gBACFgC,KAAK,EAAElS,UAAW;gBAClByT,QAAQ,EAAGxL,CAAC,IAAKhI,aAAa,CAACgI,CAAC,CAACE,MAAM,CAAC+J,KAAK;cAAE;gBAAAxT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,EACDmB,UAAU,iBACT9B,OAAA;gBACEmM,KAAK,EAAE;kBACLiE,QAAQ,EAAE,UAAU;kBACpByC,KAAK,EAAE,MAAM;kBACbF,GAAG,EAAE,KAAK;kBACVlM,SAAS,EAAE,kBAAkB;kBAC7BX,UAAU,EAAE,MAAM;kBAClBM,MAAM,EAAE,MAAM;kBACdL,KAAK,EAAE,MAAM;kBACb+K,MAAM,EAAE;gBACV,CAAE;gBACFG,OAAO,EAAEA,CAAA,KAAMlP,aAAa,CAAC,EAAE,CAAE;gBAAA6O,QAAA,eAEjC5Q,OAAA,CAAChC,GAAG;kBAACoT,IAAI,EAAE;gBAAG;kBAAA5Q,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNX,OAAA;cACEgU,KAAK,EAAEvP,MAAO;cACd8Q,QAAQ,EAAGxL,CAAC,IAAKrF,SAAS,CAACqF,CAAC,CAACE,MAAM,CAAC+J,KAAK,CAAE;cAC3C7H,KAAK,EAAE;gBACL,GAAGhE,QAAQ,CAAC,YAAY,CAAC;gBACzB6J,KAAK,EAAE,MAAM;gBACb0C,QAAQ,EAAE;cACZ,CAAE;cAAA9D,QAAA,gBAEF5Q,OAAA;gBAAQgU,KAAK,EAAC,MAAM;gBAAApD,QAAA,EAAC;cAAe;gBAAApQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7CX,OAAA;gBAAQgU,KAAK,EAAC,WAAW;gBAAApD,QAAA,EAAC;cAAiB;gBAAApQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAGLkE,eAAe,CAACkL,MAAM,GAAG,CAAC,iBACzB/P,OAAA;YAAKmM,KAAK,EAAE;cACVmH,YAAY,EAAE,MAAM;cACpBvC,OAAO,EAAE,MAAM;cACfC,YAAY,EAAE,MAAM;cACpBlL,UAAU,EAAE,SAAS;cACrBM,MAAM,EAAE;YACV,CAAE;YAAAwK,QAAA,gBACA5Q,OAAA;cAAImM,KAAK,EAAE;gBACTmF,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBQ,GAAG,EAAE,KAAK;gBACVF,QAAQ,EAAE,MAAM;gBAChByB,YAAY,EAAE,MAAM;gBACpBvN,KAAK,EAAE,MAAM;gBACbsN,MAAM,EAAE;cACV,CAAE;cAAAzC,QAAA,gBACA5Q,OAAA,CAACZ,OAAO;gBAAC2G,KAAK,EAAC;cAAM;gBAAAvF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAC1B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLX,OAAA;cAAKmM,KAAK,EAAE;gBACVmF,OAAO,EAAE,MAAM;gBACfS,GAAG,EAAE,KAAK;gBACV6B,QAAQ,EAAE;cACZ,CAAE;cAAAhD,QAAA,EACC/L,eAAe,CAACxE,GAAG,CAAC4K,OAAO,iBAC1BjL,OAAA;gBAEEiR,OAAO,EAAEA,CAAA,KAAMjG,kBAAkB,CAACC,OAAO,CAAE;gBAC3CkB,KAAK,EAAE;kBACL4E,OAAO,EAAE,UAAU;kBACnBC,YAAY,EAAE,MAAM;kBACpB5K,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACgB,OAAO,EAAE;kBACxDf,UAAU,EAAE,aAAa;kBACzBC,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACgB,OAAO;kBACxCiK,MAAM,EAAE,SAAS;kBACjBe,QAAQ,EAAE,MAAM;kBAChBjL,UAAU,EAAE;gBACd,CAAE;gBACFsK,YAAY,EAAGnH,CAAC,IAAK;kBACnBA,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACrG,UAAU,GAAGhI,YAAY,CAAC+H,YAAY,CAACgB,OAAO;kBAC7DkD,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACpG,KAAK,GAAG,OAAO;gBAChC,CAAE;gBACFoL,YAAY,EAAGpH,CAAC,IAAK;kBACnBA,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACrG,UAAU,GAAG,aAAa;kBACzCiE,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACpG,KAAK,GAAGjI,YAAY,CAAC+H,YAAY,CAACgB,OAAO;gBAC1D,CAAE;gBAAA+J,QAAA,EAED3F;cAAO,GArBHA,OAAO;gBAAAzK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsBN,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDX,OAAA;YAAKmM,KAAK,EAAE;cACVmF,OAAO,EAAE,MAAM;cACfkC,mBAAmB,EAAE,uCAAuC;cAC5DzB,GAAG,EAAE,MAAM;cACX4D,SAAS,EAAE;YACb,CAAE;YAAA/E,QAAA,EACCxE,oBAAoB,CAAC,CAAC,CAAC/L,GAAG,CAAC,CAAC4K,OAAO,EAAEoH,KAAK,kBACzCrS,OAAA;cAEEmM,KAAK,EAAE;gBACL,GAAGhE,QAAQ,CAAC,aAAa,CAAC;gBAC1BiI,QAAQ,EAAE,UAAU;gBACpB3J,SAAS,EAAE9B,iBAAiB,CAACkH,QAAQ,CAACZ,OAAO,CAAC,GAAG,aAAa,GAAG,UAAU;gBAC3E7E,MAAM,EAAEzB,iBAAiB,CAACkH,QAAQ,CAACZ,OAAO,CAAC,GACvC,aAAanN,YAAY,CAAC+H,YAAY,CAACgB,OAAO,EAAE,GAChD,aAAa/I,YAAY,CAAC+H,YAAY,CAACO,MAAM,EAAE;gBACnDN,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACU,OAAO;gBAC7CR,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG,IAAI;gBACrCgQ,SAAS,EAAE,oBAAoB3D,KAAK,GAAG,GAAG,QAAQ;gBAClD/K,SAAS,EAAE,aAAaxJ,YAAY,CAAC+H,YAAY,CAAC0B,MAAM;cAC1D,CAAE;cACF0J,OAAO,EAAEA,CAAA,KAAMjG,kBAAkB,CAACC,OAAO,CAAE;cAAA2F,QAAA,gBAG3C5Q,OAAA;gBACEiR,OAAO,EAAGlH,CAAC,IAAK4B,cAAc,CAACV,OAAO,EAAElB,CAAC,CAAE;gBAC3CoC,KAAK,EAAE;kBACLiE,QAAQ,EAAE,UAAU;kBACpBuC,GAAG,EAAE,KAAK;kBACVE,KAAK,EAAE,KAAK;kBACZ/M,UAAU,EAAE,MAAM;kBAClBM,MAAM,EAAE,MAAM;kBACd0K,MAAM,EAAE,SAAS;kBACjB/K,KAAK,EAAEpB,iBAAiB,CAACkH,QAAQ,CAACZ,OAAO,CAAC,GAAG,SAAS,GAAG,MAAM;kBAC/DrE,UAAU,EAAE,eAAe;kBAC3BiL,QAAQ,EAAE;gBACZ,CAAE;gBAAAjB,QAAA,eAEF5Q,OAAA,CAACb,OAAO;kBAACuX,IAAI,EAAE/R,iBAAiB,CAACkH,QAAQ,CAACZ,OAAO,CAAC,GAAG,cAAc,GAAG;gBAAO;kBAAAzK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC,eAGTX,OAAA;gBAAKmM,KAAK,EAAE;kBACV6F,KAAK,EAAE,MAAM;kBACbL,MAAM,EAAE,MAAM;kBACdX,YAAY,EAAE,KAAK;kBACnBlL,UAAU,EAAE,2BAA2BhI,YAAY,CAAC+H,YAAY,CAACgB,OAAO,KAAK/I,YAAY,CAAC+H,YAAY,CAAC8Q,WAAW,GAAG;kBACrH5Q,KAAK,EAAE,OAAO;kBACduL,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBU,cAAc,EAAE,QAAQ;kBACxBJ,QAAQ,EAAE,MAAM;kBAChBD,UAAU,EAAE,GAAG;kBACf0B,YAAY,EAAE,MAAM;kBACpBhM,SAAS,EAAE,aAAaxJ,YAAY,CAAC+H,YAAY,CAAC0B,MAAM;gBAC1D,CAAE;gBAAAqJ,QAAA,EACC3F,OAAO,CAAC2L,MAAM,CAAC,CAAC;cAAC;gBAAApW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eAGNX,OAAA;gBAAKmM,KAAK,EAAE;kBACVyF,UAAU,EAAE,GAAG;kBACfiC,SAAS,EAAE,QAAQ;kBACnBhC,QAAQ,EAAE,MAAM;kBAChByB,YAAY,EAAE;gBAChB,CAAE;gBAAA1C,QAAA,EACC3F;cAAO;gBAAAzK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGNX,OAAA;gBAAKmM,KAAK,EAAE;kBACVmF,OAAO,EAAE,MAAM;kBACfW,cAAc,EAAE,eAAe;kBAC/BJ,QAAQ,EAAE,MAAM;kBAChBC,OAAO,EAAE,GAAG;kBACZ6D,SAAS,EAAE;gBACb,CAAE;gBAAA/E,QAAA,gBACA5Q,OAAA;kBAAA4Q,QAAA,GAAM,eAAG,EAAC+D,IAAI,CAACE,KAAK,CAACF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,EAAC,YAAU;gBAAA;kBAAApU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/DX,OAAA;kBAAA4Q,QAAA,GAAM,SAAE,EAAC,CAAC+D,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEiC,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAArW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,EAGL4V,MAAM,CAACO,OAAO,CAACzR,iBAAiB,CAAC,CAAChF,GAAG,CAAC,CAAC,CAACoW,QAAQ,EAAEnR,SAAS,CAAC,KAAK;gBAChE,IAAIA,SAAS,CAACgH,IAAI,CAACjB,CAAC,IAAIA,CAAC,CAACtK,WAAW,CAAC,CAAC,KAAKkK,OAAO,CAAClK,WAAW,CAAC,CAAC,CAAC,EAAE;kBAClE,oBACEf,OAAA;oBAEEmM,KAAK,EAAE;sBACLiE,QAAQ,EAAE,UAAU;sBACpBuC,GAAG,EAAE,KAAK;sBACVC,IAAI,EAAE,KAAK;sBACX9M,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACgB,OAAO;sBAC7Cd,KAAK,EAAE,OAAO;sBACdgL,OAAO,EAAE,SAAS;sBAClBC,YAAY,EAAE,KAAK;sBACnBa,QAAQ,EAAE,MAAM;sBAChBD,UAAU,EAAE;oBACd,CAAE;oBAAAhB,QAAA,EAED6F;kBAAQ,GAbJA,QAAQ;oBAAAjW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAcV,CAAC;gBAEV;gBACA,OAAO,IAAI;cACb,CAAC,CAAC;YAAA,GAhGG0R,KAAK;cAAA7R,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiGP,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAGLyL,oBAAoB,CAAC,CAAC,CAAC2D,MAAM,KAAK,CAAC,iBAClC/P,OAAA;YAAKmM,KAAK,EAAE;cACV0H,SAAS,EAAE,QAAQ;cACnB9C,OAAO,EAAE,MAAM;cACfe,OAAO,EAAE,GAAG;cACZ/L,KAAK,EAAE;YACT,CAAE;YAAA6K,QAAA,gBACA5Q,OAAA;cAAKmM,KAAK,EAAE;gBAAE0F,QAAQ,EAAE,MAAM;gBAAEyB,YAAY,EAAE;cAAO,CAAE;cAAA1C,QAAA,EAAC;YAAE;cAAApQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChEX,OAAA;cAAImM,KAAK,EAAE;gBAAEpG,KAAK,EAAE;cAAO,CAAE;cAAA6K,QAAA,EAAC;YAAkB;cAAApQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrDX,OAAA;cAAGmM,KAAK,EAAE;gBAAEpG,KAAK,EAAE;cAAO,CAAE;cAAA6K,QAAA,EAAC;YAA4C;cAAApQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAiB,SAAS,KAAK,SAAS,iBACtB5B,OAAA;QAAKmM,KAAK,EAAE;UAAE4E,OAAO,EAAE;QAAO,CAAE;QAAAH,QAAA,eAC9B5Q,OAAA;UAAKmM,KAAK,EAAEhE,QAAQ,CAAC,MAAM,CAAE;UAAAyI,QAAA,gBAC3B5Q,OAAA;YAAImM,KAAK,EAAE;cAAEwJ,SAAS,EAAE;YAAE,CAAE;YAAA/E,QAAA,EAAC;UAAc;YAAApQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChDX,OAAA;YAAGmM,KAAK,EAAE;cAAE2F,OAAO,EAAE,GAAG;cAAEwB,YAAY,EAAE;YAAO,CAAE;YAAA1C,QAAA,EAAC;UAElD;YAAApQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJX,OAAA;YAAKmM,KAAK,EAAE;cACVmF,OAAO,EAAE,MAAM;cACfkC,mBAAmB,EAAE,uCAAuC;cAC5DzB,GAAG,EAAE;YACP,CAAE;YAAAnB,QAAA,EACCrL,WAAW,CAAClF,GAAG,CAAC,CAAC0W,IAAI,EAAE1E,KAAK,kBAC3BrS,OAAA;cAEEmM,KAAK,EAAEhE,QAAQ,CAAC,UAAU,CAAE;cAC5B8I,OAAO,EAAEA,CAAA,KAAMnE,YAAY,CAACiK,IAAI,CAACtR,IAAI,CAAE;cAAAmL,QAAA,gBAEvC5Q,OAAA;gBAAA4Q,QAAA,gBACE5Q,OAAA;kBAAImM,KAAK,EAAE;oBAAEkH,MAAM,EAAE;kBAAY,CAAE;kBAAAzC,QAAA,EAAEmG,IAAI,CAACjW;gBAAK;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrDX,OAAA;kBAAGmM,KAAK,EAAE;oBACRkH,MAAM,EAAE,CAAC;oBACTxB,QAAQ,EAAE,MAAM;oBAChBC,OAAO,EAAE;kBACX,CAAE;kBAAAlB,QAAA,EACCmG,IAAI,CAACvR;gBAAW;kBAAAhF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNX,OAAA;gBAAKmM,KAAK,EAAE;kBAAEpG,KAAK,EAAE;gBAAU,CAAE;gBAAA6K,QAAA,eAC/B5Q,OAAA,CAACd,cAAc;kBAACkS,IAAI,EAAE;gBAAG;kBAAA5Q,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA,GAhBD0R,KAAK;cAAA7R,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBP,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAiB,SAAS,KAAK,QAAQ,iBACrB5B,OAAA;QAAKmM,KAAK,EAAE;UAAE4E,OAAO,EAAE;QAAO,CAAE;QAAAH,QAAA,eAC9B5Q,OAAA,CAACxC,MAAM;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CACN,EACAiB,SAAS,KAAK,WAAW,iBACxB5B,OAAA;QAAKmM,KAAK,EAAE;UACV4E,OAAO,EAAE,WAAW;UACpBjL,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACmN,aAAa;UACnD7L,SAAS,EAAE;QACb,CAAE;QAAAyJ,QAAA,eACA5Q,OAAA;UAAKmM,KAAK,EAAE;YAAEiH,QAAQ,EAAE,QAAQ;YAAEC,MAAM,EAAE;UAAS,CAAE;UAAAzC,QAAA,gBAEnD5Q,OAAA;YAAKmM,KAAK,EAAE;cACVrG,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACU,OAAO;cAC7CyK,YAAY,EAAE,MAAM;cACpBD,OAAO,EAAE,MAAM;cACfuC,YAAY,EAAE,MAAM;cACpBlN,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACO,MAAM,EAAE;cACvDkB,SAAS,EAAE,cAAcxJ,YAAY,CAAC+H,YAAY,CAAC0B,MAAM,EAAE;cAC3D6I,QAAQ,EAAE;YACZ,CAAE;YAAAQ,QAAA,gBACA5Q,OAAA;cAAKmM,KAAK,EAAE;gBACViE,QAAQ,EAAE,UAAU;gBACpBuC,GAAG,EAAE,CAAC;gBACNC,IAAI,EAAE,CAAC;gBACPC,KAAK,EAAE,CAAC;gBACRlB,MAAM,EAAE,KAAK;gBACb7L,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAAC0N;cACxC;YAAE;cAAA/S,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAELX,OAAA;cAAImM,KAAK,EAAE;gBACTkH,MAAM,EAAE,YAAY;gBACpBxB,QAAQ,EAAE,QAAQ;gBAClBD,UAAU,EAAE,GAAG;gBACf7L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG,IAAI;gBACrC6N,SAAS,EAAE;cACb,CAAE;cAAAjD,QAAA,EAAC;YAEH;cAAApQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLX,OAAA;cAAGmM,KAAK,EAAE;gBACRkH,MAAM,EAAE,CAAC;gBACTxB,QAAQ,EAAE,QAAQ;gBAClB9L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAAC8N,SAAS;gBAC1CE,SAAS,EAAE;cACb,CAAE;cAAAjD,QAAA,EAAC;YAEH;cAAApQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGNX,OAAA;YAAKmM,KAAK,EAAE;cACVrG,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACU,OAAO;cAC7CyK,YAAY,EAAE,MAAM;cACpBD,OAAO,EAAE,QAAQ;cACjBuC,YAAY,EAAE,MAAM;cACpBlN,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACO,MAAM,EAAE;cACvDkB,SAAS,EAAE,cAAcxJ,YAAY,CAAC+H,YAAY,CAAC0B,MAAM;YAC3D,CAAE;YAAAqJ,QAAA,gBAEA5Q,OAAA;cACEoK,EAAE,EAAC,0BAA0B;cAC7B4M,SAAS,EAAC,0BAA0B;cACpC7K,KAAK,EAAE;gBACLiE,QAAQ,EAAE,UAAU;gBACpBkB,OAAO,EAAE,MAAM;gBACfW,cAAc,EAAE,QAAQ;gBACxBV,UAAU,EAAE,QAAQ;gBACpBzL,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACiC,SAAS;gBAC/CkJ,YAAY,EAAE,MAAM;gBACpBD,OAAO,EAAE,QAAQ;gBACjBkC,QAAQ,EAAE;cACZ,CAAE;cAAArC,QAAA,gBAGF5Q,OAAA;gBACEgX,SAAS,EAAC,uBAAuB;gBACjC7K,KAAK,EAAE;kBACLiE,QAAQ,EAAE,UAAU;kBACpBuC,GAAG,EAAE,QAAQ;kBACbG,MAAM,EAAE,QAAQ;kBAChBF,IAAI,EAAE,QAAQ;kBACdC,KAAK,EAAE,QAAQ;kBACf/M,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACgB,OAAO,GAAG,IAAI;kBACpDmK,YAAY,EAAE,KAAK;kBACnB+B,MAAM,EAAE,CAAC;kBACTkE,aAAa,EAAE,MAAM,CAAC;gBACxB;cAAE;gBAAAzW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGD,CACC;gBAAEyJ,EAAE,EAAE,SAAS;gBAAEwF,KAAK,EAAE,sBAAsB;gBAAEhP,IAAI,EAAE,IAAI;gBAAEsW,UAAU,EAAE;cAAU,CAAC,EACnF;gBAAE9M,EAAE,EAAE,WAAW;gBAAEwF,KAAK,EAAE,uBAAuB;gBAAEhP,IAAI,EAAE,IAAI;gBAAEsW,UAAU,EAAE;cAAY,CAAC,EACxF;gBAAE9M,EAAE,EAAE,OAAO;gBAAEwF,KAAK,EAAE,iBAAiB;gBAAEhP,IAAI,EAAE,KAAK;gBAAEsW,UAAU,EAAE;cAAQ,CAAC,CAC5E,CAAC7W,GAAG,CAAC,CAACQ,GAAG,EAAEwR,KAAK,kBACfrS,OAAA;gBAEEgX,SAAS,EAAE,cAAc3T,YAAY,KAAKxC,GAAG,CAACuJ,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;gBACnE6G,OAAO,EAAEA,CAAA,KAAM3N,eAAe,CAACzC,GAAG,CAACuJ,EAAE,CAAE;gBACvC8G,YAAY,EAAGnH,CAAC,IAAK;kBACnB;kBACA,IAAI1G,YAAY,KAAKxC,GAAG,CAACuJ,EAAE,EAAE;oBAC3BL,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACpG,KAAK,GAAGjI,YAAY,CAAC+H,YAAY,CAACgB,OAAO;kBAC1D;gBACF,CAAE;gBACFsK,YAAY,EAAGpH,CAAC,IAAK;kBACnB;kBACA,IAAI1G,YAAY,KAAKxC,GAAG,CAACuJ,EAAE,EAAE;oBAC3BL,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACpG,KAAK,GAAGjI,YAAY,CAAC+H,YAAY,CAACG,IAAI;kBACvD;gBACF,CAAE;gBACFmR,OAAO,EAAGpN,CAAC,IAAK;kBACd;kBACAA,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACxE,OAAO,GAAG,aAAa7J,YAAY,CAAC+H,YAAY,CAACgB,OAAO,EAAE;kBACzEkD,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACiL,aAAa,GAAG,KAAK;gBACtC,CAAE;gBACFC,MAAM,EAAGtN,CAAC,IAAK;kBACb;kBACAA,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACxE,OAAO,GAAG,MAAM;gBACjC,CAAE;gBACF,cAAY,aAAa9G,GAAG,CAAC+O,KAAK,MAAO;gBACzC,iBAAevM,YAAY,KAAKxC,GAAG,CAACuJ,EAAG;gBACvCgD,IAAI,EAAC,KAAK;gBACVvJ,QAAQ,EAAE,CAAE;gBACZsI,KAAK,EAAE;kBACLkF,IAAI,EAAE,CAAC;kBACPjB,QAAQ,EAAE,UAAU;kBACpB2C,MAAM,EAAE,EAAE;kBACVhC,OAAO,EAAE,eAAe;kBACxB5J,SAAS,EAAE,MAAM;kBAAE;kBACnBf,MAAM,EAAE,MAAM;kBACdN,UAAU,EAAE,aAAa;kBACzBC,KAAK,EAAE1C,YAAY,KAAKxC,GAAG,CAACuJ,EAAE,GAC1B,OAAO,GACPtM,YAAY,CAAC+H,YAAY,CAACG,IAAI;kBAClC8K,MAAM,EAAE,SAAS;kBACjBe,QAAQ,EAAE,MAAM;kBAChBD,UAAU,EAAEvO,YAAY,KAAKxC,GAAG,CAACuJ,EAAE,GAAG,GAAG,GAAG,GAAG;kBAC/C4G,YAAY,EAAE,KAAK;kBACnBpK,UAAU,EAAE,eAAe;kBAC3B0K,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBU,cAAc,EAAE,QAAQ;kBACxBF,GAAG,EAAE,QAAQ;kBACb8B,SAAS,EAAE,QAAQ;kBACnByD,UAAU,EAAE,QAAQ;kBACpBC,UAAU,EAAE,MAAM;kBAClBC,uBAAuB,EAAE,aAAa,CAAC;gBACzC,CAAE;gBAAA5G,QAAA,gBAGF5Q,OAAA;kBAAA4Q,QAAA,EAAO/P,GAAG,CAACD;gBAAI;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvBX,OAAA;kBAAMmM,KAAK,EAAE;oBAAEjF,UAAU,EAAE;kBAAS,CAAE;kBAAA0J,QAAA,EACnCrN,WAAW,GAAG,GAAG,GAAG1C,GAAG,CAACqW,UAAU,GAAGrW,GAAG,CAAC+O,KAAK,CAAClE,OAAO,CAAC7K,GAAG,CAACD,IAAI,GAAG,GAAG,EAAE,EAAE;gBAAC;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC;cAAA,GA1DFE,GAAG,CAACuJ,EAAE;gBAAA5J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA2DL,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNX,OAAA;cAAKmM,KAAK,EAAE;gBACV0H,SAAS,EAAE,QAAQ;gBACnB8B,SAAS,EAAE,MAAM;gBACjB5E,OAAO,EAAE,QAAQ;gBACjBc,QAAQ,EAAE,QAAQ;gBAClB9L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAAC8N,SAAS;gBAC1CiC,SAAS,EAAE;cACb,CAAE;cAAAhF,QAAA,GACCvN,YAAY,KAAK,SAAS,IAAI,+DAA+D,EAC7FA,YAAY,KAAK,WAAW,IAAI,8DAA8D,EAC9FA,YAAY,KAAK,OAAO,IAAI,qEAAqE;YAAA;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL0C,YAAY,KAAK,SAAS,iBACzBrD,OAAA;YAAKmM,KAAK,EAAE;cACVrG,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACU,OAAO;cAC7CyK,YAAY,EAAE,MAAM;cACpBD,OAAO,EAAE,MAAM;cACf3K,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACO,MAAM,EAAE;cACvDkB,SAAS,EAAE,cAAcxJ,YAAY,CAAC+H,YAAY,CAAC0B,MAAM;YAC3D,CAAE;YAAAqJ,QAAA,gBACA5Q,OAAA;cAAImM,KAAK,EAAE;gBACTkH,MAAM,EAAE,cAAc;gBACtBxB,QAAQ,EAAE,QAAQ;gBAClBD,UAAU,EAAE,GAAG;gBACf7L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG;cACnC,CAAE;cAAA4K,QAAA,EAAC;YAEH;cAAApQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAELX,OAAA;cAAKmM,KAAK,EAAE;gBAAEmH,YAAY,EAAE;cAAO,CAAE;cAAA1C,QAAA,eACnC5Q,OAAA;gBAAOmM,KAAK,EAAE;kBACZmF,OAAO,EAAE,aAAa;kBACtBC,UAAU,EAAE,QAAQ;kBACpBQ,GAAG,EAAE,QAAQ;kBACbhB,OAAO,EAAE,gBAAgB;kBACzBjL,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACgB,OAAO;kBAC7Cd,KAAK,EAAE,OAAO;kBACdiL,YAAY,EAAE,MAAM;kBACpBF,MAAM,EAAEpO,qBAAqB,GAAG,aAAa,GAAG,SAAS;kBACzDmP,QAAQ,EAAE,MAAM;kBAChBD,UAAU,EAAE,GAAG;kBACfhL,UAAU,EAAE;gBACd,CAAE;gBAAAgK,QAAA,gBACA5Q,OAAA,CAACrB,QAAQ;kBAACyS,IAAI,EAAE;gBAAG;kBAAA5Q,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACrB+B,qBAAqB,GAAG,cAAc,GAAG,iBAAiB,eAC3D1C,OAAA;kBACEuO,IAAI,EAAC,MAAM;kBACX+G,MAAM,EAAC,sBAAsB;kBAC7BC,QAAQ,EAAExK,oBAAqB;kBAC/ByK,QAAQ,EAAE9S,qBAAsB;kBAChCyJ,KAAK,EAAE;oBAAEmF,OAAO,EAAE;kBAAO;gBAAE;kBAAA9Q,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENX,OAAA;cAAA4Q,QAAA,gBACE5Q,OAAA;gBAAImM,KAAK,EAAE;kBACTkH,MAAM,EAAE,YAAY;kBACpBxB,QAAQ,EAAE,QAAQ;kBAClBD,UAAU,EAAE,GAAG;kBACf7L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG;gBACnC,CAAE;gBAAA4K,QAAA,EAAC;cAEH;gBAAApQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACJiC,aAAa,CAACmN,MAAM,KAAK,CAAC,gBACzB/P,OAAA;gBAAKmM,KAAK,EAAE;kBACV0H,SAAS,EAAE,QAAQ;kBACnB9C,OAAO,EAAE,MAAM;kBACfjL,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACiC,SAAS;kBAC/CkJ,YAAY,EAAE,MAAM;kBACpB5K,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACO,MAAM;gBACvD,CAAE;gBAAAwK,QAAA,gBACA5Q,OAAA;kBAAKmM,KAAK,EAAE;oBAAE0F,QAAQ,EAAE,MAAM;oBAAEyB,YAAY,EAAE;kBAAO,CAAE;kBAAA1C,QAAA,EAAC;gBAAE;kBAAApQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAChEX,OAAA;kBAAImM,KAAK,EAAE;oBACTkH,MAAM,EAAE,cAAc;oBACtBtN,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG;kBACnC,CAAE;kBAAA4K,QAAA,EAAC;gBAEH;kBAAApQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLX,OAAA;kBAAGmM,KAAK,EAAE;oBACRkH,MAAM,EAAE,CAAC;oBACTtN,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAAC8N;kBACnC,CAAE;kBAAA/C,QAAA,EAAC;gBAEH;kBAAApQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,gBAENX,OAAA;gBAAKmM,KAAK,EAAE;kBACVmF,OAAO,EAAE,MAAM;kBACfS,GAAG,EAAE;gBACP,CAAE;gBAAAnB,QAAA,EACChO,aAAa,CAACvC,GAAG,CAAC,CAAC2J,IAAI,EAAE+L,GAAG,KAAK;kBAChC,MAAM;oBAAE/M,IAAI,EAAE0B;kBAAQ,CAAC,GAAGtF,QAAQ,CAACkF,OAAO,CAACC,IAAI,CAAC,WAAW,CAAC,CAACI,YAAY,CAAC,GAAGvI,IAAI,CAACgI,EAAE,IAAIJ,IAAI,CAACK,IAAI,EAAE,CAAC;kBACpG,oBACErK,OAAA;oBAAemM,KAAK,EAAE;sBACpBmF,OAAO,EAAE,MAAM;sBACfW,cAAc,EAAE,eAAe;sBAC/BV,UAAU,EAAE,QAAQ;sBACpBR,OAAO,EAAE,MAAM;sBACfjL,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACiC,SAAS;sBAC/CkJ,YAAY,EAAE,MAAM;sBACpB5K,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACO,MAAM;oBACvD,CAAE;oBAAAwK,QAAA,gBACA5Q,OAAA;sBAAKmM,KAAK,EAAE;wBACVmF,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpBQ,GAAG,EAAE;sBACP,CAAE;sBAAAnB,QAAA,gBACA5Q,OAAA;wBAAKmM,KAAK,EAAE;0BACV6F,KAAK,EAAE,MAAM;0BACbL,MAAM,EAAE,MAAM;0BACdX,YAAY,EAAE,KAAK;0BACnBlL,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACgB,OAAO;0BAC7CyK,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpBU,cAAc,EAAE,QAAQ;0BACxBlM,KAAK,EAAE,OAAO;0BACd8L,QAAQ,EAAE;wBACZ,CAAE;wBAAAjB,QAAA,EAAC;sBAEH;wBAAApQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNX,OAAA;wBAAA4Q,QAAA,gBACE5Q,OAAA;0BAAKmM,KAAK,EAAE;4BACVyF,UAAU,EAAE,GAAG;4BACf7L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG,IAAI;4BACrCsN,YAAY,EAAE;0BAChB,CAAE;0BAAA1C,QAAA,EACC5G,IAAI,CAACK;wBAAI;0BAAA7J,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,eACNX,OAAA;0BAAKmM,KAAK,EAAE;4BACV0F,QAAQ,EAAE,QAAQ;4BAClB9L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAAC8N;0BACnC,CAAE;0BAAA/C,QAAA,EAAC;wBAEH;0BAAApQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNX,OAAA;sBACEwL,IAAI,EAAEd,OAAO,CAACE,SAAU;sBACxBX,MAAM,EAAC,QAAQ;sBACfwL,GAAG,EAAC,qBAAqB;sBACzBtJ,KAAK,EAAE;wBACLmF,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpBQ,GAAG,EAAE,QAAQ;wBACbhB,OAAO,EAAE,aAAa;wBACtBjL,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACgB,OAAO;wBAC7Cd,KAAK,EAAE,OAAO;wBACd2P,cAAc,EAAE,MAAM;wBACtB1E,YAAY,EAAE,KAAK;wBACnBa,QAAQ,EAAE,QAAQ;wBAClBD,UAAU,EAAE,GAAG;wBACfhL,UAAU,EAAE;sBACd,CAAE;sBAAAgK,QAAA,gBAEF5Q,OAAA,CAACd,cAAc;wBAACkS,IAAI,EAAE;sBAAG;wBAAA5Q,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,QAE9B;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA,GA/DIoV,GAAG;oBAAAvV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAgER,CAAC;gBAEV,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGA0C,YAAY,KAAK,WAAW,iBAC3BrD,OAAA;YAAKmM,KAAK,EAAE;cACVmF,OAAO,EAAE,MAAM;cACfS,GAAG,EAAE;YACP,CAAE;YAAAnB,QAAA,gBAEA5Q,OAAA;cAAKmM,KAAK,EAAE;gBACVrG,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACU,OAAO;gBAC7CyK,YAAY,EAAE,MAAM;gBACpBD,OAAO,EAAE,MAAM;gBACf3K,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACO,MAAM,EAAE;gBACvDkB,SAAS,EAAE,cAAcxJ,YAAY,CAAC+H,YAAY,CAAC0B,MAAM;cAC3D,CAAE;cAAAqJ,QAAA,gBACA5Q,OAAA;gBAAImM,KAAK,EAAE;kBACTkH,MAAM,EAAE,cAAc;kBACtBxB,QAAQ,EAAE,QAAQ;kBAClBD,UAAU,EAAE,GAAG;kBACf7L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG,IAAI;kBACrCsL,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBQ,GAAG,EAAE;gBACP,CAAE;gBAAAnB,QAAA,EAAC;cAEH;gBAAApQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAELX,OAAA;gBAAKmM,KAAK,EAAE;kBACVmF,OAAO,EAAE,MAAM;kBACfkC,mBAAmB,EAAE,sCAAsC;kBAC3DzB,GAAG,EAAE;gBACP,CAAE;gBAAAnB,QAAA,EACC,CACC;kBACE6G,OAAO,EAAE,aAAa;kBACtB7W,IAAI,EAAE,IAAI;kBACVmF,KAAK,EAAE,SAAS;kBAChB2R,MAAM,EAAE,CAAC,UAAU,EAAE,gBAAgB,EAAE,YAAY,EAAE,eAAe,CAAC;kBACrEC,SAAS,EAAE;gBACb,CAAC,EACD;kBACEF,OAAO,EAAE,kBAAkB;kBAC3B7W,IAAI,EAAE,IAAI;kBACVmF,KAAK,EAAE,SAAS;kBAChB2R,MAAM,EAAE,CAAC,iBAAiB,EAAE,YAAY,EAAE,kBAAkB,EAAE,sBAAsB,CAAC;kBACrFC,SAAS,EAAE;gBACb,CAAC,EACD;kBACEF,OAAO,EAAE,SAAS;kBAClB7W,IAAI,EAAE,IAAI;kBACVmF,KAAK,EAAE,SAAS;kBAChB2R,MAAM,EAAE,CAAC,WAAW,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,iBAAiB,CAAC;kBAC9EC,SAAS,EAAE;gBACb,CAAC,EACD;kBACEF,OAAO,EAAE,WAAW;kBACpB7W,IAAI,EAAE,IAAI;kBACVmF,KAAK,EAAE,SAAS;kBAChB2R,MAAM,EAAE,CAAC,mBAAmB,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,cAAc,CAAC;kBAC1FC,SAAS,EAAE;gBACb,CAAC,CACF,CAACtX,GAAG,CAAC,CAACoX,OAAO,EAAEpF,KAAK,kBACnBrS,OAAA;kBAAiBmM,KAAK,EAAE;oBACtBrG,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACiC,SAAS;oBAC/CkJ,YAAY,EAAE,MAAM;oBACpBD,OAAO,EAAE,QAAQ;oBACjB3K,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACO,MAAM,EAAE;oBACvD0K,MAAM,EAAE,SAAS;oBACjBlK,UAAU,EAAE;kBACd,CAAE;kBACFsK,YAAY,EAAGnH,CAAC,IAAK;oBACnBA,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAAC1F,SAAS,GAAG,kBAAkB;oBACpDsD,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAAC7E,SAAS,GAAG,cAAcxJ,YAAY,CAAC+H,YAAY,CAAC0B,MAAM,EAAE;kBACpF,CAAE;kBACF4J,YAAY,EAAGpH,CAAC,IAAK;oBACnBA,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAAC1F,SAAS,GAAG,eAAe;oBACjDsD,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAAC7E,SAAS,GAAG,MAAM;kBAC1C,CAAE;kBAAAsJ,QAAA,gBACA5Q,OAAA;oBAAKmM,KAAK,EAAE;sBACVmF,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBQ,GAAG,EAAE,MAAM;sBACXuB,YAAY,EAAE;oBAChB,CAAE;oBAAA1C,QAAA,gBACA5Q,OAAA;sBAAKmM,KAAK,EAAE;wBACV6F,KAAK,EAAE,MAAM;wBACbL,MAAM,EAAE,MAAM;wBACdX,YAAY,EAAE,MAAM;wBACpBlL,UAAU,EAAE2R,OAAO,CAAC1R,KAAK;wBACzBuL,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpBU,cAAc,EAAE,QAAQ;wBACxBJ,QAAQ,EAAE;sBACZ,CAAE;sBAAAjB,QAAA,EACC6G,OAAO,CAAC7W;oBAAI;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNX,OAAA;sBAAA4Q,QAAA,gBACE5Q,OAAA;wBAAImM,KAAK,EAAE;0BACTkH,MAAM,EAAE,CAAC;0BACTxB,QAAQ,EAAE,QAAQ;0BAClBD,UAAU,EAAE,GAAG;0BACf7L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG;wBACnC,CAAE;wBAAA4K,QAAA,EACC6G,OAAO,CAACA;sBAAO;wBAAAjX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACd,CAAC,eACLX,OAAA;wBAAGmM,KAAK,EAAE;0BACRkH,MAAM,EAAE,CAAC;0BACTxB,QAAQ,EAAE,QAAQ;0BAClB9L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAAC8N;wBACnC,CAAE;wBAAA/C,QAAA,GACC6G,OAAO,CAACE,SAAS,EAAC,sBACrB;sBAAA;wBAAAnX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENX,OAAA;oBAAKmM,KAAK,EAAE;sBACVmF,OAAO,EAAE,MAAM;sBACfsC,QAAQ,EAAE,MAAM;sBAChB7B,GAAG,EAAE,QAAQ;sBACbuB,YAAY,EAAE;oBAChB,CAAE;oBAAA1C,QAAA,EACC6G,OAAO,CAACC,MAAM,CAACrX,GAAG,CAAC,CAACuX,KAAK,EAAEC,UAAU,kBACpC7X,OAAA;sBAAuBmM,KAAK,EAAE;wBAC5B4E,OAAO,EAAE,gBAAgB;wBACzBjL,UAAU,EAAE2R,OAAO,CAAC1R,KAAK,GAAG,IAAI;wBAChCA,KAAK,EAAE0R,OAAO,CAAC1R,KAAK;wBACpBiL,YAAY,EAAE,KAAK;wBACnBa,QAAQ,EAAE,QAAQ;wBAClBD,UAAU,EAAE;sBACd,CAAE;sBAAAhB,QAAA,EACCgH;oBAAK,GARGC,UAAU;sBAAArX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OASf,CACP;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eAENX,OAAA;oBAAQmM,KAAK,EAAE;sBACb6F,KAAK,EAAE,MAAM;sBACbjB,OAAO,EAAE,SAAS;sBAClBjL,UAAU,EAAE2R,OAAO,CAAC1R,KAAK;sBACzBA,KAAK,EAAE,OAAO;sBACdK,MAAM,EAAE,MAAM;sBACd4K,YAAY,EAAE,KAAK;sBACnBa,QAAQ,EAAE,QAAQ;sBAClBD,UAAU,EAAE,GAAG;sBACfd,MAAM,EAAE,SAAS;sBACjBlK,UAAU,EAAE;oBACd,CAAE;oBAAAgK,QAAA,EAAC;kBAEH;oBAAApQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA,GAtFD0R,KAAK;kBAAA7R,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAuFV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNX,OAAA;cAAKmM,KAAK,EAAE;gBACVrG,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACU,OAAO;gBAC7CyK,YAAY,EAAE,MAAM;gBACpBD,OAAO,EAAE,MAAM;gBACf3K,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACO,MAAM,EAAE;gBACvDkB,SAAS,EAAE,cAAcxJ,YAAY,CAAC+H,YAAY,CAAC0B,MAAM;cAC3D,CAAE;cAAAqJ,QAAA,gBACA5Q,OAAA;gBAAImM,KAAK,EAAE;kBACTkH,MAAM,EAAE,cAAc;kBACtBxB,QAAQ,EAAE,QAAQ;kBAClBD,UAAU,EAAE,GAAG;kBACf7L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG,IAAI;kBACrCsL,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBQ,GAAG,EAAE;gBACP,CAAE;gBAAAnB,QAAA,EAAC;cAEH;gBAAApQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAELX,OAAA;gBAAKmM,KAAK,EAAE;kBACVmF,OAAO,EAAE,MAAM;kBACfkC,mBAAmB,EAAE,sCAAsC;kBAC3DzB,GAAG,EAAE;gBACP,CAAE;gBAAAnB,QAAA,EACC,CACC;kBAAEvG,IAAI,EAAE,sBAAsB;kBAAEzJ,IAAI,EAAE,IAAI;kBAAEkX,KAAK,EAAE;gBAAa,CAAC,EACjE;kBAAEzN,IAAI,EAAE,kBAAkB;kBAAEzJ,IAAI,EAAE,GAAG;kBAAEkX,KAAK,EAAE;gBAAiB,CAAC,EAChE;kBAAEzN,IAAI,EAAE,cAAc;kBAAEzJ,IAAI,EAAE,IAAI;kBAAEkX,KAAK,EAAE;gBAAa,CAAC,EACzD;kBAAEzN,IAAI,EAAE,iBAAiB;kBAAEzJ,IAAI,EAAE,IAAI;kBAAEkX,KAAK,EAAE;gBAAa,CAAC,EAC5D;kBAAEzN,IAAI,EAAE,gBAAgB;kBAAEzJ,IAAI,EAAE,IAAI;kBAAEkX,KAAK,EAAE;gBAAc,CAAC,EAC5D;kBAAEzN,IAAI,EAAE,gBAAgB;kBAAEzJ,IAAI,EAAE,GAAG;kBAAEkX,KAAK,EAAE;gBAAY,CAAC,CAC1D,CAACzX,GAAG,CAAC,CAAC0X,QAAQ,EAAE1F,KAAK,kBACpBrS,OAAA;kBAAiBmM,KAAK,EAAE;oBACtBmF,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBQ,GAAG,EAAE,MAAM;oBACXhB,OAAO,EAAE,MAAM;oBACfjL,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACiC,SAAS;oBAC/CkJ,YAAY,EAAE,MAAM;oBACpB5K,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACO,MAAM,EAAE;oBACvD0K,MAAM,EAAE,SAAS;oBACjBlK,UAAU,EAAE;kBACd,CAAE;kBACFsK,YAAY,EAAGnH,CAAC,IAAK;oBACnBA,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAACrG,UAAU,GAAGhI,YAAY,CAAC+H,YAAY,CAACgB,OAAO,GAAG,IAAI;oBAC3EkD,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAACzE,WAAW,GAAG5J,YAAY,CAAC+H,YAAY,CAACgB,OAAO;kBACvE,CAAE;kBACFsK,YAAY,EAAGpH,CAAC,IAAK;oBACnBA,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAACrG,UAAU,GAAGhI,YAAY,CAAC+H,YAAY,CAACiC,SAAS;oBACtEiC,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAACzE,WAAW,GAAG5J,YAAY,CAAC+H,YAAY,CAACO,MAAM;kBACtE,CAAE;kBAAAwK,QAAA,gBACA5Q,OAAA;oBAAKmM,KAAK,EAAE;sBACV6F,KAAK,EAAE,MAAM;sBACbL,MAAM,EAAE,MAAM;sBACdX,YAAY,EAAE,KAAK;sBACnBlL,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACgB,OAAO;sBAC7CyK,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBU,cAAc,EAAE,QAAQ;sBACxBJ,QAAQ,EAAE;oBACZ,CAAE;oBAAAjB,QAAA,EACCmH,QAAQ,CAACnX;kBAAI;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACNX,OAAA;oBAAA4Q,QAAA,gBACE5Q,OAAA;sBAAKmM,KAAK,EAAE;wBACVyF,UAAU,EAAE,GAAG;wBACf7L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG,IAAI;wBACrCsN,YAAY,EAAE;sBAChB,CAAE;sBAAA1C,QAAA,EACCmH,QAAQ,CAAC1N;oBAAI;sBAAA7J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,eACNX,OAAA;sBAAKmM,KAAK,EAAE;wBACV0F,QAAQ,EAAE,QAAQ;wBAClB9L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAAC8N;sBACnC,CAAE;sBAAA/C,QAAA,EACCmH,QAAQ,CAACD;oBAAK;sBAAAtX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GA7CE0R,KAAK;kBAAA7R,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA8CV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGA0C,YAAY,KAAK,OAAO,iBACvBrD,OAAA;YAAKmM,KAAK,EAAE;cACVrG,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACU,OAAO;cAC7CyK,YAAY,EAAE,MAAM;cACpBD,OAAO,EAAE,MAAM;cACf3K,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACO,MAAM,EAAE;cACvDkB,SAAS,EAAE,cAAcxJ,YAAY,CAAC+H,YAAY,CAAC0B,MAAM;YAC3D,CAAE;YAAAqJ,QAAA,gBACA5Q,OAAA;cAAImM,KAAK,EAAE;gBACTkH,MAAM,EAAE,cAAc;gBACtBxB,QAAQ,EAAE,QAAQ;gBAClBD,UAAU,EAAE,GAAG;gBACf7L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG,IAAI;gBACrCsL,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBQ,GAAG,EAAE;cACP,CAAE;cAAAnB,QAAA,EAAC;YAEH;cAAApQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAELX,OAAA;cAAKmM,KAAK,EAAE;gBACVmF,OAAO,EAAE,MAAM;gBACfkC,mBAAmB,EAAE,sCAAsC;gBAC3DzB,GAAG,EAAE;cACP,CAAE;cAAAnB,QAAA,EACC,CACC;gBACEvG,IAAI,EAAE,uBAAuB;gBAC7BzJ,IAAI,EAAE,IAAI;gBACV4E,WAAW,EAAE,8CAA8C;gBAC3DO,KAAK,EAAE;cACT,CAAC,EACD;gBACEsE,IAAI,EAAE,mBAAmB;gBACzBzJ,IAAI,EAAE,IAAI;gBACV4E,WAAW,EAAE,sDAAsD;gBACnEO,KAAK,EAAE;cACT,CAAC,EACD;gBACEsE,IAAI,EAAE,gBAAgB;gBACtBzJ,IAAI,EAAE,IAAI;gBACV4E,WAAW,EAAE,gDAAgD;gBAC7DO,KAAK,EAAE;cACT,CAAC,EACD;gBACEsE,IAAI,EAAE,aAAa;gBACnBzJ,IAAI,EAAE,GAAG;gBACT4E,WAAW,EAAE,6CAA6C;gBAC1DO,KAAK,EAAE;cACT,CAAC,EACD;gBACEsE,IAAI,EAAE,aAAa;gBACnBzJ,IAAI,EAAE,IAAI;gBACV4E,WAAW,EAAE,2CAA2C;gBACxDO,KAAK,EAAE;cACT,CAAC,EACD;gBACEsE,IAAI,EAAE,kBAAkB;gBACxBzJ,IAAI,EAAE,IAAI;gBACV4E,WAAW,EAAE,qCAAqC;gBAClDO,KAAK,EAAE;cACT,CAAC,CACF,CAAC1F,GAAG,CAAC,CAAC2X,IAAI,EAAE3F,KAAK,kBAChBrS,OAAA;gBAAiBmM,KAAK,EAAE;kBACtBrG,UAAU,EAAEhI,YAAY,CAAC+H,YAAY,CAACiC,SAAS;kBAC/CkJ,YAAY,EAAE,MAAM;kBACpBD,OAAO,EAAE,QAAQ;kBACjB3K,MAAM,EAAE,aAAatI,YAAY,CAAC+H,YAAY,CAACO,MAAM,EAAE;kBACvD0K,MAAM,EAAE,SAAS;kBACjBlK,UAAU,EAAE;gBACd,CAAE;gBACFsK,YAAY,EAAGnH,CAAC,IAAK;kBACnBA,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAAC1F,SAAS,GAAG,kBAAkB;kBACpDsD,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAAC7E,SAAS,GAAG,cAAcxJ,YAAY,CAAC+H,YAAY,CAAC0B,MAAM,EAAE;gBACpF,CAAE;gBACF4J,YAAY,EAAGpH,CAAC,IAAK;kBACnBA,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAAC1F,SAAS,GAAG,eAAe;kBACjDsD,CAAC,CAACqK,aAAa,CAACjI,KAAK,CAAC7E,SAAS,GAAG,MAAM;gBAC1C,CAAE;gBAAAsJ,QAAA,gBACA5Q,OAAA;kBAAKmM,KAAK,EAAE;oBACVmF,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBQ,GAAG,EAAE,MAAM;oBACXuB,YAAY,EAAE;kBAChB,CAAE;kBAAA1C,QAAA,gBACA5Q,OAAA;oBAAKmM,KAAK,EAAE;sBACV6F,KAAK,EAAE,MAAM;sBACbL,MAAM,EAAE,MAAM;sBACdX,YAAY,EAAE,MAAM;sBACpBlL,UAAU,EAAEkS,IAAI,CAACjS,KAAK;sBACtBuL,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBU,cAAc,EAAE,QAAQ;sBACxBJ,QAAQ,EAAE;oBACZ,CAAE;oBAAAjB,QAAA,EACCoH,IAAI,CAACpX;kBAAI;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACNX,OAAA;oBAAA4Q,QAAA,eACE5Q,OAAA;sBAAImM,KAAK,EAAE;wBACTkH,MAAM,EAAE,CAAC;wBACTxB,QAAQ,EAAE,QAAQ;wBAClBD,UAAU,EAAE,GAAG;wBACf7L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAACG;sBACnC,CAAE;sBAAA4K,QAAA,EACCoH,IAAI,CAAC3N;oBAAI;sBAAA7J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENX,OAAA;kBAAGmM,KAAK,EAAE;oBACRkH,MAAM,EAAE,YAAY;oBACpBxB,QAAQ,EAAE,QAAQ;oBAClB9L,KAAK,EAAEjI,YAAY,CAAC+H,YAAY,CAAC8N,SAAS;oBAC1CF,UAAU,EAAE;kBACd,CAAE;kBAAA7C,QAAA,EACCoH,IAAI,CAACxS;gBAAW;kBAAAhF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eAEJX,OAAA;kBAAQmM,KAAK,EAAE;oBACb6F,KAAK,EAAE,MAAM;oBACbjB,OAAO,EAAE,SAAS;oBAClBjL,UAAU,EAAEkS,IAAI,CAACjS,KAAK;oBACtBA,KAAK,EAAE,OAAO;oBACdK,MAAM,EAAE,MAAM;oBACd4K,YAAY,EAAE,KAAK;oBACnBa,QAAQ,EAAE,QAAQ;oBAClBD,UAAU,EAAE,GAAG;oBACfd,MAAM,EAAE,SAAS;oBACjBlK,UAAU,EAAE;kBACd,CAAE;kBAAAgK,QAAA,EAAC;gBAEH;kBAAApQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,GApED0R,KAAK;gBAAA7R,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAqEV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EACAiB,SAAS,KAAK,WAAW,iBAAI5B,OAAA,CAACzC,KAAK;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACtCiB,SAAS,KAAK,KAAK,iBAAI5B,OAAA,CAAC1C,GAAG;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAC9BiB,SAAS,KAAK,OAAO,IAAI,CAAAQ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+P,KAAK,MAAKtP,WAAW,iBACnD7C,OAAA;QAAKmM,KAAK,EAAE;UAAE4E,OAAO,EAAE;QAAO,CAAE;QAAAH,QAAA,eAC9B5Q,OAAA;UAAKmM,KAAK,EAAEhE,QAAQ,CAAC,MAAM,CAAE;UAAAyI,QAAA,gBAC3B5Q,OAAA;YAAImM,KAAK,EAAE;cACTwJ,SAAS,EAAE,CAAC;cACZ5P,KAAK,EAAE;YACT,CAAE;YAAA6K,QAAA,EAAC;UAAW;YAAApQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnBX,OAAA;YAAKmM,KAAK,EAAE;cACVmF,OAAO,EAAE,MAAM;cACfS,GAAG,EAAE,MAAM;cACXuB,YAAY,EAAE;YAChB,CAAE;YAAA1C,QAAA,gBACA5Q,OAAA;cACEmM,KAAK,EAAE;gBACL,GAAGhE,QAAQ,CAAC,eAAe,CAAC;gBAC5BrC,UAAU,EAAE/C,QAAQ,KAAK,OAAO,GAC9BjF,YAAY,CAAC+H,YAAY,CAACgB,OAAO,GAAG,aAAa;gBACnDd,KAAK,EAAEhD,QAAQ,KAAK,OAAO,GACzB,OAAO,GAAG,MAAM;gBAClBqD,MAAM,EAAE;cACV,CAAE;cACF6K,OAAO,EAAEA,CAAA,KAAMjO,WAAW,CAAC,OAAO,CAAE;cAAA4N,QAAA,EACrC;YAED;cAAApQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTX,OAAA;cACEmM,KAAK,EAAE;gBACL,GAAGhE,QAAQ,CAAC,eAAe,CAAC;gBAC5BrC,UAAU,EAAE/C,QAAQ,KAAK,WAAW,GAClCjF,YAAY,CAAC+H,YAAY,CAACgB,OAAO,GAAG,aAAa;gBACnDd,KAAK,EAAEhD,QAAQ,KAAK,WAAW,GAC7B,OAAO,GAAG,MAAM;gBAClBqD,MAAM,EAAE;cACV,CAAE;cACF6K,OAAO,EAAEA,CAAA,KAAMjO,WAAW,CAAC,WAAW,CAAE;cAAA4N,QAAA,EACzC;YAED;cAAApQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAELoC,QAAQ,KAAK,OAAO,iBACnB/C,OAAA;YAAA4Q,QAAA,gBACE5Q,OAAA;cAAImM,KAAK,EAAE;gBACTmH,YAAY,EAAE,MAAM;gBACpBvN,KAAK,EAAE;cACT,CAAE;cAAA6K,QAAA,EAAC;YAAS;cAAApQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjBX,OAAA;cAAKmM,KAAK,EAAE;gBACVvG,eAAe,EAAE,SAAS;gBAC1BQ,MAAM,EAAE,mBAAmB;gBAC3B4K,YAAY,EAAE,KAAK;gBACnBD,OAAO,EAAE;cACX,CAAE;cAAAH,QAAA,EACC9N,QAAQ,CAACzC,GAAG,CAAC,CAAC+B,IAAI,EAAE2T,GAAG,kBACtB/V,OAAA;gBAAemM,KAAK,EAAE;kBACpB4E,OAAO,EAAE,MAAM;kBACf5K,YAAY,EAAE,gBAAgB;kBAC9BJ,KAAK,EAAE;gBACT,CAAE;gBAAA6K,QAAA,EACCxO,IAAI,CAAC+P;cAAK,GALH4D,GAAG;gBAAAvV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMR,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEAoC,QAAQ,KAAK,WAAW,iBACvB/C,OAAA;YAAA4Q,QAAA,gBACE5Q,OAAA;cAAImM,KAAK,EAAE;gBACTmH,YAAY,EAAE,MAAM;gBACpBvN,KAAK,EAAE;cACT,CAAE;cAAA6K,QAAA,EAAC;YAAa;cAAApQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBX,OAAA;cAAGmM,KAAK,EAAE;gBACR2F,OAAO,EAAE,GAAG;gBACZ/L,KAAK,EAAE;cACT,CAAE;cAAA6K,QAAA,EAAC;YAA+B;cAAApQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,EAGNsC,YAAY,iBACXjD,OAAA;MAAKmM,KAAK,EAAE;QACV,GAAGhE,QAAQ,CAAC,cAAc,CAAC;QAC3BvC,eAAe,EAAE3C,YAAY,CAACsL,IAAI,KAAK,OAAO,GAAG,SAAS,GAC3CtL,YAAY,CAACsL,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS;QACtExI,KAAK,EAAE,OAAO;QACdK,MAAM,EAAE;MACV,CAAE;MAAAwK,QAAA,EACC3N,YAAY,CAACqL;IAAG;MAAA9N,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CACN,eAGDX,OAAA;MAAA4Q,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAApQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACM,EAAA,CA7/FID,YAAY;AAAAiX,EAAA,GAAZjX,YAAY;AA8/FlB,eAAeA,YAAY;AAAC,IAAAiX,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}