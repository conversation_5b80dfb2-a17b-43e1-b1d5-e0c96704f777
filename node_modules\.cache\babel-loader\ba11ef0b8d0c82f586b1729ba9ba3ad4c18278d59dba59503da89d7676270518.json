{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\quiz\\\\aich (4)\\\\aich (3)\\\\aich(5)\\\\src\\\\EduAIChatBot.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport { getDoc, doc } from 'firebase/firestore';\nimport Faq from './Faq';\nimport Exams from \"./Exams\";\nimport Coding from \"./Coding\";\nimport { auth, db } from './firebaseConfig';\nimport axios from \"axios\";\nimport { sidebarItems } from './sidebarItems';\nimport { onAuthStateChanged } from 'firebase/auth';\nimport globalStyles from './styles.js';\nimport { FiMenu, FiX, FiChevronDown, FiChevronRight, FiFileText, FiCode, FiHelpCircle, FiAward, FiBook, FiUser, FiShield, FiSearch, FiUpload, FiLogIn, FiLogOut, FiBriefcase, FiBarChart2, FiLayers, FiCheckCircle, FiExternalLink, FiHeart, FiClock, FiRefreshCw } from \"react-icons/fi\";\nimport { createClient } from '@supabase/supabase-js';\nimport { Bar } from 'react-chartjs-2';\nimport { Chart, BarElement, CategoryScale, LinearScale, Tooltip, Legend } from 'chart.js';\nimport ReactMarkdown from 'react-markdown';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nChart.register(BarElement, CategoryScale, LinearScale, Tooltip, Legend);\n\n// Enhanced sidebar items with icons\nconst updatedSidebarItems = sidebarItems.map(item => {\n  const iconMap = {\n    \"resume\": /*#__PURE__*/_jsxDEV(FiFileText, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 15\n    }, this),\n    \"dsa\": /*#__PURE__*/_jsxDEV(FiCode, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 12\n    }, this),\n    \"coding\": /*#__PURE__*/_jsxDEV(FiLayers, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 15\n    }, this),\n    \"resources\": /*#__PURE__*/_jsxDEV(FiBriefcase, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 18\n    }, this),\n    \"quizzes\": /*#__PURE__*/_jsxDEV(FiCheckCircle, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 16\n    }, this),\n    \"aptitude\": /*#__PURE__*/_jsxDEV(FiBarChart2, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 17\n    }, this),\n    \"academics\": /*#__PURE__*/_jsxDEV(FiBook, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 18\n    }, this),\n    \"faq\": /*#__PURE__*/_jsxDEV(FiHelpCircle, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 12\n    }, this),\n    \"admin\": /*#__PURE__*/_jsxDEV(FiShield, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 14\n    }, this)\n  };\n  return {\n    ...item,\n    icon: iconMap[item.tab] || iconMap[item.title.toLowerCase()] || /*#__PURE__*/_jsxDEV(FiAward, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 69\n    }, this)\n  };\n});\nconst EduAIChatBot = () => {\n  _s();\n  // State declarations\n  const [input, setInput] = useState(\"\");\n  const [messages, setMessages] = useState([]);\n  const [userId, setUserId] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [knowledge, setKnowledge] = useState(\"\");\n  const [activeTab, setActiveTab] = useState(\"dashboard\");\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [expandedMenus, setExpandedMenus] = useState({});\n  const [user, setUser] = useState(null);\n  const [resumeUploadLoading, setResumeUploadLoading] = useState(false);\n  const [resumeUrl, setResumeUrl] = useState(null);\n  const [resourceUploadLoading, setResourceUploadLoading] = useState(false);\n  const [userResources] = useState([]);\n  const ADMIN_EMAIL = '<EMAIL>';\n  const [allUsers] = useState([]);\n  const [adminTab, setAdminTab] = useState('users');\n  const [notification, setNotification] = useState(null);\n  const [activityLog, setActivityLog] = useState([]);\n  const [resourcesTab, setResourcesTab] = useState('general'); // New state for resources tab\n  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1024); // Responsive state\n  const chatEndRef = useRef(null);\n\n  // Handle window resize for responsive tab navigation\n  useEffect(() => {\n    const handleResize = () => {\n      setWindowWidth(window.innerWidth);\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  // Enhanced DSA section states\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [sortBy, setSortBy] = useState('name');\n  const [favoriteCompanies, setFavoriteCompanies] = useState([]);\n  const [recentCompanies, setRecentCompanies] = useState([]);\n  const [showRevertButton, setShowRevertButton] = useState(true);\n\n  // API configurations\n  const API_KEY = \"AIzaSyC6kHWto78QdqHz7Uu9RzEXb443ZO7tG5M\";\n  const SUPABASE_URL = 'https://gziaptswfepiveyylven.supabase.co';\n  const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imd6aWFwdHN3ZmVwaXZleXlsdmVuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU2NzczNTYsImV4cCI6MjA2MTI1MzM1Nn0.wmqXZGffrox8E_PuCwbzh4xJEffsvFmZCVcF6WFAX6Q';\n  const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);\n\n  // Company categories for enhanced DSA section\n  const companyCategories = {\n    'FAANG': ['Facebook', 'Apple', 'Amazon', 'Netflix', 'Google'],\n    'Big Tech': ['Microsoft', 'Adobe', 'Salesforce', 'Oracle', 'IBM', 'Intel', 'Nvidia'],\n    'Startups': ['Stripe', 'Airbnb', 'Uber', 'Lyft', 'DoorDash', 'Instacart', 'Coinbase'],\n    'Finance': ['Goldman Sachs', 'JPMorgan', 'Morgan Stanley', 'BlackRock', 'Citadel', 'Two Sigma'],\n    'Consulting': ['McKinsey', 'BCG', 'Bain', 'Deloitte', 'Accenture', 'PwC'],\n    'E-commerce': ['Amazon', 'eBay', 'Shopify', 'Etsy', 'Wayfair', 'Booking.com'],\n    'Gaming': ['Blizzard', 'Epic Games', 'Riot Games', 'Unity', 'Roblox'],\n    'Indian': ['TCS', 'Infosys', 'Wipro', 'HCL', 'Flipkart', 'Paytm', 'Zomato', 'Swiggy']\n  };\n\n  // Complete list of companies\n  const companies = [\"Accenture\", \"Accolite\", \"Adobe\", \"Affirm\", \"Agoda\", \"Airbnb\", \"Airtel\", \"Akamar\", \"Akuna Capital\", \"Alibaba\", \"Altimetrik\", \"Amazon\", \"AMD\", \"Amdocs\", \"American Express\", \"Anduril\", \"Apple\", \"Arista Networks\", \"Arcesium\", \"Atlassian\", \"Attentive\", \"athenahealth\", \"Autodesk\", \"Avito\", \"Baidu\", \"Barclays\", \"BitGo\", \"BlackRock\", \"Blizzard\", \"Block\", \"Bloomberg\", \"BNY Mellon\", \"Boft\", \"Booking.com\", \"Bos\", \"BP\", \"ByteDance\", \"Cadence\", \"Capgemini\", \"Capital One\", \"CARS24\", \"carwale\", \"Cashfree\", \"Chewy\", \"Cisco\", \"Citadel\", \"Citrix\", \"Cloudera\", \"Cloudflare\", \"Cognizant\", \"Coinbase\", \"Commvault\", \"Confluent\", \"Coupang\", \"Coursera\", \"CrowdStrike\", \"Cruise\", \"Curefit\", \"Databricks\", \"Datadog\", \"DE Shaw\", \"Deloitte\", \"Dell\", \"Deliveroo\", \"Derantior\", \"Deutsche Bank\", \"Devflev\", \"Directi\", \"Disney\", \"Docusign\", \"DoorDash\", \"Dream11\", \"Dropbox\", \"DRW\", \"Dunzo\", \"eBay\", \"EPAM Systems\", \"Epic Systems\", \"Expedia\", \"FactSet\", \"Flexport\", \"Flipkart\", \"Freshworks\", \"GE Healthcare\", \"Geico\", \"Goldman Sachs\", \"Google\", \"Grab\", \"Grammarly\", \"Graviton\", \"Groww\", \"GSN Games\", \"Hashedin\", \"HCL\", \"HPE\", \"Hubspot\", \"Hudson River Trading\", \"Huawei\", \"IBM\", \"IMC\", \"Indeed\", \"Infosys\", \"InMobi\", \"Intel\", \"Intuit\", \"JPMorgan\", \"Jane Street\", \"Josh Technology\", \"Jump Trading\", \"Juspay\", \"Karat\", \"KLA\", \"LinkedIn\", \"LiveRamp\", \"Lowe's\", \"Lucid\", \"Lyft\", \"MakeMyTrip\", \"Mastercard\", \"MathWorks\", \"Media.net\", \"Meesho\", \"Mercari\", \"Meta\", \"Microsoft\", \"Millennium\", \"Mitsogo\", \"Moloco\", \"MongoDB\", \"Morgan Stanley\", \"Moveworks\", \"Myntra\", \"Nagarro\", \"NetApp\", \"Netflix\", \"Nextdoor\", \"Nielsen\", \"Nike\", \"Niantic\", \"Nordstrom\", \"Nutanix\", \"Nvidia\", \"Okta\", \"OKX\", \"OpenAI\", \"OpenText\", \"Oracle\", \"Otter.ai\", \"Oyo\", \"Ozon\", \"Palantir Technologies\", \"Palo Alto Networks\", \"PayPal\", \"Paytm\", \"Persistent Systems\", \"PhonePe\", \"Pinterest\", \"Pocket Gems\", \"Point72\", \"Pure Storage\", \"Qualcomm\", \"Quora\", \"Rakuten\", \"Razorpay\", \"RBC\", \"Reddit\", \"Revolut\", \"Robinhood\", \"Roblox\", \"Rubrik\", \"Salesforce\", \"Samsung\", \"SAP\", \"ServiceNow\", \"Shopify\", \"Siemens\", \"Sigmoid\", \"SIG\", \"Snowflake\", \"Snap\", \"Sofi\", \"Splunk\", \"Spotify\", \"Sprinklr\", \"Squarepoint Capital\", \"Stripe\", \"Swiggy\", \"TCS\", \"Tekion\", \"Tencent\", \"Tesla\", \"ThoughtSpot\", \"ThoughtWorks\", \"TikTok\", \"Tinkoff\", \"Trilogy\", \"Turing\", \"Turo\", \"Twilio\", \"Twitch\", \"Two Sigma\", \"Uber\", \"UiPath\", \"UKG\", \"Veeva Systems\", \"Verily\", \"Verkada\", \"Virtu Financial\", \"Visa\", \"VK\", \"VMware\", \"Walmart Labs\", \"WarnerMedia\", \"Wayfair\", \"Wells Fargo\", \"Wipro\", \"Wix\", \"Workday\", \"X\", \"Yahoo\", \"Yandex\", \"Yelp\", \"Zalando\", \"Zenefits\", \"Zepto\", \"Zeta\", \"Zillow\", \"Zoho\", \"Zomato\", \"ZScaler\", \"Zopsmart\"];\n\n  // Quiz buttons data\n  const quizButtons = [{\n    title: \"OP and CN Quiz\",\n    description: \"Test your knowledge of Operating System and Computer Networks\",\n    link: \"https://opcn.netlify.app\"\n  }, {\n    title: \"OOPs and DBMS Quiz\",\n    description: \"Challenge yourself with oops and dbms\",\n    link: \"https://oopsanddbms.netlify.app/\"\n  }, {\n    title: \"System Design Quiz\",\n    description: \"Test your system design knowledge\",\n    link: \"https://system-design041.netlify.app\"\n  }, {\n    title: \"Quantitative Aptitude and Reasoning Quiz\",\n    description: \"Practice common quant and reasoning questions\",\n    link: \"https://quantandreasoning.netlify.app\"\n  }, {\n    title: \"Cloud & DevOps Quiz\",\n    description: \"Test your knowledge of Cloud and DevOps concepts\",\n    link: \"https://cloud-devops.netlify.app\"\n  }, {\n    title: \"DSA Quiz\",\n    description: \"Data Structures and Algorithms quiz\",\n    link: \"https://dsa041.netlify.app\"\n  }, {\n    title: \"Operating System & Computer Networks Quiz\",\n    description: \"Quiz on OS and Computer Networks\",\n    link: \"https://opcn.netlify.app\"\n  }, {\n    title: \"Web Development Quiz\",\n    description: \"Quiz on Web Development topics\",\n    link: \"https://web-dev041.netlify.app\"\n  }];\n\n  // Use centralized styles\n  const styles = {\n    ...globalStyles,\n    appContainer: {\n      ...globalStyles.appContainer,\n      backgroundColor: globalStyles.currentTheme.background,\n      color: globalStyles.currentTheme.text\n    },\n    navbar: {\n      ...globalStyles.navbarFixed,\n      borderBottom: `1px solid ${globalStyles.currentTheme.border}`\n    },\n    sidebar: {\n      ...globalStyles.sidebarFixed,\n      backgroundColor: globalStyles.currentTheme.surface,\n      borderRight: `1px solid ${globalStyles.currentTheme.border}`,\n      transform: sidebarOpen ? 'translateX(0)' : 'translateX(-100%)'\n    },\n    sidebarItem: {\n      ...globalStyles.sidebarItemEdu,\n      color: globalStyles.currentTheme.text,\n      background: globalStyles.currentTheme.surface,\n      border: `1px solid ${globalStyles.currentTheme.border}`,\n      transition: 'all 0.3s ease',\n      '&:hover': {\n        background: globalStyles.currentTheme.primary,\n        color: 'white'\n      }\n    },\n    sidebarItemActive: {\n      ...globalStyles.sidebarItemActiveEdu,\n      color: 'white',\n      background: globalStyles.currentTheme.primary,\n      border: `1px solid ${globalStyles.currentTheme.primary}`\n    },\n    mainContent: {\n      ...globalStyles.mainContentEdu,\n      marginLeft: sidebarOpen ? '280px' : '0',\n      backgroundColor: globalStyles.currentTheme.background,\n      color: globalStyles.currentTheme.text,\n      minHeight: '100vh'\n    },\n    card: {\n      ...globalStyles.cardEdu,\n      backgroundColor: globalStyles.currentTheme.surface,\n      color: globalStyles.currentTheme.text,\n      border: `1px solid ${globalStyles.currentTheme.border}`,\n      boxShadow: `0 4px 6px ${globalStyles.currentTheme.shadow}`\n    },\n    buttonPrimary: {\n      ...globalStyles.buttonPrimary\n    },\n    inputField: {\n      ...globalStyles.inputField,\n      backgroundColor: '#fff',\n      color: '#333',\n      border: '1px solid #ddd',\n      '&:focus': {\n        borderColor: globalStyles.currentTheme.primary,\n        outline: 'none',\n        boxShadow: `0 0 0 2px ${globalStyles.currentTheme.primary}20`\n      }\n    },\n    chatBubbleUser: {\n      ...globalStyles.chatBubbleUser,\n      backgroundColor: globalStyles.currentTheme.primary,\n      color: 'white'\n    },\n    chatBubbleBot: {\n      ...globalStyles.chatBubbleBot,\n      backgroundColor: globalStyles.currentTheme.secondary,\n      color: globalStyles.currentTheme.text,\n      border: '1px solid transparent'\n    },\n    companyCard: {\n      ...globalStyles.companyCardEdu\n    },\n    quizCard: {\n      ...globalStyles.quizCardEdu,\n      backgroundColor: globalStyles.currentTheme.surface\n    },\n    notification: {\n      ...globalStyles.notification\n    }\n  };\n\n  // Helper function to apply styles with hover states\n  const getStyle = (styleName, hover = false) => {\n    const baseStyle = styles[styleName];\n    if (typeof baseStyle === 'function') return baseStyle();\n    if (hover && baseStyle['&:hover']) {\n      return {\n        ...baseStyle,\n        ...baseStyle['&:hover']\n      };\n    }\n    return baseStyle;\n  };\n\n  // Fetch user profile\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, user => {\n      if (user) {\n        setUserId(user.uid);\n      } else {\n        console.log('User is not authenticated');\n        setLoading(false);\n      }\n    });\n    return () => unsubscribe();\n  }, []);\n  useEffect(() => {\n    if (userId) {\n      const fetchUserProfile = async () => {\n        const userRef = doc(db, \"users\", userId);\n        const userDoc = await getDoc(userRef);\n        if (userDoc.exists()) {\n          const userData = userDoc.data();\n          // Profile pic functionality can be added later\n          console.log(\"User data loaded:\", userData.dp);\n        } else {\n          console.log(\"No such user!\");\n        }\n        setLoading(false);\n      };\n      fetchUserProfile();\n    }\n  }, [userId]);\n\n  // Fetch training data\n  useEffect(() => {\n    fetch(\"/training-data.txt\").then(res => res.text()).then(data => setKnowledge(data)).catch(err => console.error(\"Failed to load training data:\", err));\n  }, []);\n\n  // Supabase auth state\n  useEffect(() => {\n    supabase.auth.getSession().then(({\n      data: {\n        session\n      }\n    }) => {\n      setUser((session === null || session === void 0 ? void 0 : session.user) || null);\n    });\n    const {\n      data: listener\n    } = supabase.auth.onAuthStateChange((_event, session) => {\n      setUser((session === null || session === void 0 ? void 0 : session.user) || null);\n    });\n    return () => {\n      listener === null || listener === void 0 ? void 0 : listener.subscription.unsubscribe();\n    };\n  }, [supabase.auth]);\n\n  // Handle resume upload\n  const handleResumeUpload = async e => {\n    const file = e.target.files[0];\n    if (!file || !user) return;\n    setResumeUploadLoading(true);\n    const filePath = `${user.id}/${file.name}`;\n    const {\n      error\n    } = await supabase.storage.from('resumes').upload(filePath, file, {\n      upsert: true\n    });\n    if (!error) {\n      const {\n        data: urlData\n      } = supabase.storage.from('resumes').getPublicUrl(filePath);\n      setResumeUrl(urlData.publicUrl);\n      showNotification('Resume uploaded successfully!', 'success');\n      logActivity('Uploaded a resume');\n    } else {\n      showNotification('Resume upload failed.', 'error');\n    }\n    setResumeUploadLoading(false);\n  };\n\n  // Handle resource upload\n  const handleResourceUpload = async e => {\n    const file = e.target.files[0];\n    if (!file || !user) return;\n    setResourceUploadLoading(true);\n    const filePath = `${user.id}/${file.name}`;\n    const {\n      error\n    } = await supabase.storage.from('resources').upload(filePath, file, {\n      upsert: true\n    });\n    if (!error) {\n      showNotification('Resource uploaded!', 'success');\n      logActivity(`Uploaded resource: ${file.name}`);\n    } else {\n      showNotification('Resource upload failed.', 'error');\n    }\n    setResourceUploadLoading(false);\n  };\n\n  // Enhanced company click handler\n  const handleCompanyClick = company => {\n    // Add to recent companies\n    setRecentCompanies(prev => {\n      const filtered = prev.filter(c => c !== company);\n      return [company, ...filtered].slice(0, 5); // Keep only 5 recent\n    });\n    logActivity(`Viewed ${company} DSA questions`);\n    if (company.toLowerCase() === 'microsoft') {\n      window.location.href = '/company-dsa/Microsoft_questions.html';\n      return;\n    }\n    const formattedCompany = company.replace(/\\s+/g, '');\n    window.location.href = `/company-dsa/${formattedCompany}.html`;\n  };\n\n  // Toggle favorite company\n  const toggleFavorite = (company, e) => {\n    e.stopPropagation(); // Prevent company click\n    setFavoriteCompanies(prev => {\n      if (prev.includes(company)) {\n        return prev.filter(c => c !== company);\n      } else {\n        return [...prev, company];\n      }\n    });\n  };\n\n  // Revert header color changes\n  const revertHeaderChanges = () => {\n    setShowRevertButton(false);\n    showNotification('Header text color reverted to theme default!', 'success');\n\n    // Actually revert the header text color by updating the DOM\n    const eduNovaElement = document.querySelector('[data-header-title]');\n    const subtitleElement = document.querySelector('[data-header-subtitle]');\n    if (eduNovaElement) {\n      eduNovaElement.style.color = '#333';\n    }\n    if (subtitleElement) {\n      subtitleElement.style.color = '#333';\n    }\n  };\n\n  // Get filtered companies based on category and search\n  const getFilteredCompanies = () => {\n    let filtered = companies;\n\n    // Filter by category\n    if (selectedCategory !== 'all') {\n      const categoryCompanies = companyCategories[selectedCategory] || [];\n      filtered = filtered.filter(company => categoryCompanies.some(catCompany => company.toLowerCase().includes(catCompany.toLowerCase()) || catCompany.toLowerCase().includes(company.toLowerCase())));\n    }\n\n    // Filter by search term\n    filtered = filtered.filter(company => company.toLowerCase().includes(searchTerm.toLowerCase()));\n\n    // Sort companies\n    if (sortBy === 'name') {\n      filtered.sort();\n    } else if (sortBy === 'favorites') {\n      filtered.sort((a, b) => {\n        const aFav = favoriteCompanies.includes(a);\n        const bFav = favoriteCompanies.includes(b);\n        if (aFav && !bFav) return -1;\n        if (!aFav && bFav) return 1;\n        return a.localeCompare(b);\n      });\n    }\n    return filtered;\n  };\n\n  // Open quiz link\n  const openQuizLink = url => {\n    window.open(url, \"_blank\");\n  };\n\n  // Send message to chatbot\n  const sendMessage = async () => {\n    if (!input.trim()) return;\n    const userMessage = {\n      role: \"user\",\n      content: input\n    };\n    setMessages(prev => [...prev, userMessage]);\n    setInput(\"\");\n    setLoading(true);\n    try {\n      var _res$data$candidates, _res$data$candidates$, _res$data$candidates$2, _res$data$candidates$3, _res$data$candidates$4;\n      const prompt = `You are a resume assistant. Help users improve their resumes, provide suggestions, and answer career-related questions. Use the following knowledge if it helps. If it's not relevant, use your own intelligence.\\n\\nKnowledge:\\n${knowledge}\\n\\nQuestion: ${input}`;\n      const res = await axios.post(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`, {\n        contents: [{\n          parts: [{\n            text: prompt\n          }]\n        }]\n      }, {\n        headers: {\n          \"Content-Type\": \"application/json\"\n        }\n      });\n      const botReply = ((_res$data$candidates = res.data.candidates) === null || _res$data$candidates === void 0 ? void 0 : (_res$data$candidates$ = _res$data$candidates[0]) === null || _res$data$candidates$ === void 0 ? void 0 : (_res$data$candidates$2 = _res$data$candidates$.content) === null || _res$data$candidates$2 === void 0 ? void 0 : (_res$data$candidates$3 = _res$data$candidates$2.parts) === null || _res$data$candidates$3 === void 0 ? void 0 : (_res$data$candidates$4 = _res$data$candidates$3[0]) === null || _res$data$candidates$4 === void 0 ? void 0 : _res$data$candidates$4.text) || \"⚠ No response received.\";\n      const botMessage = {\n        role: \"bot\",\n        content: botReply\n      };\n      setMessages(prev => [...prev, botMessage]);\n    } catch (error) {\n      console.error(\"Gemini API Error:\", error);\n      setMessages(prev => [...prev, {\n        role: \"bot\",\n        content: \"❌ Error: \" + error.message\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Authentication functionality can be added later if needed\n\n  // Handle logout\n  const handleLogout = async () => {\n    await supabase.auth.signOut();\n  };\n\n  // Show notification\n  const showNotification = (msg, type = 'info') => {\n    setNotification({\n      msg,\n      type\n    });\n    setTimeout(() => setNotification(null), 3000);\n  };\n\n  // Log activity\n  const logActivity = msg => {\n    setActivityLog(log => [{\n      type: 'activity',\n      date: new Date().toISOString(),\n      msg\n    }, ...log.slice(0, 19)]);\n  };\n\n  // Toggle menu\n  const toggleMenu = menu => {\n    setExpandedMenus(prev => ({\n      ...prev,\n      [menu]: !prev[menu]\n    }));\n  };\n\n  // Auto-scroll chat\n  useEffect(() => {\n    if (chatEndRef.current) chatEndRef.current.scrollIntoView({\n      behavior: 'smooth'\n    });\n  }, [messages, loading]);\n\n  // Chart data\n  const getLast7Days = () => {\n    const days = [];\n    for (let i = 6; i >= 0; i--) {\n      const d = new Date();\n      d.setDate(d.getDate() - i);\n      days.push(d.toLocaleDateString());\n    }\n    return days;\n  };\n  const chartLabels = getLast7Days();\n  const chartData = {\n    labels: chartLabels,\n    datasets: [{\n      label: 'Resource Uploads',\n      data: chartLabels.map(day => activityLog.filter(a => a.type === 'activity' && a.msg.startsWith('Uploaded resource') && new Date(a.date).toLocaleDateString() === day).length),\n      backgroundColor: '#3182ce'\n    }, {\n      label: 'Coding Practice',\n      data: chartLabels.map(day => activityLog.filter(a => a.type === 'activity' && a.msg === 'Clicked coding practice link' && new Date(a.date).toLocaleDateString() === day).length),\n      backgroundColor: '#805ad5'\n    }]\n  };\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'top'\n      },\n      tooltip: {\n        enabled: true\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          stepSize: 1\n        }\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: getStyle('appContainer'),\n    children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n      style: getStyle('navbar'),\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          background: 'none',\n          border: 'none',\n          color: 'white',\n          // Always white since navbar has gradient background\n          marginRight: '20px',\n          cursor: 'pointer',\n          padding: '8px',\n          borderRadius: '4px',\n          transition: 'all 0.2s ease'\n        },\n        onClick: () => setSidebarOpen(!sidebarOpen),\n        onMouseEnter: e => e.target.style.background = 'rgba(255, 255, 255, 0.1)',\n        onMouseLeave: e => e.target.style.background = 'none',\n        children: sidebarOpen ? /*#__PURE__*/_jsxDEV(FiX, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 603,\n          columnNumber: 26\n        }, this) : /*#__PURE__*/_jsxDEV(FiMenu, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 603,\n          columnNumber: 46\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 588,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: require('./eduai-logo.jpg'),\n          alt: \"EduAI Logo\",\n          style: {\n            height: '36px',\n            marginRight: '12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 607,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            \"data-header-title\": true,\n            style: {\n              fontWeight: 600,\n              fontSize: '18px',\n              color: 'white'\n            },\n            children: \"EDU NOVA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            \"data-header-subtitle\": true,\n            style: {\n              fontSize: '12px',\n              opacity: 0.8,\n              color: 'white'\n            },\n            children: \"AI POWERED LEARNING SYSTEM\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 619,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 612,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 606,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '16px'\n        },\n        children: user ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '40px',\n              height: '40px',\n              borderRadius: '50%',\n              background: 'rgba(255, 255, 255, 0.2)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              fontWeight: 600,\n              cursor: 'pointer',\n              color: 'white',\n              backdropFilter: 'blur(10px)'\n            },\n            children: user.email === ADMIN_EMAIL ? /*#__PURE__*/_jsxDEV(FiShield, {\n              size: 20,\n              color: \"#4caf50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 644,\n              columnNumber: 47\n            }, this) : user.email[0].toUpperCase()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 631,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              ...getStyle('buttonPrimary'),\n              background: 'rgba(255, 255, 255, 0.2)',\n              color: 'white',\n              border: '1px solid rgba(255, 255, 255, 0.3)',\n              backdropFilter: 'blur(10px)'\n            },\n            onClick: handleLogout,\n            children: [/*#__PURE__*/_jsxDEV(FiLogOut, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 656,\n              columnNumber: 17\n            }, this), \" Logout\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 646,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(\"button\", {\n          style: {\n            ...getStyle('buttonPrimary'),\n            background: 'rgba(255, 255, 255, 0.2)',\n            color: 'white',\n            border: '1px solid rgba(255, 255, 255, 0.3)',\n            backdropFilter: 'blur(10px)'\n          },\n          onClick: () => {\n            console.log('Login functionality to be implemented');\n          },\n          children: [/*#__PURE__*/_jsxDEV(FiLogIn, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 670,\n            columnNumber: 15\n          }, this), \" Login\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 660,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 628,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 587,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n      style: getStyle('sidebar'),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '16px'\n        },\n        children: updatedSidebarItems.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              ...getStyle('sidebarItem'),\n              ...(activeTab === item.tab ? getStyle('sidebarItemActive') : {}),\n              cursor: 'pointer',\n              transition: 'all 0.3s ease'\n            },\n            onClick: () => {\n              setActiveTab(item.tab);\n              setSidebarOpen(false);\n            },\n            onMouseEnter: e => {\n              if (activeTab !== item.tab) {\n                e.target.style.background = 'rgba(0, 0, 0, 0.05)';\n                e.target.style.transform = 'translateX(4px)';\n                e.target.style.borderLeft = `3px solid ${globalStyles.currentTheme.primary}`;\n              }\n            },\n            onMouseLeave: e => {\n              if (activeTab !== item.tab) {\n                e.target.style.background = globalStyles.currentTheme.surface;\n                e.target.style.transform = 'translateX(0)';\n                e.target.style.borderLeft = '3px solid transparent';\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginRight: '12px'\n              },\n              children: item.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 707,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                flex: 1\n              },\n              children: item.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 708,\n              columnNumber: 17\n            }, this), item.subItems.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              onClick: e => {\n                e.stopPropagation();\n                toggleMenu(item.title);\n              },\n              children: expandedMenus[item.title] ? /*#__PURE__*/_jsxDEV(FiChevronDown, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 714,\n                columnNumber: 50\n              }, this) : /*#__PURE__*/_jsxDEV(FiChevronRight, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 714,\n                columnNumber: 70\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 710,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 681,\n            columnNumber: 15\n          }, this), item.subItems.length > 0 && expandedMenus[item.title] && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginLeft: '32px'\n            },\n            children: item.subItems.map((subItem, subIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...getStyle('sidebarItem'),\n                padding: '8px 16px 8px 32px',\n                fontSize: '14px',\n                opacity: 0.9\n              },\n              onClick: () => {\n                setActiveTab(item.tab);\n                setSidebarOpen(false);\n              },\n              onMouseEnter: e => {\n                e.target.style.background = 'rgba(0, 0, 0, 0.03)';\n                e.target.style.paddingLeft = '36px';\n                e.target.style.opacity = '1';\n              },\n              onMouseLeave: e => {\n                e.target.style.background = globalStyles.currentTheme.surface;\n                e.target.style.paddingLeft = '32px';\n                e.target.style.opacity = '0.9';\n              },\n              children: subItem.title\n            }, subIndex, false, {\n              fileName: _jsxFileName,\n              lineNumber: 722,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 720,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 680,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 678,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 677,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      style: getStyle('mainContent'),\n      children: [sidebarOpen && window.innerWidth < 768 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'fixed',\n          top: '64px',\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0,0,0,0.5)',\n          zIndex: 800\n        },\n        onClick: () => setSidebarOpen(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 759,\n        columnNumber: 11\n      }, this), activeTab === \"dashboard\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '2rem 1rem',\n          background: globalStyles.currentTheme.gradientLight,\n          minHeight: '100vh',\n          position: 'relative',\n          overflow: 'hidden'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundImage: `\n                radial-gradient(circle at 20% 30%, ${globalStyles.currentTheme.primary}08 0%, transparent 50%),\n                radial-gradient(circle at 80% 70%, ${globalStyles.currentTheme.primary}05 0%, transparent 50%),\n                radial-gradient(circle at 40% 80%, ${globalStyles.currentTheme.primary}03 0%, transparent 50%)\n              `,\n            zIndex: 0\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 783,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundImage: `\n                linear-gradient(${globalStyles.currentTheme.border} 1px, transparent 1px),\n                linear-gradient(90deg, ${globalStyles.currentTheme.border} 1px, transparent 1px)\n              `,\n            backgroundSize: '50px 50px',\n            opacity: 0.3,\n            zIndex: 0\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 798,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            maxWidth: '1400px',\n            margin: '0 auto',\n            position: 'relative',\n            zIndex: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: globalStyles.currentTheme.surface,\n              backdropFilter: 'blur(20px)',\n              borderRadius: '20px',\n              padding: '3rem',\n              marginBottom: '2rem',\n              position: 'relative',\n              overflow: 'hidden',\n              border: `1px solid ${globalStyles.currentTheme.border}`,\n              boxShadow: `0 20px 40px ${globalStyles.currentTheme.shadow}`\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                height: '4px',\n                background: globalStyles.currentTheme.gradient\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 828,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'auto 1fr auto',\n                gap: '2rem',\n                alignItems: 'center',\n                position: 'relative',\n                zIndex: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '100px',\n                  height: '100px',\n                  borderRadius: '20px',\n                  background: globalStyles.currentTheme.gradient,\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  fontSize: '2.5rem',\n                  fontWeight: 'bold',\n                  color: 'white',\n                  boxShadow: `0 10px 30px ${globalStyles.currentTheme.shadow}`,\n                  border: `3px solid ${globalStyles.currentTheme.surface}`\n                },\n                children: user ? user.email[0].toUpperCase() : '👤'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 846,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '2.5rem',\n                    fontWeight: 700,\n                    marginBottom: '0.5rem',\n                    color: globalStyles.currentTheme.text,\n                    lineHeight: 1.2\n                  },\n                  children: [\"Welcome back, \", user ? user.email.split('@')[0] : 'User', \"! \\uD83D\\uDC4B\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 865,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '1.1rem',\n                    color: globalStyles.currentTheme.textLight,\n                    fontWeight: 400,\n                    marginBottom: '1.5rem'\n                  },\n                  children: \"Ready to continue your learning journey? Let's achieve your goals together.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 875,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    gap: '1rem',\n                    flexWrap: 'wrap'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      background: globalStyles.currentTheme.primary + '15',\n                      color: globalStyles.currentTheme.primary,\n                      padding: '0.5rem 1rem',\n                      borderRadius: '12px',\n                      fontSize: '0.9rem',\n                      fontWeight: 600,\n                      border: `1px solid ${globalStyles.currentTheme.primary}30`\n                    },\n                    children: \"\\uD83D\\uDCC8 Active Learner\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 891,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      background: globalStyles.currentTheme.primary + '15',\n                      color: globalStyles.currentTheme.primary,\n                      padding: '0.5rem 1rem',\n                      borderRadius: '12px',\n                      fontSize: '0.9rem',\n                      fontWeight: 600,\n                      border: `1px solid ${globalStyles.currentTheme.primary}30`\n                    },\n                    children: \"\\uD83C\\uDFAF Goal Oriented\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 902,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 886,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 864,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'center',\n                  padding: '1rem',\n                  background: globalStyles.currentTheme.secondary,\n                  borderRadius: '16px',\n                  border: `1px solid ${globalStyles.currentTheme.border}`\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '2rem',\n                    fontWeight: 700,\n                    color: globalStyles.currentTheme.primary,\n                    marginBottom: '0.5rem'\n                  },\n                  children: new Date().toLocaleDateString('en-US', {\n                    day: 'numeric'\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 924,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '0.9rem',\n                    color: globalStyles.currentTheme.textLight,\n                    fontWeight: 500\n                  },\n                  children: new Date().toLocaleDateString('en-US', {\n                    month: 'short',\n                    year: 'numeric'\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 932,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 917,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 837,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 816,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',\n              gap: '1.5rem',\n              marginBottom: '2rem'\n            },\n            children: [{\n              title: 'Learning Progress',\n              value: '78',\n              unit: '%',\n              icon: '📈',\n              trend: '+12%',\n              description: 'This month',\n              color: globalStyles.currentTheme.primary\n            }, {\n              title: 'Study Hours',\n              value: '47',\n              unit: 'hrs',\n              icon: '⏱️',\n              trend: '+8 hrs',\n              description: 'This week',\n              color: '#10B981'\n            }, {\n              title: 'Completed Tasks',\n              value: '23',\n              unit: 'tasks',\n              icon: '✅',\n              trend: '+5',\n              description: 'This week',\n              color: '#3B82F6'\n            }, {\n              title: 'Skill Rating',\n              value: '4.8',\n              unit: '/5.0',\n              icon: '⭐',\n              trend: '+0.3',\n              description: 'Overall',\n              color: '#F59E0B'\n            }].map((stat, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: globalStyles.currentTheme.surface,\n                borderRadius: '16px',\n                padding: '1.5rem',\n                position: 'relative',\n                overflow: 'hidden',\n                border: `1px solid ${globalStyles.currentTheme.border}`,\n                cursor: 'pointer',\n                transition: 'all 0.3s ease',\n                boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'translateY(-4px)';\n                e.currentTarget.style.boxShadow = `0 8px 25px ${globalStyles.currentTheme.shadow}`;\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'translateY(0)';\n                e.currentTarget.style.boxShadow = `0 4px 12px ${globalStyles.currentTheme.shadow}`;\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  right: 0,\n                  height: '3px',\n                  background: stat.color\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1009,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'relative',\n                  zIndex: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    marginBottom: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.9rem',\n                      fontWeight: 600,\n                      color: globalStyles.currentTheme.textLight,\n                      textTransform: 'uppercase',\n                      letterSpacing: '0.5px'\n                    },\n                    children: stat.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1026,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '1.5rem',\n                      opacity: 0.8\n                    },\n                    children: stat.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1035,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1020,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'baseline',\n                    gap: '0.5rem',\n                    marginBottom: '0.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '2.5rem',\n                      fontWeight: 700,\n                      color: globalStyles.currentTheme.text,\n                      lineHeight: 1\n                    },\n                    children: stat.value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1050,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '1rem',\n                      fontWeight: 500,\n                      color: globalStyles.currentTheme.textLight\n                    },\n                    children: stat.unit\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1058,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1044,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.8rem',\n                      color: globalStyles.currentTheme.textLight\n                    },\n                    children: stat.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1073,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.8rem',\n                      fontWeight: 600,\n                      color: stat.color,\n                      background: stat.color + '15',\n                      padding: '0.25rem 0.5rem',\n                      borderRadius: '8px'\n                    },\n                    children: stat.trend\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1079,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1068,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1018,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 988,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 944,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: globalStyles.currentTheme.surface,\n              borderRadius: '20px',\n              padding: '2rem',\n              marginBottom: '2rem',\n              border: `1px solid ${globalStyles.currentTheme.border}`,\n              boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n              position: 'relative',\n              overflow: 'hidden'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                height: '4px',\n                background: globalStyles.currentTheme.gradient\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1107,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              style: {\n                margin: '0 0 2rem 0',\n                fontSize: '1.5rem',\n                fontWeight: 600,\n                color: globalStyles.currentTheme.text,\n                textAlign: 'center'\n              },\n              children: \"\\uD83D\\uDE80 Quick Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1116,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                gap: '1.5rem'\n              },\n              children: [{\n                icon: '📊',\n                title: 'Take Assessment',\n                subtitle: 'Skill Evaluation',\n                desc: 'Evaluate your current skill level',\n                action: () => setActiveTab('quizzes'),\n                color: globalStyles.currentTheme.primary\n              }, {\n                icon: '💻',\n                title: 'Practice Coding',\n                subtitle: 'DSA Problems',\n                desc: 'Solve data structures & algorithms',\n                action: () => setActiveTab('dsa'),\n                color: '#3B82F6'\n              }, {\n                icon: '📄',\n                title: 'Resume Review',\n                subtitle: 'Career Enhancement',\n                desc: 'Get professional resume feedback',\n                action: () => setActiveTab('resume'),\n                color: '#10B981'\n              }, {\n                icon: '📚',\n                title: 'Study Resources',\n                subtitle: 'Learning Materials',\n                desc: 'Access curated study materials',\n                action: () => setActiveTab('resources'),\n                color: '#F59E0B'\n              }].map((action, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: action.action,\n                style: {\n                  background: globalStyles.currentTheme.surface,\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  cursor: 'pointer',\n                  transition: 'all 0.3s ease',\n                  border: `1px solid ${globalStyles.currentTheme.border}`,\n                  position: 'relative',\n                  overflow: 'hidden',\n                  boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.transform = 'translateY(-4px)';\n                  e.currentTarget.style.boxShadow = `0 8px 25px ${globalStyles.currentTheme.shadow}`;\n                  e.currentTarget.style.borderColor = action.color;\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = `0 4px 12px ${globalStyles.currentTheme.shadow}`;\n                  e.currentTarget.style.borderColor = globalStyles.currentTheme.border;\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    height: '3px',\n                    background: action.color\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1190,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'relative',\n                    zIndex: 1,\n                    textAlign: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '2.5rem',\n                      marginBottom: '1rem',\n                      opacity: 0.8\n                    },\n                    children: action.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1200,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '1.1rem',\n                      fontWeight: 600,\n                      color: globalStyles.currentTheme.text,\n                      marginBottom: '0.5rem'\n                    },\n                    children: action.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1208,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.9rem',\n                      color: action.color,\n                      fontWeight: 500,\n                      marginBottom: '0.75rem'\n                    },\n                    children: action.subtitle\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1217,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.85rem',\n                      color: globalStyles.currentTheme.textLight,\n                      lineHeight: 1.4\n                    },\n                    children: action.desc\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1226,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1199,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1165,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1126,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1096,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',\n              gap: '2rem',\n              marginBottom: '2rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: globalStyles.currentTheme.surface,\n                borderRadius: '16px',\n                padding: '1.5rem',\n                boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                border: `1px solid ${globalStyles.currentTheme.border}`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 1.5rem 0',\n                  fontSize: '1.2rem',\n                  fontWeight: 600,\n                  color: globalStyles.currentTheme.text,\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                },\n                children: \"\\uD83D\\uDCCA Weekly Progress\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1254,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '1rem'\n                },\n                children: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    marginBottom: '0.75rem',\n                    padding: '0.5rem',\n                    borderRadius: '8px',\n                    background: index < 5 ? globalStyles.currentTheme.secondary : 'transparent'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontSize: '0.9rem',\n                      fontWeight: 500,\n                      color: globalStyles.currentTheme.text,\n                      minWidth: '40px'\n                    },\n                    children: day\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1278,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1,\n                      height: '8px',\n                      background: globalStyles.currentTheme.border,\n                      borderRadius: '4px',\n                      margin: '0 1rem',\n                      overflow: 'hidden'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        height: '100%',\n                        width: `${Math.random() * 80 + 20}%`,\n                        background: index < 5 ? globalStyles.currentTheme.primary : globalStyles.currentTheme.border,\n                        borderRadius: '4px',\n                        transition: 'width 1s ease'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1294,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1286,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontSize: '0.8rem',\n                      color: globalStyles.currentTheme.textLight,\n                      minWidth: '40px',\n                      textAlign: 'right'\n                    },\n                    children: index < 5 ? `${Math.floor(Math.random() * 3 + 1)}h` : '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1302,\n                    columnNumber: 25\n                  }, this)]\n                }, day, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1269,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1267,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1247,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: globalStyles.currentTheme.surface,\n                borderRadius: '16px',\n                padding: '1.5rem',\n                boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                border: `1px solid ${globalStyles.currentTheme.border}`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 1.5rem 0',\n                  fontSize: '1.2rem',\n                  fontWeight: 600,\n                  color: globalStyles.currentTheme.text,\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                },\n                children: \"\\uD83C\\uDFAF Goals & Achievements\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1323,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: '1rem'\n                },\n                children: [{\n                  title: 'Complete 5 DSA Problems',\n                  progress: 80,\n                  status: 'In Progress'\n                }, {\n                  title: 'Finish Resume Review',\n                  progress: 100,\n                  status: 'Completed'\n                }, {\n                  title: 'Take 3 Practice Tests',\n                  progress: 33,\n                  status: 'In Progress'\n                }].map((goal, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    padding: '1rem',\n                    borderRadius: '12px',\n                    background: globalStyles.currentTheme.secondary,\n                    border: `1px solid ${globalStyles.currentTheme.border}`\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      alignItems: 'center',\n                      marginBottom: '0.5rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        fontSize: '0.9rem',\n                        fontWeight: 500,\n                        color: globalStyles.currentTheme.text\n                      },\n                      children: goal.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1353,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        fontSize: '0.8rem',\n                        fontWeight: 600,\n                        color: goal.progress === 100 ? '#10B981' : globalStyles.currentTheme.primary,\n                        background: goal.progress === 100 ? '#10B98115' : globalStyles.currentTheme.primary + '15',\n                        padding: '0.25rem 0.5rem',\n                        borderRadius: '6px'\n                      },\n                      children: goal.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1360,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1347,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      height: '6px',\n                      background: globalStyles.currentTheme.border,\n                      borderRadius: '3px',\n                      overflow: 'hidden'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        height: '100%',\n                        width: `${goal.progress}%`,\n                        background: goal.progress === 100 ? '#10B981' : globalStyles.currentTheme.primary,\n                        borderRadius: '3px',\n                        transition: 'width 1s ease'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1377,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1371,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1341,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1335,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1316,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1240,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: '1fr 1fr',\n              gap: '2rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: globalStyles.currentTheme.surface,\n                borderRadius: '16px',\n                padding: '1.5rem',\n                boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                border: `1px solid ${globalStyles.currentTheme.border}`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 1.5rem 0',\n                  fontSize: '1.3rem',\n                  fontWeight: 600,\n                  color: globalStyles.currentTheme.text\n                },\n                children: \"\\uD83D\\uDCC8 Recent Activity\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1406,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  maxHeight: '250px',\n                  overflowY: 'auto'\n                },\n                children: activityLog.slice(0, 5).map((log, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '1rem',\n                    padding: '0.75rem',\n                    borderRadius: '8px',\n                    marginBottom: '0.5rem',\n                    background: index % 2 === 0 ? globalStyles.currentTheme.secondary : 'transparent',\n                    transition: 'all 0.3s ease'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '10px',\n                      height: '10px',\n                      borderRadius: '50%',\n                      background: log.type === 'login' ? '#4ECDC4' : '#45B7D1',\n                      flexShrink: 0\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1430,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.9rem',\n                        fontWeight: 500,\n                        color: globalStyles.currentTheme.text,\n                        marginBottom: '0.2rem'\n                      },\n                      children: log.msg\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1438,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.8rem',\n                        color: globalStyles.currentTheme.textLight\n                      },\n                      children: new Date(log.date).toLocaleString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1446,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1437,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1420,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1415,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1399,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: globalStyles.currentTheme.surface,\n                borderRadius: '16px',\n                padding: '1.5rem',\n                boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                border: `1px solid ${globalStyles.currentTheme.border}`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 1.5rem 0',\n                  fontSize: '1.3rem',\n                  fontWeight: 600,\n                  color: globalStyles.currentTheme.text\n                },\n                children: \"\\uD83D\\uDCC4 Resume Management\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1466,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: '1rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.75rem',\n                    padding: '1rem',\n                    borderRadius: '12px',\n                    background: globalStyles.currentTheme.secondary,\n                    border: `2px dashed ${globalStyles.currentTheme.border}`,\n                    cursor: resumeUploadLoading ? 'not-allowed' : 'pointer',\n                    transition: 'all 0.3s ease'\n                  },\n                  onMouseEnter: e => {\n                    if (!resumeUploadLoading) {\n                      e.currentTarget.style.borderColor = globalStyles.currentTheme.primary;\n                      e.currentTarget.style.background = globalStyles.currentTheme.primary + '10';\n                    }\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.borderColor = globalStyles.currentTheme.border;\n                    e.currentTarget.style.background = globalStyles.currentTheme.secondary;\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FiUpload, {\n                    size: 20,\n                    color: globalStyles.currentTheme.primary\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1497,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontWeight: 600,\n                        color: globalStyles.currentTheme.text,\n                        marginBottom: '0.2rem'\n                      },\n                      children: resumeUploadLoading ? 'Uploading...' : 'Upload Resume'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1499,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.8rem',\n                        color: globalStyles.currentTheme.textLight\n                      },\n                      children: \"PDF files only\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1506,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1498,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"file\",\n                    accept: \"application/pdf\",\n                    onChange: handleResumeUpload,\n                    disabled: resumeUploadLoading,\n                    style: {\n                      display: 'none'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1513,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1476,\n                  columnNumber: 21\n                }, this), resumeUrl && /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: resumeUrl,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.75rem',\n                    padding: '1rem',\n                    borderRadius: '12px',\n                    background: '#4ECDC4',\n                    color: 'white',\n                    textDecoration: 'none',\n                    fontWeight: 600,\n                    transition: 'all 0.3s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.background = '#3DBDB6';\n                    e.currentTarget.style.transform = 'translateY(-2px)';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.background = '#4ECDC4';\n                    e.currentTarget.style.transform = 'translateY(0)';\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FiFileText, {\n                    size: 20\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1548,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"View Resume\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1549,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1523,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1475,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1459,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1392,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: globalStyles.currentTheme.surface,\n              borderRadius: '16px',\n              padding: '2rem',\n              marginTop: '2rem',\n              border: `1px solid ${globalStyles.currentTheme.border}`,\n              boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                gap: '2rem',\n                marginBottom: '1.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  style: {\n                    margin: '0 0 1rem 0',\n                    fontSize: '1rem',\n                    fontWeight: 600,\n                    color: globalStyles.currentTheme.text\n                  },\n                  children: \"\\uD83D\\uDCC8 Your Progress\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1574,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    flexDirection: 'column',\n                    gap: '0.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.9rem',\n                      color: globalStyles.currentTheme.textLight\n                    },\n                    children: [\"Total Study Time: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      style: {\n                        color: globalStyles.currentTheme.primary\n                      },\n                      children: \"47 hours\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1591,\n                      columnNumber: 43\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1587,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.9rem',\n                      color: globalStyles.currentTheme.textLight\n                    },\n                    children: [\"Completed Tasks: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      style: {\n                        color: globalStyles.currentTheme.primary\n                      },\n                      children: \"23\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1597,\n                      columnNumber: 42\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1593,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1582,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1573,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  style: {\n                    margin: '0 0 1rem 0',\n                    fontSize: '1rem',\n                    fontWeight: 600,\n                    color: globalStyles.currentTheme.text\n                  },\n                  children: \"\\uD83D\\uDD17 Quick Links\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1604,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    flexDirection: 'column',\n                    gap: '0.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setActiveTab('academics'),\n                    style: {\n                      background: 'none',\n                      border: 'none',\n                      color: globalStyles.currentTheme.primary,\n                      fontSize: '0.9rem',\n                      cursor: 'pointer',\n                      textAlign: 'left',\n                      padding: '0.25rem 0'\n                    },\n                    children: \"\\uD83D\\uDCDA Academic Resources\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1617,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setActiveTab('coding'),\n                    style: {\n                      background: 'none',\n                      border: 'none',\n                      color: globalStyles.currentTheme.primary,\n                      fontSize: '0.9rem',\n                      cursor: 'pointer',\n                      textAlign: 'left',\n                      padding: '0.25rem 0'\n                    },\n                    children: \"\\uD83D\\uDCBB Coding Practice\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1631,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1612,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1603,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  style: {\n                    margin: '0 0 1rem 0',\n                    fontSize: '1rem',\n                    fontWeight: 600,\n                    color: globalStyles.currentTheme.text\n                  },\n                  children: \"\\uD83D\\uDCA1 Daily Inspiration\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1650,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '0.9rem',\n                    color: globalStyles.currentTheme.textLight,\n                    fontStyle: 'italic',\n                    lineHeight: 1.4\n                  },\n                  children: \"\\\"Success is not final, failure is not fatal: it is the courage to continue that counts.\\\"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1658,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1649,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1566,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                borderTop: `1px solid ${globalStyles.currentTheme.border}`,\n                paddingTop: '1rem',\n                fontSize: '0.8rem',\n                color: globalStyles.currentTheme.textLight\n              },\n              children: \"Keep learning, keep growing! \\uD83D\\uDE80 Your journey to success starts here.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1670,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1557,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 813,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 775,\n        columnNumber: 11\n      }, this), activeTab === \"resume\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px',\n          maxWidth: '1200px',\n          margin: '0 auto'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getStyle('card'),\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginTop: 0,\n              color: '#333'\n            },\n            children: \"Career Assistant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1687,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              opacity: 0.8,\n              marginBottom: '24px',\n              color: '#666'\n            },\n            children: \"Get personalized resume advice and career guidance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1691,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              height: '50vh',\n              overflowY: 'auto',\n              marginBottom: '24px',\n              padding: '16px',\n              backgroundColor: '#f5f5f5',\n              border: '1px solid #e0e0e0',\n              borderRadius: '8px'\n            },\n            children: [messages.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                height: '100%',\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                justifyContent: 'center',\n                textAlign: 'center',\n                opacity: 0.7\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '48px',\n                  marginBottom: '16px'\n                },\n                children: \"\\uD83D\\uDCAC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1720,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: 0,\n                  color: '#333'\n                },\n                children: \"Start a conversation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1721,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#666'\n                },\n                children: \"Ask about resumes, interviews, or career advice\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1725,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1711,\n              columnNumber: 19\n            }, this) : messages.map((msg, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...(msg.role === 'user' ? getStyle('chatBubbleUser') : getStyle('chatBubbleBot')),\n                animation: 'fadeIn 0.3s ease'\n              },\n              children: msg.role === 'bot' ? /*#__PURE__*/_jsxDEV(ReactMarkdown, {\n                children: msg.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1739,\n                columnNumber: 25\n              }, this) : msg.content\n            }, idx, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1731,\n              columnNumber: 21\n            }, this)), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: getStyle('chatBubbleBot'),\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '8px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '10px',\n                    height: '10px',\n                    borderRadius: '50%',\n                    backgroundColor: '#1976d2',\n                    animation: 'pulse 1.4s infinite ease-in-out'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1749,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '10px',\n                    height: '10px',\n                    borderRadius: '50%',\n                    backgroundColor: '#1976d2',\n                    animation: 'pulse 1.4s infinite ease-in-out',\n                    animationDelay: '0.2s'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1756,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '10px',\n                    height: '10px',\n                    borderRadius: '50%',\n                    backgroundColor: '#1976d2',\n                    animation: 'pulse 1.4s infinite ease-in-out',\n                    animationDelay: '0.4s'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1764,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1748,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1747,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: chatEndRef\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1775,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1700,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            style: {\n              display: 'flex',\n              gap: '12px'\n            },\n            onSubmit: e => {\n              e.preventDefault();\n              sendMessage();\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Type your message...\",\n              style: getStyle('inputField'),\n              value: input,\n              onChange: e => setInput(e.target.value),\n              disabled: loading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1786,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              style: {\n                ...getStyle('buttonPrimary'),\n                minWidth: '100px'\n              },\n              disabled: loading || !input.trim(),\n              children: loading ? 'Sending...' : 'Send'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1794,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1779,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1686,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1685,\n        columnNumber: 11\n      }, this), activeTab === \"dsa\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getStyle('card'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: '16px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                style: {\n                  marginTop: 0,\n                  marginBottom: '8px'\n                },\n                children: \"\\uD83D\\uDE80 Company Wise DSA Questions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1816,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  opacity: 0.8,\n                  margin: 0\n                },\n                children: \"Explore DSA questions from top companies with enhanced filtering and favorites\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1817,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1815,\n              columnNumber: 17\n            }, this), showRevertButton && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: revertHeaderChanges,\n              style: {\n                ...getStyle('buttonPrimary'),\n                background: '#ff6b6b',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '8px',\n                fontSize: '14px',\n                padding: '8px 16px',\n                border: 'none',\n                borderRadius: '8px',\n                color: 'white',\n                cursor: 'pointer',\n                transition: 'all 0.3s ease'\n              },\n              onMouseEnter: e => e.target.style.background = '#ff5252',\n              onMouseLeave: e => e.target.style.background = '#ff6b6b',\n              children: [/*#__PURE__*/_jsxDEV(FiRefreshCw, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1841,\n                columnNumber: 21\n              }, this), \"Revert Header Color\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1822,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1814,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '8px',\n              marginBottom: '20px',\n              flexWrap: 'wrap',\n              borderBottom: '1px solid #eee',\n              paddingBottom: '16px'\n            },\n            children: ['all', ...Object.keys(companyCategories)].map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectedCategory(category),\n              style: {\n                padding: '8px 16px',\n                borderRadius: '20px',\n                border: selectedCategory === category ? 'none' : '1px solid #ddd',\n                background: selectedCategory === category ? globalStyles.currentTheme.primary : 'transparent',\n                color: selectedCategory === category ? 'white' : '#666',\n                cursor: 'pointer',\n                fontSize: '14px',\n                fontWeight: selectedCategory === category ? 600 : 400,\n                transition: 'all 0.3s ease',\n                textTransform: 'capitalize'\n              },\n              onMouseEnter: e => {\n                if (selectedCategory !== category) {\n                  e.target.style.background = '#f5f5f5';\n                }\n              },\n              onMouseLeave: e => {\n                if (selectedCategory !== category) {\n                  e.target.style.background = 'transparent';\n                }\n              },\n              children: category === 'all' ? '🌟 All' : `${category === 'FAANG' ? '🔥' : category === 'Big Tech' ? '💻' : category === 'Startups' ? '🚀' : category === 'Finance' ? '💰' : category === 'Indian' ? '🇮🇳' : '🏢'} ${category}`\n            }, category, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1857,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1848,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '16px',\n              marginBottom: '24px',\n              flexWrap: 'wrap',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'relative',\n                flex: 1,\n                minWidth: '300px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  left: '16px',\n                  top: '50%',\n                  transform: 'translateY(-50%)',\n                  color: '#666'\n                },\n                children: /*#__PURE__*/_jsxDEV(FiSearch, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1911,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1904,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search companies...\",\n                style: {\n                  ...getStyle('inputField'),\n                  paddingLeft: '48px',\n                  width: '100%'\n                },\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1913,\n                columnNumber: 19\n              }, this), searchTerm && /*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  position: 'absolute',\n                  right: '16px',\n                  top: '50%',\n                  transform: 'translateY(-50%)',\n                  background: 'none',\n                  border: 'none',\n                  color: '#666',\n                  cursor: 'pointer'\n                },\n                onClick: () => setSearchTerm(\"\"),\n                children: /*#__PURE__*/_jsxDEV(FiX, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1938,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1925,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1903,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: sortBy,\n              onChange: e => setSortBy(e.target.value),\n              style: {\n                ...getStyle('inputField'),\n                width: 'auto',\n                minWidth: '150px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"name\",\n                children: \"\\uD83D\\uDCDD Sort by Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1953,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"favorites\",\n                children: \"\\u2B50 Favorites First\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1954,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1944,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1895,\n            columnNumber: 15\n          }, this), recentCompanies.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '24px',\n              padding: '16px',\n              borderRadius: '12px',\n              background: '#f8f9fa',\n              border: '1px solid #e9ecef'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '8px',\n                fontSize: '16px',\n                marginBottom: '12px',\n                color: '#333',\n                margin: '0 0 12px 0'\n              },\n              children: [/*#__PURE__*/_jsxDEV(FiClock, {\n                color: \"#666\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1976,\n                columnNumber: 21\n              }, this), \" Recently Viewed\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1967,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: '8px',\n                flexWrap: 'wrap'\n              },\n              children: recentCompanies.map(company => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleCompanyClick(company),\n                style: {\n                  padding: '6px 12px',\n                  borderRadius: '16px',\n                  border: `1px solid ${globalStyles.currentTheme.primary}`,\n                  background: 'transparent',\n                  color: globalStyles.currentTheme.primary,\n                  cursor: 'pointer',\n                  fontSize: '12px',\n                  transition: 'all 0.3s ease'\n                },\n                onMouseEnter: e => {\n                  e.target.style.background = globalStyles.currentTheme.primary;\n                  e.target.style.color = 'white';\n                },\n                onMouseLeave: e => {\n                  e.target.style.background = 'transparent';\n                  e.target.style.color = globalStyles.currentTheme.primary;\n                },\n                children: company\n              }, company, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1984,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1978,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1960,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fill, minmax(220px, 1fr))',\n              gap: '16px',\n              marginTop: '24px'\n            },\n            children: getFilteredCompanies().map((company, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...getStyle('companyCard'),\n                position: 'relative',\n                transform: favoriteCompanies.includes(company) ? 'scale(1.02)' : 'scale(1)',\n                border: favoriteCompanies.includes(company) ? `2px solid ${globalStyles.currentTheme.primary}` : `1px solid ${globalStyles.currentTheme.border}`,\n                background: globalStyles.currentTheme.surface,\n                color: globalStyles.currentTheme.text,\n                animation: `fadeIn 0.3s ease ${index * 0.1}s both`,\n                boxShadow: `0 4px 6px ${globalStyles.currentTheme.shadow}`\n              },\n              onClick: () => handleCompanyClick(company),\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: e => toggleFavorite(company, e),\n                style: {\n                  position: 'absolute',\n                  top: '8px',\n                  right: '8px',\n                  background: 'none',\n                  border: 'none',\n                  cursor: 'pointer',\n                  color: favoriteCompanies.includes(company) ? '#ff6b6b' : '#ccc',\n                  transition: 'all 0.3s ease',\n                  fontSize: '18px'\n                },\n                children: /*#__PURE__*/_jsxDEV(FiHeart, {\n                  fill: favoriteCompanies.includes(company) ? 'currentColor' : 'none'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2052,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2038,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '56px',\n                  height: '56px',\n                  borderRadius: '50%',\n                  background: `linear-gradient(135deg, ${globalStyles.currentTheme.primary}, ${globalStyles.currentTheme.primaryDark})`,\n                  color: 'white',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  fontSize: '24px',\n                  fontWeight: 700,\n                  marginBottom: '12px',\n                  boxShadow: `0 4px 8px ${globalStyles.currentTheme.shadow}`\n                },\n                children: company.charAt(0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2056,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: 600,\n                  textAlign: 'center',\n                  fontSize: '14px',\n                  marginBottom: '8px'\n                },\n                children: company\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2074,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  fontSize: '12px',\n                  opacity: 0.7,\n                  marginTop: '8px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"\\uD83D\\uDCCA \", Math.floor(Math.random() * 50) + 10, \" Questions\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2091,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"\\u2B50 \", (Math.random() * 2 + 3).toFixed(1)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2092,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2084,\n                columnNumber: 21\n              }, this), Object.entries(companyCategories).map(([category, companies]) => {\n                if (companies.some(c => c.toLowerCase() === company.toLowerCase())) {\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      position: 'absolute',\n                      top: '8px',\n                      left: '8px',\n                      background: globalStyles.currentTheme.primary,\n                      color: 'white',\n                      padding: '2px 6px',\n                      borderRadius: '8px',\n                      fontSize: '10px',\n                      fontWeight: 600\n                    },\n                    children: category\n                  }, category, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2099,\n                    columnNumber: 27\n                  }, this);\n                }\n                return null;\n              })]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2021,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2014,\n            columnNumber: 15\n          }, this), getFilteredCompanies().length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '40px',\n              opacity: 0.7,\n              color: '#666'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '48px',\n                marginBottom: '16px'\n              },\n              children: \"\\uD83D\\uDD0D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2131,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                color: '#333'\n              },\n              children: \"No companies found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2132,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#666'\n              },\n              children: \"Try adjusting your search or category filter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2133,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2125,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1812,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1811,\n        columnNumber: 11\n      }, this), activeTab === \"quizzes\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getStyle('card'),\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginTop: 0\n            },\n            children: \"Career Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2144,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              opacity: 0.8,\n              marginBottom: '24px'\n            },\n            children: \"Test your knowledge with our career-focused quizzes!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2145,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',\n              gap: '16px'\n            },\n            children: quizButtons.map((quiz, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: getStyle('quizCard'),\n              onClick: () => openQuizLink(quiz.link),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  style: {\n                    margin: '0 0 8px 0'\n                  },\n                  children: quiz.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2161,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '14px',\n                    opacity: 0.8\n                  },\n                  children: quiz.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2162,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2160,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#1976d2'\n                },\n                children: /*#__PURE__*/_jsxDEV(FiExternalLink, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2171,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2170,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2155,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2149,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2143,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2142,\n        columnNumber: 11\n      }, this), activeTab === \"coding\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Coding, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2183,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2182,\n        columnNumber: 11\n      }, this), activeTab === \"resources\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '2rem 1rem',\n          background: globalStyles.currentTheme.gradientLight,\n          minHeight: '100vh'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            maxWidth: '1400px',\n            margin: '0 auto'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: globalStyles.currentTheme.surface,\n              borderRadius: '20px',\n              padding: '2rem',\n              marginBottom: '2rem',\n              border: `1px solid ${globalStyles.currentTheme.border}`,\n              boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n              position: 'relative'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                height: '4px',\n                background: globalStyles.currentTheme.gradient\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2203,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              style: {\n                margin: '0 0 1rem 0',\n                fontSize: '2.5rem',\n                fontWeight: 700,\n                color: globalStyles.currentTheme.text,\n                textAlign: 'center'\n              },\n              children: \"\\uD83D\\uDCDA Study Resources & Materials\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2212,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: 0,\n                fontSize: '1.1rem',\n                color: globalStyles.currentTheme.textLight,\n                textAlign: 'center'\n              },\n              children: \"Access comprehensive study materials, academic resources, and learning tools\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2221,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2194,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: globalStyles.currentTheme.surface,\n              borderRadius: '16px',\n              padding: '1.5rem',\n              marginBottom: '2rem',\n              border: `1px solid ${globalStyles.currentTheme.border}`,\n              boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              id: \"tab-navigation-container\",\n              className: \"tab-navigation-container\",\n              style: {\n                position: 'relative',\n                display: 'flex',\n                justifyContent: 'center',\n                alignItems: 'center',\n                background: globalStyles.currentTheme.secondary,\n                borderRadius: '12px',\n                padding: '0.5rem',\n                overflow: 'hidden'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                id: \"sliding-indicator\",\n                style: {\n                  position: 'absolute',\n                  top: '0.5rem',\n                  bottom: '0.5rem',\n                  background: globalStyles.currentTheme.primary,\n                  borderRadius: '8px',\n                  transition: 'all 0.3s ease-in-out',\n                  zIndex: 1,\n                  // Dynamic positioning based on active tab\n                  left: resourcesTab === 'general' ? '0.5rem' : resourcesTab === 'academics' ? 'calc(33.333% + 0.167rem)' : 'calc(66.666% - 0.167rem)',\n                  width: 'calc(33.333% - 0.333rem)',\n                  boxShadow: `0 2px 8px ${globalStyles.currentTheme.primary}40`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2256,\n                columnNumber: 19\n              }, this), [{\n                id: 'general',\n                label: '📁 General Resources',\n                icon: '📁',\n                shortLabel: 'General'\n              }, {\n                id: 'academics',\n                label: '🎓 Academic Materials',\n                icon: '🎓',\n                shortLabel: 'Academics'\n              }, {\n                id: 'tools',\n                label: '🛠️ Study Tools',\n                icon: '🛠️',\n                shortLabel: 'Tools'\n              }].map((tab, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setResourcesTab(tab.id),\n                onMouseEnter: e => {\n                  // Enhanced hover effect\n                  if (resourcesTab !== tab.id) {\n                    e.target.style.transform = 'translateY(-1px)';\n                    e.target.style.color = globalStyles.currentTheme.primary;\n                  }\n                },\n                onMouseLeave: e => {\n                  // Reset hover effect\n                  if (resourcesTab !== tab.id) {\n                    e.target.style.transform = 'translateY(0)';\n                    e.target.style.color = globalStyles.currentTheme.text;\n                  }\n                },\n                onFocus: e => {\n                  // Keyboard navigation support\n                  e.target.style.outline = `2px solid ${globalStyles.currentTheme.primary}`;\n                  e.target.style.outlineOffset = '2px';\n                },\n                onBlur: e => {\n                  // Remove focus outline\n                  e.target.style.outline = 'none';\n                },\n                \"aria-label\": `Switch to ${tab.label} tab`,\n                \"aria-selected\": resourcesTab === tab.id,\n                role: \"tab\",\n                tabIndex: 0,\n                style: {\n                  flex: 1,\n                  position: 'relative',\n                  zIndex: 2,\n                  padding: '0.875rem 1rem',\n                  minHeight: '44px',\n                  // Touch target requirement\n                  border: 'none',\n                  background: 'transparent',\n                  color: resourcesTab === tab.id ? 'white' : globalStyles.currentTheme.text,\n                  cursor: 'pointer',\n                  fontSize: windowWidth < 768 ? '0.9rem' : '1rem',\n                  fontWeight: resourcesTab === tab.id ? 600 : 500,\n                  borderRadius: '8px',\n                  transition: 'all 0.3s ease',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  gap: '0.5rem',\n                  textAlign: 'center',\n                  whiteSpace: 'nowrap',\n                  userSelect: 'none',\n                  WebkitTapHighlightColor: 'transparent' // Remove tap highlight on mobile\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    display: windowWidth < 640 ? 'none' : 'inline'\n                  },\n                  children: tab.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2338,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    display: windowWidth < 480 ? 'none' : 'inline'\n                  },\n                  children: windowWidth < 640 ? tab.shortLabel : tab.label.replace(tab.icon + ' ', '')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2343,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    display: windowWidth < 480 ? 'inline' : 'none',\n                    fontSize: '1.2rem'\n                  },\n                  children: tab.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2349,\n                  columnNumber: 23\n                }, this)]\n              }, tab.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2281,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2241,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                marginTop: '1rem',\n                padding: '0.5rem',\n                fontSize: '0.9rem',\n                color: globalStyles.currentTheme.textLight,\n                fontStyle: 'italic'\n              },\n              children: [resourcesTab === 'general' && 'Upload and manage your personal study materials and documents', resourcesTab === 'academics' && 'Access comprehensive academic resources organized by subject', resourcesTab === 'tools' && 'Utilize powerful study tools and utilities to enhance your learning']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2360,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2232,\n            columnNumber: 15\n          }, this), resourcesTab === 'general' && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: globalStyles.currentTheme.surface,\n              borderRadius: '16px',\n              padding: '2rem',\n              border: `1px solid ${globalStyles.currentTheme.border}`,\n              boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              style: {\n                margin: '0 0 1.5rem 0',\n                fontSize: '1.5rem',\n                fontWeight: 600,\n                color: globalStyles.currentTheme.text\n              },\n              children: \"\\uD83D\\uDCC1 General Resources\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2383,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '2rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'inline-flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  padding: '0.75rem 1.5rem',\n                  background: globalStyles.currentTheme.primary,\n                  color: 'white',\n                  borderRadius: '12px',\n                  cursor: resourceUploadLoading ? 'not-allowed' : 'pointer',\n                  fontSize: '1rem',\n                  fontWeight: 500,\n                  transition: 'all 0.3s ease'\n                },\n                children: [/*#__PURE__*/_jsxDEV(FiUpload, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2406,\n                  columnNumber: 23\n                }, this), resourceUploadLoading ? 'Uploading...' : 'Upload Resource', /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  accept: \".pdf,.doc,.docx,.txt\",\n                  onChange: handleResourceUpload,\n                  disabled: resourceUploadLoading,\n                  style: {\n                    display: 'none'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2408,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2393,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2392,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 1rem 0',\n                  fontSize: '1.2rem',\n                  fontWeight: 600,\n                  color: globalStyles.currentTheme.text\n                },\n                children: \"Your Uploaded Resources\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2419,\n                columnNumber: 21\n              }, this), userResources.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'center',\n                  padding: '3rem',\n                  background: globalStyles.currentTheme.secondary,\n                  borderRadius: '12px',\n                  border: `1px solid ${globalStyles.currentTheme.border}`\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '3rem',\n                    marginBottom: '1rem'\n                  },\n                  children: \"\\uD83D\\uDCC4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2435,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  style: {\n                    margin: '0 0 0.5rem 0',\n                    color: globalStyles.currentTheme.text\n                  },\n                  children: \"No resources uploaded yet\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2436,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    color: globalStyles.currentTheme.textLight\n                  },\n                  children: \"Upload your study materials, notes, and documents to get started\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2442,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2428,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'grid',\n                  gap: '1rem'\n                },\n                children: userResources.map((file, idx) => {\n                  const {\n                    data: urlData\n                  } = supabase.storage.from('resources').getPublicUrl(`${user.id}/${file.name}`);\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      alignItems: 'center',\n                      padding: '1rem',\n                      background: globalStyles.currentTheme.secondary,\n                      borderRadius: '12px',\n                      border: `1px solid ${globalStyles.currentTheme.border}`\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '40px',\n                          height: '40px',\n                          borderRadius: '8px',\n                          background: globalStyles.currentTheme.primary,\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          color: 'white',\n                          fontSize: '1.2rem'\n                        },\n                        children: \"\\uD83D\\uDCC4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2471,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            fontWeight: 600,\n                            color: globalStyles.currentTheme.text,\n                            marginBottom: '0.25rem'\n                          },\n                          children: file.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2485,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            fontSize: '0.8rem',\n                            color: globalStyles.currentTheme.textLight\n                          },\n                          children: \"Uploaded resource\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2492,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2484,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2466,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: urlData.publicUrl,\n                      target: \"_blank\",\n                      rel: \"noopener noreferrer\",\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.5rem',\n                        padding: '0.5rem 1rem',\n                        background: globalStyles.currentTheme.primary,\n                        color: 'white',\n                        textDecoration: 'none',\n                        borderRadius: '8px',\n                        fontSize: '0.9rem',\n                        fontWeight: 500,\n                        transition: 'all 0.3s ease'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(FiExternalLink, {\n                        size: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2518,\n                        columnNumber: 33\n                      }, this), \"Open\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2500,\n                      columnNumber: 31\n                    }, this)]\n                  }, idx, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2457,\n                    columnNumber: 29\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2450,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2418,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2376,\n            columnNumber: 17\n          }, this), resourcesTab === 'academics' && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gap: '2rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: globalStyles.currentTheme.surface,\n                borderRadius: '16px',\n                padding: '2rem',\n                border: `1px solid ${globalStyles.currentTheme.border}`,\n                boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                style: {\n                  margin: '0 0 1.5rem 0',\n                  fontSize: '1.5rem',\n                  fontWeight: 600,\n                  color: globalStyles.currentTheme.text,\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                },\n                children: \"\\uD83C\\uDF93 Academic Study Materials\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2544,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'grid',\n                  gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n                  gap: '1.5rem'\n                },\n                children: [{\n                  subject: 'Mathematics',\n                  icon: '📐',\n                  color: '#DC2626',\n                  topics: ['Calculus', 'Linear Algebra', 'Statistics', 'Discrete Math'],\n                  resources: 15\n                }, {\n                  subject: 'Computer Science',\n                  icon: '💻',\n                  color: '#7C3AED',\n                  topics: ['Data Structures', 'Algorithms', 'Database Systems', 'Software Engineering'],\n                  resources: 23\n                }, {\n                  subject: 'Physics',\n                  icon: '⚛️',\n                  color: '#059669',\n                  topics: ['Mechanics', 'Thermodynamics', 'Electromagnetism', 'Quantum Physics'],\n                  resources: 18\n                }, {\n                  subject: 'Chemistry',\n                  icon: '🧪',\n                  color: '#EA580C',\n                  topics: ['Organic Chemistry', 'Inorganic Chemistry', 'Physical Chemistry', 'Biochemistry'],\n                  resources: 12\n                }].map((subject, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    background: globalStyles.currentTheme.secondary,\n                    borderRadius: '12px',\n                    padding: '1.5rem',\n                    border: `1px solid ${globalStyles.currentTheme.border}`,\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.transform = 'translateY(-4px)';\n                    e.currentTarget.style.boxShadow = `0 8px 25px ${globalStyles.currentTheme.shadow}`;\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.transform = 'translateY(0)';\n                    e.currentTarget.style.boxShadow = 'none';\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '1rem',\n                      marginBottom: '1rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        width: '50px',\n                        height: '50px',\n                        borderRadius: '12px',\n                        background: subject.color,\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        fontSize: '1.5rem'\n                      },\n                      children: subject.icon\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2613,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        style: {\n                          margin: 0,\n                          fontSize: '1.2rem',\n                          fontWeight: 600,\n                          color: globalStyles.currentTheme.text\n                        },\n                        children: subject.subject\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2626,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        style: {\n                          margin: 0,\n                          fontSize: '0.9rem',\n                          color: globalStyles.currentTheme.textLight\n                        },\n                        children: [subject.resources, \" resources available\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2634,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2625,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2607,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      flexWrap: 'wrap',\n                      gap: '0.5rem',\n                      marginBottom: '1rem'\n                    },\n                    children: subject.topics.map((topic, topicIndex) => /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        padding: '0.25rem 0.5rem',\n                        background: subject.color + '15',\n                        color: subject.color,\n                        borderRadius: '6px',\n                        fontSize: '0.8rem',\n                        fontWeight: 500\n                      },\n                      children: topic\n                    }, topicIndex, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2651,\n                      columnNumber: 31\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2644,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    style: {\n                      width: '100%',\n                      padding: '0.75rem',\n                      background: subject.color,\n                      color: 'white',\n                      border: 'none',\n                      borderRadius: '8px',\n                      fontSize: '0.9rem',\n                      fontWeight: 500,\n                      cursor: 'pointer',\n                      transition: 'all 0.3s ease'\n                    },\n                    children: \"Browse Materials\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2664,\n                    columnNumber: 27\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2591,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2556,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2537,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: globalStyles.currentTheme.surface,\n                borderRadius: '16px',\n                padding: '2rem',\n                border: `1px solid ${globalStyles.currentTheme.border}`,\n                boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                style: {\n                  margin: '0 0 1.5rem 0',\n                  fontSize: '1.5rem',\n                  fontWeight: 600,\n                  color: globalStyles.currentTheme.text,\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                },\n                children: \"\\uD83D\\uDCDD Exam Preparation Resources\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2691,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'grid',\n                  gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                  gap: '1rem'\n                },\n                children: [{\n                  name: 'Previous Year Papers',\n                  icon: '📄',\n                  count: '50+ papers'\n                }, {\n                  name: 'Sample Questions',\n                  icon: '❓',\n                  count: '500+ questions'\n                }, {\n                  name: 'Study Guides',\n                  icon: '📖',\n                  count: '25+ guides'\n                }, {\n                  name: 'Reference Books',\n                  icon: '📚',\n                  count: '100+ books'\n                }, {\n                  name: 'Video Lectures',\n                  icon: '🎥',\n                  count: '200+ videos'\n                }, {\n                  name: 'Practice Tests',\n                  icon: '✅',\n                  count: '30+ tests'\n                }].map((resource, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '1rem',\n                    padding: '1rem',\n                    background: globalStyles.currentTheme.secondary,\n                    borderRadius: '12px',\n                    border: `1px solid ${globalStyles.currentTheme.border}`,\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.background = globalStyles.currentTheme.primary + '10';\n                    e.currentTarget.style.borderColor = globalStyles.currentTheme.primary;\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.background = globalStyles.currentTheme.secondary;\n                    e.currentTarget.style.borderColor = globalStyles.currentTheme.border;\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '40px',\n                      height: '40px',\n                      borderRadius: '8px',\n                      background: globalStyles.currentTheme.primary,\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      fontSize: '1.2rem'\n                    },\n                    children: resource.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2735,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontWeight: 600,\n                        color: globalStyles.currentTheme.text,\n                        marginBottom: '0.25rem'\n                      },\n                      children: resource.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2748,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.8rem',\n                        color: globalStyles.currentTheme.textLight\n                      },\n                      children: resource.count\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2755,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2747,\n                    columnNumber: 27\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2716,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2703,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2684,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2532,\n            columnNumber: 17\n          }, this), resourcesTab === 'tools' && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: globalStyles.currentTheme.surface,\n              borderRadius: '16px',\n              padding: '2rem',\n              border: `1px solid ${globalStyles.currentTheme.border}`,\n              boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              style: {\n                margin: '0 0 1.5rem 0',\n                fontSize: '1.5rem',\n                fontWeight: 600,\n                color: globalStyles.currentTheme.text,\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              },\n              children: \"\\uD83D\\uDEE0\\uFE0F Study Tools & Utilities\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2778,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',\n                gap: '1.5rem'\n              },\n              children: [{\n                name: 'Scientific Calculator',\n                icon: '🧮',\n                description: 'Advanced calculator for complex calculations',\n                color: '#DC2626'\n              }, {\n                name: 'Formula Reference',\n                icon: '📐',\n                description: 'Quick access to mathematical and scientific formulas',\n                color: '#7C3AED'\n              }, {\n                name: 'Unit Converter',\n                icon: '⚖️',\n                description: 'Convert between different units of measurement',\n                color: '#059669'\n              }, {\n                name: 'Study Timer',\n                icon: '⏰',\n                description: 'Pomodoro timer for effective study sessions',\n                color: '#EA580C'\n              }, {\n                name: 'Note Taking',\n                icon: '📝',\n                description: 'Digital notepad for quick notes and ideas',\n                color: '#0284C7'\n              }, {\n                name: 'Progress Tracker',\n                icon: '📊',\n                description: 'Track your study progress and goals',\n                color: '#7C2D12'\n              }].map((tool, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: globalStyles.currentTheme.secondary,\n                  borderRadius: '12px',\n                  padding: '1.5rem',\n                  border: `1px solid ${globalStyles.currentTheme.border}`,\n                  cursor: 'pointer',\n                  transition: 'all 0.3s ease'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.transform = 'translateY(-4px)';\n                  e.currentTarget.style.boxShadow = `0 8px 25px ${globalStyles.currentTheme.shadow}`;\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = 'none';\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '1rem',\n                    marginBottom: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '50px',\n                      height: '50px',\n                      borderRadius: '12px',\n                      background: tool.color,\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      fontSize: '1.5rem'\n                    },\n                    children: tool.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2855,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                      style: {\n                        margin: 0,\n                        fontSize: '1.1rem',\n                        fontWeight: 600,\n                        color: globalStyles.currentTheme.text\n                      },\n                      children: tool.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2868,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2867,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2849,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: '0 0 1rem 0',\n                    fontSize: '0.9rem',\n                    color: globalStyles.currentTheme.textLight,\n                    lineHeight: 1.4\n                  },\n                  children: tool.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2879,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    background: tool.color,\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: '8px',\n                    fontSize: '0.9rem',\n                    fontWeight: 500,\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease'\n                  },\n                  children: \"Launch Tool\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2888,\n                  columnNumber: 25\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2833,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2790,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2771,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2192,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2187,\n        columnNumber: 11\n      }, this), activeTab === \"academics\" && /*#__PURE__*/_jsxDEV(Exams, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2910,\n        columnNumber: 39\n      }, this), activeTab === \"faq\" && /*#__PURE__*/_jsxDEV(Faq, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2911,\n        columnNumber: 33\n      }, this), activeTab === \"admin\" && (user === null || user === void 0 ? void 0 : user.email) === ADMIN_EMAIL && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getStyle('card'),\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginTop: 0,\n              color: '#333'\n            },\n            children: \"Admin Panel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2915,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '16px',\n              marginBottom: '24px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                ...getStyle('buttonPrimary'),\n                background: adminTab === 'users' ? globalStyles.currentTheme.primary : 'transparent',\n                color: adminTab === 'users' ? 'white' : '#333',\n                border: '1px solid #ddd'\n              },\n              onClick: () => setAdminTab('users'),\n              children: \"Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2924,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                ...getStyle('buttonPrimary'),\n                background: adminTab === 'resources' ? globalStyles.currentTheme.primary : 'transparent',\n                color: adminTab === 'resources' ? 'white' : '#333',\n                border: '1px solid #ddd'\n              },\n              onClick: () => setAdminTab('resources'),\n              children: \"Resources\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2937,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2919,\n            columnNumber: 15\n          }, this), adminTab === 'users' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                marginBottom: '16px',\n                color: '#333'\n              },\n              children: \"All Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2954,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                backgroundColor: '#f5f5f5',\n                border: '1px solid #e0e0e0',\n                borderRadius: '8px',\n                padding: '16px'\n              },\n              children: allUsers.map((user, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '12px',\n                  borderBottom: '1px solid #eee',\n                  color: '#333'\n                },\n                children: user.email\n              }, idx, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2965,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2958,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2953,\n            columnNumber: 17\n          }, this), adminTab === 'resources' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                marginBottom: '16px',\n                color: '#333'\n              },\n              children: \"All Resources\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2979,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                opacity: 0.7,\n                color: '#666'\n              },\n              children: \"Resource management coming soon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2983,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2978,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2914,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2913,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 756,\n      columnNumber: 7\n    }, this), notification && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...getStyle('notification'),\n        backgroundColor: notification.type === 'error' ? '#f44336' : notification.type === 'success' ? '#4caf50' : '#2196f3',\n        color: 'white',\n        border: 'none'\n      },\n      children: notification.msg\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2996,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes fadeIn {\n          from { opacity: 0; transform: translateY(10px); }\n          to { opacity: 1; transform: translateY(0); }\n        }\n        @keyframes slideIn {\n          from { transform: translateX(100%); }\n          to { transform: translateX(0); }\n        }\n        @keyframes pulse {\n          0%, 100% { opacity: 1; }\n          50% { opacity: 0.5; }\n        }\n        @keyframes bounce {\n          0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }\n          40%, 43% { transform: translate3d(0,-8px,0); }\n          70% { transform: translate3d(0,-4px,0); }\n          90% { transform: translate3d(0,-2px,0); }\n        }\n        @keyframes glow {\n          0%, 100% { box-shadow: 0 0 5px rgba(99, 102, 241, 0.3); }\n          50% { box-shadow: 0 0 20px rgba(99, 102, 241, 0.6); }\n        }\n        @keyframes shimmer {\n          0% { background-position: -200px 0; }\n          100% { background-position: calc(200px + 100%) 0; }\n        }\n\n        /* Enhanced Tab Navigation Styles */\n        .tab-navigation-container {\n          position: relative;\n          overflow: hidden;\n        }\n\n        .tab-button {\n          position: relative;\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n          will-change: transform, color;\n        }\n\n        .tab-button:hover:not(.active) {\n          transform: translateY(-1px);\n        }\n\n        .tab-button:active {\n          transform: translateY(0);\n        }\n\n        .tab-button.active {\n          color: white;\n        }\n\n        .sliding-indicator {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n          will-change: left, width;\n        }\n\n        /* Responsive Tab Styles */\n        @media (max-width: 768px) {\n          .tab-button {\n            font-size: 0.9rem;\n            padding: 0.75rem 0.75rem;\n          }\n        }\n\n        @media (max-width: 640px) {\n          .tab-button {\n            padding: 0.75rem 0.5rem;\n          }\n        }\n\n        @media (max-width: 480px) {\n          .tab-button {\n            padding: 0.75rem 0.25rem;\n            min-width: 44px;\n          }\n        }\n\n        /* Focus styles for accessibility */\n        .tab-button:focus-visible {\n          outline: 2px solid var(--primary-color);\n          outline-offset: 2px;\n          border-radius: 8px;\n        }\n\n        /* Touch device optimizations */\n        @media (hover: none) and (pointer: coarse) {\n          .tab-button:hover {\n            transform: none;\n          }\n\n          .tab-button:active {\n            transform: scale(0.98);\n          }\n        }\n\n        /* Enhanced hover effects */\n        .company-card:hover {\n          animation: bounce 0.6s ease;\n        }\n\n        .favorite-btn:hover {\n          animation: pulse 0.5s ease;\n        }\n\n        /* Smooth transitions for all interactive elements */\n        button, input, select {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        button:hover {\n          transform: translateY(-1px);\n        }\n\n        * {\n          box-sizing: border-box;\n        }\n        body {\n          margin: 0;\n          font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 3008,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 585,\n    columnNumber: 5\n  }, this);\n};\n_s(EduAIChatBot, \"4N/NG6NnJtEDIEZuim389AjoX/o=\");\n_c = EduAIChatBot;\nexport default EduAIChatBot;\nvar _c;\n$RefreshReg$(_c, \"EduAIChatBot\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "getDoc", "doc", "Faq", "<PERSON><PERSON>", "Coding", "auth", "db", "axios", "sidebarItems", "onAuthStateChanged", "globalStyles", "FiMenu", "FiX", "FiChevronDown", "FiChevronRight", "FiFileText", "FiCode", "FiHelpCircle", "FiAward", "FiBook", "FiUser", "FiShield", "FiSearch", "FiUpload", "FiLogIn", "FiLogOut", "FiBriefcase", "FiBarChart2", "FiLayers", "FiCheckCircle", "FiExternalLink", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "FiRefreshCw", "createClient", "Bar", "Chart", "BarElement", "CategoryScale", "LinearScale", "<PERSON><PERSON><PERSON>", "Legend", "ReactMarkdown", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "register", "updatedSidebarItems", "map", "item", "iconMap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "tab", "title", "toLowerCase", "EduAIChatBot", "_s", "input", "setInput", "messages", "setMessages", "userId", "setUserId", "loading", "setLoading", "knowledge", "setKnowledge", "activeTab", "setActiveTab", "searchTerm", "setSearchTerm", "sidebarOpen", "setSidebarOpen", "expandedMenus", "setExpandedMenus", "user", "setUser", "resumeUploadLoading", "setResumeUploadLoading", "resumeUrl", "setResumeUrl", "resourceUploadLoading", "setResourceUploadLoading", "userResources", "ADMIN_EMAIL", "allUsers", "adminTab", "setAdminTab", "notification", "setNotification", "activityLog", "setActivityLog", "resourcesTab", "setResourcesTab", "windowWidth", "setW<PERSON>owWidth", "window", "innerWidth", "chatEndRef", "handleResize", "addEventListener", "removeEventListener", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "sortBy", "setSortBy", "favoriteCompanies", "setFavoriteCompanies", "recentCompanies", "setRecentCompanies", "showRevertButton", "setShowRevertButton", "API_KEY", "SUPABASE_URL", "SUPABASE_ANON_KEY", "supabase", "companyCategories", "companies", "quizButtons", "description", "link", "styles", "appContainer", "backgroundColor", "currentTheme", "background", "color", "text", "navbar", "navbarFixed", "borderBottom", "border", "sidebar", "sidebarFixed", "surface", "borderRight", "transform", "sidebarItem", "sidebarItemEdu", "transition", "primary", "sidebarItemActive", "sidebarItemActiveEdu", "mainContent", "mainContentEdu", "marginLeft", "minHeight", "card", "cardEdu", "boxShadow", "shadow", "buttonPrimary", "inputField", "borderColor", "outline", "chatBubbleUser", "chatBubbleBot", "secondary", "companyCard", "companyCardEdu", "quizCard", "quizCardEdu", "getStyle", "styleName", "hover", "baseStyle", "unsubscribe", "uid", "console", "log", "fetchUserProfile", "userRef", "userDoc", "exists", "userData", "data", "dp", "fetch", "then", "res", "catch", "err", "error", "getSession", "session", "listener", "onAuthStateChange", "_event", "subscription", "handleResumeUpload", "e", "file", "target", "files", "filePath", "id", "name", "storage", "from", "upload", "upsert", "urlData", "getPublicUrl", "publicUrl", "showNotification", "logActivity", "handleResourceUpload", "handleCompanyClick", "company", "prev", "filtered", "filter", "c", "slice", "location", "href", "formattedCompany", "replace", "toggleFavorite", "stopPropagation", "includes", "revertHeaderChanges", "eduNovaElement", "document", "querySelector", "subtitleElement", "style", "getFilteredCompanies", "categoryCompanies", "some", "catCompany", "sort", "a", "b", "aFav", "bFav", "localeCompare", "openQuizLink", "url", "open", "sendMessage", "trim", "userMessage", "role", "content", "_res$data$candidates", "_res$data$candidates$", "_res$data$candidates$2", "_res$data$candidates$3", "_res$data$candidates$4", "prompt", "post", "contents", "parts", "headers", "botReply", "candidates", "botMessage", "message", "handleLogout", "signOut", "msg", "type", "setTimeout", "date", "Date", "toISOString", "toggleMenu", "menu", "current", "scrollIntoView", "behavior", "getLast7Days", "days", "i", "d", "setDate", "getDate", "push", "toLocaleDateString", "chartLabels", "chartData", "labels", "datasets", "label", "day", "startsWith", "length", "chartOptions", "responsive", "plugins", "legend", "position", "tooltip", "enabled", "scales", "y", "beginAtZero", "ticks", "stepSize", "children", "marginRight", "cursor", "padding", "borderRadius", "onClick", "onMouseEnter", "onMouseLeave", "size", "flex", "display", "alignItems", "src", "require", "alt", "height", "fontWeight", "fontSize", "opacity", "gap", "width", "justifyContent", "<PERSON><PERSON>ilter", "email", "toUpperCase", "index", "borderLeft", "subItems", "subItem", "subIndex", "paddingLeft", "top", "left", "right", "bottom", "zIndex", "gradientLight", "overflow", "backgroundImage", "backgroundSize", "max<PERSON><PERSON><PERSON>", "margin", "marginBottom", "gradient", "gridTemplateColumns", "lineHeight", "split", "textLight", "flexWrap", "textAlign", "month", "year", "value", "unit", "trend", "stat", "currentTarget", "textTransform", "letterSpacing", "subtitle", "desc", "action", "min<PERSON><PERSON><PERSON>", "Math", "random", "floor", "flexDirection", "progress", "status", "goal", "maxHeight", "overflowY", "flexShrink", "toLocaleString", "accept", "onChange", "disabled", "rel", "textDecoration", "marginTop", "fontStyle", "borderTop", "paddingTop", "idx", "animation", "animationDelay", "ref", "onSubmit", "preventDefault", "placeholder", "paddingBottom", "Object", "keys", "category", "fill", "primaryDark", "char<PERSON>t", "toFixed", "entries", "quiz", "className", "<PERSON><PERSON><PERSON><PERSON>", "onFocus", "outlineOffset", "onBlur", "tabIndex", "whiteSpace", "userSelect", "WebkitTapHighlightColor", "subject", "topics", "resources", "topic", "topicIndex", "count", "resource", "tool", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(5)/src/EduAIChatBot.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\nimport { getDoc, doc } from 'firebase/firestore';\nimport Faq from './Faq';\nimport Exams from \"./Exams\";\nimport Coding from \"./Coding\";\nimport { auth, db } from './firebaseConfig';\nimport axios from \"axios\";\nimport { sidebarItems } from './sidebarItems';\nimport { onAuthStateChanged } from 'firebase/auth';\nimport globalStyles from './styles.js';\nimport {\n  FiMenu, FiX, FiChevronDown, FiChevronRight, FiFileText,\n  FiCode, FiHelpCircle, FiAward, FiBook, FiUser, FiShield,\n  FiSearch, FiUpload, FiLogIn, FiLogOut,\n  FiBriefcase, FiBarChart2, FiLayers, FiCheckCircle, FiExternalLink,\n  FiHeart, FiClock, FiRefreshCw\n} from \"react-icons/fi\";\nimport { createClient } from '@supabase/supabase-js';\nimport { Bar } from 'react-chartjs-2';\nimport { Chart, BarElement, CategoryScale, LinearScale, Tooltip, Legend } from 'chart.js';\nimport ReactMarkdown from 'react-markdown';\n\nChart.register(BarElement, CategoryScale, LinearScale, Tooltip, Legend);\n\n// Enhanced sidebar items with icons\nconst updatedSidebarItems = sidebarItems.map(item => {\n  const iconMap = {\n    \"resume\": <FiFileText />,\n    \"dsa\": <FiCode />,\n    \"coding\": <FiLayers />,\n    \"resources\": <FiBriefcase />,\n    \"quizzes\": <FiCheckCircle />,\n    \"aptitude\": <FiBarChart2 />,\n    \"academics\": <FiBook />,\n    \"faq\": <FiHelpCircle />,\n    \"admin\": <FiShield />\n  };\n\n  return {\n    ...item,\n    icon: iconMap[item.tab] || iconMap[item.title.toLowerCase()] || <FiAward />\n  };\n});\n\nconst EduAIChatBot = () => {\n  // State declarations\n  const [input, setInput] = useState(\"\");\n  const [messages, setMessages] = useState([]);\n  const [userId, setUserId] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [knowledge, setKnowledge] = useState(\"\");\n  const [activeTab, setActiveTab] = useState(\"dashboard\");\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [expandedMenus, setExpandedMenus] = useState({});\n  const [user, setUser] = useState(null);\n\n  const [resumeUploadLoading, setResumeUploadLoading] = useState(false);\n  const [resumeUrl, setResumeUrl] = useState(null);\n  const [resourceUploadLoading, setResourceUploadLoading] = useState(false);\n  const [userResources] = useState([]);\n  const ADMIN_EMAIL = '<EMAIL>';\n  const [allUsers] = useState([]);\n  const [adminTab, setAdminTab] = useState('users');\n  const [notification, setNotification] = useState(null);\n  const [activityLog, setActivityLog] = useState([]);\n  const [resourcesTab, setResourcesTab] = useState('general'); // New state for resources tab\n  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1024); // Responsive state\n  const chatEndRef = useRef(null);\n\n  // Handle window resize for responsive tab navigation\n  useEffect(() => {\n    const handleResize = () => {\n      setWindowWidth(window.innerWidth);\n    };\n\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  // Enhanced DSA section states\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [sortBy, setSortBy] = useState('name');\n  const [favoriteCompanies, setFavoriteCompanies] = useState([]);\n  const [recentCompanies, setRecentCompanies] = useState([]);\n  const [showRevertButton, setShowRevertButton] = useState(true);\n\n  // API configurations\n  const API_KEY = \"AIzaSyC6kHWto78QdqHz7Uu9RzEXb443ZO7tG5M\";\n  const SUPABASE_URL = 'https://gziaptswfepiveyylven.supabase.co';\n  const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imd6aWFwdHN3ZmVwaXZleXlsdmVuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU2NzczNTYsImV4cCI6MjA2MTI1MzM1Nn0.wmqXZGffrox8E_PuCwbzh4xJEffsvFmZCVcF6WFAX6Q';\n  const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);\n\n  // Company categories for enhanced DSA section\n  const companyCategories = {\n    'FAANG': ['Facebook', 'Apple', 'Amazon', 'Netflix', 'Google'],\n    'Big Tech': ['Microsoft', 'Adobe', 'Salesforce', 'Oracle', 'IBM', 'Intel', 'Nvidia'],\n    'Startups': ['Stripe', 'Airbnb', 'Uber', 'Lyft', 'DoorDash', 'Instacart', 'Coinbase'],\n    'Finance': ['Goldman Sachs', 'JPMorgan', 'Morgan Stanley', 'BlackRock', 'Citadel', 'Two Sigma'],\n    'Consulting': ['McKinsey', 'BCG', 'Bain', 'Deloitte', 'Accenture', 'PwC'],\n    'E-commerce': ['Amazon', 'eBay', 'Shopify', 'Etsy', 'Wayfair', 'Booking.com'],\n    'Gaming': ['Blizzard', 'Epic Games', 'Riot Games', 'Unity', 'Roblox'],\n    'Indian': ['TCS', 'Infosys', 'Wipro', 'HCL', 'Flipkart', 'Paytm', 'Zomato', 'Swiggy']\n  };\n\n  // Complete list of companies\n  const companies = [\n     \"Accenture\", \"Accolite\", \"Adobe\", \"Affirm\", \"Agoda\", \"Airbnb\", \"Airtel\",\n    \"Akamar\", \"Akuna Capital\", \"Alibaba\", \"Altimetrik\", \"Amazon\", \"AMD\",\n    \"Amdocs\", \"American Express\", \"Anduril\", \"Apple\", \"Arista Networks\",\n    \"Arcesium\", \"Atlassian\", \"Attentive\", \"athenahealth\", \"Autodesk\",\n    \"Avito\", \"Baidu\", \"Barclays\", \"BitGo\", \"BlackRock\", \"Blizzard\",\n    \"Block\", \"Bloomberg\", \"BNY Mellon\", \"Boft\", \"Booking.com\", \"Bos\",\n    \"BP\", \"ByteDance\", \"Cadence\", \"Capgemini\", \"Capital One\", \"CARS24\",\n    \"carwale\", \"Cashfree\", \"Chewy\", \"Cisco\", \"Citadel\", \"Citrix\",\n    \"Cloudera\", \"Cloudflare\", \"Cognizant\", \"Coinbase\", \"Commvault\",\n    \"Confluent\", \"Coupang\", \"Coursera\", \"CrowdStrike\", \"Cruise\",\n    \"Curefit\", \"Databricks\", \"Datadog\", \"DE Shaw\", \"Deloitte\", \"Dell\",\n    \"Deliveroo\", \"Derantior\", \"Deutsche Bank\", \"Devflev\", \"Directi\",\n    \"Disney\", \"Docusign\", \"DoorDash\", \"Dream11\", \"Dropbox\", \"DRW\",\n    \"Dunzo\", \"eBay\", \"EPAM Systems\", \"Epic Systems\", \"Expedia\",\n    \"FactSet\", \"Flexport\", \"Flipkart\", \"Freshworks\", \"GE Healthcare\",\n    \"Geico\", \"Goldman Sachs\", \"Google\", \"Grab\", \"Grammarly\", \"Graviton\",\n    \"Groww\", \"GSN Games\", \"Hashedin\", \"HCL\", \"HPE\", \"Hubspot\",\n    \"Hudson River Trading\", \"Huawei\", \"IBM\", \"IMC\", \"Indeed\", \"Infosys\",\n    \"InMobi\", \"Intel\", \"Intuit\", \"JPMorgan\", \"Jane Street\",\n    \"Josh Technology\", \"Jump Trading\", \"Juspay\", \"Karat\", \"KLA\",\n    \"LinkedIn\", \"LiveRamp\", \"Lowe's\", \"Lucid\", \"Lyft\", \"MakeMyTrip\",\n    \"Mastercard\", \"MathWorks\", \"Media.net\", \"Meesho\", \"Mercari\", \"Meta\",\n    \"Microsoft\", \"Millennium\", \"Mitsogo\", \"Moloco\", \"MongoDB\",\n    \"Morgan Stanley\", \"Moveworks\", \"Myntra\", \"Nagarro\", \"NetApp\",\n    \"Netflix\", \"Nextdoor\", \"Nielsen\", \"Nike\", \"Niantic\", \"Nordstrom\",\n    \"Nutanix\", \"Nvidia\", \"Okta\", \"OKX\", \"OpenAI\", \"OpenText\", \"Oracle\",\n    \"Otter.ai\", \"Oyo\", \"Ozon\", \"Palantir Technologies\", \"Palo Alto Networks\",\n    \"PayPal\", \"Paytm\", \"Persistent Systems\", \"PhonePe\", \"Pinterest\",\n    \"Pocket Gems\", \"Point72\", \"Pure Storage\", \"Qualcomm\", \"Quora\",\n    \"Rakuten\", \"Razorpay\", \"RBC\", \"Reddit\", \"Revolut\", \"Robinhood\",\n    \"Roblox\", \"Rubrik\", \"Salesforce\", \"Samsung\", \"SAP\", \"ServiceNow\",\n    \"Shopify\", \"Siemens\", \"Sigmoid\", \"SIG\", \"Snowflake\", \"Snap\", \"Sofi\",\n    \"Splunk\", \"Spotify\", \"Sprinklr\", \"Squarepoint Capital\", \"Stripe\",\n    \"Swiggy\", \"TCS\", \"Tekion\", \"Tencent\", \"Tesla\", \"ThoughtSpot\",\n    \"ThoughtWorks\", \"TikTok\", \"Tinkoff\", \"Trilogy\", \"Turing\", \"Turo\",\n    \"Twilio\", \"Twitch\", \"Two Sigma\", \"Uber\", \"UiPath\", \"UKG\",\n    \"Veeva Systems\", \"Verily\", \"Verkada\", \"Virtu Financial\", \"Visa\",\n    \"VK\", \"VMware\", \"Walmart Labs\", \"WarnerMedia\", \"Wayfair\",\n    \"Wells Fargo\", \"Wipro\", \"Wix\", \"Workday\", \"X\", \"Yahoo\", \"Yandex\",\n    \"Yelp\", \"Zalando\", \"Zenefits\", \"Zepto\", \"Zeta\", \"Zillow\", \"Zoho\",\n    \"Zomato\", \"ZScaler\", \"Zopsmart\"\n  ];\n\n  // Quiz buttons data\n  const quizButtons = [\n    {\n      title: \"OP and CN Quiz\",\n      description: \"Test your knowledge of Operating System and Computer Networks\",\n      link: \"https://opcn.netlify.app\",\n    },\n    {\n      title: \"OOPs and DBMS Quiz\",\n      description: \"Challenge yourself with oops and dbms\",\n      link: \"https://oopsanddbms.netlify.app/\",\n    },\n    {\n      title: \"System Design Quiz\",\n      description: \"Test your system design knowledge\",\n      link: \"https://system-design041.netlify.app\",\n    },\n    {\n      title: \"Quantitative Aptitude and Reasoning Quiz\",\n      description: \"Practice common quant and reasoning questions\",\n      link: \"https://quantandreasoning.netlify.app\",\n    },\n    {\n      title: \"Cloud & DevOps Quiz\",\n      description: \"Test your knowledge of Cloud and DevOps concepts\",\n      link: \"https://cloud-devops.netlify.app\",\n    },\n    {\n      title: \"DSA Quiz\",\n      description: \"Data Structures and Algorithms quiz\",\n      link: \"https://dsa041.netlify.app\",\n    },\n    {\n      title: \"Operating System & Computer Networks Quiz\",\n      description: \"Quiz on OS and Computer Networks\",\n      link: \"https://opcn.netlify.app\",\n    },\n     {\n      title: \"Web Development Quiz\",\n      description: \"Quiz on Web Development topics\",\n      link: \"https://web-dev041.netlify.app\",\n\n    },\n  ];\n\n  // Use centralized styles\n  const styles = {\n    ...globalStyles,\n    appContainer: {\n      ...globalStyles.appContainer,\n      backgroundColor: globalStyles.currentTheme.background,\n      color: globalStyles.currentTheme.text,\n    },\n    navbar: {\n      ...globalStyles.navbarFixed,\n      borderBottom: `1px solid ${globalStyles.currentTheme.border}`\n    },\n    sidebar: {\n      ...globalStyles.sidebarFixed,\n      backgroundColor: globalStyles.currentTheme.surface,\n      borderRight: `1px solid ${globalStyles.currentTheme.border}`,\n      transform: sidebarOpen ? 'translateX(0)' : 'translateX(-100%)',\n    },\n    sidebarItem: {\n      ...globalStyles.sidebarItemEdu,\n      color: globalStyles.currentTheme.text,\n      background: globalStyles.currentTheme.surface,\n      border: `1px solid ${globalStyles.currentTheme.border}`,\n      transition: 'all 0.3s ease',\n      '&:hover': {\n        background: globalStyles.currentTheme.primary,\n        color: 'white'\n      }\n    },\n    sidebarItemActive: {\n      ...globalStyles.sidebarItemActiveEdu,\n      color: 'white',\n      background: globalStyles.currentTheme.primary,\n      border: `1px solid ${globalStyles.currentTheme.primary}`,\n    },\n    mainContent: {\n      ...globalStyles.mainContentEdu,\n      marginLeft: sidebarOpen ? '280px' : '0',\n      backgroundColor: globalStyles.currentTheme.background,\n      color: globalStyles.currentTheme.text,\n      minHeight: '100vh'\n    },\n    card: {\n      ...globalStyles.cardEdu,\n      backgroundColor: globalStyles.currentTheme.surface,\n      color: globalStyles.currentTheme.text,\n      border: `1px solid ${globalStyles.currentTheme.border}`,\n      boxShadow: `0 4px 6px ${globalStyles.currentTheme.shadow}`\n    },\n    buttonPrimary: {\n      ...globalStyles.buttonPrimary,\n    },\n    inputField: {\n      ...globalStyles.inputField,\n      backgroundColor: '#fff',\n      color: '#333',\n      border: '1px solid #ddd',\n      '&:focus': {\n        borderColor: globalStyles.currentTheme.primary,\n        outline: 'none',\n        boxShadow: `0 0 0 2px ${globalStyles.currentTheme.primary}20`\n      }\n    },\n    chatBubbleUser: {\n      ...globalStyles.chatBubbleUser,\n      backgroundColor: globalStyles.currentTheme.primary,\n      color: 'white'\n    },\n    chatBubbleBot: {\n      ...globalStyles.chatBubbleBot,\n      backgroundColor: globalStyles.currentTheme.secondary,\n      color: globalStyles.currentTheme.text,\n      border: '1px solid transparent'\n    },\n    companyCard: {\n      ...globalStyles.companyCardEdu,\n    },\n    quizCard: {\n      ...globalStyles.quizCardEdu,\n      backgroundColor: globalStyles.currentTheme.surface,\n    },\n    notification: {\n      ...globalStyles.notification,\n    }\n  };\n\n  // Helper function to apply styles with hover states\n  const getStyle = (styleName, hover = false) => {\n    const baseStyle = styles[styleName];\n    if (typeof baseStyle === 'function') return baseStyle();\n    if (hover && baseStyle['&:hover']) {\n      return { ...baseStyle, ...baseStyle['&:hover'] };\n    }\n    return baseStyle;\n  };\n\n  // Fetch user profile\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, (user) => {\n      if (user) {\n        setUserId(user.uid);\n      } else {\n        console.log('User is not authenticated');\n        setLoading(false);\n      }\n    });\n    return () => unsubscribe();\n  }, []);\n\n  useEffect(() => {\n    if (userId) {\n      const fetchUserProfile = async () => {\n        const userRef = doc(db, \"users\", userId);\n        const userDoc = await getDoc(userRef);\n\n        if (userDoc.exists()) {\n          const userData = userDoc.data();\n          // Profile pic functionality can be added later\n          console.log(\"User data loaded:\", userData.dp);\n        } else {\n          console.log(\"No such user!\");\n        }\n        setLoading(false);\n      };\n      fetchUserProfile();\n    }\n  }, [userId]);\n\n  // Fetch training data\n  useEffect(() => {\n    fetch(\"/training-data.txt\")\n      .then((res) => res.text())\n      .then((data) => setKnowledge(data))\n      .catch((err) => console.error(\"Failed to load training data:\", err));\n  }, []);\n\n  // Supabase auth state\n  useEffect(() => {\n    supabase.auth.getSession().then(({ data: { session } }) => {\n      setUser(session?.user || null);\n    });\n    const { data: listener } = supabase.auth.onAuthStateChange((_event, session) => {\n      setUser(session?.user || null);\n    });\n    return () => {\n      listener?.subscription.unsubscribe();\n    };\n  }, [supabase.auth]);\n\n  // Handle resume upload\n  const handleResumeUpload = async (e) => {\n    const file = e.target.files[0];\n    if (!file || !user) return;\n    setResumeUploadLoading(true);\n    const filePath = `${user.id}/${file.name}`;\n    const { error } = await supabase.storage.from('resumes').upload(filePath, file, { upsert: true });\n    if (!error) {\n      const { data: urlData } = supabase.storage.from('resumes').getPublicUrl(filePath);\n      setResumeUrl(urlData.publicUrl);\n      showNotification('Resume uploaded successfully!', 'success');\n      logActivity('Uploaded a resume');\n    } else {\n      showNotification('Resume upload failed.', 'error');\n    }\n    setResumeUploadLoading(false);\n  };\n\n  // Handle resource upload\n  const handleResourceUpload = async (e) => {\n    const file = e.target.files[0];\n    if (!file || !user) return;\n    setResourceUploadLoading(true);\n    const filePath = `${user.id}/${file.name}`;\n    const { error } = await supabase.storage.from('resources').upload(filePath, file, { upsert: true });\n    if (!error) {\n      showNotification('Resource uploaded!', 'success');\n      logActivity(`Uploaded resource: ${file.name}`);\n    } else {\n      showNotification('Resource upload failed.', 'error');\n    }\n    setResourceUploadLoading(false);\n  };\n\n  // Enhanced company click handler\n  const handleCompanyClick = (company) => {\n    // Add to recent companies\n    setRecentCompanies(prev => {\n      const filtered = prev.filter(c => c !== company);\n      return [company, ...filtered].slice(0, 5); // Keep only 5 recent\n    });\n\n    logActivity(`Viewed ${company} DSA questions`);\n\n    if (company.toLowerCase() === 'microsoft') {\n      window.location.href = '/company-dsa/Microsoft_questions.html';\n      return;\n    }\n    const formattedCompany = company.replace(/\\s+/g, '');\n    window.location.href = `/company-dsa/${formattedCompany}.html`;\n  };\n\n  // Toggle favorite company\n  const toggleFavorite = (company, e) => {\n    e.stopPropagation(); // Prevent company click\n    setFavoriteCompanies(prev => {\n      if (prev.includes(company)) {\n        return prev.filter(c => c !== company);\n      } else {\n        return [...prev, company];\n      }\n    });\n  };\n\n  // Revert header color changes\n  const revertHeaderChanges = () => {\n    setShowRevertButton(false);\n    showNotification('Header text color reverted to theme default!', 'success');\n\n    // Actually revert the header text color by updating the DOM\n    const eduNovaElement = document.querySelector('[data-header-title]');\n    const subtitleElement = document.querySelector('[data-header-subtitle]');\n\n    if (eduNovaElement) {\n      eduNovaElement.style.color = '#333';\n    }\n    if (subtitleElement) {\n      subtitleElement.style.color = '#333';\n    }\n  };\n\n  // Get filtered companies based on category and search\n  const getFilteredCompanies = () => {\n    let filtered = companies;\n\n    // Filter by category\n    if (selectedCategory !== 'all') {\n      const categoryCompanies = companyCategories[selectedCategory] || [];\n      filtered = filtered.filter(company =>\n        categoryCompanies.some(catCompany =>\n          company.toLowerCase().includes(catCompany.toLowerCase()) ||\n          catCompany.toLowerCase().includes(company.toLowerCase())\n        )\n      );\n    }\n\n    // Filter by search term\n    filtered = filtered.filter(company =>\n      company.toLowerCase().includes(searchTerm.toLowerCase())\n    );\n\n    // Sort companies\n    if (sortBy === 'name') {\n      filtered.sort();\n    } else if (sortBy === 'favorites') {\n      filtered.sort((a, b) => {\n        const aFav = favoriteCompanies.includes(a);\n        const bFav = favoriteCompanies.includes(b);\n        if (aFav && !bFav) return -1;\n        if (!aFav && bFav) return 1;\n        return a.localeCompare(b);\n      });\n    }\n\n    return filtered;\n  };\n\n  // Open quiz link\n  const openQuizLink = (url) => {\n    window.open(url, \"_blank\");\n  };\n\n  // Send message to chatbot\n  const sendMessage = async () => {\n    if (!input.trim()) return;\n\n    const userMessage = { role: \"user\", content: input };\n    setMessages((prev) => [...prev, userMessage]);\n    setInput(\"\");\n    setLoading(true);\n\n    try {\n      const prompt = `You are a resume assistant. Help users improve their resumes, provide suggestions, and answer career-related questions. Use the following knowledge if it helps. If it's not relevant, use your own intelligence.\\n\\nKnowledge:\\n${knowledge}\\n\\nQuestion: ${input}`;\n\n      const res = await axios.post(\n        `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`,\n        {\n          contents: [\n            {\n              parts: [{ text: prompt }],\n            },\n          ],\n        },\n        {\n          headers: {\n            \"Content-Type\": \"application/json\",\n          },\n        }\n      );\n\n      const botReply =\n        res.data.candidates?.[0]?.content?.parts?.[0]?.text ||\n        \"⚠ No response received.\";\n      const botMessage = { role: \"bot\", content: botReply };\n      setMessages((prev) => [...prev, botMessage]);\n    } catch (error) {\n      console.error(\"Gemini API Error:\", error);\n      setMessages((prev) => [\n        ...prev,\n        { role: \"bot\", content: \"❌ Error: \" + error.message },\n      ]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Authentication functionality can be added later if needed\n\n  // Handle logout\n  const handleLogout = async () => {\n    await supabase.auth.signOut();\n  };\n\n  // Show notification\n  const showNotification = (msg, type = 'info') => {\n    setNotification({ msg, type });\n    setTimeout(() => setNotification(null), 3000);\n  };\n\n  // Log activity\n  const logActivity = (msg) => {\n    setActivityLog(log => [\n      { type: 'activity', date: new Date().toISOString(), msg },\n      ...log.slice(0, 19)\n    ]);\n  };\n\n  // Toggle menu\n  const toggleMenu = (menu) => {\n    setExpandedMenus(prev => ({\n      ...prev,\n      [menu]: !prev[menu]\n    }));\n  };\n\n  // Auto-scroll chat\n  useEffect(() => {\n    if (chatEndRef.current) chatEndRef.current.scrollIntoView({ behavior: 'smooth' });\n  }, [messages, loading]);\n\n  // Chart data\n  const getLast7Days = () => {\n    const days = [];\n    for (let i = 6; i >= 0; i--) {\n      const d = new Date();\n      d.setDate(d.getDate() - i);\n      days.push(d.toLocaleDateString());\n    }\n    return days;\n  };\n\n  const chartLabels = getLast7Days();\n  const chartData = {\n    labels: chartLabels,\n    datasets: [\n      {\n        label: 'Resource Uploads',\n        data: chartLabels.map(day => activityLog.filter(a => a.type === 'activity' && a.msg.startsWith('Uploaded resource') && new Date(a.date).toLocaleDateString() === day).length),\n        backgroundColor: '#3182ce',\n      },\n      {\n        label: 'Coding Practice',\n        data: chartLabels.map(day => activityLog.filter(a => a.type === 'activity' && a.msg === 'Clicked coding practice link' && new Date(a.date).toLocaleDateString() === day).length),\n        backgroundColor: '#805ad5',\n      },\n    ],\n  };\n\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: { position: 'top' },\n      tooltip: { enabled: true },\n    },\n    scales: {\n      y: { beginAtZero: true, ticks: { stepSize: 1 } },\n    },\n  };\n\n  return (\n    <div style={getStyle('appContainer')}>\n      {/* Top Navigation Bar */}\n      <nav style={getStyle('navbar')}>\n        <button\n          style={{\n            background: 'none',\n            border: 'none',\n            color: 'white', // Always white since navbar has gradient background\n            marginRight: '20px',\n            cursor: 'pointer',\n            padding: '8px',\n            borderRadius: '4px',\n            transition: 'all 0.2s ease'\n          }}\n          onClick={() => setSidebarOpen(!sidebarOpen)}\n          onMouseEnter={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.1)'}\n          onMouseLeave={(e) => e.target.style.background = 'none'}\n        >\n          {sidebarOpen ? <FiX size={24} /> : <FiMenu size={24} />}\n        </button>\n\n        <div style={{ flex: 1, display: 'flex', alignItems: 'center' }}>\n          <img\n            src={require('./eduai-logo.jpg')}\n            alt=\"EduAI Logo\"\n            style={{ height: '36px', marginRight: '12px' }}\n          />\n          <div>\n            <div\n              data-header-title\n              style={{ fontWeight: 600, fontSize: '18px', color: 'white' }}\n            >\n              EDU NOVA\n            </div>\n            <div\n              data-header-subtitle\n              style={{ fontSize: '12px', opacity: 0.8, color: 'white' }}\n            >\n              AI POWERED LEARNING SYSTEM\n            </div>\n          </div>\n        </div>\n\n        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>\n          {user ? (\n            <>\n              <div style={{\n                width: '40px',\n                height: '40px',\n                borderRadius: '50%',\n                background: 'rgba(255, 255, 255, 0.2)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontWeight: 600,\n                cursor: 'pointer',\n                color: 'white',\n                backdropFilter: 'blur(10px)'\n              }}>\n                {user.email === ADMIN_EMAIL ? <FiShield size={20} color=\"#4caf50\" /> : user.email[0].toUpperCase()}\n              </div>\n              <button\n                style={{\n                  ...getStyle('buttonPrimary'),\n                  background: 'rgba(255, 255, 255, 0.2)',\n                  color: 'white',\n                  border: '1px solid rgba(255, 255, 255, 0.3)',\n                  backdropFilter: 'blur(10px)'\n                }}\n                onClick={handleLogout}\n              >\n                <FiLogOut /> Logout\n              </button>\n            </>\n          ) : (\n            <button\n              style={{\n                ...getStyle('buttonPrimary'),\n                background: 'rgba(255, 255, 255, 0.2)',\n                color: 'white',\n                border: '1px solid rgba(255, 255, 255, 0.3)',\n                backdropFilter: 'blur(10px)'\n              }}\n              onClick={() => { console.log('Login functionality to be implemented'); }}\n            >\n              <FiLogIn /> Login\n            </button>\n          )}\n        </div>\n      </nav>\n\n      {/* Sidebar */}\n      <aside style={getStyle('sidebar')}>\n        <div style={{ padding: '16px' }}>\n          {updatedSidebarItems.map((item, index) => (\n            <div key={index}>\n              <div\n                style={{\n                  ...getStyle('sidebarItem'),\n                  ...(activeTab === item.tab ? getStyle('sidebarItemActive') : {}),\n                  cursor: 'pointer',\n                  transition: 'all 0.3s ease'\n                }}\n                onClick={() => {\n                  setActiveTab(item.tab);\n                  setSidebarOpen(false);\n                }}\n                onMouseEnter={(e) => {\n                  if (activeTab !== item.tab) {\n                    e.target.style.background = 'rgba(0, 0, 0, 0.05)';\n                    e.target.style.transform = 'translateX(4px)';\n                    e.target.style.borderLeft = `3px solid ${globalStyles.currentTheme.primary}`;\n                  }\n                }}\n                onMouseLeave={(e) => {\n                  if (activeTab !== item.tab) {\n                    e.target.style.background = globalStyles.currentTheme.surface;\n                    e.target.style.transform = 'translateX(0)';\n                    e.target.style.borderLeft = '3px solid transparent';\n                  }\n                }}\n              >\n                <div style={{ marginRight: '12px' }}>{item.icon}</div>\n                <span style={{ flex: 1 }}>{item.title}</span>\n                {item.subItems.length > 0 && (\n                  <div onClick={(e) => {\n                    e.stopPropagation();\n                    toggleMenu(item.title);\n                  }}>\n                    {expandedMenus[item.title] ? <FiChevronDown /> : <FiChevronRight />}\n                  </div>\n                )}\n              </div>\n\n              {item.subItems.length > 0 && expandedMenus[item.title] && (\n                <div style={{ marginLeft: '32px' }}>\n                  {item.subItems.map((subItem, subIndex) => (\n                    <div\n                      key={subIndex}\n                      style={{\n                        ...getStyle('sidebarItem'),\n                        padding: '8px 16px 8px 32px',\n                        fontSize: '14px',\n                        opacity: 0.9\n                      }}\n                      onClick={() => {\n                        setActiveTab(item.tab);\n                        setSidebarOpen(false);\n                      }}\n                      onMouseEnter={(e) => {\n                        e.target.style.background = 'rgba(0, 0, 0, 0.03)';\n                        e.target.style.paddingLeft = '36px';\n                        e.target.style.opacity = '1';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.target.style.background = globalStyles.currentTheme.surface;\n                        e.target.style.paddingLeft = '32px';\n                        e.target.style.opacity = '0.9';\n                      }}\n                    >\n                      {subItem.title}\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n      </aside>\n\n      {/* Main Content */}\n      <main style={getStyle('mainContent')}>\n        {/* Overlay when sidebar is open on mobile */}\n        {sidebarOpen && window.innerWidth < 768 && (\n          <div\n            style={{\n              position: 'fixed',\n              top: '64px',\n              left: 0,\n              right: 0,\n              bottom: 0,\n              backgroundColor: 'rgba(0,0,0,0.5)',\n              zIndex: 800\n            }}\n            onClick={() => setSidebarOpen(false)}\n          />\n        )}\n\n        {/* Dashboard Content */}\n        {activeTab === \"dashboard\" && (\n          <div style={{\n            padding: '2rem 1rem',\n            background: globalStyles.currentTheme.gradientLight,\n            minHeight: '100vh',\n            position: 'relative',\n            overflow: 'hidden'\n          }}>\n            {/* Professional Background Pattern */}\n            <div style={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              backgroundImage: `\n                radial-gradient(circle at 20% 30%, ${globalStyles.currentTheme.primary}08 0%, transparent 50%),\n                radial-gradient(circle at 80% 70%, ${globalStyles.currentTheme.primary}05 0%, transparent 50%),\n                radial-gradient(circle at 40% 80%, ${globalStyles.currentTheme.primary}03 0%, transparent 50%)\n              `,\n              zIndex: 0\n            }} />\n\n            {/* Subtle Grid Pattern */}\n            <div style={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              backgroundImage: `\n                linear-gradient(${globalStyles.currentTheme.border} 1px, transparent 1px),\n                linear-gradient(90deg, ${globalStyles.currentTheme.border} 1px, transparent 1px)\n              `,\n              backgroundSize: '50px 50px',\n              opacity: 0.3,\n              zIndex: 0\n            }} />\n\n            <div style={{ maxWidth: '1400px', margin: '0 auto', position: 'relative', zIndex: 1 }}>\n\n              {/* Professional Hero Section */}\n              <div style={{\n                background: globalStyles.currentTheme.surface,\n                backdropFilter: 'blur(20px)',\n                borderRadius: '20px',\n                padding: '3rem',\n                marginBottom: '2rem',\n                position: 'relative',\n                overflow: 'hidden',\n                border: `1px solid ${globalStyles.currentTheme.border}`,\n                boxShadow: `0 20px 40px ${globalStyles.currentTheme.shadow}`\n              }}>\n                {/* Professional accent line */}\n                <div style={{\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  right: 0,\n                  height: '4px',\n                  background: globalStyles.currentTheme.gradient\n                }} />\n\n                <div style={{\n                  display: 'grid',\n                  gridTemplateColumns: 'auto 1fr auto',\n                  gap: '2rem',\n                  alignItems: 'center',\n                  position: 'relative',\n                  zIndex: 1\n                }}>\n                  {/* Professional Avatar */}\n                  <div style={{\n                    width: '100px',\n                    height: '100px',\n                    borderRadius: '20px',\n                    background: globalStyles.currentTheme.gradient,\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    fontSize: '2.5rem',\n                    fontWeight: 'bold',\n                    color: 'white',\n                    boxShadow: `0 10px 30px ${globalStyles.currentTheme.shadow}`,\n                    border: `3px solid ${globalStyles.currentTheme.surface}`\n                  }}>\n                    {user ? user.email[0].toUpperCase() : '👤'}\n                  </div>\n\n                  {/* Welcome Content */}\n                  <div>\n                    <h1 style={{\n                      margin: 0,\n                      fontSize: '2.5rem',\n                      fontWeight: 700,\n                      marginBottom: '0.5rem',\n                      color: globalStyles.currentTheme.text,\n                      lineHeight: 1.2\n                    }}>\n                      Welcome back, {user ? user.email.split('@')[0] : 'User'}! 👋\n                    </h1>\n                    <p style={{\n                      margin: 0,\n                      fontSize: '1.1rem',\n                      color: globalStyles.currentTheme.textLight,\n                      fontWeight: 400,\n                      marginBottom: '1.5rem'\n                    }}>\n                      Ready to continue your learning journey? Let's achieve your goals together.\n                    </p>\n\n                    {/* Professional Status Badges */}\n                    <div style={{\n                      display: 'flex',\n                      gap: '1rem',\n                      flexWrap: 'wrap'\n                    }}>\n                      <div style={{\n                        background: globalStyles.currentTheme.primary + '15',\n                        color: globalStyles.currentTheme.primary,\n                        padding: '0.5rem 1rem',\n                        borderRadius: '12px',\n                        fontSize: '0.9rem',\n                        fontWeight: 600,\n                        border: `1px solid ${globalStyles.currentTheme.primary}30`\n                      }}>\n                        📈 Active Learner\n                      </div>\n                      <div style={{\n                        background: globalStyles.currentTheme.primary + '15',\n                        color: globalStyles.currentTheme.primary,\n                        padding: '0.5rem 1rem',\n                        borderRadius: '12px',\n                        fontSize: '0.9rem',\n                        fontWeight: 600,\n                        border: `1px solid ${globalStyles.currentTheme.primary}30`\n                      }}>\n                        🎯 Goal Oriented\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Quick Stats */}\n                  <div style={{\n                    textAlign: 'center',\n                    padding: '1rem',\n                    background: globalStyles.currentTheme.secondary,\n                    borderRadius: '16px',\n                    border: `1px solid ${globalStyles.currentTheme.border}`\n                  }}>\n                    <div style={{\n                      fontSize: '2rem',\n                      fontWeight: 700,\n                      color: globalStyles.currentTheme.primary,\n                      marginBottom: '0.5rem'\n                    }}>\n                      {new Date().toLocaleDateString('en-US', { day: 'numeric' })}\n                    </div>\n                    <div style={{\n                      fontSize: '0.9rem',\n                      color: globalStyles.currentTheme.textLight,\n                      fontWeight: 500\n                    }}>\n                      {new Date().toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Professional Analytics Cards */}\n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',\n                gap: '1.5rem',\n                marginBottom: '2rem'\n              }}>\n                {[\n                  {\n                    title: 'Learning Progress',\n                    value: '78',\n                    unit: '%',\n                    icon: '📈',\n                    trend: '+12%',\n                    description: 'This month',\n                    color: globalStyles.currentTheme.primary\n                  },\n                  {\n                    title: 'Study Hours',\n                    value: '47',\n                    unit: 'hrs',\n                    icon: '⏱️',\n                    trend: '+8 hrs',\n                    description: 'This week',\n                    color: '#10B981'\n                  },\n                  {\n                    title: 'Completed Tasks',\n                    value: '23',\n                    unit: 'tasks',\n                    icon: '✅',\n                    trend: '+5',\n                    description: 'This week',\n                    color: '#3B82F6'\n                  },\n                  {\n                    title: 'Skill Rating',\n                    value: '4.8',\n                    unit: '/5.0',\n                    icon: '⭐',\n                    trend: '+0.3',\n                    description: 'Overall',\n                    color: '#F59E0B'\n                  }\n                ].map((stat, index) => (\n                  <div key={index} style={{\n                    background: globalStyles.currentTheme.surface,\n                    borderRadius: '16px',\n                    padding: '1.5rem',\n                    position: 'relative',\n                    overflow: 'hidden',\n                    border: `1px solid ${globalStyles.currentTheme.border}`,\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n                  }}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.transform = 'translateY(-4px)';\n                    e.currentTarget.style.boxShadow = `0 8px 25px ${globalStyles.currentTheme.shadow}`;\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.transform = 'translateY(0)';\n                    e.currentTarget.style.boxShadow = `0 4px 12px ${globalStyles.currentTheme.shadow}`;\n                  }}>\n\n                    {/* Subtle accent line */}\n                    <div style={{\n                      position: 'absolute',\n                      top: 0,\n                      left: 0,\n                      right: 0,\n                      height: '3px',\n                      background: stat.color\n                    }} />\n\n                    <div style={{ position: 'relative', zIndex: 1 }}>\n                      {/* Header */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        marginBottom: '1rem'\n                      }}>\n                        <div style={{\n                          fontSize: '0.9rem',\n                          fontWeight: 600,\n                          color: globalStyles.currentTheme.textLight,\n                          textTransform: 'uppercase',\n                          letterSpacing: '0.5px'\n                        }}>\n                          {stat.title}\n                        </div>\n                        <div style={{\n                          fontSize: '1.5rem',\n                          opacity: 0.8\n                        }}>\n                          {stat.icon}\n                        </div>\n                      </div>\n\n                      {/* Main Value */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'baseline',\n                        gap: '0.5rem',\n                        marginBottom: '0.5rem'\n                      }}>\n                        <div style={{\n                          fontSize: '2.5rem',\n                          fontWeight: 700,\n                          color: globalStyles.currentTheme.text,\n                          lineHeight: 1\n                        }}>\n                          {stat.value}\n                        </div>\n                        <div style={{\n                          fontSize: '1rem',\n                          fontWeight: 500,\n                          color: globalStyles.currentTheme.textLight\n                        }}>\n                          {stat.unit}\n                        </div>\n                      </div>\n\n                      {/* Trend and Description */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between'\n                      }}>\n                        <div style={{\n                          fontSize: '0.8rem',\n                          color: globalStyles.currentTheme.textLight\n                        }}>\n                          {stat.description}\n                        </div>\n                        <div style={{\n                          fontSize: '0.8rem',\n                          fontWeight: 600,\n                          color: stat.color,\n                          background: stat.color + '15',\n                          padding: '0.25rem 0.5rem',\n                          borderRadius: '8px'\n                        }}>\n                          {stat.trend}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n\n              {/* Professional Quick Actions */}\n              <div style={{\n                background: globalStyles.currentTheme.surface,\n                borderRadius: '20px',\n                padding: '2rem',\n                marginBottom: '2rem',\n                border: `1px solid ${globalStyles.currentTheme.border}`,\n                boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                position: 'relative',\n                overflow: 'hidden'\n              }}>\n                {/* Professional accent line */}\n                <div style={{\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  right: 0,\n                  height: '4px',\n                  background: globalStyles.currentTheme.gradient\n                }} />\n\n                <h2 style={{\n                  margin: '0 0 2rem 0',\n                  fontSize: '1.5rem',\n                  fontWeight: 600,\n                  color: globalStyles.currentTheme.text,\n                  textAlign: 'center'\n                }}>\n                  🚀 Quick Actions\n                </h2>\n\n                <div style={{\n                  display: 'grid',\n                  gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                  gap: '1.5rem'\n                }}>\n                  {[\n                    {\n                      icon: '📊',\n                      title: 'Take Assessment',\n                      subtitle: 'Skill Evaluation',\n                      desc: 'Evaluate your current skill level',\n                      action: () => setActiveTab('quizzes'),\n                      color: globalStyles.currentTheme.primary\n                    },\n                    {\n                      icon: '💻',\n                      title: 'Practice Coding',\n                      subtitle: 'DSA Problems',\n                      desc: 'Solve data structures & algorithms',\n                      action: () => setActiveTab('dsa'),\n                      color: '#3B82F6'\n                    },\n                    {\n                      icon: '📄',\n                      title: 'Resume Review',\n                      subtitle: 'Career Enhancement',\n                      desc: 'Get professional resume feedback',\n                      action: () => setActiveTab('resume'),\n                      color: '#10B981'\n                    },\n                    {\n                      icon: '📚',\n                      title: 'Study Resources',\n                      subtitle: 'Learning Materials',\n                      desc: 'Access curated study materials',\n                      action: () => setActiveTab('resources'),\n                      color: '#F59E0B'\n                    }\n                  ].map((action, index) => (\n                    <div key={index}\n                      onClick={action.action}\n                      style={{\n                        background: globalStyles.currentTheme.surface,\n                        borderRadius: '16px',\n                        padding: '1.5rem',\n                        cursor: 'pointer',\n                        transition: 'all 0.3s ease',\n                        border: `1px solid ${globalStyles.currentTheme.border}`,\n                        position: 'relative',\n                        overflow: 'hidden',\n                        boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-4px)';\n                        e.currentTarget.style.boxShadow = `0 8px 25px ${globalStyles.currentTheme.shadow}`;\n                        e.currentTarget.style.borderColor = action.color;\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = `0 4px 12px ${globalStyles.currentTheme.shadow}`;\n                        e.currentTarget.style.borderColor = globalStyles.currentTheme.border;\n                      }}\n                    >\n                      {/* Subtle accent line */}\n                      <div style={{\n                        position: 'absolute',\n                        top: 0,\n                        left: 0,\n                        right: 0,\n                        height: '3px',\n                        background: action.color\n                      }} />\n\n                      <div style={{ position: 'relative', zIndex: 1, textAlign: 'center' }}>\n                        <div style={{\n                          fontSize: '2.5rem',\n                          marginBottom: '1rem',\n                          opacity: 0.8\n                        }}>\n                          {action.icon}\n                        </div>\n\n                        <div style={{\n                          fontSize: '1.1rem',\n                          fontWeight: 600,\n                          color: globalStyles.currentTheme.text,\n                          marginBottom: '0.5rem'\n                        }}>\n                          {action.title}\n                        </div>\n\n                        <div style={{\n                          fontSize: '0.9rem',\n                          color: action.color,\n                          fontWeight: 500,\n                          marginBottom: '0.75rem'\n                        }}>\n                          {action.subtitle}\n                        </div>\n\n                        <div style={{\n                          fontSize: '0.85rem',\n                          color: globalStyles.currentTheme.textLight,\n                          lineHeight: 1.4\n                        }}>\n                          {action.desc}\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* Professional Insights Section */}\n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',\n                gap: '2rem',\n                marginBottom: '2rem'\n              }}>\n                {/* Learning Progress Chart */}\n                <div style={{\n                  background: globalStyles.currentTheme.surface,\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                  border: `1px solid ${globalStyles.currentTheme.border}`\n                }}>\n                  <h3 style={{\n                    margin: '0 0 1.5rem 0',\n                    fontSize: '1.2rem',\n                    fontWeight: 600,\n                    color: globalStyles.currentTheme.text,\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem'\n                  }}>\n                    📊 Weekly Progress\n                  </h3>\n\n                  {/* Simple Progress Visualization */}\n                  <div style={{ marginBottom: '1rem' }}>\n                    {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, index) => (\n                      <div key={day} style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        marginBottom: '0.75rem',\n                        padding: '0.5rem',\n                        borderRadius: '8px',\n                        background: index < 5 ? globalStyles.currentTheme.secondary : 'transparent'\n                      }}>\n                        <span style={{\n                          fontSize: '0.9rem',\n                          fontWeight: 500,\n                          color: globalStyles.currentTheme.text,\n                          minWidth: '40px'\n                        }}>\n                          {day}\n                        </span>\n                        <div style={{\n                          flex: 1,\n                          height: '8px',\n                          background: globalStyles.currentTheme.border,\n                          borderRadius: '4px',\n                          margin: '0 1rem',\n                          overflow: 'hidden'\n                        }}>\n                          <div style={{\n                            height: '100%',\n                            width: `${Math.random() * 80 + 20}%`,\n                            background: index < 5 ? globalStyles.currentTheme.primary : globalStyles.currentTheme.border,\n                            borderRadius: '4px',\n                            transition: 'width 1s ease'\n                          }} />\n                        </div>\n                        <span style={{\n                          fontSize: '0.8rem',\n                          color: globalStyles.currentTheme.textLight,\n                          minWidth: '40px',\n                          textAlign: 'right'\n                        }}>\n                          {index < 5 ? `${Math.floor(Math.random() * 3 + 1)}h` : '-'}\n                        </span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Goals & Achievements */}\n                <div style={{\n                  background: globalStyles.currentTheme.surface,\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                  border: `1px solid ${globalStyles.currentTheme.border}`\n                }}>\n                  <h3 style={{\n                    margin: '0 0 1.5rem 0',\n                    fontSize: '1.2rem',\n                    fontWeight: 600,\n                    color: globalStyles.currentTheme.text,\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem'\n                  }}>\n                    🎯 Goals & Achievements\n                  </h3>\n\n                  <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n                    {[\n                      { title: 'Complete 5 DSA Problems', progress: 80, status: 'In Progress' },\n                      { title: 'Finish Resume Review', progress: 100, status: 'Completed' },\n                      { title: 'Take 3 Practice Tests', progress: 33, status: 'In Progress' }\n                    ].map((goal, index) => (\n                      <div key={index} style={{\n                        padding: '1rem',\n                        borderRadius: '12px',\n                        background: globalStyles.currentTheme.secondary,\n                        border: `1px solid ${globalStyles.currentTheme.border}`\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          justifyContent: 'space-between',\n                          alignItems: 'center',\n                          marginBottom: '0.5rem'\n                        }}>\n                          <span style={{\n                            fontSize: '0.9rem',\n                            fontWeight: 500,\n                            color: globalStyles.currentTheme.text\n                          }}>\n                            {goal.title}\n                          </span>\n                          <span style={{\n                            fontSize: '0.8rem',\n                            fontWeight: 600,\n                            color: goal.progress === 100 ? '#10B981' : globalStyles.currentTheme.primary,\n                            background: goal.progress === 100 ? '#10B98115' : globalStyles.currentTheme.primary + '15',\n                            padding: '0.25rem 0.5rem',\n                            borderRadius: '6px'\n                          }}>\n                            {goal.status}\n                          </span>\n                        </div>\n                        <div style={{\n                          height: '6px',\n                          background: globalStyles.currentTheme.border,\n                          borderRadius: '3px',\n                          overflow: 'hidden'\n                        }}>\n                          <div style={{\n                            height: '100%',\n                            width: `${goal.progress}%`,\n                            background: goal.progress === 100 ? '#10B981' : globalStyles.currentTheme.primary,\n                            borderRadius: '3px',\n                            transition: 'width 1s ease'\n                          }} />\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n\n              {/* Recent Activity & Resume Management */}\n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr',\n                gap: '2rem'\n              }}>\n\n                {/* Recent Activity */}\n                <div style={{\n                  background: globalStyles.currentTheme.surface,\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                  border: `1px solid ${globalStyles.currentTheme.border}`\n                }}>\n                  <h3 style={{\n                    margin: '0 0 1.5rem 0',\n                    fontSize: '1.3rem',\n                    fontWeight: 600,\n                    color: globalStyles.currentTheme.text\n                  }}>\n                    📈 Recent Activity\n                  </h3>\n\n                  <div style={{\n                    maxHeight: '250px',\n                    overflowY: 'auto'\n                  }}>\n                    {activityLog.slice(0, 5).map((log, index) => (\n                      <div key={index} style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem',\n                        padding: '0.75rem',\n                        borderRadius: '8px',\n                        marginBottom: '0.5rem',\n                        background: index % 2 === 0 ? globalStyles.currentTheme.secondary : 'transparent',\n                        transition: 'all 0.3s ease'\n                      }}>\n                        <div style={{\n                          width: '10px',\n                          height: '10px',\n                          borderRadius: '50%',\n                          background: log.type === 'login' ? '#4ECDC4' : '#45B7D1',\n                          flexShrink: 0\n                        }} />\n                        <div style={{ flex: 1 }}>\n                          <div style={{\n                            fontSize: '0.9rem',\n                            fontWeight: 500,\n                            color: globalStyles.currentTheme.text,\n                            marginBottom: '0.2rem'\n                          }}>\n                            {log.msg}\n                          </div>\n                          <div style={{\n                            fontSize: '0.8rem',\n                            color: globalStyles.currentTheme.textLight\n                          }}>\n                            {new Date(log.date).toLocaleString()}\n                          </div>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Resume Management */}\n                <div style={{\n                  background: globalStyles.currentTheme.surface,\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                  border: `1px solid ${globalStyles.currentTheme.border}`\n                }}>\n                  <h3 style={{\n                    margin: '0 0 1.5rem 0',\n                    fontSize: '1.3rem',\n                    fontWeight: 600,\n                    color: globalStyles.currentTheme.text\n                  }}>\n                    📄 Resume Management\n                  </h3>\n\n                  <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n                    <label style={{\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.75rem',\n                      padding: '1rem',\n                      borderRadius: '12px',\n                      background: globalStyles.currentTheme.secondary,\n                      border: `2px dashed ${globalStyles.currentTheme.border}`,\n                      cursor: resumeUploadLoading ? 'not-allowed' : 'pointer',\n                      transition: 'all 0.3s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      if (!resumeUploadLoading) {\n                        e.currentTarget.style.borderColor = globalStyles.currentTheme.primary;\n                        e.currentTarget.style.background = globalStyles.currentTheme.primary + '10';\n                      }\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.borderColor = globalStyles.currentTheme.border;\n                      e.currentTarget.style.background = globalStyles.currentTheme.secondary;\n                    }}>\n                      <FiUpload size={20} color={globalStyles.currentTheme.primary} />\n                      <div>\n                        <div style={{\n                          fontWeight: 600,\n                          color: globalStyles.currentTheme.text,\n                          marginBottom: '0.2rem'\n                        }}>\n                          {resumeUploadLoading ? 'Uploading...' : 'Upload Resume'}\n                        </div>\n                        <div style={{\n                          fontSize: '0.8rem',\n                          color: globalStyles.currentTheme.textLight\n                        }}>\n                          PDF files only\n                        </div>\n                      </div>\n                      <input\n                        type=\"file\"\n                        accept=\"application/pdf\"\n                        onChange={handleResumeUpload}\n                        disabled={resumeUploadLoading}\n                        style={{ display: 'none' }}\n                      />\n                    </label>\n\n                    {resumeUrl && (\n                      <a\n                        href={resumeUrl}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.75rem',\n                          padding: '1rem',\n                          borderRadius: '12px',\n                          background: '#4ECDC4',\n                          color: 'white',\n                          textDecoration: 'none',\n                          fontWeight: 600,\n                          transition: 'all 0.3s ease'\n                        }}\n                        onMouseEnter={(e) => {\n                          e.currentTarget.style.background = '#3DBDB6';\n                          e.currentTarget.style.transform = 'translateY(-2px)';\n                        }}\n                        onMouseLeave={(e) => {\n                          e.currentTarget.style.background = '#4ECDC4';\n                          e.currentTarget.style.transform = 'translateY(0)';\n                        }}\n                      >\n                        <FiFileText size={20} />\n                        <span>View Resume</span>\n                      </a>\n                    )}\n                  </div>\n                </div>\n              </div>\n\n              {/* Professional Footer Section */}\n              <div style={{\n                background: globalStyles.currentTheme.surface,\n                borderRadius: '16px',\n                padding: '2rem',\n                marginTop: '2rem',\n                border: `1px solid ${globalStyles.currentTheme.border}`,\n                boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                textAlign: 'center'\n              }}>\n                <div style={{\n                  display: 'grid',\n                  gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                  gap: '2rem',\n                  marginBottom: '1.5rem'\n                }}>\n                  {/* Quick Stats */}\n                  <div>\n                    <h4 style={{\n                      margin: '0 0 1rem 0',\n                      fontSize: '1rem',\n                      fontWeight: 600,\n                      color: globalStyles.currentTheme.text\n                    }}>\n                      📈 Your Progress\n                    </h4>\n                    <div style={{\n                      display: 'flex',\n                      flexDirection: 'column',\n                      gap: '0.5rem'\n                    }}>\n                      <div style={{\n                        fontSize: '0.9rem',\n                        color: globalStyles.currentTheme.textLight\n                      }}>\n                        Total Study Time: <strong style={{ color: globalStyles.currentTheme.primary }}>47 hours</strong>\n                      </div>\n                      <div style={{\n                        fontSize: '0.9rem',\n                        color: globalStyles.currentTheme.textLight\n                      }}>\n                        Completed Tasks: <strong style={{ color: globalStyles.currentTheme.primary }}>23</strong>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Quick Links */}\n                  <div>\n                    <h4 style={{\n                      margin: '0 0 1rem 0',\n                      fontSize: '1rem',\n                      fontWeight: 600,\n                      color: globalStyles.currentTheme.text\n                    }}>\n                      🔗 Quick Links\n                    </h4>\n                    <div style={{\n                      display: 'flex',\n                      flexDirection: 'column',\n                      gap: '0.5rem'\n                    }}>\n                      <button\n                        onClick={() => setActiveTab('academics')}\n                        style={{\n                          background: 'none',\n                          border: 'none',\n                          color: globalStyles.currentTheme.primary,\n                          fontSize: '0.9rem',\n                          cursor: 'pointer',\n                          textAlign: 'left',\n                          padding: '0.25rem 0'\n                        }}\n                      >\n                        📚 Academic Resources\n                      </button>\n                      <button\n                        onClick={() => setActiveTab('coding')}\n                        style={{\n                          background: 'none',\n                          border: 'none',\n                          color: globalStyles.currentTheme.primary,\n                          fontSize: '0.9rem',\n                          cursor: 'pointer',\n                          textAlign: 'left',\n                          padding: '0.25rem 0'\n                        }}\n                      >\n                        💻 Coding Practice\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Motivational Quote */}\n                  <div>\n                    <h4 style={{\n                      margin: '0 0 1rem 0',\n                      fontSize: '1rem',\n                      fontWeight: 600,\n                      color: globalStyles.currentTheme.text\n                    }}>\n                      💡 Daily Inspiration\n                    </h4>\n                    <div style={{\n                      fontSize: '0.9rem',\n                      color: globalStyles.currentTheme.textLight,\n                      fontStyle: 'italic',\n                      lineHeight: 1.4\n                    }}>\n                      \"Success is not final, failure is not fatal: it is the courage to continue that counts.\"\n                    </div>\n                  </div>\n                </div>\n\n                {/* Bottom Footer */}\n                <div style={{\n                  borderTop: `1px solid ${globalStyles.currentTheme.border}`,\n                  paddingTop: '1rem',\n                  fontSize: '0.8rem',\n                  color: globalStyles.currentTheme.textLight\n                }}>\n                  Keep learning, keep growing! 🚀 Your journey to success starts here.\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Chat Interface */}\n        {activeTab === \"resume\" && (\n          <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>\n            <div style={getStyle('card')}>\n              <h2 style={{\n                marginTop: 0,\n                color: '#333'\n              }}>Career Assistant</h2>\n              <p style={{\n                opacity: 0.8,\n                marginBottom: '24px',\n                color: '#666'\n              }}>\n                Get personalized resume advice and career guidance\n              </p>\n\n              {/* Chat messages */}\n              <div style={{\n                height: '50vh',\n                overflowY: 'auto',\n                marginBottom: '24px',\n                padding: '16px',\n                backgroundColor: '#f5f5f5',\n                border: '1px solid #e0e0e0',\n                borderRadius: '8px'\n              }}>\n\n                {messages.length === 0 ? (\n                  <div style={{\n                    height: '100%',\n                    display: 'flex',\n                    flexDirection: 'column',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    textAlign: 'center',\n                    opacity: 0.7\n                  }}>\n                    <div style={{ fontSize: '48px', marginBottom: '16px' }}>💬</div>\n                    <h3 style={{\n                      margin: 0,\n                      color: '#333'\n                    }}>Start a conversation</h3>\n                    <p style={{\n                      color: '#666'\n                    }}>Ask about resumes, interviews, or career advice</p>\n                  </div>\n                ) : (\n                  messages.map((msg, idx) => (\n                    <div\n                      key={idx}\n                      style={{\n                        ...(msg.role === 'user' ? getStyle('chatBubbleUser') : getStyle('chatBubbleBot')),\n                        animation: 'fadeIn 0.3s ease'\n                      }}\n                    >\n                      {msg.role === 'bot' ? (\n                        <ReactMarkdown>{msg.content}</ReactMarkdown>\n                      ) : (\n                        msg.content\n                      )}\n                    </div>\n                  ))\n                )}\n                {loading && (\n                  <div style={getStyle('chatBubbleBot')}>\n                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n                      <div style={{\n                        width: '10px',\n                        height: '10px',\n                        borderRadius: '50%',\n                        backgroundColor: '#1976d2',\n                        animation: 'pulse 1.4s infinite ease-in-out'\n                      }} />\n                      <div style={{\n                        width: '10px',\n                        height: '10px',\n                        borderRadius: '50%',\n                        backgroundColor: '#1976d2',\n                        animation: 'pulse 1.4s infinite ease-in-out',\n                        animationDelay: '0.2s'\n                      }} />\n                      <div style={{\n                        width: '10px',\n                        height: '10px',\n                        borderRadius: '50%',\n                        backgroundColor: '#1976d2',\n                        animation: 'pulse 1.4s infinite ease-in-out',\n                        animationDelay: '0.4s'\n                      }} />\n                    </div>\n                  </div>\n                )}\n                <div ref={chatEndRef} />\n              </div>\n\n              {/* Input area */}\n              <form\n                style={{ display: 'flex', gap: '12px' }}\n                onSubmit={e => {\n                  e.preventDefault();\n                  sendMessage();\n                }}\n              >\n                <input\n                  type=\"text\"\n                  placeholder=\"Type your message...\"\n                  style={getStyle('inputField')}\n                  value={input}\n                  onChange={e => setInput(e.target.value)}\n                  disabled={loading}\n                />\n                <button\n                  type=\"submit\"\n                  style={{\n                    ...getStyle('buttonPrimary'),\n                    minWidth: '100px'\n                  }}\n                  disabled={loading || !input.trim()}\n                >\n                  {loading ? 'Sending...' : 'Send'}\n                </button>\n              </form>\n            </div>\n          </div>\n        )}\n\n        {/* Enhanced DSA Company Questions */}\n        {activeTab === \"dsa\" && (\n          <div style={{ padding: '24px' }}>\n            <div style={getStyle('card')}>\n              {/* Header with revert button */}\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>\n                <div>\n                  <h2 style={{ marginTop: 0, marginBottom: '8px' }}>🚀 Company Wise DSA Questions</h2>\n                  <p style={{ opacity: 0.8, margin: 0 }}>\n                    Explore DSA questions from top companies with enhanced filtering and favorites\n                  </p>\n                </div>\n                {showRevertButton && (\n                  <button\n                    onClick={revertHeaderChanges}\n                    style={{\n                      ...getStyle('buttonPrimary'),\n                      background: '#ff6b6b',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '8px',\n                      fontSize: '14px',\n                      padding: '8px 16px',\n                      border: 'none',\n                      borderRadius: '8px',\n                      color: 'white',\n                      cursor: 'pointer',\n                      transition: 'all 0.3s ease'\n                    }}\n                    onMouseEnter={(e) => e.target.style.background = '#ff5252'}\n                    onMouseLeave={(e) => e.target.style.background = '#ff6b6b'}\n                  >\n                    <FiRefreshCw size={16} />\n                    Revert Header Color\n                  </button>\n                )}\n              </div>\n\n              {/* Category Tabs */}\n              <div style={{\n                display: 'flex',\n                gap: '8px',\n                marginBottom: '20px',\n                flexWrap: 'wrap',\n                borderBottom: '1px solid #eee',\n                paddingBottom: '16px'\n              }}>\n                {['all', ...Object.keys(companyCategories)].map(category => (\n                  <button\n                    key={category}\n                    onClick={() => setSelectedCategory(category)}\n                    style={{\n                      padding: '8px 16px',\n                      borderRadius: '20px',\n                      border: selectedCategory === category\n                        ? 'none'\n                        : '1px solid #ddd',\n                      background: selectedCategory === category\n                        ? globalStyles.currentTheme.primary\n                        : 'transparent',\n                      color: selectedCategory === category\n                        ? 'white'\n                        : '#666',\n                      cursor: 'pointer',\n                      fontSize: '14px',\n                      fontWeight: selectedCategory === category ? 600 : 400,\n                      transition: 'all 0.3s ease',\n                      textTransform: 'capitalize'\n                    }}\n                    onMouseEnter={(e) => {\n                      if (selectedCategory !== category) {\n                        e.target.style.background = '#f5f5f5';\n                      }\n                    }}\n                    onMouseLeave={(e) => {\n                      if (selectedCategory !== category) {\n                        e.target.style.background = 'transparent';\n                      }\n                    }}\n                  >\n                    {category === 'all' ? '🌟 All' : `${category === 'FAANG' ? '🔥' : category === 'Big Tech' ? '💻' : category === 'Startups' ? '🚀' : category === 'Finance' ? '💰' : category === 'Indian' ? '🇮🇳' : '🏢'} ${category}`}\n                  </button>\n                ))}\n              </div>\n\n              {/* Controls Row */}\n              <div style={{\n                display: 'flex',\n                gap: '16px',\n                marginBottom: '24px',\n                flexWrap: 'wrap',\n                alignItems: 'center'\n              }}>\n                {/* Search box */}\n                <div style={{ position: 'relative', flex: 1, minWidth: '300px' }}>\n                  <div style={{\n                    position: 'absolute',\n                    left: '16px',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    color: '#666'\n                  }}>\n                    <FiSearch size={20} />\n                  </div>\n                  <input\n                    type=\"text\"\n                    placeholder=\"Search companies...\"\n                    style={{\n                      ...getStyle('inputField'),\n                      paddingLeft: '48px',\n                      width: '100%'\n                    }}\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                  />\n                  {searchTerm && (\n                    <button\n                      style={{\n                        position: 'absolute',\n                        right: '16px',\n                        top: '50%',\n                        transform: 'translateY(-50%)',\n                        background: 'none',\n                        border: 'none',\n                        color: '#666',\n                        cursor: 'pointer'\n                      }}\n                      onClick={() => setSearchTerm(\"\")}\n                    >\n                      <FiX size={20} />\n                    </button>\n                  )}\n                </div>\n\n                {/* Sort dropdown */}\n                <select\n                  value={sortBy}\n                  onChange={(e) => setSortBy(e.target.value)}\n                  style={{\n                    ...getStyle('inputField'),\n                    width: 'auto',\n                    minWidth: '150px'\n                  }}\n                >\n                  <option value=\"name\">📝 Sort by Name</option>\n                  <option value=\"favorites\">⭐ Favorites First</option>\n                </select>\n              </div>\n\n              {/* Recent Companies */}\n              {recentCompanies.length > 0 && (\n                <div style={{\n                  marginBottom: '24px',\n                  padding: '16px',\n                  borderRadius: '12px',\n                  background: '#f8f9fa',\n                  border: '1px solid #e9ecef'\n                }}>\n                  <h3 style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '8px',\n                    fontSize: '16px',\n                    marginBottom: '12px',\n                    color: '#333',\n                    margin: '0 0 12px 0'\n                  }}>\n                    <FiClock color=\"#666\" /> Recently Viewed\n                  </h3>\n                  <div style={{\n                    display: 'flex',\n                    gap: '8px',\n                    flexWrap: 'wrap'\n                  }}>\n                    {recentCompanies.map(company => (\n                      <button\n                        key={company}\n                        onClick={() => handleCompanyClick(company)}\n                        style={{\n                          padding: '6px 12px',\n                          borderRadius: '16px',\n                          border: `1px solid ${globalStyles.currentTheme.primary}`,\n                          background: 'transparent',\n                          color: globalStyles.currentTheme.primary,\n                          cursor: 'pointer',\n                          fontSize: '12px',\n                          transition: 'all 0.3s ease'\n                        }}\n                        onMouseEnter={(e) => {\n                          e.target.style.background = globalStyles.currentTheme.primary;\n                          e.target.style.color = 'white';\n                        }}\n                        onMouseLeave={(e) => {\n                          e.target.style.background = 'transparent';\n                          e.target.style.color = globalStyles.currentTheme.primary;\n                        }}\n                      >\n                        {company}\n                      </button>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {/* Companies grid */}\n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fill, minmax(220px, 1fr))',\n                gap: '16px',\n                marginTop: '24px'\n              }}>\n                {getFilteredCompanies().map((company, index) => (\n                  <div\n                    key={index}\n                    style={{\n                      ...getStyle('companyCard'),\n                      position: 'relative',\n                      transform: favoriteCompanies.includes(company) ? 'scale(1.02)' : 'scale(1)',\n                      border: favoriteCompanies.includes(company)\n                        ? `2px solid ${globalStyles.currentTheme.primary}`\n                        : `1px solid ${globalStyles.currentTheme.border}`,\n                      background: globalStyles.currentTheme.surface,\n                      color: globalStyles.currentTheme.text,\n                      animation: `fadeIn 0.3s ease ${index * 0.1}s both`,\n                      boxShadow: `0 4px 6px ${globalStyles.currentTheme.shadow}`\n                    }}\n                    onClick={() => handleCompanyClick(company)}\n                  >\n                    {/* Favorite button */}\n                    <button\n                      onClick={(e) => toggleFavorite(company, e)}\n                      style={{\n                        position: 'absolute',\n                        top: '8px',\n                        right: '8px',\n                        background: 'none',\n                        border: 'none',\n                        cursor: 'pointer',\n                        color: favoriteCompanies.includes(company) ? '#ff6b6b' : '#ccc',\n                        transition: 'all 0.3s ease',\n                        fontSize: '18px'\n                      }}\n                    >\n                      <FiHeart fill={favoriteCompanies.includes(company) ? 'currentColor' : 'none'} />\n                    </button>\n\n                    {/* Company initial with gradient */}\n                    <div style={{\n                      width: '56px',\n                      height: '56px',\n                      borderRadius: '50%',\n                      background: `linear-gradient(135deg, ${globalStyles.currentTheme.primary}, ${globalStyles.currentTheme.primaryDark})`,\n                      color: 'white',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      fontSize: '24px',\n                      fontWeight: 700,\n                      marginBottom: '12px',\n                      boxShadow: `0 4px 8px ${globalStyles.currentTheme.shadow}`\n                    }}>\n                      {company.charAt(0)}\n                    </div>\n\n                    {/* Company name */}\n                    <div style={{\n                      fontWeight: 600,\n                      textAlign: 'center',\n                      fontSize: '14px',\n                      marginBottom: '8px'\n                    }}>\n                      {company}\n                    </div>\n\n                    {/* Mock stats */}\n                    <div style={{\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      fontSize: '12px',\n                      opacity: 0.7,\n                      marginTop: '8px'\n                    }}>\n                      <span>📊 {Math.floor(Math.random() * 50) + 10} Questions</span>\n                      <span>⭐ {(Math.random() * 2 + 3).toFixed(1)}</span>\n                    </div>\n\n                    {/* Category badge */}\n                    {Object.entries(companyCategories).map(([category, companies]) => {\n                      if (companies.some(c => c.toLowerCase() === company.toLowerCase())) {\n                        return (\n                          <div\n                            key={category}\n                            style={{\n                              position: 'absolute',\n                              top: '8px',\n                              left: '8px',\n                              background: globalStyles.currentTheme.primary,\n                              color: 'white',\n                              padding: '2px 6px',\n                              borderRadius: '8px',\n                              fontSize: '10px',\n                              fontWeight: 600\n                            }}\n                          >\n                            {category}\n                          </div>\n                        );\n                      }\n                      return null;\n                    })}\n                  </div>\n                ))}\n              </div>\n\n              {/* No results message */}\n              {getFilteredCompanies().length === 0 && (\n                <div style={{\n                  textAlign: 'center',\n                  padding: '40px',\n                  opacity: 0.7,\n                  color: '#666'\n                }}>\n                  <div style={{ fontSize: '48px', marginBottom: '16px' }}>🔍</div>\n                  <h3 style={{ color: '#333' }}>No companies found</h3>\n                  <p style={{ color: '#666' }}>Try adjusting your search or category filter</p>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Quizzes Section */}\n        {activeTab === \"quizzes\" && (\n          <div style={{ padding: '24px' }}>\n            <div style={getStyle('card')}>\n              <h2 style={{ marginTop: 0 }}>Career Quizzes</h2>\n              <p style={{ opacity: 0.8, marginBottom: '24px' }}>\n                Test your knowledge with our career-focused quizzes!\n              </p>\n\n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',\n                gap: '16px'\n              }}>\n                {quizButtons.map((quiz, index) => (\n                  <div\n                    key={index}\n                    style={getStyle('quizCard')}\n                    onClick={() => openQuizLink(quiz.link)}\n                  >\n                    <div>\n                      <h3 style={{ margin: '0 0 8px 0' }}>{quiz.title}</h3>\n                      <p style={{\n                        margin: 0,\n                        fontSize: '14px',\n                        opacity: 0.8\n                      }}>\n                        {quiz.description}\n                      </p>\n                    </div>\n                    <div style={{ color: '#1976d2' }}>\n                      <FiExternalLink size={20} />\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Other tabs */}\n        {activeTab === \"coding\" && (\n          <div style={{ padding: '24px' }}>\n            <Coding />\n          </div>\n        )}\n        {activeTab === \"resources\" && (\n          <div style={{\n            padding: '2rem 1rem',\n            background: globalStyles.currentTheme.gradientLight,\n            minHeight: '100vh'\n          }}>\n            <div style={{ maxWidth: '1400px', margin: '0 auto' }}>\n              {/* Header */}\n              <div style={{\n                background: globalStyles.currentTheme.surface,\n                borderRadius: '20px',\n                padding: '2rem',\n                marginBottom: '2rem',\n                border: `1px solid ${globalStyles.currentTheme.border}`,\n                boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                position: 'relative'\n              }}>\n                <div style={{\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  right: 0,\n                  height: '4px',\n                  background: globalStyles.currentTheme.gradient\n                }} />\n\n                <h1 style={{\n                  margin: '0 0 1rem 0',\n                  fontSize: '2.5rem',\n                  fontWeight: 700,\n                  color: globalStyles.currentTheme.text,\n                  textAlign: 'center'\n                }}>\n                  📚 Study Resources & Materials\n                </h1>\n                <p style={{\n                  margin: 0,\n                  fontSize: '1.1rem',\n                  color: globalStyles.currentTheme.textLight,\n                  textAlign: 'center'\n                }}>\n                  Access comprehensive study materials, academic resources, and learning tools\n                </p>\n              </div>\n\n              {/* Enhanced Tab Navigation with Sliding Underline */}\n              <div style={{\n                background: globalStyles.currentTheme.surface,\n                borderRadius: '16px',\n                padding: '1.5rem',\n                marginBottom: '2rem',\n                border: `1px solid ${globalStyles.currentTheme.border}`,\n                boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n              }}>\n                {/* Tab Container with Sliding Underline */}\n                <div\n                  id=\"tab-navigation-container\"\n                  className=\"tab-navigation-container\"\n                  style={{\n                    position: 'relative',\n                    display: 'flex',\n                    justifyContent: 'center',\n                    alignItems: 'center',\n                    background: globalStyles.currentTheme.secondary,\n                    borderRadius: '12px',\n                    padding: '0.5rem',\n                    overflow: 'hidden'\n                  }}\n                >\n                  {/* Sliding Background Indicator */}\n                  <div\n                    id=\"sliding-indicator\"\n                    style={{\n                      position: 'absolute',\n                      top: '0.5rem',\n                      bottom: '0.5rem',\n                      background: globalStyles.currentTheme.primary,\n                      borderRadius: '8px',\n                      transition: 'all 0.3s ease-in-out',\n                      zIndex: 1,\n                      // Dynamic positioning based on active tab\n                      left: resourcesTab === 'general' ? '0.5rem' :\n                            resourcesTab === 'academics' ? 'calc(33.333% + 0.167rem)' :\n                            'calc(66.666% - 0.167rem)',\n                      width: 'calc(33.333% - 0.333rem)',\n                      boxShadow: `0 2px 8px ${globalStyles.currentTheme.primary}40`\n                    }}\n                  />\n\n                  {/* Tab Buttons */}\n                  {[\n                    { id: 'general', label: '📁 General Resources', icon: '📁', shortLabel: 'General' },\n                    { id: 'academics', label: '🎓 Academic Materials', icon: '🎓', shortLabel: 'Academics' },\n                    { id: 'tools', label: '🛠️ Study Tools', icon: '🛠️', shortLabel: 'Tools' }\n                  ].map((tab, index) => (\n                    <button\n                      key={tab.id}\n                      onClick={() => setResourcesTab(tab.id)}\n                      onMouseEnter={(e) => {\n                        // Enhanced hover effect\n                        if (resourcesTab !== tab.id) {\n                          e.target.style.transform = 'translateY(-1px)';\n                          e.target.style.color = globalStyles.currentTheme.primary;\n                        }\n                      }}\n                      onMouseLeave={(e) => {\n                        // Reset hover effect\n                        if (resourcesTab !== tab.id) {\n                          e.target.style.transform = 'translateY(0)';\n                          e.target.style.color = globalStyles.currentTheme.text;\n                        }\n                      }}\n                      onFocus={(e) => {\n                        // Keyboard navigation support\n                        e.target.style.outline = `2px solid ${globalStyles.currentTheme.primary}`;\n                        e.target.style.outlineOffset = '2px';\n                      }}\n                      onBlur={(e) => {\n                        // Remove focus outline\n                        e.target.style.outline = 'none';\n                      }}\n                      aria-label={`Switch to ${tab.label} tab`}\n                      aria-selected={resourcesTab === tab.id}\n                      role=\"tab\"\n                      tabIndex={0}\n                      style={{\n                        flex: 1,\n                        position: 'relative',\n                        zIndex: 2,\n                        padding: '0.875rem 1rem',\n                        minHeight: '44px', // Touch target requirement\n                        border: 'none',\n                        background: 'transparent',\n                        color: resourcesTab === tab.id\n                          ? 'white'\n                          : globalStyles.currentTheme.text,\n                        cursor: 'pointer',\n                        fontSize: windowWidth < 768 ? '0.9rem' : '1rem',\n                        fontWeight: resourcesTab === tab.id ? 600 : 500,\n                        borderRadius: '8px',\n                        transition: 'all 0.3s ease',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        gap: '0.5rem',\n                        textAlign: 'center',\n                        whiteSpace: 'nowrap',\n                        userSelect: 'none',\n                        WebkitTapHighlightColor: 'transparent' // Remove tap highlight on mobile\n                      }}\n                    >\n                      {/* Responsive label display */}\n                      <span style={{\n                        display: windowWidth < 640 ? 'none' : 'inline'\n                      }}>\n                        {tab.icon}\n                      </span>\n                      <span style={{\n                        display: windowWidth < 480 ? 'none' : 'inline'\n                      }}>\n                        {windowWidth < 640 ? tab.shortLabel : tab.label.replace(tab.icon + ' ', '')}\n                      </span>\n                      {/* Mobile-only icon */}\n                      <span style={{\n                        display: windowWidth < 480 ? 'inline' : 'none',\n                        fontSize: '1.2rem'\n                      }}>\n                        {tab.icon}\n                      </span>\n                    </button>\n                  ))}\n                </div>\n\n                {/* Tab Description */}\n                <div style={{\n                  textAlign: 'center',\n                  marginTop: '1rem',\n                  padding: '0.5rem',\n                  fontSize: '0.9rem',\n                  color: globalStyles.currentTheme.textLight,\n                  fontStyle: 'italic'\n                }}>\n                  {resourcesTab === 'general' && 'Upload and manage your personal study materials and documents'}\n                  {resourcesTab === 'academics' && 'Access comprehensive academic resources organized by subject'}\n                  {resourcesTab === 'tools' && 'Utilize powerful study tools and utilities to enhance your learning'}\n                </div>\n              </div>\n\n              {/* Content based on selected tab */}\n              {resourcesTab === 'general' && (\n                <div style={{\n                  background: globalStyles.currentTheme.surface,\n                  borderRadius: '16px',\n                  padding: '2rem',\n                  border: `1px solid ${globalStyles.currentTheme.border}`,\n                  boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n                }}>\n                  <h2 style={{\n                    margin: '0 0 1.5rem 0',\n                    fontSize: '1.5rem',\n                    fontWeight: 600,\n                    color: globalStyles.currentTheme.text\n                  }}>\n                    📁 General Resources\n                  </h2>\n\n                  <div style={{ marginBottom: '2rem' }}>\n                    <label style={{\n                      display: 'inline-flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      padding: '0.75rem 1.5rem',\n                      background: globalStyles.currentTheme.primary,\n                      color: 'white',\n                      borderRadius: '12px',\n                      cursor: resourceUploadLoading ? 'not-allowed' : 'pointer',\n                      fontSize: '1rem',\n                      fontWeight: 500,\n                      transition: 'all 0.3s ease'\n                    }}>\n                      <FiUpload size={20} />\n                      {resourceUploadLoading ? 'Uploading...' : 'Upload Resource'}\n                      <input\n                        type=\"file\"\n                        accept=\".pdf,.doc,.docx,.txt\"\n                        onChange={handleResourceUpload}\n                        disabled={resourceUploadLoading}\n                        style={{ display: 'none' }}\n                      />\n                    </label>\n                  </div>\n\n                  <div>\n                    <h3 style={{\n                      margin: '0 0 1rem 0',\n                      fontSize: '1.2rem',\n                      fontWeight: 600,\n                      color: globalStyles.currentTheme.text\n                    }}>\n                      Your Uploaded Resources\n                    </h3>\n                    {userResources.length === 0 ? (\n                      <div style={{\n                        textAlign: 'center',\n                        padding: '3rem',\n                        background: globalStyles.currentTheme.secondary,\n                        borderRadius: '12px',\n                        border: `1px solid ${globalStyles.currentTheme.border}`\n                      }}>\n                        <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📄</div>\n                        <h3 style={{\n                          margin: '0 0 0.5rem 0',\n                          color: globalStyles.currentTheme.text\n                        }}>\n                          No resources uploaded yet\n                        </h3>\n                        <p style={{\n                          margin: 0,\n                          color: globalStyles.currentTheme.textLight\n                        }}>\n                          Upload your study materials, notes, and documents to get started\n                        </p>\n                      </div>\n                    ) : (\n                      <div style={{\n                        display: 'grid',\n                        gap: '1rem'\n                      }}>\n                        {userResources.map((file, idx) => {\n                          const { data: urlData } = supabase.storage.from('resources').getPublicUrl(`${user.id}/${file.name}`);\n                          return (\n                            <div key={idx} style={{\n                              display: 'flex',\n                              justifyContent: 'space-between',\n                              alignItems: 'center',\n                              padding: '1rem',\n                              background: globalStyles.currentTheme.secondary,\n                              borderRadius: '12px',\n                              border: `1px solid ${globalStyles.currentTheme.border}`\n                            }}>\n                              <div style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '1rem'\n                              }}>\n                                <div style={{\n                                  width: '40px',\n                                  height: '40px',\n                                  borderRadius: '8px',\n                                  background: globalStyles.currentTheme.primary,\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  justifyContent: 'center',\n                                  color: 'white',\n                                  fontSize: '1.2rem'\n                                }}>\n                                  📄\n                                </div>\n                                <div>\n                                  <div style={{\n                                    fontWeight: 600,\n                                    color: globalStyles.currentTheme.text,\n                                    marginBottom: '0.25rem'\n                                  }}>\n                                    {file.name}\n                                  </div>\n                                  <div style={{\n                                    fontSize: '0.8rem',\n                                    color: globalStyles.currentTheme.textLight\n                                  }}>\n                                    Uploaded resource\n                                  </div>\n                                </div>\n                              </div>\n                              <a\n                                href={urlData.publicUrl}\n                                target=\"_blank\"\n                                rel=\"noopener noreferrer\"\n                                style={{\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: '0.5rem',\n                                  padding: '0.5rem 1rem',\n                                  background: globalStyles.currentTheme.primary,\n                                  color: 'white',\n                                  textDecoration: 'none',\n                                  borderRadius: '8px',\n                                  fontSize: '0.9rem',\n                                  fontWeight: 500,\n                                  transition: 'all 0.3s ease'\n                                }}\n                              >\n                                <FiExternalLink size={16} />\n                                Open\n                              </a>\n                            </div>\n                          );\n                        })}\n                      </div>\n                    )}\n                  </div>\n                </div>\n              )}\n\n              {/* Academics Tab Content */}\n              {resourcesTab === 'academics' && (\n                <div style={{\n                  display: 'grid',\n                  gap: '2rem'\n                }}>\n                  {/* Academic Subjects */}\n                  <div style={{\n                    background: globalStyles.currentTheme.surface,\n                    borderRadius: '16px',\n                    padding: '2rem',\n                    border: `1px solid ${globalStyles.currentTheme.border}`,\n                    boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n                  }}>\n                    <h2 style={{\n                      margin: '0 0 1.5rem 0',\n                      fontSize: '1.5rem',\n                      fontWeight: 600,\n                      color: globalStyles.currentTheme.text,\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem'\n                    }}>\n                      🎓 Academic Study Materials\n                    </h2>\n\n                    <div style={{\n                      display: 'grid',\n                      gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n                      gap: '1.5rem'\n                    }}>\n                      {[\n                        {\n                          subject: 'Mathematics',\n                          icon: '📐',\n                          color: '#DC2626',\n                          topics: ['Calculus', 'Linear Algebra', 'Statistics', 'Discrete Math'],\n                          resources: 15\n                        },\n                        {\n                          subject: 'Computer Science',\n                          icon: '💻',\n                          color: '#7C3AED',\n                          topics: ['Data Structures', 'Algorithms', 'Database Systems', 'Software Engineering'],\n                          resources: 23\n                        },\n                        {\n                          subject: 'Physics',\n                          icon: '⚛️',\n                          color: '#059669',\n                          topics: ['Mechanics', 'Thermodynamics', 'Electromagnetism', 'Quantum Physics'],\n                          resources: 18\n                        },\n                        {\n                          subject: 'Chemistry',\n                          icon: '🧪',\n                          color: '#EA580C',\n                          topics: ['Organic Chemistry', 'Inorganic Chemistry', 'Physical Chemistry', 'Biochemistry'],\n                          resources: 12\n                        }\n                      ].map((subject, index) => (\n                        <div key={index} style={{\n                          background: globalStyles.currentTheme.secondary,\n                          borderRadius: '12px',\n                          padding: '1.5rem',\n                          border: `1px solid ${globalStyles.currentTheme.border}`,\n                          cursor: 'pointer',\n                          transition: 'all 0.3s ease'\n                        }}\n                        onMouseEnter={(e) => {\n                          e.currentTarget.style.transform = 'translateY(-4px)';\n                          e.currentTarget.style.boxShadow = `0 8px 25px ${globalStyles.currentTheme.shadow}`;\n                        }}\n                        onMouseLeave={(e) => {\n                          e.currentTarget.style.transform = 'translateY(0)';\n                          e.currentTarget.style.boxShadow = 'none';\n                        }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '1rem',\n                            marginBottom: '1rem'\n                          }}>\n                            <div style={{\n                              width: '50px',\n                              height: '50px',\n                              borderRadius: '12px',\n                              background: subject.color,\n                              display: 'flex',\n                              alignItems: 'center',\n                              justifyContent: 'center',\n                              fontSize: '1.5rem'\n                            }}>\n                              {subject.icon}\n                            </div>\n                            <div>\n                              <h3 style={{\n                                margin: 0,\n                                fontSize: '1.2rem',\n                                fontWeight: 600,\n                                color: globalStyles.currentTheme.text\n                              }}>\n                                {subject.subject}\n                              </h3>\n                              <p style={{\n                                margin: 0,\n                                fontSize: '0.9rem',\n                                color: globalStyles.currentTheme.textLight\n                              }}>\n                                {subject.resources} resources available\n                              </p>\n                            </div>\n                          </div>\n\n                          <div style={{\n                            display: 'flex',\n                            flexWrap: 'wrap',\n                            gap: '0.5rem',\n                            marginBottom: '1rem'\n                          }}>\n                            {subject.topics.map((topic, topicIndex) => (\n                              <span key={topicIndex} style={{\n                                padding: '0.25rem 0.5rem',\n                                background: subject.color + '15',\n                                color: subject.color,\n                                borderRadius: '6px',\n                                fontSize: '0.8rem',\n                                fontWeight: 500\n                              }}>\n                                {topic}\n                              </span>\n                            ))}\n                          </div>\n\n                          <button style={{\n                            width: '100%',\n                            padding: '0.75rem',\n                            background: subject.color,\n                            color: 'white',\n                            border: 'none',\n                            borderRadius: '8px',\n                            fontSize: '0.9rem',\n                            fontWeight: 500,\n                            cursor: 'pointer',\n                            transition: 'all 0.3s ease'\n                          }}>\n                            Browse Materials\n                          </button>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n\n                  {/* Exam Preparation */}\n                  <div style={{\n                    background: globalStyles.currentTheme.surface,\n                    borderRadius: '16px',\n                    padding: '2rem',\n                    border: `1px solid ${globalStyles.currentTheme.border}`,\n                    boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n                  }}>\n                    <h2 style={{\n                      margin: '0 0 1.5rem 0',\n                      fontSize: '1.5rem',\n                      fontWeight: 600,\n                      color: globalStyles.currentTheme.text,\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem'\n                    }}>\n                      📝 Exam Preparation Resources\n                    </h2>\n\n                    <div style={{\n                      display: 'grid',\n                      gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                      gap: '1rem'\n                    }}>\n                      {[\n                        { name: 'Previous Year Papers', icon: '📄', count: '50+ papers' },\n                        { name: 'Sample Questions', icon: '❓', count: '500+ questions' },\n                        { name: 'Study Guides', icon: '📖', count: '25+ guides' },\n                        { name: 'Reference Books', icon: '📚', count: '100+ books' },\n                        { name: 'Video Lectures', icon: '🎥', count: '200+ videos' },\n                        { name: 'Practice Tests', icon: '✅', count: '30+ tests' }\n                      ].map((resource, index) => (\n                        <div key={index} style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1rem',\n                          padding: '1rem',\n                          background: globalStyles.currentTheme.secondary,\n                          borderRadius: '12px',\n                          border: `1px solid ${globalStyles.currentTheme.border}`,\n                          cursor: 'pointer',\n                          transition: 'all 0.3s ease'\n                        }}\n                        onMouseEnter={(e) => {\n                          e.currentTarget.style.background = globalStyles.currentTheme.primary + '10';\n                          e.currentTarget.style.borderColor = globalStyles.currentTheme.primary;\n                        }}\n                        onMouseLeave={(e) => {\n                          e.currentTarget.style.background = globalStyles.currentTheme.secondary;\n                          e.currentTarget.style.borderColor = globalStyles.currentTheme.border;\n                        }}>\n                          <div style={{\n                            width: '40px',\n                            height: '40px',\n                            borderRadius: '8px',\n                            background: globalStyles.currentTheme.primary,\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            fontSize: '1.2rem'\n                          }}>\n                            {resource.icon}\n                          </div>\n                          <div>\n                            <div style={{\n                              fontWeight: 600,\n                              color: globalStyles.currentTheme.text,\n                              marginBottom: '0.25rem'\n                            }}>\n                              {resource.name}\n                            </div>\n                            <div style={{\n                              fontSize: '0.8rem',\n                              color: globalStyles.currentTheme.textLight\n                            }}>\n                              {resource.count}\n                            </div>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {/* Study Tools Tab Content */}\n              {resourcesTab === 'tools' && (\n                <div style={{\n                  background: globalStyles.currentTheme.surface,\n                  borderRadius: '16px',\n                  padding: '2rem',\n                  border: `1px solid ${globalStyles.currentTheme.border}`,\n                  boxShadow: `0 4px 12px ${globalStyles.currentTheme.shadow}`\n                }}>\n                  <h2 style={{\n                    margin: '0 0 1.5rem 0',\n                    fontSize: '1.5rem',\n                    fontWeight: 600,\n                    color: globalStyles.currentTheme.text,\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem'\n                  }}>\n                    🛠️ Study Tools & Utilities\n                  </h2>\n\n                  <div style={{\n                    display: 'grid',\n                    gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',\n                    gap: '1.5rem'\n                  }}>\n                    {[\n                      {\n                        name: 'Scientific Calculator',\n                        icon: '🧮',\n                        description: 'Advanced calculator for complex calculations',\n                        color: '#DC2626'\n                      },\n                      {\n                        name: 'Formula Reference',\n                        icon: '📐',\n                        description: 'Quick access to mathematical and scientific formulas',\n                        color: '#7C3AED'\n                      },\n                      {\n                        name: 'Unit Converter',\n                        icon: '⚖️',\n                        description: 'Convert between different units of measurement',\n                        color: '#059669'\n                      },\n                      {\n                        name: 'Study Timer',\n                        icon: '⏰',\n                        description: 'Pomodoro timer for effective study sessions',\n                        color: '#EA580C'\n                      },\n                      {\n                        name: 'Note Taking',\n                        icon: '📝',\n                        description: 'Digital notepad for quick notes and ideas',\n                        color: '#0284C7'\n                      },\n                      {\n                        name: 'Progress Tracker',\n                        icon: '📊',\n                        description: 'Track your study progress and goals',\n                        color: '#7C2D12'\n                      }\n                    ].map((tool, index) => (\n                      <div key={index} style={{\n                        background: globalStyles.currentTheme.secondary,\n                        borderRadius: '12px',\n                        padding: '1.5rem',\n                        border: `1px solid ${globalStyles.currentTheme.border}`,\n                        cursor: 'pointer',\n                        transition: 'all 0.3s ease'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-4px)';\n                        e.currentTarget.style.boxShadow = `0 8px 25px ${globalStyles.currentTheme.shadow}`;\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = 'none';\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1rem',\n                          marginBottom: '1rem'\n                        }}>\n                          <div style={{\n                            width: '50px',\n                            height: '50px',\n                            borderRadius: '12px',\n                            background: tool.color,\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            fontSize: '1.5rem'\n                          }}>\n                            {tool.icon}\n                          </div>\n                          <div>\n                            <h3 style={{\n                              margin: 0,\n                              fontSize: '1.1rem',\n                              fontWeight: 600,\n                              color: globalStyles.currentTheme.text\n                            }}>\n                              {tool.name}\n                            </h3>\n                          </div>\n                        </div>\n\n                        <p style={{\n                          margin: '0 0 1rem 0',\n                          fontSize: '0.9rem',\n                          color: globalStyles.currentTheme.textLight,\n                          lineHeight: 1.4\n                        }}>\n                          {tool.description}\n                        </p>\n\n                        <button style={{\n                          width: '100%',\n                          padding: '0.75rem',\n                          background: tool.color,\n                          color: 'white',\n                          border: 'none',\n                          borderRadius: '8px',\n                          fontSize: '0.9rem',\n                          fontWeight: 500,\n                          cursor: 'pointer',\n                          transition: 'all 0.3s ease'\n                        }}>\n                          Launch Tool\n                        </button>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n        {activeTab === \"academics\" && <Exams />}\n        {activeTab === \"faq\" && <Faq />}\n        {activeTab === \"admin\" && user?.email === ADMIN_EMAIL && (\n          <div style={{ padding: '24px' }}>\n            <div style={getStyle('card')}>\n              <h2 style={{\n                marginTop: 0,\n                color: '#333'\n              }}>Admin Panel</h2>\n              <div style={{\n                display: 'flex',\n                gap: '16px',\n                marginBottom: '24px'\n              }}>\n                <button\n                  style={{\n                    ...getStyle('buttonPrimary'),\n                    background: adminTab === 'users' ?\n                      globalStyles.currentTheme.primary : 'transparent',\n                    color: adminTab === 'users' ?\n                      'white' : '#333',\n                    border: '1px solid #ddd'\n                  }}\n                  onClick={() => setAdminTab('users')}\n                >\n                  Users\n                </button>\n                <button\n                  style={{\n                    ...getStyle('buttonPrimary'),\n                    background: adminTab === 'resources' ?\n                      globalStyles.currentTheme.primary : 'transparent',\n                    color: adminTab === 'resources' ?\n                      'white' : '#333',\n                    border: '1px solid #ddd'\n                  }}\n                  onClick={() => setAdminTab('resources')}\n                >\n                  Resources\n                </button>\n              </div>\n\n              {adminTab === 'users' && (\n                <div>\n                  <h3 style={{\n                    marginBottom: '16px',\n                    color: '#333'\n                  }}>All Users</h3>\n                  <div style={{\n                    backgroundColor: '#f5f5f5',\n                    border: '1px solid #e0e0e0',\n                    borderRadius: '8px',\n                    padding: '16px'\n                  }}>\n                    {allUsers.map((user, idx) => (\n                      <div key={idx} style={{\n                        padding: '12px',\n                        borderBottom: '1px solid #eee',\n                        color: '#333'\n                      }}>\n                        {user.email}\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {adminTab === 'resources' && (\n                <div>\n                  <h3 style={{\n                    marginBottom: '16px',\n                    color: '#333'\n                  }}>All Resources</h3>\n                  <p style={{\n                    opacity: 0.7,\n                    color: '#666'\n                  }}>Resource management coming soon</p>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </main>\n\n      {/* Notification */}\n      {notification && (\n        <div style={{\n          ...getStyle('notification'),\n          backgroundColor: notification.type === 'error' ? '#f44336' :\n                         notification.type === 'success' ? '#4caf50' : '#2196f3',\n          color: 'white',\n          border: 'none'\n        }}>\n          {notification.msg}\n        </div>\n      )}\n\n      {/* Enhanced Global styles */}\n      <style>{`\n        @keyframes fadeIn {\n          from { opacity: 0; transform: translateY(10px); }\n          to { opacity: 1; transform: translateY(0); }\n        }\n        @keyframes slideIn {\n          from { transform: translateX(100%); }\n          to { transform: translateX(0); }\n        }\n        @keyframes pulse {\n          0%, 100% { opacity: 1; }\n          50% { opacity: 0.5; }\n        }\n        @keyframes bounce {\n          0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }\n          40%, 43% { transform: translate3d(0,-8px,0); }\n          70% { transform: translate3d(0,-4px,0); }\n          90% { transform: translate3d(0,-2px,0); }\n        }\n        @keyframes glow {\n          0%, 100% { box-shadow: 0 0 5px rgba(99, 102, 241, 0.3); }\n          50% { box-shadow: 0 0 20px rgba(99, 102, 241, 0.6); }\n        }\n        @keyframes shimmer {\n          0% { background-position: -200px 0; }\n          100% { background-position: calc(200px + 100%) 0; }\n        }\n\n        /* Enhanced Tab Navigation Styles */\n        .tab-navigation-container {\n          position: relative;\n          overflow: hidden;\n        }\n\n        .tab-button {\n          position: relative;\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n          will-change: transform, color;\n        }\n\n        .tab-button:hover:not(.active) {\n          transform: translateY(-1px);\n        }\n\n        .tab-button:active {\n          transform: translateY(0);\n        }\n\n        .tab-button.active {\n          color: white;\n        }\n\n        .sliding-indicator {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n          will-change: left, width;\n        }\n\n        /* Responsive Tab Styles */\n        @media (max-width: 768px) {\n          .tab-button {\n            font-size: 0.9rem;\n            padding: 0.75rem 0.75rem;\n          }\n        }\n\n        @media (max-width: 640px) {\n          .tab-button {\n            padding: 0.75rem 0.5rem;\n          }\n        }\n\n        @media (max-width: 480px) {\n          .tab-button {\n            padding: 0.75rem 0.25rem;\n            min-width: 44px;\n          }\n        }\n\n        /* Focus styles for accessibility */\n        .tab-button:focus-visible {\n          outline: 2px solid var(--primary-color);\n          outline-offset: 2px;\n          border-radius: 8px;\n        }\n\n        /* Touch device optimizations */\n        @media (hover: none) and (pointer: coarse) {\n          .tab-button:hover {\n            transform: none;\n          }\n\n          .tab-button:active {\n            transform: scale(0.98);\n          }\n        }\n\n        /* Enhanced hover effects */\n        .company-card:hover {\n          animation: bounce 0.6s ease;\n        }\n\n        .favorite-btn:hover {\n          animation: pulse 0.5s ease;\n        }\n\n        /* Smooth transitions for all interactive elements */\n        button, input, select {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        button:hover {\n          transform: translateY(-1px);\n        }\n\n        * {\n          box-sizing: border-box;\n        }\n        body {\n          margin: 0;\n          font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n        }\n      `}</style>\n    </div>\n  );\n};\nexport default EduAIChatBot;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,GAAG,QAAQ,oBAAoB;AAChD,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,IAAI,EAAEC,EAAE,QAAQ,kBAAkB;AAC3C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,kBAAkB,QAAQ,eAAe;AAClD,OAAOC,YAAY,MAAM,aAAa;AACtC,SACEC,MAAM,EAAEC,GAAG,EAAEC,aAAa,EAAEC,cAAc,EAAEC,UAAU,EACtDC,MAAM,EAAEC,YAAY,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EACvDC,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,QAAQ,EACrCC,WAAW,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,cAAc,EACjEC,OAAO,EAAEC,OAAO,EAAEC,WAAW,QACxB,gBAAgB;AACvB,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,GAAG,QAAQ,iBAAiB;AACrC,SAASC,KAAK,EAAEC,UAAU,EAAEC,aAAa,EAAEC,WAAW,EAAEC,OAAO,EAAEC,MAAM,QAAQ,UAAU;AACzF,OAAOC,aAAa,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3CV,KAAK,CAACW,QAAQ,CAACV,UAAU,EAAEC,aAAa,EAAEC,WAAW,EAAEC,OAAO,EAAEC,MAAM,CAAC;;AAEvE;AACA,MAAMO,mBAAmB,GAAGxC,YAAY,CAACyC,GAAG,CAACC,IAAI,IAAI;EACnD,MAAMC,OAAO,GAAG;IACd,QAAQ,eAAEP,OAAA,CAAC7B,UAAU;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxB,KAAK,eAAEX,OAAA,CAAC5B,MAAM;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjB,QAAQ,eAAEX,OAAA,CAAChB,QAAQ;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtB,WAAW,eAAEX,OAAA,CAAClB,WAAW;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5B,SAAS,eAAEX,OAAA,CAACf,aAAa;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5B,UAAU,eAAEX,OAAA,CAACjB,WAAW;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3B,WAAW,eAAEX,OAAA,CAACzB,MAAM;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvB,KAAK,eAAEX,OAAA,CAAC3B,YAAY;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvB,OAAO,eAAEX,OAAA,CAACvB,QAAQ;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACtB,CAAC;EAED,OAAO;IACL,GAAGL,IAAI;IACPM,IAAI,EAAEL,OAAO,CAACD,IAAI,CAACO,GAAG,CAAC,IAAIN,OAAO,CAACD,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,iBAAIf,OAAA,CAAC1B,OAAO;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC5E,CAAC;AACH,CAAC,CAAC;AAEF,MAAMK,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB;EACA,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACmE,QAAQ,EAAEC,WAAW,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqE,MAAM,EAAEC,SAAS,CAAC,GAAGtE,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACuE,OAAO,EAAEC,UAAU,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyE,SAAS,EAAEC,YAAY,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC2E,SAAS,EAAEC,YAAY,CAAC,GAAG5E,QAAQ,CAAC,WAAW,CAAC;EACvD,MAAM,CAAC6E,UAAU,EAAEC,aAAa,CAAC,GAAG9E,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+E,WAAW,EAAEC,cAAc,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiF,aAAa,EAAEC,gBAAgB,CAAC,GAAGlF,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAACmF,IAAI,EAAEC,OAAO,CAAC,GAAGpF,QAAQ,CAAC,IAAI,CAAC;EAEtC,MAAM,CAACqF,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGtF,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACuF,SAAS,EAAEC,YAAY,CAAC,GAAGxF,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACyF,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG1F,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAAC2F,aAAa,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM4F,WAAW,GAAG,mBAAmB;EACvC,MAAM,CAACC,QAAQ,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;EAC/B,MAAM,CAAC8F,QAAQ,EAAEC,WAAW,CAAC,GAAG/F,QAAQ,CAAC,OAAO,CAAC;EACjD,MAAM,CAACgG,YAAY,EAAEC,eAAe,CAAC,GAAGjG,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACkG,WAAW,EAAEC,cAAc,CAAC,GAAGnG,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoG,YAAY,EAAEC,eAAe,CAAC,GAAGrG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACsG,WAAW,EAAEC,cAAc,CAAC,GAAGvG,QAAQ,CAAC,OAAOwG,MAAM,KAAK,WAAW,GAAGA,MAAM,CAACC,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC;EAC1G,MAAMC,UAAU,GAAGxG,MAAM,CAAC,IAAI,CAAC;;EAE/B;EACAD,SAAS,CAAC,MAAM;IACd,MAAM0G,YAAY,GAAGA,CAAA,KAAM;MACzBJ,cAAc,CAACC,MAAM,CAACC,UAAU,CAAC;IACnC,CAAC;IAEDD,MAAM,CAACI,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMH,MAAM,CAACK,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM,CAACG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/G,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgH,MAAM,EAAEC,SAAS,CAAC,GAAGjH,QAAQ,CAAC,MAAM,CAAC;EAC5C,MAAM,CAACkH,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnH,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACoH,eAAe,EAAEC,kBAAkB,CAAC,GAAGrH,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACsH,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvH,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAMwH,OAAO,GAAG,yCAAyC;EACzD,MAAMC,YAAY,GAAG,0CAA0C;EAC/D,MAAMC,iBAAiB,GAAG,kNAAkN;EAC5O,MAAMC,QAAQ,GAAGtF,YAAY,CAACoF,YAAY,EAAEC,iBAAiB,CAAC;;EAE9D;EACA,MAAME,iBAAiB,GAAG;IACxB,OAAO,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC;IAC7D,UAAU,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC;IACpF,UAAU,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC;IACrF,SAAS,EAAE,CAAC,eAAe,EAAE,UAAU,EAAE,gBAAgB,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,CAAC;IAC/F,YAAY,EAAE,CAAC,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,KAAK,CAAC;IACzE,YAAY,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,CAAC;IAC7E,QAAQ,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC;IACrE,QAAQ,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ;EACtF,CAAC;;EAED;EACA,MAAMC,SAAS,GAAG,CACf,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EACxE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EACnE,QAAQ,EAAE,kBAAkB,EAAE,SAAS,EAAE,OAAO,EAAE,iBAAiB,EACnE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE,UAAU,EAChE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAC9D,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM,EAAE,aAAa,EAAE,KAAK,EAChE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,QAAQ,EAClE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAC5D,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAC9D,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,aAAa,EAAE,QAAQ,EAC3D,SAAS,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EACjE,WAAW,EAAE,WAAW,EAAE,eAAe,EAAE,SAAS,EAAE,SAAS,EAC/D,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAC7D,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,SAAS,EAC1D,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,EAChE,OAAO,EAAE,eAAe,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EACnE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EACzD,sBAAsB,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EACnE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,aAAa,EACtD,iBAAiB,EAAE,cAAc,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAC3D,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAC/D,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EACnE,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EACzD,gBAAgB,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAC5D,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAChE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAClE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,uBAAuB,EAAE,oBAAoB,EACxE,QAAQ,EAAE,OAAO,EAAE,oBAAoB,EAAE,SAAS,EAAE,WAAW,EAC/D,aAAa,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU,EAAE,OAAO,EAC7D,SAAS,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAC9D,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,KAAK,EAAE,YAAY,EAChE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,EACnE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,qBAAqB,EAAE,QAAQ,EAChE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,aAAa,EAC5D,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAChE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EACxD,eAAe,EAAE,QAAQ,EAAE,SAAS,EAAE,iBAAiB,EAAE,MAAM,EAC/D,IAAI,EAAE,QAAQ,EAAE,cAAc,EAAE,aAAa,EAAE,SAAS,EACxD,aAAa,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,QAAQ,EAChE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAChE,QAAQ,EAAE,SAAS,EAAE,UAAU,CAChC;;EAED;EACA,MAAMC,WAAW,GAAG,CAClB;IACEjE,KAAK,EAAE,gBAAgB;IACvBkE,WAAW,EAAE,+DAA+D;IAC5EC,IAAI,EAAE;EACR,CAAC,EACD;IACEnE,KAAK,EAAE,oBAAoB;IAC3BkE,WAAW,EAAE,uCAAuC;IACpDC,IAAI,EAAE;EACR,CAAC,EACD;IACEnE,KAAK,EAAE,oBAAoB;IAC3BkE,WAAW,EAAE,mCAAmC;IAChDC,IAAI,EAAE;EACR,CAAC,EACD;IACEnE,KAAK,EAAE,0CAA0C;IACjDkE,WAAW,EAAE,+CAA+C;IAC5DC,IAAI,EAAE;EACR,CAAC,EACD;IACEnE,KAAK,EAAE,qBAAqB;IAC5BkE,WAAW,EAAE,kDAAkD;IAC/DC,IAAI,EAAE;EACR,CAAC,EACD;IACEnE,KAAK,EAAE,UAAU;IACjBkE,WAAW,EAAE,qCAAqC;IAClDC,IAAI,EAAE;EACR,CAAC,EACD;IACEnE,KAAK,EAAE,2CAA2C;IAClDkE,WAAW,EAAE,kCAAkC;IAC/CC,IAAI,EAAE;EACR,CAAC,EACA;IACCnE,KAAK,EAAE,sBAAsB;IAC7BkE,WAAW,EAAE,gCAAgC;IAC7CC,IAAI,EAAE;EAER,CAAC,CACF;;EAED;EACA,MAAMC,MAAM,GAAG;IACb,GAAGpH,YAAY;IACfqH,YAAY,EAAE;MACZ,GAAGrH,YAAY,CAACqH,YAAY;MAC5BC,eAAe,EAAEtH,YAAY,CAACuH,YAAY,CAACC,UAAU;MACrDC,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG;IACnC,CAAC;IACDC,MAAM,EAAE;MACN,GAAG3H,YAAY,CAAC4H,WAAW;MAC3BC,YAAY,EAAE,aAAa7H,YAAY,CAACuH,YAAY,CAACO,MAAM;IAC7D,CAAC;IACDC,OAAO,EAAE;MACP,GAAG/H,YAAY,CAACgI,YAAY;MAC5BV,eAAe,EAAEtH,YAAY,CAACuH,YAAY,CAACU,OAAO;MAClDC,WAAW,EAAE,aAAalI,YAAY,CAACuH,YAAY,CAACO,MAAM,EAAE;MAC5DK,SAAS,EAAEjE,WAAW,GAAG,eAAe,GAAG;IAC7C,CAAC;IACDkE,WAAW,EAAE;MACX,GAAGpI,YAAY,CAACqI,cAAc;MAC9BZ,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG,IAAI;MACrCF,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACU,OAAO;MAC7CH,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACO,MAAM,EAAE;MACvDQ,UAAU,EAAE,eAAe;MAC3B,SAAS,EAAE;QACTd,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACgB,OAAO;QAC7Cd,KAAK,EAAE;MACT;IACF,CAAC;IACDe,iBAAiB,EAAE;MACjB,GAAGxI,YAAY,CAACyI,oBAAoB;MACpChB,KAAK,EAAE,OAAO;MACdD,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACgB,OAAO;MAC7CT,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACgB,OAAO;IACxD,CAAC;IACDG,WAAW,EAAE;MACX,GAAG1I,YAAY,CAAC2I,cAAc;MAC9BC,UAAU,EAAE1E,WAAW,GAAG,OAAO,GAAG,GAAG;MACvCoD,eAAe,EAAEtH,YAAY,CAACuH,YAAY,CAACC,UAAU;MACrDC,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG,IAAI;MACrCmB,SAAS,EAAE;IACb,CAAC;IACDC,IAAI,EAAE;MACJ,GAAG9I,YAAY,CAAC+I,OAAO;MACvBzB,eAAe,EAAEtH,YAAY,CAACuH,YAAY,CAACU,OAAO;MAClDR,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG,IAAI;MACrCI,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACO,MAAM,EAAE;MACvDkB,SAAS,EAAE,aAAahJ,YAAY,CAACuH,YAAY,CAAC0B,MAAM;IAC1D,CAAC;IACDC,aAAa,EAAE;MACb,GAAGlJ,YAAY,CAACkJ;IAClB,CAAC;IACDC,UAAU,EAAE;MACV,GAAGnJ,YAAY,CAACmJ,UAAU;MAC1B7B,eAAe,EAAE,MAAM;MACvBG,KAAK,EAAE,MAAM;MACbK,MAAM,EAAE,gBAAgB;MACxB,SAAS,EAAE;QACTsB,WAAW,EAAEpJ,YAAY,CAACuH,YAAY,CAACgB,OAAO;QAC9Cc,OAAO,EAAE,MAAM;QACfL,SAAS,EAAE,aAAahJ,YAAY,CAACuH,YAAY,CAACgB,OAAO;MAC3D;IACF,CAAC;IACDe,cAAc,EAAE;MACd,GAAGtJ,YAAY,CAACsJ,cAAc;MAC9BhC,eAAe,EAAEtH,YAAY,CAACuH,YAAY,CAACgB,OAAO;MAClDd,KAAK,EAAE;IACT,CAAC;IACD8B,aAAa,EAAE;MACb,GAAGvJ,YAAY,CAACuJ,aAAa;MAC7BjC,eAAe,EAAEtH,YAAY,CAACuH,YAAY,CAACiC,SAAS;MACpD/B,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG,IAAI;MACrCI,MAAM,EAAE;IACV,CAAC;IACD2B,WAAW,EAAE;MACX,GAAGzJ,YAAY,CAAC0J;IAClB,CAAC;IACDC,QAAQ,EAAE;MACR,GAAG3J,YAAY,CAAC4J,WAAW;MAC3BtC,eAAe,EAAEtH,YAAY,CAACuH,YAAY,CAACU;IAC7C,CAAC;IACD9C,YAAY,EAAE;MACZ,GAAGnF,YAAY,CAACmF;IAClB;EACF,CAAC;;EAED;EACA,MAAM0E,QAAQ,GAAGA,CAACC,SAAS,EAAEC,KAAK,GAAG,KAAK,KAAK;IAC7C,MAAMC,SAAS,GAAG5C,MAAM,CAAC0C,SAAS,CAAC;IACnC,IAAI,OAAOE,SAAS,KAAK,UAAU,EAAE,OAAOA,SAAS,CAAC,CAAC;IACvD,IAAID,KAAK,IAAIC,SAAS,CAAC,SAAS,CAAC,EAAE;MACjC,OAAO;QAAE,GAAGA,SAAS;QAAE,GAAGA,SAAS,CAAC,SAAS;MAAE,CAAC;IAClD;IACA,OAAOA,SAAS;EAClB,CAAC;;EAED;EACA5K,SAAS,CAAC,MAAM;IACd,MAAM6K,WAAW,GAAGlK,kBAAkB,CAACJ,IAAI,EAAG2E,IAAI,IAAK;MACrD,IAAIA,IAAI,EAAE;QACRb,SAAS,CAACa,IAAI,CAAC4F,GAAG,CAAC;MACrB,CAAC,MAAM;QACLC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxCzG,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC;IACF,OAAO,MAAMsG,WAAW,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;EAEN7K,SAAS,CAAC,MAAM;IACd,IAAIoE,MAAM,EAAE;MACV,MAAM6G,gBAAgB,GAAG,MAAAA,CAAA,KAAY;QACnC,MAAMC,OAAO,GAAG/K,GAAG,CAACK,EAAE,EAAE,OAAO,EAAE4D,MAAM,CAAC;QACxC,MAAM+G,OAAO,GAAG,MAAMjL,MAAM,CAACgL,OAAO,CAAC;QAErC,IAAIC,OAAO,CAACC,MAAM,CAAC,CAAC,EAAE;UACpB,MAAMC,QAAQ,GAAGF,OAAO,CAACG,IAAI,CAAC,CAAC;UAC/B;UACAP,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEK,QAAQ,CAACE,EAAE,CAAC;QAC/C,CAAC,MAAM;UACLR,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;QAC9B;QACAzG,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC;MACD0G,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAAC7G,MAAM,CAAC,CAAC;;EAEZ;EACApE,SAAS,CAAC,MAAM;IACdwL,KAAK,CAAC,oBAAoB,CAAC,CACxBC,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACpD,IAAI,CAAC,CAAC,CAAC,CACzBmD,IAAI,CAAEH,IAAI,IAAK7G,YAAY,CAAC6G,IAAI,CAAC,CAAC,CAClCK,KAAK,CAAEC,GAAG,IAAKb,OAAO,CAACc,KAAK,CAAC,+BAA+B,EAAED,GAAG,CAAC,CAAC;EACxE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA5L,SAAS,CAAC,MAAM;IACd0H,QAAQ,CAACnH,IAAI,CAACuL,UAAU,CAAC,CAAC,CAACL,IAAI,CAAC,CAAC;MAAEH,IAAI,EAAE;QAAES;MAAQ;IAAE,CAAC,KAAK;MACzD5G,OAAO,CAAC,CAAA4G,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE7G,IAAI,KAAI,IAAI,CAAC;IAChC,CAAC,CAAC;IACF,MAAM;MAAEoG,IAAI,EAAEU;IAAS,CAAC,GAAGtE,QAAQ,CAACnH,IAAI,CAAC0L,iBAAiB,CAAC,CAACC,MAAM,EAAEH,OAAO,KAAK;MAC9E5G,OAAO,CAAC,CAAA4G,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE7G,IAAI,KAAI,IAAI,CAAC;IAChC,CAAC,CAAC;IACF,OAAO,MAAM;MACX8G,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEG,YAAY,CAACtB,WAAW,CAAC,CAAC;IACtC,CAAC;EACH,CAAC,EAAE,CAACnD,QAAQ,CAACnH,IAAI,CAAC,CAAC;;EAEnB;EACA,MAAM6L,kBAAkB,GAAG,MAAOC,CAAC,IAAK;IACtC,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACF,IAAI,IAAI,CAACpH,IAAI,EAAE;IACpBG,sBAAsB,CAAC,IAAI,CAAC;IAC5B,MAAMoH,QAAQ,GAAG,GAAGvH,IAAI,CAACwH,EAAE,IAAIJ,IAAI,CAACK,IAAI,EAAE;IAC1C,MAAM;MAAEd;IAAM,CAAC,GAAG,MAAMnE,QAAQ,CAACkF,OAAO,CAACC,IAAI,CAAC,SAAS,CAAC,CAACC,MAAM,CAACL,QAAQ,EAAEH,IAAI,EAAE;MAAES,MAAM,EAAE;IAAK,CAAC,CAAC;IACjG,IAAI,CAAClB,KAAK,EAAE;MACV,MAAM;QAAEP,IAAI,EAAE0B;MAAQ,CAAC,GAAGtF,QAAQ,CAACkF,OAAO,CAACC,IAAI,CAAC,SAAS,CAAC,CAACI,YAAY,CAACR,QAAQ,CAAC;MACjFlH,YAAY,CAACyH,OAAO,CAACE,SAAS,CAAC;MAC/BC,gBAAgB,CAAC,+BAA+B,EAAE,SAAS,CAAC;MAC5DC,WAAW,CAAC,mBAAmB,CAAC;IAClC,CAAC,MAAM;MACLD,gBAAgB,CAAC,uBAAuB,EAAE,OAAO,CAAC;IACpD;IACA9H,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMgI,oBAAoB,GAAG,MAAOhB,CAAC,IAAK;IACxC,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACF,IAAI,IAAI,CAACpH,IAAI,EAAE;IACpBO,wBAAwB,CAAC,IAAI,CAAC;IAC9B,MAAMgH,QAAQ,GAAG,GAAGvH,IAAI,CAACwH,EAAE,IAAIJ,IAAI,CAACK,IAAI,EAAE;IAC1C,MAAM;MAAEd;IAAM,CAAC,GAAG,MAAMnE,QAAQ,CAACkF,OAAO,CAACC,IAAI,CAAC,WAAW,CAAC,CAACC,MAAM,CAACL,QAAQ,EAAEH,IAAI,EAAE;MAAES,MAAM,EAAE;IAAK,CAAC,CAAC;IACnG,IAAI,CAAClB,KAAK,EAAE;MACVsB,gBAAgB,CAAC,oBAAoB,EAAE,SAAS,CAAC;MACjDC,WAAW,CAAC,sBAAsBd,IAAI,CAACK,IAAI,EAAE,CAAC;IAChD,CAAC,MAAM;MACLQ,gBAAgB,CAAC,yBAAyB,EAAE,OAAO,CAAC;IACtD;IACA1H,wBAAwB,CAAC,KAAK,CAAC;EACjC,CAAC;;EAED;EACA,MAAM6H,kBAAkB,GAAIC,OAAO,IAAK;IACtC;IACAnG,kBAAkB,CAACoG,IAAI,IAAI;MACzB,MAAMC,QAAQ,GAAGD,IAAI,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKJ,OAAO,CAAC;MAChD,OAAO,CAACA,OAAO,EAAE,GAAGE,QAAQ,CAAC,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC;IAEFR,WAAW,CAAC,UAAUG,OAAO,gBAAgB,CAAC;IAE9C,IAAIA,OAAO,CAAC1J,WAAW,CAAC,CAAC,KAAK,WAAW,EAAE;MACzC0C,MAAM,CAACsH,QAAQ,CAACC,IAAI,GAAG,uCAAuC;MAC9D;IACF;IACA,MAAMC,gBAAgB,GAAGR,OAAO,CAACS,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;IACpDzH,MAAM,CAACsH,QAAQ,CAACC,IAAI,GAAG,gBAAgBC,gBAAgB,OAAO;EAChE,CAAC;;EAED;EACA,MAAME,cAAc,GAAGA,CAACV,OAAO,EAAElB,CAAC,KAAK;IACrCA,CAAC,CAAC6B,eAAe,CAAC,CAAC,CAAC,CAAC;IACrBhH,oBAAoB,CAACsG,IAAI,IAAI;MAC3B,IAAIA,IAAI,CAACW,QAAQ,CAACZ,OAAO,CAAC,EAAE;QAC1B,OAAOC,IAAI,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKJ,OAAO,CAAC;MACxC,CAAC,MAAM;QACL,OAAO,CAAC,GAAGC,IAAI,EAAED,OAAO,CAAC;MAC3B;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMa,mBAAmB,GAAGA,CAAA,KAAM;IAChC9G,mBAAmB,CAAC,KAAK,CAAC;IAC1B6F,gBAAgB,CAAC,8CAA8C,EAAE,SAAS,CAAC;;IAE3E;IACA,MAAMkB,cAAc,GAAGC,QAAQ,CAACC,aAAa,CAAC,qBAAqB,CAAC;IACpE,MAAMC,eAAe,GAAGF,QAAQ,CAACC,aAAa,CAAC,wBAAwB,CAAC;IAExE,IAAIF,cAAc,EAAE;MAClBA,cAAc,CAACI,KAAK,CAACpG,KAAK,GAAG,MAAM;IACrC;IACA,IAAImG,eAAe,EAAE;MACnBA,eAAe,CAACC,KAAK,CAACpG,KAAK,GAAG,MAAM;IACtC;EACF,CAAC;;EAED;EACA,MAAMqG,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIjB,QAAQ,GAAG7F,SAAS;;IAExB;IACA,IAAIf,gBAAgB,KAAK,KAAK,EAAE;MAC9B,MAAM8H,iBAAiB,GAAGhH,iBAAiB,CAACd,gBAAgB,CAAC,IAAI,EAAE;MACnE4G,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACH,OAAO,IAChCoB,iBAAiB,CAACC,IAAI,CAACC,UAAU,IAC/BtB,OAAO,CAAC1J,WAAW,CAAC,CAAC,CAACsK,QAAQ,CAACU,UAAU,CAAChL,WAAW,CAAC,CAAC,CAAC,IACxDgL,UAAU,CAAChL,WAAW,CAAC,CAAC,CAACsK,QAAQ,CAACZ,OAAO,CAAC1J,WAAW,CAAC,CAAC,CACzD,CACF,CAAC;IACH;;IAEA;IACA4J,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACH,OAAO,IAChCA,OAAO,CAAC1J,WAAW,CAAC,CAAC,CAACsK,QAAQ,CAACvJ,UAAU,CAACf,WAAW,CAAC,CAAC,CACzD,CAAC;;IAED;IACA,IAAIkD,MAAM,KAAK,MAAM,EAAE;MACrB0G,QAAQ,CAACqB,IAAI,CAAC,CAAC;IACjB,CAAC,MAAM,IAAI/H,MAAM,KAAK,WAAW,EAAE;MACjC0G,QAAQ,CAACqB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QACtB,MAAMC,IAAI,GAAGhI,iBAAiB,CAACkH,QAAQ,CAACY,CAAC,CAAC;QAC1C,MAAMG,IAAI,GAAGjI,iBAAiB,CAACkH,QAAQ,CAACa,CAAC,CAAC;QAC1C,IAAIC,IAAI,IAAI,CAACC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC5B,IAAI,CAACD,IAAI,IAAIC,IAAI,EAAE,OAAO,CAAC;QAC3B,OAAOH,CAAC,CAACI,aAAa,CAACH,CAAC,CAAC;MAC3B,CAAC,CAAC;IACJ;IAEA,OAAOvB,QAAQ;EACjB,CAAC;;EAED;EACA,MAAM2B,YAAY,GAAIC,GAAG,IAAK;IAC5B9I,MAAM,CAAC+I,IAAI,CAACD,GAAG,EAAE,QAAQ,CAAC;EAC5B,CAAC;;EAED;EACA,MAAME,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAACvL,KAAK,CAACwL,IAAI,CAAC,CAAC,EAAE;IAEnB,MAAMC,WAAW,GAAG;MAAEC,IAAI,EAAE,MAAM;MAAEC,OAAO,EAAE3L;IAAM,CAAC;IACpDG,WAAW,CAAEqJ,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEiC,WAAW,CAAC,CAAC;IAC7CxL,QAAQ,CAAC,EAAE,CAAC;IACZM,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MAAA,IAAAqL,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACF,MAAMC,MAAM,GAAG,oOAAoOzL,SAAS,iBAAiBR,KAAK,EAAE;MAEpR,MAAM0H,GAAG,GAAG,MAAMjL,KAAK,CAACyP,IAAI,CAC1B,gGAAgG3I,OAAO,EAAE,EACzG;QACE4I,QAAQ,EAAE,CACR;UACEC,KAAK,EAAE,CAAC;YAAE9H,IAAI,EAAE2H;UAAO,CAAC;QAC1B,CAAC;MAEL,CAAC,EACD;QACEI,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CACF,CAAC;MAED,MAAMC,QAAQ,GACZ,EAAAV,oBAAA,GAAAlE,GAAG,CAACJ,IAAI,CAACiF,UAAU,cAAAX,oBAAA,wBAAAC,qBAAA,GAAnBD,oBAAA,CAAsB,CAAC,CAAC,cAAAC,qBAAA,wBAAAC,sBAAA,GAAxBD,qBAAA,CAA0BF,OAAO,cAAAG,sBAAA,wBAAAC,sBAAA,GAAjCD,sBAAA,CAAmCM,KAAK,cAAAL,sBAAA,wBAAAC,sBAAA,GAAxCD,sBAAA,CAA2C,CAAC,CAAC,cAAAC,sBAAA,uBAA7CA,sBAAA,CAA+C1H,IAAI,KACnD,yBAAyB;MAC3B,MAAMkI,UAAU,GAAG;QAAEd,IAAI,EAAE,KAAK;QAAEC,OAAO,EAAEW;MAAS,CAAC;MACrDnM,WAAW,CAAEqJ,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEgD,UAAU,CAAC,CAAC;IAC9C,CAAC,CAAC,OAAO3E,KAAK,EAAE;MACdd,OAAO,CAACc,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzC1H,WAAW,CAAEqJ,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QAAEkC,IAAI,EAAE,KAAK;QAAEC,OAAO,EAAE,WAAW,GAAG9D,KAAK,CAAC4E;MAAQ,CAAC,CACtD,CAAC;IACJ,CAAC,SAAS;MACRlM,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;;EAEA;EACA,MAAMmM,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMhJ,QAAQ,CAACnH,IAAI,CAACoQ,OAAO,CAAC,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMxD,gBAAgB,GAAGA,CAACyD,GAAG,EAAEC,IAAI,GAAG,MAAM,KAAK;IAC/C7K,eAAe,CAAC;MAAE4K,GAAG;MAAEC;IAAK,CAAC,CAAC;IAC9BC,UAAU,CAAC,MAAM9K,eAAe,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;EAC/C,CAAC;;EAED;EACA,MAAMoH,WAAW,GAAIwD,GAAG,IAAK;IAC3B1K,cAAc,CAAC8E,GAAG,IAAI,CACpB;MAAE6F,IAAI,EAAE,UAAU;MAAEE,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAAEL;IAAI,CAAC,EACzD,GAAG5F,GAAG,CAAC4C,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CACpB,CAAC;EACJ,CAAC;;EAED;EACA,MAAMsD,UAAU,GAAIC,IAAI,IAAK;IAC3BlM,gBAAgB,CAACuI,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP,CAAC2D,IAAI,GAAG,CAAC3D,IAAI,CAAC2D,IAAI;IACpB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACAnR,SAAS,CAAC,MAAM;IACd,IAAIyG,UAAU,CAAC2K,OAAO,EAAE3K,UAAU,CAAC2K,OAAO,CAACC,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACnF,CAAC,EAAE,CAACpN,QAAQ,EAAEI,OAAO,CAAC,CAAC;;EAEvB;EACA,MAAMiN,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,IAAI,GAAG,EAAE;IACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAMC,CAAC,GAAG,IAAIV,IAAI,CAAC,CAAC;MACpBU,CAAC,CAACC,OAAO,CAACD,CAAC,CAACE,OAAO,CAAC,CAAC,GAAGH,CAAC,CAAC;MAC1BD,IAAI,CAACK,IAAI,CAACH,CAAC,CAACI,kBAAkB,CAAC,CAAC,CAAC;IACnC;IACA,OAAON,IAAI;EACb,CAAC;EAED,MAAMO,WAAW,GAAGR,YAAY,CAAC,CAAC;EAClC,MAAMS,SAAS,GAAG;IAChBC,MAAM,EAAEF,WAAW;IACnBG,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,kBAAkB;MACzB7G,IAAI,EAAEyG,WAAW,CAAC5O,GAAG,CAACiP,GAAG,IAAInM,WAAW,CAACyH,MAAM,CAACqB,CAAC,IAAIA,CAAC,CAAC8B,IAAI,KAAK,UAAU,IAAI9B,CAAC,CAAC6B,GAAG,CAACyB,UAAU,CAAC,mBAAmB,CAAC,IAAI,IAAIrB,IAAI,CAACjC,CAAC,CAACgC,IAAI,CAAC,CAACe,kBAAkB,CAAC,CAAC,KAAKM,GAAG,CAAC,CAACE,MAAM,CAAC;MAC7KpK,eAAe,EAAE;IACnB,CAAC,EACD;MACEiK,KAAK,EAAE,iBAAiB;MACxB7G,IAAI,EAAEyG,WAAW,CAAC5O,GAAG,CAACiP,GAAG,IAAInM,WAAW,CAACyH,MAAM,CAACqB,CAAC,IAAIA,CAAC,CAAC8B,IAAI,KAAK,UAAU,IAAI9B,CAAC,CAAC6B,GAAG,KAAK,8BAA8B,IAAI,IAAII,IAAI,CAACjC,CAAC,CAACgC,IAAI,CAAC,CAACe,kBAAkB,CAAC,CAAC,KAAKM,GAAG,CAAC,CAACE,MAAM,CAAC;MAChLpK,eAAe,EAAE;IACnB,CAAC;EAEL,CAAC;EAED,MAAMqK,YAAY,GAAG;IACnBC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE;MACPC,MAAM,EAAE;QAAEC,QAAQ,EAAE;MAAM,CAAC;MAC3BC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAK;IAC3B,CAAC;IACDC,MAAM,EAAE;MACNC,CAAC,EAAE;QAAEC,WAAW,EAAE,IAAI;QAAEC,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAE;MAAE;IACjD;EACF,CAAC;EAED,oBACEpQ,OAAA;IAAK2L,KAAK,EAAEhE,QAAQ,CAAC,cAAc,CAAE;IAAA0I,QAAA,gBAEnCrQ,OAAA;MAAK2L,KAAK,EAAEhE,QAAQ,CAAC,QAAQ,CAAE;MAAA0I,QAAA,gBAC7BrQ,OAAA;QACE2L,KAAK,EAAE;UACLrG,UAAU,EAAE,MAAM;UAClBM,MAAM,EAAE,MAAM;UACdL,KAAK,EAAE,OAAO;UAAE;UAChB+K,WAAW,EAAE,MAAM;UACnBC,MAAM,EAAE,SAAS;UACjBC,OAAO,EAAE,KAAK;UACdC,YAAY,EAAE,KAAK;UACnBrK,UAAU,EAAE;QACd,CAAE;QACFsK,OAAO,EAAEA,CAAA,KAAMzO,cAAc,CAAC,CAACD,WAAW,CAAE;QAC5C2O,YAAY,EAAGpH,CAAC,IAAKA,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACrG,UAAU,GAAG,0BAA2B;QAC5EsL,YAAY,EAAGrH,CAAC,IAAKA,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACrG,UAAU,GAAG,MAAO;QAAA+K,QAAA,EAEvDrO,WAAW,gBAAGhC,OAAA,CAAChC,GAAG;UAAC6S,IAAI,EAAE;QAAG;UAAArQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGX,OAAA,CAACjC,MAAM;UAAC8S,IAAI,EAAE;QAAG;UAAArQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eAETX,OAAA;QAAK2L,KAAK,EAAE;UAAEmF,IAAI,EAAE,CAAC;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAX,QAAA,gBAC7DrQ,OAAA;UACEiR,GAAG,EAAEC,OAAO,CAAC,kBAAkB,CAAE;UACjCC,GAAG,EAAC,YAAY;UAChBxF,KAAK,EAAE;YAAEyF,MAAM,EAAE,MAAM;YAAEd,WAAW,EAAE;UAAO;QAAE;UAAA9P,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACFX,OAAA;UAAAqQ,QAAA,gBACErQ,OAAA;YACE,yBAAiB;YACjB2L,KAAK,EAAE;cAAE0F,UAAU,EAAE,GAAG;cAAEC,QAAQ,EAAE,MAAM;cAAE/L,KAAK,EAAE;YAAQ,CAAE;YAAA8K,QAAA,EAC9D;UAED;YAAA7P,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNX,OAAA;YACE,4BAAoB;YACpB2L,KAAK,EAAE;cAAE2F,QAAQ,EAAE,MAAM;cAAEC,OAAO,EAAE,GAAG;cAAEhM,KAAK,EAAE;YAAQ,CAAE;YAAA8K,QAAA,EAC3D;UAED;YAAA7P,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENX,OAAA;QAAK2L,KAAK,EAAE;UAAEoF,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEQ,GAAG,EAAE;QAAO,CAAE;QAAAnB,QAAA,EAChEjO,IAAI,gBACHpC,OAAA,CAAAE,SAAA;UAAAmQ,QAAA,gBACErQ,OAAA;YAAK2L,KAAK,EAAE;cACV8F,KAAK,EAAE,MAAM;cACbL,MAAM,EAAE,MAAM;cACdX,YAAY,EAAE,KAAK;cACnBnL,UAAU,EAAE,0BAA0B;cACtCyL,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBU,cAAc,EAAE,QAAQ;cACxBL,UAAU,EAAE,GAAG;cACfd,MAAM,EAAE,SAAS;cACjBhL,KAAK,EAAE,OAAO;cACdoM,cAAc,EAAE;YAClB,CAAE;YAAAtB,QAAA,EACCjO,IAAI,CAACwP,KAAK,KAAK/O,WAAW,gBAAG7C,OAAA,CAACvB,QAAQ;cAACoS,IAAI,EAAE,EAAG;cAACtL,KAAK,EAAC;YAAS;cAAA/E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAGyB,IAAI,CAACwP,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;UAAC;YAAArR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC,eACNX,OAAA;YACE2L,KAAK,EAAE;cACL,GAAGhE,QAAQ,CAAC,eAAe,CAAC;cAC5BrC,UAAU,EAAE,0BAA0B;cACtCC,KAAK,EAAE,OAAO;cACdK,MAAM,EAAE,oCAAoC;cAC5C+L,cAAc,EAAE;YAClB,CAAE;YACFjB,OAAO,EAAE9C,YAAa;YAAAyC,QAAA,gBAEtBrQ,OAAA,CAACnB,QAAQ;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,WACd;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CAAC,gBAEHX,OAAA;UACE2L,KAAK,EAAE;YACL,GAAGhE,QAAQ,CAAC,eAAe,CAAC;YAC5BrC,UAAU,EAAE,0BAA0B;YACtCC,KAAK,EAAE,OAAO;YACdK,MAAM,EAAE,oCAAoC;YAC5C+L,cAAc,EAAE;UAClB,CAAE;UACFjB,OAAO,EAAEA,CAAA,KAAM;YAAEzI,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;UAAE,CAAE;UAAAmI,QAAA,gBAEzErQ,OAAA,CAACpB,OAAO;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNX,OAAA;MAAO2L,KAAK,EAAEhE,QAAQ,CAAC,SAAS,CAAE;MAAA0I,QAAA,eAChCrQ,OAAA;QAAK2L,KAAK,EAAE;UAAE6E,OAAO,EAAE;QAAO,CAAE;QAAAH,QAAA,EAC7BjQ,mBAAmB,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEwR,KAAK,kBACnC9R,OAAA;UAAAqQ,QAAA,gBACErQ,OAAA;YACE2L,KAAK,EAAE;cACL,GAAGhE,QAAQ,CAAC,aAAa,CAAC;cAC1B,IAAI/F,SAAS,KAAKtB,IAAI,CAACO,GAAG,GAAG8G,QAAQ,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC;cAChE4I,MAAM,EAAE,SAAS;cACjBnK,UAAU,EAAE;YACd,CAAE;YACFsK,OAAO,EAAEA,CAAA,KAAM;cACb7O,YAAY,CAACvB,IAAI,CAACO,GAAG,CAAC;cACtBoB,cAAc,CAAC,KAAK,CAAC;YACvB,CAAE;YACF0O,YAAY,EAAGpH,CAAC,IAAK;cACnB,IAAI3H,SAAS,KAAKtB,IAAI,CAACO,GAAG,EAAE;gBAC1B0I,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACrG,UAAU,GAAG,qBAAqB;gBACjDiE,CAAC,CAACE,MAAM,CAACkC,KAAK,CAAC1F,SAAS,GAAG,iBAAiB;gBAC5CsD,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACoG,UAAU,GAAG,aAAajU,YAAY,CAACuH,YAAY,CAACgB,OAAO,EAAE;cAC9E;YACF,CAAE;YACFuK,YAAY,EAAGrH,CAAC,IAAK;cACnB,IAAI3H,SAAS,KAAKtB,IAAI,CAACO,GAAG,EAAE;gBAC1B0I,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACrG,UAAU,GAAGxH,YAAY,CAACuH,YAAY,CAACU,OAAO;gBAC7DwD,CAAC,CAACE,MAAM,CAACkC,KAAK,CAAC1F,SAAS,GAAG,eAAe;gBAC1CsD,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACoG,UAAU,GAAG,uBAAuB;cACrD;YACF,CAAE;YAAA1B,QAAA,gBAEFrQ,OAAA;cAAK2L,KAAK,EAAE;gBAAE2E,WAAW,EAAE;cAAO,CAAE;cAAAD,QAAA,EAAE/P,IAAI,CAACM;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtDX,OAAA;cAAM2L,KAAK,EAAE;gBAAEmF,IAAI,EAAE;cAAE,CAAE;cAAAT,QAAA,EAAE/P,IAAI,CAACQ;YAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC5CL,IAAI,CAAC0R,QAAQ,CAACxC,MAAM,GAAG,CAAC,iBACvBxP,OAAA;cAAK0Q,OAAO,EAAGnH,CAAC,IAAK;gBACnBA,CAAC,CAAC6B,eAAe,CAAC,CAAC;gBACnBgD,UAAU,CAAC9N,IAAI,CAACQ,KAAK,CAAC;cACxB,CAAE;cAAAuP,QAAA,EACCnO,aAAa,CAAC5B,IAAI,CAACQ,KAAK,CAAC,gBAAGd,OAAA,CAAC/B,aAAa;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGX,OAAA,CAAC9B,cAAc;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAELL,IAAI,CAAC0R,QAAQ,CAACxC,MAAM,GAAG,CAAC,IAAItN,aAAa,CAAC5B,IAAI,CAACQ,KAAK,CAAC,iBACpDd,OAAA;YAAK2L,KAAK,EAAE;cAAEjF,UAAU,EAAE;YAAO,CAAE;YAAA2J,QAAA,EAChC/P,IAAI,CAAC0R,QAAQ,CAAC3R,GAAG,CAAC,CAAC4R,OAAO,EAAEC,QAAQ,kBACnClS,OAAA;cAEE2L,KAAK,EAAE;gBACL,GAAGhE,QAAQ,CAAC,aAAa,CAAC;gBAC1B6I,OAAO,EAAE,mBAAmB;gBAC5Bc,QAAQ,EAAE,MAAM;gBAChBC,OAAO,EAAE;cACX,CAAE;cACFb,OAAO,EAAEA,CAAA,KAAM;gBACb7O,YAAY,CAACvB,IAAI,CAACO,GAAG,CAAC;gBACtBoB,cAAc,CAAC,KAAK,CAAC;cACvB,CAAE;cACF0O,YAAY,EAAGpH,CAAC,IAAK;gBACnBA,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACrG,UAAU,GAAG,qBAAqB;gBACjDiE,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACwG,WAAW,GAAG,MAAM;gBACnC5I,CAAC,CAACE,MAAM,CAACkC,KAAK,CAAC4F,OAAO,GAAG,GAAG;cAC9B,CAAE;cACFX,YAAY,EAAGrH,CAAC,IAAK;gBACnBA,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACrG,UAAU,GAAGxH,YAAY,CAACuH,YAAY,CAACU,OAAO;gBAC7DwD,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACwG,WAAW,GAAG,MAAM;gBACnC5I,CAAC,CAACE,MAAM,CAACkC,KAAK,CAAC4F,OAAO,GAAG,KAAK;cAChC,CAAE;cAAAlB,QAAA,EAED4B,OAAO,CAACnR;YAAK,GAtBToR,QAAQ;cAAA1R,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuBV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA,GArEOmR,KAAK;UAAAtR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsEV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRX,OAAA;MAAM2L,KAAK,EAAEhE,QAAQ,CAAC,aAAa,CAAE;MAAA0I,QAAA,GAElCrO,WAAW,IAAIyB,MAAM,CAACC,UAAU,GAAG,GAAG,iBACrC1D,OAAA;QACE2L,KAAK,EAAE;UACLkE,QAAQ,EAAE,OAAO;UACjBuC,GAAG,EAAE,MAAM;UACXC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTnN,eAAe,EAAE,iBAAiB;UAClCoN,MAAM,EAAE;QACV,CAAE;QACF9B,OAAO,EAAEA,CAAA,KAAMzO,cAAc,CAAC,KAAK;MAAE;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CACF,EAGAiB,SAAS,KAAK,WAAW,iBACxB5B,OAAA;QAAK2L,KAAK,EAAE;UACV6E,OAAO,EAAE,WAAW;UACpBlL,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACoN,aAAa;UACnD9L,SAAS,EAAE,OAAO;UAClBkJ,QAAQ,EAAE,UAAU;UACpB6C,QAAQ,EAAE;QACZ,CAAE;QAAArC,QAAA,gBAEArQ,OAAA;UAAK2L,KAAK,EAAE;YACVkE,QAAQ,EAAE,UAAU;YACpBuC,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTI,eAAe,EAAE;AAC/B,qDAAqD7U,YAAY,CAACuH,YAAY,CAACgB,OAAO;AACtF,qDAAqDvI,YAAY,CAACuH,YAAY,CAACgB,OAAO;AACtF,qDAAqDvI,YAAY,CAACuH,YAAY,CAACgB,OAAO;AACtF,eAAe;YACDmM,MAAM,EAAE;UACV;QAAE;UAAAhS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGLX,OAAA;UAAK2L,KAAK,EAAE;YACVkE,QAAQ,EAAE,UAAU;YACpBuC,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTI,eAAe,EAAE;AAC/B,kCAAkC7U,YAAY,CAACuH,YAAY,CAACO,MAAM;AAClE,yCAAyC9H,YAAY,CAACuH,YAAY,CAACO,MAAM;AACzE,eAAe;YACDgN,cAAc,EAAE,WAAW;YAC3BrB,OAAO,EAAE,GAAG;YACZiB,MAAM,EAAE;UACV;QAAE;UAAAhS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAELX,OAAA;UAAK2L,KAAK,EAAE;YAAEkH,QAAQ,EAAE,QAAQ;YAAEC,MAAM,EAAE,QAAQ;YAAEjD,QAAQ,EAAE,UAAU;YAAE2C,MAAM,EAAE;UAAE,CAAE;UAAAnC,QAAA,gBAGpFrQ,OAAA;YAAK2L,KAAK,EAAE;cACVrG,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACU,OAAO;cAC7C4L,cAAc,EAAE,YAAY;cAC5BlB,YAAY,EAAE,MAAM;cACpBD,OAAO,EAAE,MAAM;cACfuC,YAAY,EAAE,MAAM;cACpBlD,QAAQ,EAAE,UAAU;cACpB6C,QAAQ,EAAE,QAAQ;cAClB9M,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACO,MAAM,EAAE;cACvDkB,SAAS,EAAE,eAAehJ,YAAY,CAACuH,YAAY,CAAC0B,MAAM;YAC5D,CAAE;YAAAsJ,QAAA,gBAEArQ,OAAA;cAAK2L,KAAK,EAAE;gBACVkE,QAAQ,EAAE,UAAU;gBACpBuC,GAAG,EAAE,CAAC;gBACNC,IAAI,EAAE,CAAC;gBACPC,KAAK,EAAE,CAAC;gBACRlB,MAAM,EAAE,KAAK;gBACb9L,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAAC2N;cACxC;YAAE;cAAAxS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAELX,OAAA;cAAK2L,KAAK,EAAE;gBACVoF,OAAO,EAAE,MAAM;gBACfkC,mBAAmB,EAAE,eAAe;gBACpCzB,GAAG,EAAE,MAAM;gBACXR,UAAU,EAAE,QAAQ;gBACpBnB,QAAQ,EAAE,UAAU;gBACpB2C,MAAM,EAAE;cACV,CAAE;cAAAnC,QAAA,gBAEArQ,OAAA;gBAAK2L,KAAK,EAAE;kBACV8F,KAAK,EAAE,OAAO;kBACdL,MAAM,EAAE,OAAO;kBACfX,YAAY,EAAE,MAAM;kBACpBnL,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAAC2N,QAAQ;kBAC9CjC,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBU,cAAc,EAAE,QAAQ;kBACxBJ,QAAQ,EAAE,QAAQ;kBAClBD,UAAU,EAAE,MAAM;kBAClB9L,KAAK,EAAE,OAAO;kBACduB,SAAS,EAAE,eAAehJ,YAAY,CAACuH,YAAY,CAAC0B,MAAM,EAAE;kBAC5DnB,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACU,OAAO;gBACxD,CAAE;gBAAAsK,QAAA,EACCjO,IAAI,GAAGA,IAAI,CAACwP,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG;cAAI;gBAAArR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eAGNX,OAAA;gBAAAqQ,QAAA,gBACErQ,OAAA;kBAAI2L,KAAK,EAAE;oBACTmH,MAAM,EAAE,CAAC;oBACTxB,QAAQ,EAAE,QAAQ;oBAClBD,UAAU,EAAE,GAAG;oBACf0B,YAAY,EAAE,QAAQ;oBACtBxN,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG,IAAI;oBACrC0N,UAAU,EAAE;kBACd,CAAE;kBAAA7C,QAAA,GAAC,gBACa,EAACjO,IAAI,GAAGA,IAAI,CAACwP,KAAK,CAACuB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,EAAC,gBAC1D;gBAAA;kBAAA3S,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLX,OAAA;kBAAG2L,KAAK,EAAE;oBACRmH,MAAM,EAAE,CAAC;oBACTxB,QAAQ,EAAE,QAAQ;oBAClB/L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAAC+N,SAAS;oBAC1C/B,UAAU,EAAE,GAAG;oBACf0B,YAAY,EAAE;kBAChB,CAAE;kBAAA1C,QAAA,EAAC;gBAEH;kBAAA7P,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAGJX,OAAA;kBAAK2L,KAAK,EAAE;oBACVoF,OAAO,EAAE,MAAM;oBACfS,GAAG,EAAE,MAAM;oBACX6B,QAAQ,EAAE;kBACZ,CAAE;kBAAAhD,QAAA,gBACArQ,OAAA;oBAAK2L,KAAK,EAAE;sBACVrG,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACgB,OAAO,GAAG,IAAI;sBACpDd,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACgB,OAAO;sBACxCmK,OAAO,EAAE,aAAa;sBACtBC,YAAY,EAAE,MAAM;sBACpBa,QAAQ,EAAE,QAAQ;sBAClBD,UAAU,EAAE,GAAG;sBACfzL,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACgB,OAAO;oBACxD,CAAE;oBAAAgK,QAAA,EAAC;kBAEH;oBAAA7P,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNX,OAAA;oBAAK2L,KAAK,EAAE;sBACVrG,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACgB,OAAO,GAAG,IAAI;sBACpDd,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACgB,OAAO;sBACxCmK,OAAO,EAAE,aAAa;sBACtBC,YAAY,EAAE,MAAM;sBACpBa,QAAQ,EAAE,QAAQ;sBAClBD,UAAU,EAAE,GAAG;sBACfzL,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACgB,OAAO;oBACxD,CAAE;oBAAAgK,QAAA,EAAC;kBAEH;oBAAA7P,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNX,OAAA;gBAAK2L,KAAK,EAAE;kBACV2H,SAAS,EAAE,QAAQ;kBACnB9C,OAAO,EAAE,MAAM;kBACflL,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACiC,SAAS;kBAC/CmJ,YAAY,EAAE,MAAM;kBACpB7K,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACO,MAAM;gBACvD,CAAE;gBAAAyK,QAAA,gBACArQ,OAAA;kBAAK2L,KAAK,EAAE;oBACV2F,QAAQ,EAAE,MAAM;oBAChBD,UAAU,EAAE,GAAG;oBACf9L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACgB,OAAO;oBACxC0M,YAAY,EAAE;kBAChB,CAAE;kBAAA1C,QAAA,EACC,IAAInC,IAAI,CAAC,CAAC,CAACc,kBAAkB,CAAC,OAAO,EAAE;oBAAEM,GAAG,EAAE;kBAAU,CAAC;gBAAC;kBAAA9O,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC,eACNX,OAAA;kBAAK2L,KAAK,EAAE;oBACV2F,QAAQ,EAAE,QAAQ;oBAClB/L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAAC+N,SAAS;oBAC1C/B,UAAU,EAAE;kBACd,CAAE;kBAAAhB,QAAA,EACC,IAAInC,IAAI,CAAC,CAAC,CAACc,kBAAkB,CAAC,OAAO,EAAE;oBAAEuE,KAAK,EAAE,OAAO;oBAAEC,IAAI,EAAE;kBAAU,CAAC;gBAAC;kBAAAhT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNX,OAAA;YAAK2L,KAAK,EAAE;cACVoF,OAAO,EAAE,MAAM;cACfkC,mBAAmB,EAAE,sCAAsC;cAC3DzB,GAAG,EAAE,QAAQ;cACbuB,YAAY,EAAE;YAChB,CAAE;YAAA1C,QAAA,EACC,CACC;cACEvP,KAAK,EAAE,mBAAmB;cAC1B2S,KAAK,EAAE,IAAI;cACXC,IAAI,EAAE,GAAG;cACT9S,IAAI,EAAE,IAAI;cACV+S,KAAK,EAAE,MAAM;cACb3O,WAAW,EAAE,YAAY;cACzBO,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACgB;YACnC,CAAC,EACD;cACEvF,KAAK,EAAE,aAAa;cACpB2S,KAAK,EAAE,IAAI;cACXC,IAAI,EAAE,KAAK;cACX9S,IAAI,EAAE,IAAI;cACV+S,KAAK,EAAE,QAAQ;cACf3O,WAAW,EAAE,WAAW;cACxBO,KAAK,EAAE;YACT,CAAC,EACD;cACEzE,KAAK,EAAE,iBAAiB;cACxB2S,KAAK,EAAE,IAAI;cACXC,IAAI,EAAE,OAAO;cACb9S,IAAI,EAAE,GAAG;cACT+S,KAAK,EAAE,IAAI;cACX3O,WAAW,EAAE,WAAW;cACxBO,KAAK,EAAE;YACT,CAAC,EACD;cACEzE,KAAK,EAAE,cAAc;cACrB2S,KAAK,EAAE,KAAK;cACZC,IAAI,EAAE,MAAM;cACZ9S,IAAI,EAAE,GAAG;cACT+S,KAAK,EAAE,MAAM;cACb3O,WAAW,EAAE,SAAS;cACtBO,KAAK,EAAE;YACT,CAAC,CACF,CAAClF,GAAG,CAAC,CAACuT,IAAI,EAAE9B,KAAK,kBAChB9R,OAAA;cAAiB2L,KAAK,EAAE;gBACtBrG,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACU,OAAO;gBAC7C0K,YAAY,EAAE,MAAM;gBACpBD,OAAO,EAAE,QAAQ;gBACjBX,QAAQ,EAAE,UAAU;gBACpB6C,QAAQ,EAAE,QAAQ;gBAClB9M,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACO,MAAM,EAAE;gBACvD2K,MAAM,EAAE,SAAS;gBACjBnK,UAAU,EAAE,eAAe;gBAC3BU,SAAS,EAAE,cAAchJ,YAAY,CAACuH,YAAY,CAAC0B,MAAM;cAC3D,CAAE;cACF4J,YAAY,EAAGpH,CAAC,IAAK;gBACnBA,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAAC1F,SAAS,GAAG,kBAAkB;gBACpDsD,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAAC7E,SAAS,GAAG,cAAchJ,YAAY,CAACuH,YAAY,CAAC0B,MAAM,EAAE;cACpF,CAAE;cACF6J,YAAY,EAAGrH,CAAC,IAAK;gBACnBA,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAAC1F,SAAS,GAAG,eAAe;gBACjDsD,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAAC7E,SAAS,GAAG,cAAchJ,YAAY,CAACuH,YAAY,CAAC0B,MAAM,EAAE;cACpF,CAAE;cAAAsJ,QAAA,gBAGArQ,OAAA;gBAAK2L,KAAK,EAAE;kBACVkE,QAAQ,EAAE,UAAU;kBACpBuC,GAAG,EAAE,CAAC;kBACNC,IAAI,EAAE,CAAC;kBACPC,KAAK,EAAE,CAAC;kBACRlB,MAAM,EAAE,KAAK;kBACb9L,UAAU,EAAEsO,IAAI,CAACrO;gBACnB;cAAE;gBAAA/E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAELX,OAAA;gBAAK2L,KAAK,EAAE;kBAAEkE,QAAQ,EAAE,UAAU;kBAAE2C,MAAM,EAAE;gBAAE,CAAE;gBAAAnC,QAAA,gBAE9CrQ,OAAA;kBAAK2L,KAAK,EAAE;oBACVoF,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBU,cAAc,EAAE,eAAe;oBAC/BqB,YAAY,EAAE;kBAChB,CAAE;kBAAA1C,QAAA,gBACArQ,OAAA;oBAAK2L,KAAK,EAAE;sBACV2F,QAAQ,EAAE,QAAQ;sBAClBD,UAAU,EAAE,GAAG;sBACf9L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAAC+N,SAAS;sBAC1CU,aAAa,EAAE,WAAW;sBAC1BC,aAAa,EAAE;oBACjB,CAAE;oBAAA1D,QAAA,EACCuD,IAAI,CAAC9S;kBAAK;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,eACNX,OAAA;oBAAK2L,KAAK,EAAE;sBACV2F,QAAQ,EAAE,QAAQ;sBAClBC,OAAO,EAAE;oBACX,CAAE;oBAAAlB,QAAA,EACCuD,IAAI,CAAChT;kBAAI;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNX,OAAA;kBAAK2L,KAAK,EAAE;oBACVoF,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,UAAU;oBACtBQ,GAAG,EAAE,QAAQ;oBACbuB,YAAY,EAAE;kBAChB,CAAE;kBAAA1C,QAAA,gBACArQ,OAAA;oBAAK2L,KAAK,EAAE;sBACV2F,QAAQ,EAAE,QAAQ;sBAClBD,UAAU,EAAE,GAAG;sBACf9L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG,IAAI;sBACrC0N,UAAU,EAAE;oBACd,CAAE;oBAAA7C,QAAA,EACCuD,IAAI,CAACH;kBAAK;oBAAAjT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,eACNX,OAAA;oBAAK2L,KAAK,EAAE;sBACV2F,QAAQ,EAAE,MAAM;sBAChBD,UAAU,EAAE,GAAG;sBACf9L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAAC+N;oBACnC,CAAE;oBAAA/C,QAAA,EACCuD,IAAI,CAACF;kBAAI;oBAAAlT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNX,OAAA;kBAAK2L,KAAK,EAAE;oBACVoF,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBU,cAAc,EAAE;kBAClB,CAAE;kBAAArB,QAAA,gBACArQ,OAAA;oBAAK2L,KAAK,EAAE;sBACV2F,QAAQ,EAAE,QAAQ;sBAClB/L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAAC+N;oBACnC,CAAE;oBAAA/C,QAAA,EACCuD,IAAI,CAAC5O;kBAAW;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC,eACNX,OAAA;oBAAK2L,KAAK,EAAE;sBACV2F,QAAQ,EAAE,QAAQ;sBAClBD,UAAU,EAAE,GAAG;sBACf9L,KAAK,EAAEqO,IAAI,CAACrO,KAAK;sBACjBD,UAAU,EAAEsO,IAAI,CAACrO,KAAK,GAAG,IAAI;sBAC7BiL,OAAO,EAAE,gBAAgB;sBACzBC,YAAY,EAAE;oBAChB,CAAE;oBAAAJ,QAAA,EACCuD,IAAI,CAACD;kBAAK;oBAAAnT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GAtGEmR,KAAK;cAAAtR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuGV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNX,OAAA;YAAK2L,KAAK,EAAE;cACVrG,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACU,OAAO;cAC7C0K,YAAY,EAAE,MAAM;cACpBD,OAAO,EAAE,MAAM;cACfuC,YAAY,EAAE,MAAM;cACpBnN,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACO,MAAM,EAAE;cACvDkB,SAAS,EAAE,cAAchJ,YAAY,CAACuH,YAAY,CAAC0B,MAAM,EAAE;cAC3D8I,QAAQ,EAAE,UAAU;cACpB6C,QAAQ,EAAE;YACZ,CAAE;YAAArC,QAAA,gBAEArQ,OAAA;cAAK2L,KAAK,EAAE;gBACVkE,QAAQ,EAAE,UAAU;gBACpBuC,GAAG,EAAE,CAAC;gBACNC,IAAI,EAAE,CAAC;gBACPC,KAAK,EAAE,CAAC;gBACRlB,MAAM,EAAE,KAAK;gBACb9L,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAAC2N;cACxC;YAAE;cAAAxS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAELX,OAAA;cAAI2L,KAAK,EAAE;gBACTmH,MAAM,EAAE,YAAY;gBACpBxB,QAAQ,EAAE,QAAQ;gBAClBD,UAAU,EAAE,GAAG;gBACf9L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG,IAAI;gBACrC8N,SAAS,EAAE;cACb,CAAE;cAAAjD,QAAA,EAAC;YAEH;cAAA7P,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAELX,OAAA;cAAK2L,KAAK,EAAE;gBACVoF,OAAO,EAAE,MAAM;gBACfkC,mBAAmB,EAAE,sCAAsC;gBAC3DzB,GAAG,EAAE;cACP,CAAE;cAAAnB,QAAA,EACC,CACC;gBACEzP,IAAI,EAAE,IAAI;gBACVE,KAAK,EAAE,iBAAiB;gBACxBkT,QAAQ,EAAE,kBAAkB;gBAC5BC,IAAI,EAAE,mCAAmC;gBACzCC,MAAM,EAAEA,CAAA,KAAMrS,YAAY,CAAC,SAAS,CAAC;gBACrC0D,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACgB;cACnC,CAAC,EACD;gBACEzF,IAAI,EAAE,IAAI;gBACVE,KAAK,EAAE,iBAAiB;gBACxBkT,QAAQ,EAAE,cAAc;gBACxBC,IAAI,EAAE,oCAAoC;gBAC1CC,MAAM,EAAEA,CAAA,KAAMrS,YAAY,CAAC,KAAK,CAAC;gBACjC0D,KAAK,EAAE;cACT,CAAC,EACD;gBACE3E,IAAI,EAAE,IAAI;gBACVE,KAAK,EAAE,eAAe;gBACtBkT,QAAQ,EAAE,oBAAoB;gBAC9BC,IAAI,EAAE,kCAAkC;gBACxCC,MAAM,EAAEA,CAAA,KAAMrS,YAAY,CAAC,QAAQ,CAAC;gBACpC0D,KAAK,EAAE;cACT,CAAC,EACD;gBACE3E,IAAI,EAAE,IAAI;gBACVE,KAAK,EAAE,iBAAiB;gBACxBkT,QAAQ,EAAE,oBAAoB;gBAC9BC,IAAI,EAAE,gCAAgC;gBACtCC,MAAM,EAAEA,CAAA,KAAMrS,YAAY,CAAC,WAAW,CAAC;gBACvC0D,KAAK,EAAE;cACT,CAAC,CACF,CAAClF,GAAG,CAAC,CAAC6T,MAAM,EAAEpC,KAAK,kBAClB9R,OAAA;gBACE0Q,OAAO,EAAEwD,MAAM,CAACA,MAAO;gBACvBvI,KAAK,EAAE;kBACLrG,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACU,OAAO;kBAC7C0K,YAAY,EAAE,MAAM;kBACpBD,OAAO,EAAE,QAAQ;kBACjBD,MAAM,EAAE,SAAS;kBACjBnK,UAAU,EAAE,eAAe;kBAC3BR,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACO,MAAM,EAAE;kBACvDiK,QAAQ,EAAE,UAAU;kBACpB6C,QAAQ,EAAE,QAAQ;kBAClB5L,SAAS,EAAE,cAAchJ,YAAY,CAACuH,YAAY,CAAC0B,MAAM;gBAC3D,CAAE;gBACF4J,YAAY,EAAGpH,CAAC,IAAK;kBACnBA,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAAC1F,SAAS,GAAG,kBAAkB;kBACpDsD,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAAC7E,SAAS,GAAG,cAAchJ,YAAY,CAACuH,YAAY,CAAC0B,MAAM,EAAE;kBAClFwC,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAACzE,WAAW,GAAGgN,MAAM,CAAC3O,KAAK;gBAClD,CAAE;gBACFqL,YAAY,EAAGrH,CAAC,IAAK;kBACnBA,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAAC1F,SAAS,GAAG,eAAe;kBACjDsD,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAAC7E,SAAS,GAAG,cAAchJ,YAAY,CAACuH,YAAY,CAAC0B,MAAM,EAAE;kBAClFwC,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAACzE,WAAW,GAAGpJ,YAAY,CAACuH,YAAY,CAACO,MAAM;gBACtE,CAAE;gBAAAyK,QAAA,gBAGFrQ,OAAA;kBAAK2L,KAAK,EAAE;oBACVkE,QAAQ,EAAE,UAAU;oBACpBuC,GAAG,EAAE,CAAC;oBACNC,IAAI,EAAE,CAAC;oBACPC,KAAK,EAAE,CAAC;oBACRlB,MAAM,EAAE,KAAK;oBACb9L,UAAU,EAAE4O,MAAM,CAAC3O;kBACrB;gBAAE;kBAAA/E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAELX,OAAA;kBAAK2L,KAAK,EAAE;oBAAEkE,QAAQ,EAAE,UAAU;oBAAE2C,MAAM,EAAE,CAAC;oBAAEc,SAAS,EAAE;kBAAS,CAAE;kBAAAjD,QAAA,gBACnErQ,OAAA;oBAAK2L,KAAK,EAAE;sBACV2F,QAAQ,EAAE,QAAQ;sBAClByB,YAAY,EAAE,MAAM;sBACpBxB,OAAO,EAAE;oBACX,CAAE;oBAAAlB,QAAA,EACC6D,MAAM,CAACtT;kBAAI;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eAENX,OAAA;oBAAK2L,KAAK,EAAE;sBACV2F,QAAQ,EAAE,QAAQ;sBAClBD,UAAU,EAAE,GAAG;sBACf9L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG,IAAI;sBACrCuN,YAAY,EAAE;oBAChB,CAAE;oBAAA1C,QAAA,EACC6D,MAAM,CAACpT;kBAAK;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eAENX,OAAA;oBAAK2L,KAAK,EAAE;sBACV2F,QAAQ,EAAE,QAAQ;sBAClB/L,KAAK,EAAE2O,MAAM,CAAC3O,KAAK;sBACnB8L,UAAU,EAAE,GAAG;sBACf0B,YAAY,EAAE;oBAChB,CAAE;oBAAA1C,QAAA,EACC6D,MAAM,CAACF;kBAAQ;oBAAAxT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC,eAENX,OAAA;oBAAK2L,KAAK,EAAE;sBACV2F,QAAQ,EAAE,SAAS;sBACnB/L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAAC+N,SAAS;sBAC1CF,UAAU,EAAE;oBACd,CAAE;oBAAA7C,QAAA,EACC6D,MAAM,CAACD;kBAAI;oBAAAzT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GApEEmR,KAAK;gBAAAtR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAqEV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNX,OAAA;YAAK2L,KAAK,EAAE;cACVoF,OAAO,EAAE,MAAM;cACfkC,mBAAmB,EAAE,sCAAsC;cAC3DzB,GAAG,EAAE,MAAM;cACXuB,YAAY,EAAE;YAChB,CAAE;YAAA1C,QAAA,gBAEArQ,OAAA;cAAK2L,KAAK,EAAE;gBACVrG,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACU,OAAO;gBAC7C0K,YAAY,EAAE,MAAM;gBACpBD,OAAO,EAAE,QAAQ;gBACjB1J,SAAS,EAAE,cAAchJ,YAAY,CAACuH,YAAY,CAAC0B,MAAM,EAAE;gBAC3DnB,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACO,MAAM;cACvD,CAAE;cAAAyK,QAAA,gBACArQ,OAAA;gBAAI2L,KAAK,EAAE;kBACTmH,MAAM,EAAE,cAAc;kBACtBxB,QAAQ,EAAE,QAAQ;kBAClBD,UAAU,EAAE,GAAG;kBACf9L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG,IAAI;kBACrCuL,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBQ,GAAG,EAAE;gBACP,CAAE;gBAAAnB,QAAA,EAAC;cAEH;gBAAA7P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAGLX,OAAA;gBAAK2L,KAAK,EAAE;kBAAEoH,YAAY,EAAE;gBAAO,CAAE;gBAAA1C,QAAA,EAClC,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAChQ,GAAG,CAAC,CAACiP,GAAG,EAAEwC,KAAK,kBAChE9R,OAAA;kBAAe2L,KAAK,EAAE;oBACpBoF,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBU,cAAc,EAAE,eAAe;oBAC/BqB,YAAY,EAAE,SAAS;oBACvBvC,OAAO,EAAE,QAAQ;oBACjBC,YAAY,EAAE,KAAK;oBACnBnL,UAAU,EAAEwM,KAAK,GAAG,CAAC,GAAGhU,YAAY,CAACuH,YAAY,CAACiC,SAAS,GAAG;kBAChE,CAAE;kBAAA+I,QAAA,gBACArQ,OAAA;oBAAM2L,KAAK,EAAE;sBACX2F,QAAQ,EAAE,QAAQ;sBAClBD,UAAU,EAAE,GAAG;sBACf9L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG,IAAI;sBACrC2O,QAAQ,EAAE;oBACZ,CAAE;oBAAA9D,QAAA,EACCf;kBAAG;oBAAA9O,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACPX,OAAA;oBAAK2L,KAAK,EAAE;sBACVmF,IAAI,EAAE,CAAC;sBACPM,MAAM,EAAE,KAAK;sBACb9L,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACO,MAAM;sBAC5C6K,YAAY,EAAE,KAAK;sBACnBqC,MAAM,EAAE,QAAQ;sBAChBJ,QAAQ,EAAE;oBACZ,CAAE;oBAAArC,QAAA,eACArQ,OAAA;sBAAK2L,KAAK,EAAE;wBACVyF,MAAM,EAAE,MAAM;wBACdK,KAAK,EAAE,GAAG2C,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;wBACpC/O,UAAU,EAAEwM,KAAK,GAAG,CAAC,GAAGhU,YAAY,CAACuH,YAAY,CAACgB,OAAO,GAAGvI,YAAY,CAACuH,YAAY,CAACO,MAAM;wBAC5F6K,YAAY,EAAE,KAAK;wBACnBrK,UAAU,EAAE;sBACd;oBAAE;sBAAA5F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACNX,OAAA;oBAAM2L,KAAK,EAAE;sBACX2F,QAAQ,EAAE,QAAQ;sBAClB/L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAAC+N,SAAS;sBAC1Ce,QAAQ,EAAE,MAAM;sBAChBb,SAAS,EAAE;oBACb,CAAE;oBAAAjD,QAAA,EACCyB,KAAK,GAAG,CAAC,GAAG,GAAGsC,IAAI,CAACE,KAAK,CAACF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;kBAAG;oBAAA7T,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC;gBAAA,GAxCC2O,GAAG;kBAAA9O,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyCR,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNX,OAAA;cAAK2L,KAAK,EAAE;gBACVrG,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACU,OAAO;gBAC7C0K,YAAY,EAAE,MAAM;gBACpBD,OAAO,EAAE,QAAQ;gBACjB1J,SAAS,EAAE,cAAchJ,YAAY,CAACuH,YAAY,CAAC0B,MAAM,EAAE;gBAC3DnB,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACO,MAAM;cACvD,CAAE;cAAAyK,QAAA,gBACArQ,OAAA;gBAAI2L,KAAK,EAAE;kBACTmH,MAAM,EAAE,cAAc;kBACtBxB,QAAQ,EAAE,QAAQ;kBAClBD,UAAU,EAAE,GAAG;kBACf9L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG,IAAI;kBACrCuL,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBQ,GAAG,EAAE;gBACP,CAAE;gBAAAnB,QAAA,EAAC;cAEH;gBAAA7P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAELX,OAAA;gBAAK2L,KAAK,EAAE;kBAAEoF,OAAO,EAAE,MAAM;kBAAEwD,aAAa,EAAE,QAAQ;kBAAE/C,GAAG,EAAE;gBAAO,CAAE;gBAAAnB,QAAA,EACnE,CACC;kBAAEvP,KAAK,EAAE,yBAAyB;kBAAE0T,QAAQ,EAAE,EAAE;kBAAEC,MAAM,EAAE;gBAAc,CAAC,EACzE;kBAAE3T,KAAK,EAAE,sBAAsB;kBAAE0T,QAAQ,EAAE,GAAG;kBAAEC,MAAM,EAAE;gBAAY,CAAC,EACrE;kBAAE3T,KAAK,EAAE,uBAAuB;kBAAE0T,QAAQ,EAAE,EAAE;kBAAEC,MAAM,EAAE;gBAAc,CAAC,CACxE,CAACpU,GAAG,CAAC,CAACqU,IAAI,EAAE5C,KAAK,kBAChB9R,OAAA;kBAAiB2L,KAAK,EAAE;oBACtB6E,OAAO,EAAE,MAAM;oBACfC,YAAY,EAAE,MAAM;oBACpBnL,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACiC,SAAS;oBAC/C1B,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACO,MAAM;kBACvD,CAAE;kBAAAyK,QAAA,gBACArQ,OAAA;oBAAK2L,KAAK,EAAE;sBACVoF,OAAO,EAAE,MAAM;sBACfW,cAAc,EAAE,eAAe;sBAC/BV,UAAU,EAAE,QAAQ;sBACpB+B,YAAY,EAAE;oBAChB,CAAE;oBAAA1C,QAAA,gBACArQ,OAAA;sBAAM2L,KAAK,EAAE;wBACX2F,QAAQ,EAAE,QAAQ;wBAClBD,UAAU,EAAE,GAAG;wBACf9L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG;sBACnC,CAAE;sBAAA6K,QAAA,EACCqE,IAAI,CAAC5T;oBAAK;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC,eACPX,OAAA;sBAAM2L,KAAK,EAAE;wBACX2F,QAAQ,EAAE,QAAQ;wBAClBD,UAAU,EAAE,GAAG;wBACf9L,KAAK,EAAEmP,IAAI,CAACF,QAAQ,KAAK,GAAG,GAAG,SAAS,GAAG1W,YAAY,CAACuH,YAAY,CAACgB,OAAO;wBAC5Ef,UAAU,EAAEoP,IAAI,CAACF,QAAQ,KAAK,GAAG,GAAG,WAAW,GAAG1W,YAAY,CAACuH,YAAY,CAACgB,OAAO,GAAG,IAAI;wBAC1FmK,OAAO,EAAE,gBAAgB;wBACzBC,YAAY,EAAE;sBAChB,CAAE;sBAAAJ,QAAA,EACCqE,IAAI,CAACD;oBAAM;sBAAAjU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNX,OAAA;oBAAK2L,KAAK,EAAE;sBACVyF,MAAM,EAAE,KAAK;sBACb9L,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACO,MAAM;sBAC5C6K,YAAY,EAAE,KAAK;sBACnBiC,QAAQ,EAAE;oBACZ,CAAE;oBAAArC,QAAA,eACArQ,OAAA;sBAAK2L,KAAK,EAAE;wBACVyF,MAAM,EAAE,MAAM;wBACdK,KAAK,EAAE,GAAGiD,IAAI,CAACF,QAAQ,GAAG;wBAC1BlP,UAAU,EAAEoP,IAAI,CAACF,QAAQ,KAAK,GAAG,GAAG,SAAS,GAAG1W,YAAY,CAACuH,YAAY,CAACgB,OAAO;wBACjFoK,YAAY,EAAE,KAAK;wBACnBrK,UAAU,EAAE;sBACd;oBAAE;sBAAA5F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA,GA3CEmR,KAAK;kBAAAtR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA4CV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNX,OAAA;YAAK2L,KAAK,EAAE;cACVoF,OAAO,EAAE,MAAM;cACfkC,mBAAmB,EAAE,SAAS;cAC9BzB,GAAG,EAAE;YACP,CAAE;YAAAnB,QAAA,gBAGArQ,OAAA;cAAK2L,KAAK,EAAE;gBACVrG,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACU,OAAO;gBAC7C0K,YAAY,EAAE,MAAM;gBACpBD,OAAO,EAAE,QAAQ;gBACjB1J,SAAS,EAAE,cAAchJ,YAAY,CAACuH,YAAY,CAAC0B,MAAM,EAAE;gBAC3DnB,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACO,MAAM;cACvD,CAAE;cAAAyK,QAAA,gBACArQ,OAAA;gBAAI2L,KAAK,EAAE;kBACTmH,MAAM,EAAE,cAAc;kBACtBxB,QAAQ,EAAE,QAAQ;kBAClBD,UAAU,EAAE,GAAG;kBACf9L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG;gBACnC,CAAE;gBAAA6K,QAAA,EAAC;cAEH;gBAAA7P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAELX,OAAA;gBAAK2L,KAAK,EAAE;kBACVgJ,SAAS,EAAE,OAAO;kBAClBC,SAAS,EAAE;gBACb,CAAE;gBAAAvE,QAAA,EACClN,WAAW,CAAC2H,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACzK,GAAG,CAAC,CAAC6H,GAAG,EAAE4J,KAAK,kBACtC9R,OAAA;kBAAiB2L,KAAK,EAAE;oBACtBoF,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBQ,GAAG,EAAE,MAAM;oBACXhB,OAAO,EAAE,SAAS;oBAClBC,YAAY,EAAE,KAAK;oBACnBsC,YAAY,EAAE,QAAQ;oBACtBzN,UAAU,EAAEwM,KAAK,GAAG,CAAC,KAAK,CAAC,GAAGhU,YAAY,CAACuH,YAAY,CAACiC,SAAS,GAAG,aAAa;oBACjFlB,UAAU,EAAE;kBACd,CAAE;kBAAAiK,QAAA,gBACArQ,OAAA;oBAAK2L,KAAK,EAAE;sBACV8F,KAAK,EAAE,MAAM;sBACbL,MAAM,EAAE,MAAM;sBACdX,YAAY,EAAE,KAAK;sBACnBnL,UAAU,EAAE4C,GAAG,CAAC6F,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;sBACxD8G,UAAU,EAAE;oBACd;kBAAE;oBAAArU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACLX,OAAA;oBAAK2L,KAAK,EAAE;sBAAEmF,IAAI,EAAE;oBAAE,CAAE;oBAAAT,QAAA,gBACtBrQ,OAAA;sBAAK2L,KAAK,EAAE;wBACV2F,QAAQ,EAAE,QAAQ;wBAClBD,UAAU,EAAE,GAAG;wBACf9L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG,IAAI;wBACrCuN,YAAY,EAAE;sBAChB,CAAE;sBAAA1C,QAAA,EACCnI,GAAG,CAAC4F;oBAAG;sBAAAtN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACNX,OAAA;sBAAK2L,KAAK,EAAE;wBACV2F,QAAQ,EAAE,QAAQ;wBAClB/L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAAC+N;sBACnC,CAAE;sBAAA/C,QAAA,EACC,IAAInC,IAAI,CAAChG,GAAG,CAAC+F,IAAI,CAAC,CAAC6G,cAAc,CAAC;oBAAC;sBAAAtU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAhCEmR,KAAK;kBAAAtR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiCV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNX,OAAA;cAAK2L,KAAK,EAAE;gBACVrG,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACU,OAAO;gBAC7C0K,YAAY,EAAE,MAAM;gBACpBD,OAAO,EAAE,QAAQ;gBACjB1J,SAAS,EAAE,cAAchJ,YAAY,CAACuH,YAAY,CAAC0B,MAAM,EAAE;gBAC3DnB,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACO,MAAM;cACvD,CAAE;cAAAyK,QAAA,gBACArQ,OAAA;gBAAI2L,KAAK,EAAE;kBACTmH,MAAM,EAAE,cAAc;kBACtBxB,QAAQ,EAAE,QAAQ;kBAClBD,UAAU,EAAE,GAAG;kBACf9L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG;gBACnC,CAAE;gBAAA6K,QAAA,EAAC;cAEH;gBAAA7P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAELX,OAAA;gBAAK2L,KAAK,EAAE;kBAAEoF,OAAO,EAAE,MAAM;kBAAEwD,aAAa,EAAE,QAAQ;kBAAE/C,GAAG,EAAE;gBAAO,CAAE;gBAAAnB,QAAA,gBACpErQ,OAAA;kBAAO2L,KAAK,EAAE;oBACZoF,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBQ,GAAG,EAAE,SAAS;oBACdhB,OAAO,EAAE,MAAM;oBACfC,YAAY,EAAE,MAAM;oBACpBnL,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACiC,SAAS;oBAC/C1B,MAAM,EAAE,cAAc9H,YAAY,CAACuH,YAAY,CAACO,MAAM,EAAE;oBACxD2K,MAAM,EAAEjO,mBAAmB,GAAG,aAAa,GAAG,SAAS;oBACvD8D,UAAU,EAAE;kBACd,CAAE;kBACFuK,YAAY,EAAGpH,CAAC,IAAK;oBACnB,IAAI,CAACjH,mBAAmB,EAAE;sBACxBiH,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAACzE,WAAW,GAAGpJ,YAAY,CAACuH,YAAY,CAACgB,OAAO;sBACrEkD,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAACrG,UAAU,GAAGxH,YAAY,CAACuH,YAAY,CAACgB,OAAO,GAAG,IAAI;oBAC7E;kBACF,CAAE;kBACFuK,YAAY,EAAGrH,CAAC,IAAK;oBACnBA,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAACzE,WAAW,GAAGpJ,YAAY,CAACuH,YAAY,CAACO,MAAM;oBACpE2D,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAACrG,UAAU,GAAGxH,YAAY,CAACuH,YAAY,CAACiC,SAAS;kBACxE,CAAE;kBAAA+I,QAAA,gBACArQ,OAAA,CAACrB,QAAQ;oBAACkS,IAAI,EAAE,EAAG;oBAACtL,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACgB;kBAAQ;oBAAA7F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAChEX,OAAA;oBAAAqQ,QAAA,gBACErQ,OAAA;sBAAK2L,KAAK,EAAE;wBACV0F,UAAU,EAAE,GAAG;wBACf9L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG,IAAI;wBACrCuN,YAAY,EAAE;sBAChB,CAAE;sBAAA1C,QAAA,EACC/N,mBAAmB,GAAG,cAAc,GAAG;oBAAe;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD,CAAC,eACNX,OAAA;sBAAK2L,KAAK,EAAE;wBACV2F,QAAQ,EAAE,QAAQ;wBAClB/L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAAC+N;sBACnC,CAAE;sBAAA/C,QAAA,EAAC;oBAEH;sBAAA7P,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNX,OAAA;oBACE+N,IAAI,EAAC,MAAM;oBACXgH,MAAM,EAAC,iBAAiB;oBACxBC,QAAQ,EAAE1L,kBAAmB;oBAC7B2L,QAAQ,EAAE3S,mBAAoB;oBAC9BqJ,KAAK,EAAE;sBAAEoF,OAAO,EAAE;oBAAO;kBAAE;oBAAAvQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,EAEP6B,SAAS,iBACRxC,OAAA;kBACEgL,IAAI,EAAExI,SAAU;kBAChBiH,MAAM,EAAC,QAAQ;kBACfyL,GAAG,EAAC,qBAAqB;kBACzBvJ,KAAK,EAAE;oBACLoF,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBQ,GAAG,EAAE,SAAS;oBACdhB,OAAO,EAAE,MAAM;oBACfC,YAAY,EAAE,MAAM;oBACpBnL,UAAU,EAAE,SAAS;oBACrBC,KAAK,EAAE,OAAO;oBACd4P,cAAc,EAAE,MAAM;oBACtB9D,UAAU,EAAE,GAAG;oBACfjL,UAAU,EAAE;kBACd,CAAE;kBACFuK,YAAY,EAAGpH,CAAC,IAAK;oBACnBA,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAACrG,UAAU,GAAG,SAAS;oBAC5CiE,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAAC1F,SAAS,GAAG,kBAAkB;kBACtD,CAAE;kBACF2K,YAAY,EAAGrH,CAAC,IAAK;oBACnBA,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAACrG,UAAU,GAAG,SAAS;oBAC5CiE,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAAC1F,SAAS,GAAG,eAAe;kBACnD,CAAE;kBAAAoK,QAAA,gBAEFrQ,OAAA,CAAC7B,UAAU;oBAAC0S,IAAI,EAAE;kBAAG;oBAAArQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACxBX,OAAA;oBAAAqQ,QAAA,EAAM;kBAAW;oBAAA7P,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CACJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNX,OAAA;YAAK2L,KAAK,EAAE;cACVrG,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACU,OAAO;cAC7C0K,YAAY,EAAE,MAAM;cACpBD,OAAO,EAAE,MAAM;cACf4E,SAAS,EAAE,MAAM;cACjBxP,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACO,MAAM,EAAE;cACvDkB,SAAS,EAAE,cAAchJ,YAAY,CAACuH,YAAY,CAAC0B,MAAM,EAAE;cAC3DuM,SAAS,EAAE;YACb,CAAE;YAAAjD,QAAA,gBACArQ,OAAA;cAAK2L,KAAK,EAAE;gBACVoF,OAAO,EAAE,MAAM;gBACfkC,mBAAmB,EAAE,sCAAsC;gBAC3DzB,GAAG,EAAE,MAAM;gBACXuB,YAAY,EAAE;cAChB,CAAE;cAAA1C,QAAA,gBAEArQ,OAAA;gBAAAqQ,QAAA,gBACErQ,OAAA;kBAAI2L,KAAK,EAAE;oBACTmH,MAAM,EAAE,YAAY;oBACpBxB,QAAQ,EAAE,MAAM;oBAChBD,UAAU,EAAE,GAAG;oBACf9L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG;kBACnC,CAAE;kBAAA6K,QAAA,EAAC;gBAEH;kBAAA7P,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLX,OAAA;kBAAK2L,KAAK,EAAE;oBACVoF,OAAO,EAAE,MAAM;oBACfwD,aAAa,EAAE,QAAQ;oBACvB/C,GAAG,EAAE;kBACP,CAAE;kBAAAnB,QAAA,gBACArQ,OAAA;oBAAK2L,KAAK,EAAE;sBACV2F,QAAQ,EAAE,QAAQ;sBAClB/L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAAC+N;oBACnC,CAAE;oBAAA/C,QAAA,GAAC,oBACiB,eAAArQ,OAAA;sBAAQ2L,KAAK,EAAE;wBAAEpG,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACgB;sBAAQ,CAAE;sBAAAgK,QAAA,EAAC;oBAAQ;sBAAA7P,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7F,CAAC,eACNX,OAAA;oBAAK2L,KAAK,EAAE;sBACV2F,QAAQ,EAAE,QAAQ;sBAClB/L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAAC+N;oBACnC,CAAE;oBAAA/C,QAAA,GAAC,mBACgB,eAAArQ,OAAA;sBAAQ2L,KAAK,EAAE;wBAAEpG,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACgB;sBAAQ,CAAE;sBAAAgK,QAAA,EAAC;oBAAE;sBAAA7P,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNX,OAAA;gBAAAqQ,QAAA,gBACErQ,OAAA;kBAAI2L,KAAK,EAAE;oBACTmH,MAAM,EAAE,YAAY;oBACpBxB,QAAQ,EAAE,MAAM;oBAChBD,UAAU,EAAE,GAAG;oBACf9L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG;kBACnC,CAAE;kBAAA6K,QAAA,EAAC;gBAEH;kBAAA7P,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLX,OAAA;kBAAK2L,KAAK,EAAE;oBACVoF,OAAO,EAAE,MAAM;oBACfwD,aAAa,EAAE,QAAQ;oBACvB/C,GAAG,EAAE;kBACP,CAAE;kBAAAnB,QAAA,gBACArQ,OAAA;oBACE0Q,OAAO,EAAEA,CAAA,KAAM7O,YAAY,CAAC,WAAW,CAAE;oBACzC8J,KAAK,EAAE;sBACLrG,UAAU,EAAE,MAAM;sBAClBM,MAAM,EAAE,MAAM;sBACdL,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACgB,OAAO;sBACxCiL,QAAQ,EAAE,QAAQ;sBAClBf,MAAM,EAAE,SAAS;sBACjB+C,SAAS,EAAE,MAAM;sBACjB9C,OAAO,EAAE;oBACX,CAAE;oBAAAH,QAAA,EACH;kBAED;oBAAA7P,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTX,OAAA;oBACE0Q,OAAO,EAAEA,CAAA,KAAM7O,YAAY,CAAC,QAAQ,CAAE;oBACtC8J,KAAK,EAAE;sBACLrG,UAAU,EAAE,MAAM;sBAClBM,MAAM,EAAE,MAAM;sBACdL,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACgB,OAAO;sBACxCiL,QAAQ,EAAE,QAAQ;sBAClBf,MAAM,EAAE,SAAS;sBACjB+C,SAAS,EAAE,MAAM;sBACjB9C,OAAO,EAAE;oBACX,CAAE;oBAAAH,QAAA,EACH;kBAED;oBAAA7P,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNX,OAAA;gBAAAqQ,QAAA,gBACErQ,OAAA;kBAAI2L,KAAK,EAAE;oBACTmH,MAAM,EAAE,YAAY;oBACpBxB,QAAQ,EAAE,MAAM;oBAChBD,UAAU,EAAE,GAAG;oBACf9L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG;kBACnC,CAAE;kBAAA6K,QAAA,EAAC;gBAEH;kBAAA7P,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLX,OAAA;kBAAK2L,KAAK,EAAE;oBACV2F,QAAQ,EAAE,QAAQ;oBAClB/L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAAC+N,SAAS;oBAC1CiC,SAAS,EAAE,QAAQ;oBACnBnC,UAAU,EAAE;kBACd,CAAE;kBAAA7C,QAAA,EAAC;gBAEH;kBAAA7P,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNX,OAAA;cAAK2L,KAAK,EAAE;gBACV2J,SAAS,EAAE,aAAaxX,YAAY,CAACuH,YAAY,CAACO,MAAM,EAAE;gBAC1D2P,UAAU,EAAE,MAAM;gBAClBjE,QAAQ,EAAE,QAAQ;gBAClB/L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAAC+N;cACnC,CAAE;cAAA/C,QAAA,EAAC;YAEH;cAAA7P,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAiB,SAAS,KAAK,QAAQ,iBACrB5B,OAAA;QAAK2L,KAAK,EAAE;UAAE6E,OAAO,EAAE,MAAM;UAAEqC,QAAQ,EAAE,QAAQ;UAAEC,MAAM,EAAE;QAAS,CAAE;QAAAzC,QAAA,eACpErQ,OAAA;UAAK2L,KAAK,EAAEhE,QAAQ,CAAC,MAAM,CAAE;UAAA0I,QAAA,gBAC3BrQ,OAAA;YAAI2L,KAAK,EAAE;cACTyJ,SAAS,EAAE,CAAC;cACZ7P,KAAK,EAAE;YACT,CAAE;YAAA8K,QAAA,EAAC;UAAgB;YAAA7P,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBX,OAAA;YAAG2L,KAAK,EAAE;cACR4F,OAAO,EAAE,GAAG;cACZwB,YAAY,EAAE,MAAM;cACpBxN,KAAK,EAAE;YACT,CAAE;YAAA8K,QAAA,EAAC;UAEH;YAAA7P,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAGJX,OAAA;YAAK2L,KAAK,EAAE;cACVyF,MAAM,EAAE,MAAM;cACdwD,SAAS,EAAE,MAAM;cACjB7B,YAAY,EAAE,MAAM;cACpBvC,OAAO,EAAE,MAAM;cACfpL,eAAe,EAAE,SAAS;cAC1BQ,MAAM,EAAE,mBAAmB;cAC3B6K,YAAY,EAAE;YAChB,CAAE;YAAAJ,QAAA,GAECjP,QAAQ,CAACoO,MAAM,KAAK,CAAC,gBACpBxP,OAAA;cAAK2L,KAAK,EAAE;gBACVyF,MAAM,EAAE,MAAM;gBACdL,OAAO,EAAE,MAAM;gBACfwD,aAAa,EAAE,QAAQ;gBACvBvD,UAAU,EAAE,QAAQ;gBACpBU,cAAc,EAAE,QAAQ;gBACxB4B,SAAS,EAAE,QAAQ;gBACnB/B,OAAO,EAAE;cACX,CAAE;cAAAlB,QAAA,gBACArQ,OAAA;gBAAK2L,KAAK,EAAE;kBAAE2F,QAAQ,EAAE,MAAM;kBAAEyB,YAAY,EAAE;gBAAO,CAAE;gBAAA1C,QAAA,EAAC;cAAE;gBAAA7P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChEX,OAAA;gBAAI2L,KAAK,EAAE;kBACTmH,MAAM,EAAE,CAAC;kBACTvN,KAAK,EAAE;gBACT,CAAE;gBAAA8K,QAAA,EAAC;cAAoB;gBAAA7P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5BX,OAAA;gBAAG2L,KAAK,EAAE;kBACRpG,KAAK,EAAE;gBACT,CAAE;gBAAA8K,QAAA,EAAC;cAA+C;gBAAA7P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,GAENS,QAAQ,CAACf,GAAG,CAAC,CAACyN,GAAG,EAAE0H,GAAG,kBACpBxV,OAAA;cAEE2L,KAAK,EAAE;gBACL,IAAImC,GAAG,CAAClB,IAAI,KAAK,MAAM,GAAGjF,QAAQ,CAAC,gBAAgB,CAAC,GAAGA,QAAQ,CAAC,eAAe,CAAC,CAAC;gBACjF8N,SAAS,EAAE;cACb,CAAE;cAAApF,QAAA,EAEDvC,GAAG,CAAClB,IAAI,KAAK,KAAK,gBACjB5M,OAAA,CAACF,aAAa;gBAAAuQ,QAAA,EAAEvC,GAAG,CAACjB;cAAO;gBAAArM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAgB,CAAC,GAE5CmN,GAAG,CAACjB;YACL,GAVI2I,GAAG;cAAAhV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWL,CACN,CACF,EACAa,OAAO,iBACNxB,OAAA;cAAK2L,KAAK,EAAEhE,QAAQ,CAAC,eAAe,CAAE;cAAA0I,QAAA,eACpCrQ,OAAA;gBAAK2L,KAAK,EAAE;kBAAEoF,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEQ,GAAG,EAAE;gBAAM,CAAE;gBAAAnB,QAAA,gBAChErQ,OAAA;kBAAK2L,KAAK,EAAE;oBACV8F,KAAK,EAAE,MAAM;oBACbL,MAAM,EAAE,MAAM;oBACdX,YAAY,EAAE,KAAK;oBACnBrL,eAAe,EAAE,SAAS;oBAC1BqQ,SAAS,EAAE;kBACb;gBAAE;kBAAAjV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACLX,OAAA;kBAAK2L,KAAK,EAAE;oBACV8F,KAAK,EAAE,MAAM;oBACbL,MAAM,EAAE,MAAM;oBACdX,YAAY,EAAE,KAAK;oBACnBrL,eAAe,EAAE,SAAS;oBAC1BqQ,SAAS,EAAE,iCAAiC;oBAC5CC,cAAc,EAAE;kBAClB;gBAAE;kBAAAlV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACLX,OAAA;kBAAK2L,KAAK,EAAE;oBACV8F,KAAK,EAAE,MAAM;oBACbL,MAAM,EAAE,MAAM;oBACdX,YAAY,EAAE,KAAK;oBACnBrL,eAAe,EAAE,SAAS;oBAC1BqQ,SAAS,EAAE,iCAAiC;oBAC5CC,cAAc,EAAE;kBAClB;gBAAE;kBAAAlV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,eACDX,OAAA;cAAK2V,GAAG,EAAEhS;YAAW;cAAAnD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eAGNX,OAAA;YACE2L,KAAK,EAAE;cAAEoF,OAAO,EAAE,MAAM;cAAES,GAAG,EAAE;YAAO,CAAE;YACxCoE,QAAQ,EAAErM,CAAC,IAAI;cACbA,CAAC,CAACsM,cAAc,CAAC,CAAC;cAClBpJ,WAAW,CAAC,CAAC;YACf,CAAE;YAAA4D,QAAA,gBAEFrQ,OAAA;cACE+N,IAAI,EAAC,MAAM;cACX+H,WAAW,EAAC,sBAAsB;cAClCnK,KAAK,EAAEhE,QAAQ,CAAC,YAAY,CAAE;cAC9B8L,KAAK,EAAEvS,KAAM;cACb8T,QAAQ,EAAEzL,CAAC,IAAIpI,QAAQ,CAACoI,CAAC,CAACE,MAAM,CAACgK,KAAK,CAAE;cACxCwB,QAAQ,EAAEzT;YAAQ;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACFX,OAAA;cACE+N,IAAI,EAAC,QAAQ;cACbpC,KAAK,EAAE;gBACL,GAAGhE,QAAQ,CAAC,eAAe,CAAC;gBAC5BwM,QAAQ,EAAE;cACZ,CAAE;cACFc,QAAQ,EAAEzT,OAAO,IAAI,CAACN,KAAK,CAACwL,IAAI,CAAC,CAAE;cAAA2D,QAAA,EAElC7O,OAAO,GAAG,YAAY,GAAG;YAAM;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAiB,SAAS,KAAK,KAAK,iBAClB5B,OAAA;QAAK2L,KAAK,EAAE;UAAE6E,OAAO,EAAE;QAAO,CAAE;QAAAH,QAAA,eAC9BrQ,OAAA;UAAK2L,KAAK,EAAEhE,QAAQ,CAAC,MAAM,CAAE;UAAA0I,QAAA,gBAE3BrQ,OAAA;YAAK2L,KAAK,EAAE;cAAEoF,OAAO,EAAE,MAAM;cAAEW,cAAc,EAAE,eAAe;cAAEV,UAAU,EAAE,QAAQ;cAAE+B,YAAY,EAAE;YAAO,CAAE;YAAA1C,QAAA,gBAC3GrQ,OAAA;cAAAqQ,QAAA,gBACErQ,OAAA;gBAAI2L,KAAK,EAAE;kBAAEyJ,SAAS,EAAE,CAAC;kBAAErC,YAAY,EAAE;gBAAM,CAAE;gBAAA1C,QAAA,EAAC;cAA6B;gBAAA7P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpFX,OAAA;gBAAG2L,KAAK,EAAE;kBAAE4F,OAAO,EAAE,GAAG;kBAAEuB,MAAM,EAAE;gBAAE,CAAE;gBAAAzC,QAAA,EAAC;cAEvC;gBAAA7P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,EACL4D,gBAAgB,iBACfvE,OAAA;cACE0Q,OAAO,EAAEpF,mBAAoB;cAC7BK,KAAK,EAAE;gBACL,GAAGhE,QAAQ,CAAC,eAAe,CAAC;gBAC5BrC,UAAU,EAAE,SAAS;gBACrByL,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBQ,GAAG,EAAE,KAAK;gBACVF,QAAQ,EAAE,MAAM;gBAChBd,OAAO,EAAE,UAAU;gBACnB5K,MAAM,EAAE,MAAM;gBACd6K,YAAY,EAAE,KAAK;gBACnBlL,KAAK,EAAE,OAAO;gBACdgL,MAAM,EAAE,SAAS;gBACjBnK,UAAU,EAAE;cACd,CAAE;cACFuK,YAAY,EAAGpH,CAAC,IAAKA,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACrG,UAAU,GAAG,SAAU;cAC3DsL,YAAY,EAAGrH,CAAC,IAAKA,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACrG,UAAU,GAAG,SAAU;cAAA+K,QAAA,gBAE3DrQ,OAAA,CAACX,WAAW;gBAACwR,IAAI,EAAE;cAAG;gBAAArQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,uBAE3B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNX,OAAA;YAAK2L,KAAK,EAAE;cACVoF,OAAO,EAAE,MAAM;cACfS,GAAG,EAAE,KAAK;cACVuB,YAAY,EAAE,MAAM;cACpBM,QAAQ,EAAE,MAAM;cAChB1N,YAAY,EAAE,gBAAgB;cAC9BoQ,aAAa,EAAE;YACjB,CAAE;YAAA1F,QAAA,EACC,CAAC,KAAK,EAAE,GAAG2F,MAAM,CAACC,IAAI,CAACpR,iBAAiB,CAAC,CAAC,CAACxE,GAAG,CAAC6V,QAAQ,iBACtDlW,OAAA;cAEE0Q,OAAO,EAAEA,CAAA,KAAM1M,mBAAmB,CAACkS,QAAQ,CAAE;cAC7CvK,KAAK,EAAE;gBACL6E,OAAO,EAAE,UAAU;gBACnBC,YAAY,EAAE,MAAM;gBACpB7K,MAAM,EAAE7B,gBAAgB,KAAKmS,QAAQ,GACjC,MAAM,GACN,gBAAgB;gBACpB5Q,UAAU,EAAEvB,gBAAgB,KAAKmS,QAAQ,GACrCpY,YAAY,CAACuH,YAAY,CAACgB,OAAO,GACjC,aAAa;gBACjBd,KAAK,EAAExB,gBAAgB,KAAKmS,QAAQ,GAChC,OAAO,GACP,MAAM;gBACV3F,MAAM,EAAE,SAAS;gBACjBe,QAAQ,EAAE,MAAM;gBAChBD,UAAU,EAAEtN,gBAAgB,KAAKmS,QAAQ,GAAG,GAAG,GAAG,GAAG;gBACrD9P,UAAU,EAAE,eAAe;gBAC3B0N,aAAa,EAAE;cACjB,CAAE;cACFnD,YAAY,EAAGpH,CAAC,IAAK;gBACnB,IAAIxF,gBAAgB,KAAKmS,QAAQ,EAAE;kBACjC3M,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACrG,UAAU,GAAG,SAAS;gBACvC;cACF,CAAE;cACFsL,YAAY,EAAGrH,CAAC,IAAK;gBACnB,IAAIxF,gBAAgB,KAAKmS,QAAQ,EAAE;kBACjC3M,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACrG,UAAU,GAAG,aAAa;gBAC3C;cACF,CAAE;cAAA+K,QAAA,EAED6F,QAAQ,KAAK,KAAK,GAAG,QAAQ,GAAG,GAAGA,QAAQ,KAAK,OAAO,GAAG,IAAI,GAAGA,QAAQ,KAAK,UAAU,GAAG,IAAI,GAAGA,QAAQ,KAAK,UAAU,GAAG,IAAI,GAAGA,QAAQ,KAAK,SAAS,GAAG,IAAI,GAAGA,QAAQ,KAAK,QAAQ,GAAG,MAAM,GAAG,IAAI,IAAIA,QAAQ;YAAE,GA/BlNA,QAAQ;cAAA1V,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgCP,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNX,OAAA;YAAK2L,KAAK,EAAE;cACVoF,OAAO,EAAE,MAAM;cACfS,GAAG,EAAE,MAAM;cACXuB,YAAY,EAAE,MAAM;cACpBM,QAAQ,EAAE,MAAM;cAChBrC,UAAU,EAAE;YACd,CAAE;YAAAX,QAAA,gBAEArQ,OAAA;cAAK2L,KAAK,EAAE;gBAAEkE,QAAQ,EAAE,UAAU;gBAAEiB,IAAI,EAAE,CAAC;gBAAEqD,QAAQ,EAAE;cAAQ,CAAE;cAAA9D,QAAA,gBAC/DrQ,OAAA;gBAAK2L,KAAK,EAAE;kBACVkE,QAAQ,EAAE,UAAU;kBACpBwC,IAAI,EAAE,MAAM;kBACZD,GAAG,EAAE,KAAK;kBACVnM,SAAS,EAAE,kBAAkB;kBAC7BV,KAAK,EAAE;gBACT,CAAE;gBAAA8K,QAAA,eACArQ,OAAA,CAACtB,QAAQ;kBAACmS,IAAI,EAAE;gBAAG;kBAAArQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACNX,OAAA;gBACE+N,IAAI,EAAC,MAAM;gBACX+H,WAAW,EAAC,qBAAqB;gBACjCnK,KAAK,EAAE;kBACL,GAAGhE,QAAQ,CAAC,YAAY,CAAC;kBACzBwK,WAAW,EAAE,MAAM;kBACnBV,KAAK,EAAE;gBACT,CAAE;gBACFgC,KAAK,EAAE3R,UAAW;gBAClBkT,QAAQ,EAAGzL,CAAC,IAAKxH,aAAa,CAACwH,CAAC,CAACE,MAAM,CAACgK,KAAK;cAAE;gBAAAjT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,EACDmB,UAAU,iBACT9B,OAAA;gBACE2L,KAAK,EAAE;kBACLkE,QAAQ,EAAE,UAAU;kBACpByC,KAAK,EAAE,MAAM;kBACbF,GAAG,EAAE,KAAK;kBACVnM,SAAS,EAAE,kBAAkB;kBAC7BX,UAAU,EAAE,MAAM;kBAClBM,MAAM,EAAE,MAAM;kBACdL,KAAK,EAAE,MAAM;kBACbgL,MAAM,EAAE;gBACV,CAAE;gBACFG,OAAO,EAAEA,CAAA,KAAM3O,aAAa,CAAC,EAAE,CAAE;gBAAAsO,QAAA,eAEjCrQ,OAAA,CAAChC,GAAG;kBAAC6S,IAAI,EAAE;gBAAG;kBAAArQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNX,OAAA;cACEyT,KAAK,EAAExP,MAAO;cACd+Q,QAAQ,EAAGzL,CAAC,IAAKrF,SAAS,CAACqF,CAAC,CAACE,MAAM,CAACgK,KAAK,CAAE;cAC3C9H,KAAK,EAAE;gBACL,GAAGhE,QAAQ,CAAC,YAAY,CAAC;gBACzB8J,KAAK,EAAE,MAAM;gBACb0C,QAAQ,EAAE;cACZ,CAAE;cAAA9D,QAAA,gBAEFrQ,OAAA;gBAAQyT,KAAK,EAAC,MAAM;gBAAApD,QAAA,EAAC;cAAe;gBAAA7P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7CX,OAAA;gBAAQyT,KAAK,EAAC,WAAW;gBAAApD,QAAA,EAAC;cAAiB;gBAAA7P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAGL0D,eAAe,CAACmL,MAAM,GAAG,CAAC,iBACzBxP,OAAA;YAAK2L,KAAK,EAAE;cACVoH,YAAY,EAAE,MAAM;cACpBvC,OAAO,EAAE,MAAM;cACfC,YAAY,EAAE,MAAM;cACpBnL,UAAU,EAAE,SAAS;cACrBM,MAAM,EAAE;YACV,CAAE;YAAAyK,QAAA,gBACArQ,OAAA;cAAI2L,KAAK,EAAE;gBACToF,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBQ,GAAG,EAAE,KAAK;gBACVF,QAAQ,EAAE,MAAM;gBAChByB,YAAY,EAAE,MAAM;gBACpBxN,KAAK,EAAE,MAAM;gBACbuN,MAAM,EAAE;cACV,CAAE;cAAAzC,QAAA,gBACArQ,OAAA,CAACZ,OAAO;gBAACmG,KAAK,EAAC;cAAM;gBAAA/E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAC1B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLX,OAAA;cAAK2L,KAAK,EAAE;gBACVoF,OAAO,EAAE,MAAM;gBACfS,GAAG,EAAE,KAAK;gBACV6B,QAAQ,EAAE;cACZ,CAAE;cAAAhD,QAAA,EACChM,eAAe,CAAChE,GAAG,CAACoK,OAAO,iBAC1BzK,OAAA;gBAEE0Q,OAAO,EAAEA,CAAA,KAAMlG,kBAAkB,CAACC,OAAO,CAAE;gBAC3CkB,KAAK,EAAE;kBACL6E,OAAO,EAAE,UAAU;kBACnBC,YAAY,EAAE,MAAM;kBACpB7K,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACgB,OAAO,EAAE;kBACxDf,UAAU,EAAE,aAAa;kBACzBC,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACgB,OAAO;kBACxCkK,MAAM,EAAE,SAAS;kBACjBe,QAAQ,EAAE,MAAM;kBAChBlL,UAAU,EAAE;gBACd,CAAE;gBACFuK,YAAY,EAAGpH,CAAC,IAAK;kBACnBA,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACrG,UAAU,GAAGxH,YAAY,CAACuH,YAAY,CAACgB,OAAO;kBAC7DkD,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACpG,KAAK,GAAG,OAAO;gBAChC,CAAE;gBACFqL,YAAY,EAAGrH,CAAC,IAAK;kBACnBA,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACrG,UAAU,GAAG,aAAa;kBACzCiE,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACpG,KAAK,GAAGzH,YAAY,CAACuH,YAAY,CAACgB,OAAO;gBAC1D,CAAE;gBAAAgK,QAAA,EAED5F;cAAO,GArBHA,OAAO;gBAAAjK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsBN,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDX,OAAA;YAAK2L,KAAK,EAAE;cACVoF,OAAO,EAAE,MAAM;cACfkC,mBAAmB,EAAE,uCAAuC;cAC5DzB,GAAG,EAAE,MAAM;cACX4D,SAAS,EAAE;YACb,CAAE;YAAA/E,QAAA,EACCzE,oBAAoB,CAAC,CAAC,CAACvL,GAAG,CAAC,CAACoK,OAAO,EAAEqH,KAAK,kBACzC9R,OAAA;cAEE2L,KAAK,EAAE;gBACL,GAAGhE,QAAQ,CAAC,aAAa,CAAC;gBAC1BkI,QAAQ,EAAE,UAAU;gBACpB5J,SAAS,EAAE9B,iBAAiB,CAACkH,QAAQ,CAACZ,OAAO,CAAC,GAAG,aAAa,GAAG,UAAU;gBAC3E7E,MAAM,EAAEzB,iBAAiB,CAACkH,QAAQ,CAACZ,OAAO,CAAC,GACvC,aAAa3M,YAAY,CAACuH,YAAY,CAACgB,OAAO,EAAE,GAChD,aAAavI,YAAY,CAACuH,YAAY,CAACO,MAAM,EAAE;gBACnDN,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACU,OAAO;gBAC7CR,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG,IAAI;gBACrCiQ,SAAS,EAAE,oBAAoB3D,KAAK,GAAG,GAAG,QAAQ;gBAClDhL,SAAS,EAAE,aAAahJ,YAAY,CAACuH,YAAY,CAAC0B,MAAM;cAC1D,CAAE;cACF2J,OAAO,EAAEA,CAAA,KAAMlG,kBAAkB,CAACC,OAAO,CAAE;cAAA4F,QAAA,gBAG3CrQ,OAAA;gBACE0Q,OAAO,EAAGnH,CAAC,IAAK4B,cAAc,CAACV,OAAO,EAAElB,CAAC,CAAE;gBAC3CoC,KAAK,EAAE;kBACLkE,QAAQ,EAAE,UAAU;kBACpBuC,GAAG,EAAE,KAAK;kBACVE,KAAK,EAAE,KAAK;kBACZhN,UAAU,EAAE,MAAM;kBAClBM,MAAM,EAAE,MAAM;kBACd2K,MAAM,EAAE,SAAS;kBACjBhL,KAAK,EAAEpB,iBAAiB,CAACkH,QAAQ,CAACZ,OAAO,CAAC,GAAG,SAAS,GAAG,MAAM;kBAC/DrE,UAAU,EAAE,eAAe;kBAC3BkL,QAAQ,EAAE;gBACZ,CAAE;gBAAAjB,QAAA,eAEFrQ,OAAA,CAACb,OAAO;kBAACgX,IAAI,EAAEhS,iBAAiB,CAACkH,QAAQ,CAACZ,OAAO,CAAC,GAAG,cAAc,GAAG;gBAAO;kBAAAjK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC,eAGTX,OAAA;gBAAK2L,KAAK,EAAE;kBACV8F,KAAK,EAAE,MAAM;kBACbL,MAAM,EAAE,MAAM;kBACdX,YAAY,EAAE,KAAK;kBACnBnL,UAAU,EAAE,2BAA2BxH,YAAY,CAACuH,YAAY,CAACgB,OAAO,KAAKvI,YAAY,CAACuH,YAAY,CAAC+Q,WAAW,GAAG;kBACrH7Q,KAAK,EAAE,OAAO;kBACdwL,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBU,cAAc,EAAE,QAAQ;kBACxBJ,QAAQ,EAAE,MAAM;kBAChBD,UAAU,EAAE,GAAG;kBACf0B,YAAY,EAAE,MAAM;kBACpBjM,SAAS,EAAE,aAAahJ,YAAY,CAACuH,YAAY,CAAC0B,MAAM;gBAC1D,CAAE;gBAAAsJ,QAAA,EACC5F,OAAO,CAAC4L,MAAM,CAAC,CAAC;cAAC;gBAAA7V,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eAGNX,OAAA;gBAAK2L,KAAK,EAAE;kBACV0F,UAAU,EAAE,GAAG;kBACfiC,SAAS,EAAE,QAAQ;kBACnBhC,QAAQ,EAAE,MAAM;kBAChByB,YAAY,EAAE;gBAChB,CAAE;gBAAA1C,QAAA,EACC5F;cAAO;gBAAAjK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGNX,OAAA;gBAAK2L,KAAK,EAAE;kBACVoF,OAAO,EAAE,MAAM;kBACfW,cAAc,EAAE,eAAe;kBAC/BJ,QAAQ,EAAE,MAAM;kBAChBC,OAAO,EAAE,GAAG;kBACZ6D,SAAS,EAAE;gBACb,CAAE;gBAAA/E,QAAA,gBACArQ,OAAA;kBAAAqQ,QAAA,GAAM,eAAG,EAAC+D,IAAI,CAACE,KAAK,CAACF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,EAAC,YAAU;gBAAA;kBAAA7T,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/DX,OAAA;kBAAAqQ,QAAA,GAAM,SAAE,EAAC,CAAC+D,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEiC,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAA9V,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,EAGLqV,MAAM,CAACO,OAAO,CAAC1R,iBAAiB,CAAC,CAACxE,GAAG,CAAC,CAAC,CAAC6V,QAAQ,EAAEpR,SAAS,CAAC,KAAK;gBAChE,IAAIA,SAAS,CAACgH,IAAI,CAACjB,CAAC,IAAIA,CAAC,CAAC9J,WAAW,CAAC,CAAC,KAAK0J,OAAO,CAAC1J,WAAW,CAAC,CAAC,CAAC,EAAE;kBAClE,oBACEf,OAAA;oBAEE2L,KAAK,EAAE;sBACLkE,QAAQ,EAAE,UAAU;sBACpBuC,GAAG,EAAE,KAAK;sBACVC,IAAI,EAAE,KAAK;sBACX/M,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACgB,OAAO;sBAC7Cd,KAAK,EAAE,OAAO;sBACdiL,OAAO,EAAE,SAAS;sBAClBC,YAAY,EAAE,KAAK;sBACnBa,QAAQ,EAAE,MAAM;sBAChBD,UAAU,EAAE;oBACd,CAAE;oBAAAhB,QAAA,EAED6F;kBAAQ,GAbJA,QAAQ;oBAAA1V,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAcV,CAAC;gBAEV;gBACA,OAAO,IAAI;cACb,CAAC,CAAC;YAAA,GAhGGmR,KAAK;cAAAtR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiGP,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAGLiL,oBAAoB,CAAC,CAAC,CAAC4D,MAAM,KAAK,CAAC,iBAClCxP,OAAA;YAAK2L,KAAK,EAAE;cACV2H,SAAS,EAAE,QAAQ;cACnB9C,OAAO,EAAE,MAAM;cACfe,OAAO,EAAE,GAAG;cACZhM,KAAK,EAAE;YACT,CAAE;YAAA8K,QAAA,gBACArQ,OAAA;cAAK2L,KAAK,EAAE;gBAAE2F,QAAQ,EAAE,MAAM;gBAAEyB,YAAY,EAAE;cAAO,CAAE;cAAA1C,QAAA,EAAC;YAAE;cAAA7P,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChEX,OAAA;cAAI2L,KAAK,EAAE;gBAAEpG,KAAK,EAAE;cAAO,CAAE;cAAA8K,QAAA,EAAC;YAAkB;cAAA7P,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrDX,OAAA;cAAG2L,KAAK,EAAE;gBAAEpG,KAAK,EAAE;cAAO,CAAE;cAAA8K,QAAA,EAAC;YAA4C;cAAA7P,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAiB,SAAS,KAAK,SAAS,iBACtB5B,OAAA;QAAK2L,KAAK,EAAE;UAAE6E,OAAO,EAAE;QAAO,CAAE;QAAAH,QAAA,eAC9BrQ,OAAA;UAAK2L,KAAK,EAAEhE,QAAQ,CAAC,MAAM,CAAE;UAAA0I,QAAA,gBAC3BrQ,OAAA;YAAI2L,KAAK,EAAE;cAAEyJ,SAAS,EAAE;YAAE,CAAE;YAAA/E,QAAA,EAAC;UAAc;YAAA7P,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChDX,OAAA;YAAG2L,KAAK,EAAE;cAAE4F,OAAO,EAAE,GAAG;cAAEwB,YAAY,EAAE;YAAO,CAAE;YAAA1C,QAAA,EAAC;UAElD;YAAA7P,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJX,OAAA;YAAK2L,KAAK,EAAE;cACVoF,OAAO,EAAE,MAAM;cACfkC,mBAAmB,EAAE,uCAAuC;cAC5DzB,GAAG,EAAE;YACP,CAAE;YAAAnB,QAAA,EACCtL,WAAW,CAAC1E,GAAG,CAAC,CAACmW,IAAI,EAAE1E,KAAK,kBAC3B9R,OAAA;cAEE2L,KAAK,EAAEhE,QAAQ,CAAC,UAAU,CAAE;cAC5B+I,OAAO,EAAEA,CAAA,KAAMpE,YAAY,CAACkK,IAAI,CAACvR,IAAI,CAAE;cAAAoL,QAAA,gBAEvCrQ,OAAA;gBAAAqQ,QAAA,gBACErQ,OAAA;kBAAI2L,KAAK,EAAE;oBAAEmH,MAAM,EAAE;kBAAY,CAAE;kBAAAzC,QAAA,EAAEmG,IAAI,CAAC1V;gBAAK;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrDX,OAAA;kBAAG2L,KAAK,EAAE;oBACRmH,MAAM,EAAE,CAAC;oBACTxB,QAAQ,EAAE,MAAM;oBAChBC,OAAO,EAAE;kBACX,CAAE;kBAAAlB,QAAA,EACCmG,IAAI,CAACxR;gBAAW;kBAAAxE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNX,OAAA;gBAAK2L,KAAK,EAAE;kBAAEpG,KAAK,EAAE;gBAAU,CAAE;gBAAA8K,QAAA,eAC/BrQ,OAAA,CAACd,cAAc;kBAAC2R,IAAI,EAAE;gBAAG;kBAAArQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA,GAhBDmR,KAAK;cAAAtR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBP,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAiB,SAAS,KAAK,QAAQ,iBACrB5B,OAAA;QAAK2L,KAAK,EAAE;UAAE6E,OAAO,EAAE;QAAO,CAAE;QAAAH,QAAA,eAC9BrQ,OAAA,CAACxC,MAAM;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CACN,EACAiB,SAAS,KAAK,WAAW,iBACxB5B,OAAA;QAAK2L,KAAK,EAAE;UACV6E,OAAO,EAAE,WAAW;UACpBlL,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACoN,aAAa;UACnD9L,SAAS,EAAE;QACb,CAAE;QAAA0J,QAAA,eACArQ,OAAA;UAAK2L,KAAK,EAAE;YAAEkH,QAAQ,EAAE,QAAQ;YAAEC,MAAM,EAAE;UAAS,CAAE;UAAAzC,QAAA,gBAEnDrQ,OAAA;YAAK2L,KAAK,EAAE;cACVrG,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACU,OAAO;cAC7C0K,YAAY,EAAE,MAAM;cACpBD,OAAO,EAAE,MAAM;cACfuC,YAAY,EAAE,MAAM;cACpBnN,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACO,MAAM,EAAE;cACvDkB,SAAS,EAAE,cAAchJ,YAAY,CAACuH,YAAY,CAAC0B,MAAM,EAAE;cAC3D8I,QAAQ,EAAE;YACZ,CAAE;YAAAQ,QAAA,gBACArQ,OAAA;cAAK2L,KAAK,EAAE;gBACVkE,QAAQ,EAAE,UAAU;gBACpBuC,GAAG,EAAE,CAAC;gBACNC,IAAI,EAAE,CAAC;gBACPC,KAAK,EAAE,CAAC;gBACRlB,MAAM,EAAE,KAAK;gBACb9L,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAAC2N;cACxC;YAAE;cAAAxS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAELX,OAAA;cAAI2L,KAAK,EAAE;gBACTmH,MAAM,EAAE,YAAY;gBACpBxB,QAAQ,EAAE,QAAQ;gBAClBD,UAAU,EAAE,GAAG;gBACf9L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG,IAAI;gBACrC8N,SAAS,EAAE;cACb,CAAE;cAAAjD,QAAA,EAAC;YAEH;cAAA7P,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLX,OAAA;cAAG2L,KAAK,EAAE;gBACRmH,MAAM,EAAE,CAAC;gBACTxB,QAAQ,EAAE,QAAQ;gBAClB/L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAAC+N,SAAS;gBAC1CE,SAAS,EAAE;cACb,CAAE;cAAAjD,QAAA,EAAC;YAEH;cAAA7P,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGNX,OAAA;YAAK2L,KAAK,EAAE;cACVrG,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACU,OAAO;cAC7C0K,YAAY,EAAE,MAAM;cACpBD,OAAO,EAAE,QAAQ;cACjBuC,YAAY,EAAE,MAAM;cACpBnN,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACO,MAAM,EAAE;cACvDkB,SAAS,EAAE,cAAchJ,YAAY,CAACuH,YAAY,CAAC0B,MAAM;YAC3D,CAAE;YAAAsJ,QAAA,gBAEArQ,OAAA;cACE4J,EAAE,EAAC,0BAA0B;cAC7B6M,SAAS,EAAC,0BAA0B;cACpC9K,KAAK,EAAE;gBACLkE,QAAQ,EAAE,UAAU;gBACpBkB,OAAO,EAAE,MAAM;gBACfW,cAAc,EAAE,QAAQ;gBACxBV,UAAU,EAAE,QAAQ;gBACpB1L,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACiC,SAAS;gBAC/CmJ,YAAY,EAAE,MAAM;gBACpBD,OAAO,EAAE,QAAQ;gBACjBkC,QAAQ,EAAE;cACZ,CAAE;cAAArC,QAAA,gBAGFrQ,OAAA;gBACE4J,EAAE,EAAC,mBAAmB;gBACtB+B,KAAK,EAAE;kBACLkE,QAAQ,EAAE,UAAU;kBACpBuC,GAAG,EAAE,QAAQ;kBACbG,MAAM,EAAE,QAAQ;kBAChBjN,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACgB,OAAO;kBAC7CoK,YAAY,EAAE,KAAK;kBACnBrK,UAAU,EAAE,sBAAsB;kBAClCoM,MAAM,EAAE,CAAC;kBACT;kBACAH,IAAI,EAAEhP,YAAY,KAAK,SAAS,GAAG,QAAQ,GACrCA,YAAY,KAAK,WAAW,GAAG,0BAA0B,GACzD,0BAA0B;kBAChCoO,KAAK,EAAE,0BAA0B;kBACjC3K,SAAS,EAAE,aAAahJ,YAAY,CAACuH,YAAY,CAACgB,OAAO;gBAC3D;cAAE;gBAAA7F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGD,CACC;gBAAEiJ,EAAE,EAAE,SAAS;gBAAEyF,KAAK,EAAE,sBAAsB;gBAAEzO,IAAI,EAAE,IAAI;gBAAE8V,UAAU,EAAE;cAAU,CAAC,EACnF;gBAAE9M,EAAE,EAAE,WAAW;gBAAEyF,KAAK,EAAE,uBAAuB;gBAAEzO,IAAI,EAAE,IAAI;gBAAE8V,UAAU,EAAE;cAAY,CAAC,EACxF;gBAAE9M,EAAE,EAAE,OAAO;gBAAEyF,KAAK,EAAE,iBAAiB;gBAAEzO,IAAI,EAAE,KAAK;gBAAE8V,UAAU,EAAE;cAAQ,CAAC,CAC5E,CAACrW,GAAG,CAAC,CAACQ,GAAG,EAAEiR,KAAK,kBACf9R,OAAA;gBAEE0Q,OAAO,EAAEA,CAAA,KAAMpN,eAAe,CAACzC,GAAG,CAAC+I,EAAE,CAAE;gBACvC+G,YAAY,EAAGpH,CAAC,IAAK;kBACnB;kBACA,IAAIlG,YAAY,KAAKxC,GAAG,CAAC+I,EAAE,EAAE;oBAC3BL,CAAC,CAACE,MAAM,CAACkC,KAAK,CAAC1F,SAAS,GAAG,kBAAkB;oBAC7CsD,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACpG,KAAK,GAAGzH,YAAY,CAACuH,YAAY,CAACgB,OAAO;kBAC1D;gBACF,CAAE;gBACFuK,YAAY,EAAGrH,CAAC,IAAK;kBACnB;kBACA,IAAIlG,YAAY,KAAKxC,GAAG,CAAC+I,EAAE,EAAE;oBAC3BL,CAAC,CAACE,MAAM,CAACkC,KAAK,CAAC1F,SAAS,GAAG,eAAe;oBAC1CsD,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACpG,KAAK,GAAGzH,YAAY,CAACuH,YAAY,CAACG,IAAI;kBACvD;gBACF,CAAE;gBACFmR,OAAO,EAAGpN,CAAC,IAAK;kBACd;kBACAA,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACxE,OAAO,GAAG,aAAarJ,YAAY,CAACuH,YAAY,CAACgB,OAAO,EAAE;kBACzEkD,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACiL,aAAa,GAAG,KAAK;gBACtC,CAAE;gBACFC,MAAM,EAAGtN,CAAC,IAAK;kBACb;kBACAA,CAAC,CAACE,MAAM,CAACkC,KAAK,CAACxE,OAAO,GAAG,MAAM;gBACjC,CAAE;gBACF,cAAY,aAAatG,GAAG,CAACwO,KAAK,MAAO;gBACzC,iBAAehM,YAAY,KAAKxC,GAAG,CAAC+I,EAAG;gBACvCgD,IAAI,EAAC,KAAK;gBACVkK,QAAQ,EAAE,CAAE;gBACZnL,KAAK,EAAE;kBACLmF,IAAI,EAAE,CAAC;kBACPjB,QAAQ,EAAE,UAAU;kBACpB2C,MAAM,EAAE,CAAC;kBACThC,OAAO,EAAE,eAAe;kBACxB7J,SAAS,EAAE,MAAM;kBAAE;kBACnBf,MAAM,EAAE,MAAM;kBACdN,UAAU,EAAE,aAAa;kBACzBC,KAAK,EAAElC,YAAY,KAAKxC,GAAG,CAAC+I,EAAE,GAC1B,OAAO,GACP9L,YAAY,CAACuH,YAAY,CAACG,IAAI;kBAClC+K,MAAM,EAAE,SAAS;kBACjBe,QAAQ,EAAE/N,WAAW,GAAG,GAAG,GAAG,QAAQ,GAAG,MAAM;kBAC/C8N,UAAU,EAAEhO,YAAY,KAAKxC,GAAG,CAAC+I,EAAE,GAAG,GAAG,GAAG,GAAG;kBAC/C6G,YAAY,EAAE,KAAK;kBACnBrK,UAAU,EAAE,eAAe;kBAC3B2K,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBU,cAAc,EAAE,QAAQ;kBACxBF,GAAG,EAAE,QAAQ;kBACb8B,SAAS,EAAE,QAAQ;kBACnByD,UAAU,EAAE,QAAQ;kBACpBC,UAAU,EAAE,MAAM;kBAClBC,uBAAuB,EAAE,aAAa,CAAC;gBACzC,CAAE;gBAAA5G,QAAA,gBAGFrQ,OAAA;kBAAM2L,KAAK,EAAE;oBACXoF,OAAO,EAAExN,WAAW,GAAG,GAAG,GAAG,MAAM,GAAG;kBACxC,CAAE;kBAAA8M,QAAA,EACCxP,GAAG,CAACD;gBAAI;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACPX,OAAA;kBAAM2L,KAAK,EAAE;oBACXoF,OAAO,EAAExN,WAAW,GAAG,GAAG,GAAG,MAAM,GAAG;kBACxC,CAAE;kBAAA8M,QAAA,EACC9M,WAAW,GAAG,GAAG,GAAG1C,GAAG,CAAC6V,UAAU,GAAG7V,GAAG,CAACwO,KAAK,CAACnE,OAAO,CAACrK,GAAG,CAACD,IAAI,GAAG,GAAG,EAAE,EAAE;gBAAC;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC,eAEPX,OAAA;kBAAM2L,KAAK,EAAE;oBACXoF,OAAO,EAAExN,WAAW,GAAG,GAAG,GAAG,QAAQ,GAAG,MAAM;oBAC9C+N,QAAQ,EAAE;kBACZ,CAAE;kBAAAjB,QAAA,EACCxP,GAAG,CAACD;gBAAI;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA,GAxEFE,GAAG,CAAC+I,EAAE;gBAAApJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAyEL,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNX,OAAA;cAAK2L,KAAK,EAAE;gBACV2H,SAAS,EAAE,QAAQ;gBACnB8B,SAAS,EAAE,MAAM;gBACjB5E,OAAO,EAAE,QAAQ;gBACjBc,QAAQ,EAAE,QAAQ;gBAClB/L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAAC+N,SAAS;gBAC1CiC,SAAS,EAAE;cACb,CAAE;cAAAhF,QAAA,GACChN,YAAY,KAAK,SAAS,IAAI,+DAA+D,EAC7FA,YAAY,KAAK,WAAW,IAAI,8DAA8D,EAC9FA,YAAY,KAAK,OAAO,IAAI,qEAAqE;YAAA;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL0C,YAAY,KAAK,SAAS,iBACzBrD,OAAA;YAAK2L,KAAK,EAAE;cACVrG,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACU,OAAO;cAC7C0K,YAAY,EAAE,MAAM;cACpBD,OAAO,EAAE,MAAM;cACf5K,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACO,MAAM,EAAE;cACvDkB,SAAS,EAAE,cAAchJ,YAAY,CAACuH,YAAY,CAAC0B,MAAM;YAC3D,CAAE;YAAAsJ,QAAA,gBACArQ,OAAA;cAAI2L,KAAK,EAAE;gBACTmH,MAAM,EAAE,cAAc;gBACtBxB,QAAQ,EAAE,QAAQ;gBAClBD,UAAU,EAAE,GAAG;gBACf9L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG;cACnC,CAAE;cAAA6K,QAAA,EAAC;YAEH;cAAA7P,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAELX,OAAA;cAAK2L,KAAK,EAAE;gBAAEoH,YAAY,EAAE;cAAO,CAAE;cAAA1C,QAAA,eACnCrQ,OAAA;gBAAO2L,KAAK,EAAE;kBACZoF,OAAO,EAAE,aAAa;kBACtBC,UAAU,EAAE,QAAQ;kBACpBQ,GAAG,EAAE,QAAQ;kBACbhB,OAAO,EAAE,gBAAgB;kBACzBlL,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACgB,OAAO;kBAC7Cd,KAAK,EAAE,OAAO;kBACdkL,YAAY,EAAE,MAAM;kBACpBF,MAAM,EAAE7N,qBAAqB,GAAG,aAAa,GAAG,SAAS;kBACzD4O,QAAQ,EAAE,MAAM;kBAChBD,UAAU,EAAE,GAAG;kBACfjL,UAAU,EAAE;gBACd,CAAE;gBAAAiK,QAAA,gBACArQ,OAAA,CAACrB,QAAQ;kBAACkS,IAAI,EAAE;gBAAG;kBAAArQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACrB+B,qBAAqB,GAAG,cAAc,GAAG,iBAAiB,eAC3D1C,OAAA;kBACE+N,IAAI,EAAC,MAAM;kBACXgH,MAAM,EAAC,sBAAsB;kBAC7BC,QAAQ,EAAEzK,oBAAqB;kBAC/B0K,QAAQ,EAAEvS,qBAAsB;kBAChCiJ,KAAK,EAAE;oBAAEoF,OAAO,EAAE;kBAAO;gBAAE;kBAAAvQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENX,OAAA;cAAAqQ,QAAA,gBACErQ,OAAA;gBAAI2L,KAAK,EAAE;kBACTmH,MAAM,EAAE,YAAY;kBACpBxB,QAAQ,EAAE,QAAQ;kBAClBD,UAAU,EAAE,GAAG;kBACf9L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG;gBACnC,CAAE;gBAAA6K,QAAA,EAAC;cAEH;gBAAA7P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACJiC,aAAa,CAAC4M,MAAM,KAAK,CAAC,gBACzBxP,OAAA;gBAAK2L,KAAK,EAAE;kBACV2H,SAAS,EAAE,QAAQ;kBACnB9C,OAAO,EAAE,MAAM;kBACflL,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACiC,SAAS;kBAC/CmJ,YAAY,EAAE,MAAM;kBACpB7K,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACO,MAAM;gBACvD,CAAE;gBAAAyK,QAAA,gBACArQ,OAAA;kBAAK2L,KAAK,EAAE;oBAAE2F,QAAQ,EAAE,MAAM;oBAAEyB,YAAY,EAAE;kBAAO,CAAE;kBAAA1C,QAAA,EAAC;gBAAE;kBAAA7P,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAChEX,OAAA;kBAAI2L,KAAK,EAAE;oBACTmH,MAAM,EAAE,cAAc;oBACtBvN,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG;kBACnC,CAAE;kBAAA6K,QAAA,EAAC;gBAEH;kBAAA7P,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLX,OAAA;kBAAG2L,KAAK,EAAE;oBACRmH,MAAM,EAAE,CAAC;oBACTvN,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAAC+N;kBACnC,CAAE;kBAAA/C,QAAA,EAAC;gBAEH;kBAAA7P,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,gBAENX,OAAA;gBAAK2L,KAAK,EAAE;kBACVoF,OAAO,EAAE,MAAM;kBACfS,GAAG,EAAE;gBACP,CAAE;gBAAAnB,QAAA,EACCzN,aAAa,CAACvC,GAAG,CAAC,CAACmJ,IAAI,EAAEgM,GAAG,KAAK;kBAChC,MAAM;oBAAEhN,IAAI,EAAE0B;kBAAQ,CAAC,GAAGtF,QAAQ,CAACkF,OAAO,CAACC,IAAI,CAAC,WAAW,CAAC,CAACI,YAAY,CAAC,GAAG/H,IAAI,CAACwH,EAAE,IAAIJ,IAAI,CAACK,IAAI,EAAE,CAAC;kBACpG,oBACE7J,OAAA;oBAAe2L,KAAK,EAAE;sBACpBoF,OAAO,EAAE,MAAM;sBACfW,cAAc,EAAE,eAAe;sBAC/BV,UAAU,EAAE,QAAQ;sBACpBR,OAAO,EAAE,MAAM;sBACflL,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACiC,SAAS;sBAC/CmJ,YAAY,EAAE,MAAM;sBACpB7K,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACO,MAAM;oBACvD,CAAE;oBAAAyK,QAAA,gBACArQ,OAAA;sBAAK2L,KAAK,EAAE;wBACVoF,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpBQ,GAAG,EAAE;sBACP,CAAE;sBAAAnB,QAAA,gBACArQ,OAAA;wBAAK2L,KAAK,EAAE;0BACV8F,KAAK,EAAE,MAAM;0BACbL,MAAM,EAAE,MAAM;0BACdX,YAAY,EAAE,KAAK;0BACnBnL,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACgB,OAAO;0BAC7C0K,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpBU,cAAc,EAAE,QAAQ;0BACxBnM,KAAK,EAAE,OAAO;0BACd+L,QAAQ,EAAE;wBACZ,CAAE;wBAAAjB,QAAA,EAAC;sBAEH;wBAAA7P,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNX,OAAA;wBAAAqQ,QAAA,gBACErQ,OAAA;0BAAK2L,KAAK,EAAE;4BACV0F,UAAU,EAAE,GAAG;4BACf9L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG,IAAI;4BACrCuN,YAAY,EAAE;0BAChB,CAAE;0BAAA1C,QAAA,EACC7G,IAAI,CAACK;wBAAI;0BAAArJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,eACNX,OAAA;0BAAK2L,KAAK,EAAE;4BACV2F,QAAQ,EAAE,QAAQ;4BAClB/L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAAC+N;0BACnC,CAAE;0BAAA/C,QAAA,EAAC;wBAEH;0BAAA7P,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNX,OAAA;sBACEgL,IAAI,EAAEd,OAAO,CAACE,SAAU;sBACxBX,MAAM,EAAC,QAAQ;sBACfyL,GAAG,EAAC,qBAAqB;sBACzBvJ,KAAK,EAAE;wBACLoF,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpBQ,GAAG,EAAE,QAAQ;wBACbhB,OAAO,EAAE,aAAa;wBACtBlL,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACgB,OAAO;wBAC7Cd,KAAK,EAAE,OAAO;wBACd4P,cAAc,EAAE,MAAM;wBACtB1E,YAAY,EAAE,KAAK;wBACnBa,QAAQ,EAAE,QAAQ;wBAClBD,UAAU,EAAE,GAAG;wBACfjL,UAAU,EAAE;sBACd,CAAE;sBAAAiK,QAAA,gBAEFrQ,OAAA,CAACd,cAAc;wBAAC2R,IAAI,EAAE;sBAAG;wBAAArQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,QAE9B;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA,GA/DI6U,GAAG;oBAAAhV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAgER,CAAC;gBAEV,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGA0C,YAAY,KAAK,WAAW,iBAC3BrD,OAAA;YAAK2L,KAAK,EAAE;cACVoF,OAAO,EAAE,MAAM;cACfS,GAAG,EAAE;YACP,CAAE;YAAAnB,QAAA,gBAEArQ,OAAA;cAAK2L,KAAK,EAAE;gBACVrG,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACU,OAAO;gBAC7C0K,YAAY,EAAE,MAAM;gBACpBD,OAAO,EAAE,MAAM;gBACf5K,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACO,MAAM,EAAE;gBACvDkB,SAAS,EAAE,cAAchJ,YAAY,CAACuH,YAAY,CAAC0B,MAAM;cAC3D,CAAE;cAAAsJ,QAAA,gBACArQ,OAAA;gBAAI2L,KAAK,EAAE;kBACTmH,MAAM,EAAE,cAAc;kBACtBxB,QAAQ,EAAE,QAAQ;kBAClBD,UAAU,EAAE,GAAG;kBACf9L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG,IAAI;kBACrCuL,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBQ,GAAG,EAAE;gBACP,CAAE;gBAAAnB,QAAA,EAAC;cAEH;gBAAA7P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAELX,OAAA;gBAAK2L,KAAK,EAAE;kBACVoF,OAAO,EAAE,MAAM;kBACfkC,mBAAmB,EAAE,sCAAsC;kBAC3DzB,GAAG,EAAE;gBACP,CAAE;gBAAAnB,QAAA,EACC,CACC;kBACE6G,OAAO,EAAE,aAAa;kBACtBtW,IAAI,EAAE,IAAI;kBACV2E,KAAK,EAAE,SAAS;kBAChB4R,MAAM,EAAE,CAAC,UAAU,EAAE,gBAAgB,EAAE,YAAY,EAAE,eAAe,CAAC;kBACrEC,SAAS,EAAE;gBACb,CAAC,EACD;kBACEF,OAAO,EAAE,kBAAkB;kBAC3BtW,IAAI,EAAE,IAAI;kBACV2E,KAAK,EAAE,SAAS;kBAChB4R,MAAM,EAAE,CAAC,iBAAiB,EAAE,YAAY,EAAE,kBAAkB,EAAE,sBAAsB,CAAC;kBACrFC,SAAS,EAAE;gBACb,CAAC,EACD;kBACEF,OAAO,EAAE,SAAS;kBAClBtW,IAAI,EAAE,IAAI;kBACV2E,KAAK,EAAE,SAAS;kBAChB4R,MAAM,EAAE,CAAC,WAAW,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,iBAAiB,CAAC;kBAC9EC,SAAS,EAAE;gBACb,CAAC,EACD;kBACEF,OAAO,EAAE,WAAW;kBACpBtW,IAAI,EAAE,IAAI;kBACV2E,KAAK,EAAE,SAAS;kBAChB4R,MAAM,EAAE,CAAC,mBAAmB,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,cAAc,CAAC;kBAC1FC,SAAS,EAAE;gBACb,CAAC,CACF,CAAC/W,GAAG,CAAC,CAAC6W,OAAO,EAAEpF,KAAK,kBACnB9R,OAAA;kBAAiB2L,KAAK,EAAE;oBACtBrG,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACiC,SAAS;oBAC/CmJ,YAAY,EAAE,MAAM;oBACpBD,OAAO,EAAE,QAAQ;oBACjB5K,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACO,MAAM,EAAE;oBACvD2K,MAAM,EAAE,SAAS;oBACjBnK,UAAU,EAAE;kBACd,CAAE;kBACFuK,YAAY,EAAGpH,CAAC,IAAK;oBACnBA,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAAC1F,SAAS,GAAG,kBAAkB;oBACpDsD,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAAC7E,SAAS,GAAG,cAAchJ,YAAY,CAACuH,YAAY,CAAC0B,MAAM,EAAE;kBACpF,CAAE;kBACF6J,YAAY,EAAGrH,CAAC,IAAK;oBACnBA,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAAC1F,SAAS,GAAG,eAAe;oBACjDsD,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAAC7E,SAAS,GAAG,MAAM;kBAC1C,CAAE;kBAAAuJ,QAAA,gBACArQ,OAAA;oBAAK2L,KAAK,EAAE;sBACVoF,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBQ,GAAG,EAAE,MAAM;sBACXuB,YAAY,EAAE;oBAChB,CAAE;oBAAA1C,QAAA,gBACArQ,OAAA;sBAAK2L,KAAK,EAAE;wBACV8F,KAAK,EAAE,MAAM;wBACbL,MAAM,EAAE,MAAM;wBACdX,YAAY,EAAE,MAAM;wBACpBnL,UAAU,EAAE4R,OAAO,CAAC3R,KAAK;wBACzBwL,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpBU,cAAc,EAAE,QAAQ;wBACxBJ,QAAQ,EAAE;sBACZ,CAAE;sBAAAjB,QAAA,EACC6G,OAAO,CAACtW;oBAAI;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNX,OAAA;sBAAAqQ,QAAA,gBACErQ,OAAA;wBAAI2L,KAAK,EAAE;0BACTmH,MAAM,EAAE,CAAC;0BACTxB,QAAQ,EAAE,QAAQ;0BAClBD,UAAU,EAAE,GAAG;0BACf9L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG;wBACnC,CAAE;wBAAA6K,QAAA,EACC6G,OAAO,CAACA;sBAAO;wBAAA1W,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACd,CAAC,eACLX,OAAA;wBAAG2L,KAAK,EAAE;0BACRmH,MAAM,EAAE,CAAC;0BACTxB,QAAQ,EAAE,QAAQ;0BAClB/L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAAC+N;wBACnC,CAAE;wBAAA/C,QAAA,GACC6G,OAAO,CAACE,SAAS,EAAC,sBACrB;sBAAA;wBAAA5W,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENX,OAAA;oBAAK2L,KAAK,EAAE;sBACVoF,OAAO,EAAE,MAAM;sBACfsC,QAAQ,EAAE,MAAM;sBAChB7B,GAAG,EAAE,QAAQ;sBACbuB,YAAY,EAAE;oBAChB,CAAE;oBAAA1C,QAAA,EACC6G,OAAO,CAACC,MAAM,CAAC9W,GAAG,CAAC,CAACgX,KAAK,EAAEC,UAAU,kBACpCtX,OAAA;sBAAuB2L,KAAK,EAAE;wBAC5B6E,OAAO,EAAE,gBAAgB;wBACzBlL,UAAU,EAAE4R,OAAO,CAAC3R,KAAK,GAAG,IAAI;wBAChCA,KAAK,EAAE2R,OAAO,CAAC3R,KAAK;wBACpBkL,YAAY,EAAE,KAAK;wBACnBa,QAAQ,EAAE,QAAQ;wBAClBD,UAAU,EAAE;sBACd,CAAE;sBAAAhB,QAAA,EACCgH;oBAAK,GARGC,UAAU;sBAAA9W,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OASf,CACP;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eAENX,OAAA;oBAAQ2L,KAAK,EAAE;sBACb8F,KAAK,EAAE,MAAM;sBACbjB,OAAO,EAAE,SAAS;sBAClBlL,UAAU,EAAE4R,OAAO,CAAC3R,KAAK;sBACzBA,KAAK,EAAE,OAAO;sBACdK,MAAM,EAAE,MAAM;sBACd6K,YAAY,EAAE,KAAK;sBACnBa,QAAQ,EAAE,QAAQ;sBAClBD,UAAU,EAAE,GAAG;sBACfd,MAAM,EAAE,SAAS;sBACjBnK,UAAU,EAAE;oBACd,CAAE;oBAAAiK,QAAA,EAAC;kBAEH;oBAAA7P,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA,GAtFDmR,KAAK;kBAAAtR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAuFV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNX,OAAA;cAAK2L,KAAK,EAAE;gBACVrG,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACU,OAAO;gBAC7C0K,YAAY,EAAE,MAAM;gBACpBD,OAAO,EAAE,MAAM;gBACf5K,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACO,MAAM,EAAE;gBACvDkB,SAAS,EAAE,cAAchJ,YAAY,CAACuH,YAAY,CAAC0B,MAAM;cAC3D,CAAE;cAAAsJ,QAAA,gBACArQ,OAAA;gBAAI2L,KAAK,EAAE;kBACTmH,MAAM,EAAE,cAAc;kBACtBxB,QAAQ,EAAE,QAAQ;kBAClBD,UAAU,EAAE,GAAG;kBACf9L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG,IAAI;kBACrCuL,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBQ,GAAG,EAAE;gBACP,CAAE;gBAAAnB,QAAA,EAAC;cAEH;gBAAA7P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAELX,OAAA;gBAAK2L,KAAK,EAAE;kBACVoF,OAAO,EAAE,MAAM;kBACfkC,mBAAmB,EAAE,sCAAsC;kBAC3DzB,GAAG,EAAE;gBACP,CAAE;gBAAAnB,QAAA,EACC,CACC;kBAAExG,IAAI,EAAE,sBAAsB;kBAAEjJ,IAAI,EAAE,IAAI;kBAAE2W,KAAK,EAAE;gBAAa,CAAC,EACjE;kBAAE1N,IAAI,EAAE,kBAAkB;kBAAEjJ,IAAI,EAAE,GAAG;kBAAE2W,KAAK,EAAE;gBAAiB,CAAC,EAChE;kBAAE1N,IAAI,EAAE,cAAc;kBAAEjJ,IAAI,EAAE,IAAI;kBAAE2W,KAAK,EAAE;gBAAa,CAAC,EACzD;kBAAE1N,IAAI,EAAE,iBAAiB;kBAAEjJ,IAAI,EAAE,IAAI;kBAAE2W,KAAK,EAAE;gBAAa,CAAC,EAC5D;kBAAE1N,IAAI,EAAE,gBAAgB;kBAAEjJ,IAAI,EAAE,IAAI;kBAAE2W,KAAK,EAAE;gBAAc,CAAC,EAC5D;kBAAE1N,IAAI,EAAE,gBAAgB;kBAAEjJ,IAAI,EAAE,GAAG;kBAAE2W,KAAK,EAAE;gBAAY,CAAC,CAC1D,CAAClX,GAAG,CAAC,CAACmX,QAAQ,EAAE1F,KAAK,kBACpB9R,OAAA;kBAAiB2L,KAAK,EAAE;oBACtBoF,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBQ,GAAG,EAAE,MAAM;oBACXhB,OAAO,EAAE,MAAM;oBACflL,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACiC,SAAS;oBAC/CmJ,YAAY,EAAE,MAAM;oBACpB7K,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACO,MAAM,EAAE;oBACvD2K,MAAM,EAAE,SAAS;oBACjBnK,UAAU,EAAE;kBACd,CAAE;kBACFuK,YAAY,EAAGpH,CAAC,IAAK;oBACnBA,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAACrG,UAAU,GAAGxH,YAAY,CAACuH,YAAY,CAACgB,OAAO,GAAG,IAAI;oBAC3EkD,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAACzE,WAAW,GAAGpJ,YAAY,CAACuH,YAAY,CAACgB,OAAO;kBACvE,CAAE;kBACFuK,YAAY,EAAGrH,CAAC,IAAK;oBACnBA,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAACrG,UAAU,GAAGxH,YAAY,CAACuH,YAAY,CAACiC,SAAS;oBACtEiC,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAACzE,WAAW,GAAGpJ,YAAY,CAACuH,YAAY,CAACO,MAAM;kBACtE,CAAE;kBAAAyK,QAAA,gBACArQ,OAAA;oBAAK2L,KAAK,EAAE;sBACV8F,KAAK,EAAE,MAAM;sBACbL,MAAM,EAAE,MAAM;sBACdX,YAAY,EAAE,KAAK;sBACnBnL,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACgB,OAAO;sBAC7C0K,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBU,cAAc,EAAE,QAAQ;sBACxBJ,QAAQ,EAAE;oBACZ,CAAE;oBAAAjB,QAAA,EACCmH,QAAQ,CAAC5W;kBAAI;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACNX,OAAA;oBAAAqQ,QAAA,gBACErQ,OAAA;sBAAK2L,KAAK,EAAE;wBACV0F,UAAU,EAAE,GAAG;wBACf9L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG,IAAI;wBACrCuN,YAAY,EAAE;sBAChB,CAAE;sBAAA1C,QAAA,EACCmH,QAAQ,CAAC3N;oBAAI;sBAAArJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,eACNX,OAAA;sBAAK2L,KAAK,EAAE;wBACV2F,QAAQ,EAAE,QAAQ;wBAClB/L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAAC+N;sBACnC,CAAE;sBAAA/C,QAAA,EACCmH,QAAQ,CAACD;oBAAK;sBAAA/W,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GA7CEmR,KAAK;kBAAAtR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA8CV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGA0C,YAAY,KAAK,OAAO,iBACvBrD,OAAA;YAAK2L,KAAK,EAAE;cACVrG,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACU,OAAO;cAC7C0K,YAAY,EAAE,MAAM;cACpBD,OAAO,EAAE,MAAM;cACf5K,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACO,MAAM,EAAE;cACvDkB,SAAS,EAAE,cAAchJ,YAAY,CAACuH,YAAY,CAAC0B,MAAM;YAC3D,CAAE;YAAAsJ,QAAA,gBACArQ,OAAA;cAAI2L,KAAK,EAAE;gBACTmH,MAAM,EAAE,cAAc;gBACtBxB,QAAQ,EAAE,QAAQ;gBAClBD,UAAU,EAAE,GAAG;gBACf9L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG,IAAI;gBACrCuL,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBQ,GAAG,EAAE;cACP,CAAE;cAAAnB,QAAA,EAAC;YAEH;cAAA7P,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAELX,OAAA;cAAK2L,KAAK,EAAE;gBACVoF,OAAO,EAAE,MAAM;gBACfkC,mBAAmB,EAAE,sCAAsC;gBAC3DzB,GAAG,EAAE;cACP,CAAE;cAAAnB,QAAA,EACC,CACC;gBACExG,IAAI,EAAE,uBAAuB;gBAC7BjJ,IAAI,EAAE,IAAI;gBACVoE,WAAW,EAAE,8CAA8C;gBAC3DO,KAAK,EAAE;cACT,CAAC,EACD;gBACEsE,IAAI,EAAE,mBAAmB;gBACzBjJ,IAAI,EAAE,IAAI;gBACVoE,WAAW,EAAE,sDAAsD;gBACnEO,KAAK,EAAE;cACT,CAAC,EACD;gBACEsE,IAAI,EAAE,gBAAgB;gBACtBjJ,IAAI,EAAE,IAAI;gBACVoE,WAAW,EAAE,gDAAgD;gBAC7DO,KAAK,EAAE;cACT,CAAC,EACD;gBACEsE,IAAI,EAAE,aAAa;gBACnBjJ,IAAI,EAAE,GAAG;gBACToE,WAAW,EAAE,6CAA6C;gBAC1DO,KAAK,EAAE;cACT,CAAC,EACD;gBACEsE,IAAI,EAAE,aAAa;gBACnBjJ,IAAI,EAAE,IAAI;gBACVoE,WAAW,EAAE,2CAA2C;gBACxDO,KAAK,EAAE;cACT,CAAC,EACD;gBACEsE,IAAI,EAAE,kBAAkB;gBACxBjJ,IAAI,EAAE,IAAI;gBACVoE,WAAW,EAAE,qCAAqC;gBAClDO,KAAK,EAAE;cACT,CAAC,CACF,CAAClF,GAAG,CAAC,CAACoX,IAAI,EAAE3F,KAAK,kBAChB9R,OAAA;gBAAiB2L,KAAK,EAAE;kBACtBrG,UAAU,EAAExH,YAAY,CAACuH,YAAY,CAACiC,SAAS;kBAC/CmJ,YAAY,EAAE,MAAM;kBACpBD,OAAO,EAAE,QAAQ;kBACjB5K,MAAM,EAAE,aAAa9H,YAAY,CAACuH,YAAY,CAACO,MAAM,EAAE;kBACvD2K,MAAM,EAAE,SAAS;kBACjBnK,UAAU,EAAE;gBACd,CAAE;gBACFuK,YAAY,EAAGpH,CAAC,IAAK;kBACnBA,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAAC1F,SAAS,GAAG,kBAAkB;kBACpDsD,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAAC7E,SAAS,GAAG,cAAchJ,YAAY,CAACuH,YAAY,CAAC0B,MAAM,EAAE;gBACpF,CAAE;gBACF6J,YAAY,EAAGrH,CAAC,IAAK;kBACnBA,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAAC1F,SAAS,GAAG,eAAe;kBACjDsD,CAAC,CAACsK,aAAa,CAAClI,KAAK,CAAC7E,SAAS,GAAG,MAAM;gBAC1C,CAAE;gBAAAuJ,QAAA,gBACArQ,OAAA;kBAAK2L,KAAK,EAAE;oBACVoF,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBQ,GAAG,EAAE,MAAM;oBACXuB,YAAY,EAAE;kBAChB,CAAE;kBAAA1C,QAAA,gBACArQ,OAAA;oBAAK2L,KAAK,EAAE;sBACV8F,KAAK,EAAE,MAAM;sBACbL,MAAM,EAAE,MAAM;sBACdX,YAAY,EAAE,MAAM;sBACpBnL,UAAU,EAAEmS,IAAI,CAAClS,KAAK;sBACtBwL,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBU,cAAc,EAAE,QAAQ;sBACxBJ,QAAQ,EAAE;oBACZ,CAAE;oBAAAjB,QAAA,EACCoH,IAAI,CAAC7W;kBAAI;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACNX,OAAA;oBAAAqQ,QAAA,eACErQ,OAAA;sBAAI2L,KAAK,EAAE;wBACTmH,MAAM,EAAE,CAAC;wBACTxB,QAAQ,EAAE,QAAQ;wBAClBD,UAAU,EAAE,GAAG;wBACf9L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAACG;sBACnC,CAAE;sBAAA6K,QAAA,EACCoH,IAAI,CAAC5N;oBAAI;sBAAArJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENX,OAAA;kBAAG2L,KAAK,EAAE;oBACRmH,MAAM,EAAE,YAAY;oBACpBxB,QAAQ,EAAE,QAAQ;oBAClB/L,KAAK,EAAEzH,YAAY,CAACuH,YAAY,CAAC+N,SAAS;oBAC1CF,UAAU,EAAE;kBACd,CAAE;kBAAA7C,QAAA,EACCoH,IAAI,CAACzS;gBAAW;kBAAAxE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eAEJX,OAAA;kBAAQ2L,KAAK,EAAE;oBACb8F,KAAK,EAAE,MAAM;oBACbjB,OAAO,EAAE,SAAS;oBAClBlL,UAAU,EAAEmS,IAAI,CAAClS,KAAK;oBACtBA,KAAK,EAAE,OAAO;oBACdK,MAAM,EAAE,MAAM;oBACd6K,YAAY,EAAE,KAAK;oBACnBa,QAAQ,EAAE,QAAQ;oBAClBD,UAAU,EAAE,GAAG;oBACfd,MAAM,EAAE,SAAS;oBACjBnK,UAAU,EAAE;kBACd,CAAE;kBAAAiK,QAAA,EAAC;gBAEH;kBAAA7P,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,GApEDmR,KAAK;gBAAAtR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAqEV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EACAiB,SAAS,KAAK,WAAW,iBAAI5B,OAAA,CAACzC,KAAK;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACtCiB,SAAS,KAAK,KAAK,iBAAI5B,OAAA,CAAC1C,GAAG;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAC9BiB,SAAS,KAAK,OAAO,IAAI,CAAAQ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwP,KAAK,MAAK/O,WAAW,iBACnD7C,OAAA;QAAK2L,KAAK,EAAE;UAAE6E,OAAO,EAAE;QAAO,CAAE;QAAAH,QAAA,eAC9BrQ,OAAA;UAAK2L,KAAK,EAAEhE,QAAQ,CAAC,MAAM,CAAE;UAAA0I,QAAA,gBAC3BrQ,OAAA;YAAI2L,KAAK,EAAE;cACTyJ,SAAS,EAAE,CAAC;cACZ7P,KAAK,EAAE;YACT,CAAE;YAAA8K,QAAA,EAAC;UAAW;YAAA7P,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnBX,OAAA;YAAK2L,KAAK,EAAE;cACVoF,OAAO,EAAE,MAAM;cACfS,GAAG,EAAE,MAAM;cACXuB,YAAY,EAAE;YAChB,CAAE;YAAA1C,QAAA,gBACArQ,OAAA;cACE2L,KAAK,EAAE;gBACL,GAAGhE,QAAQ,CAAC,eAAe,CAAC;gBAC5BrC,UAAU,EAAEvC,QAAQ,KAAK,OAAO,GAC9BjF,YAAY,CAACuH,YAAY,CAACgB,OAAO,GAAG,aAAa;gBACnDd,KAAK,EAAExC,QAAQ,KAAK,OAAO,GACzB,OAAO,GAAG,MAAM;gBAClB6C,MAAM,EAAE;cACV,CAAE;cACF8K,OAAO,EAAEA,CAAA,KAAM1N,WAAW,CAAC,OAAO,CAAE;cAAAqN,QAAA,EACrC;YAED;cAAA7P,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTX,OAAA;cACE2L,KAAK,EAAE;gBACL,GAAGhE,QAAQ,CAAC,eAAe,CAAC;gBAC5BrC,UAAU,EAAEvC,QAAQ,KAAK,WAAW,GAClCjF,YAAY,CAACuH,YAAY,CAACgB,OAAO,GAAG,aAAa;gBACnDd,KAAK,EAAExC,QAAQ,KAAK,WAAW,GAC7B,OAAO,GAAG,MAAM;gBAClB6C,MAAM,EAAE;cACV,CAAE;cACF8K,OAAO,EAAEA,CAAA,KAAM1N,WAAW,CAAC,WAAW,CAAE;cAAAqN,QAAA,EACzC;YAED;cAAA7P,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAELoC,QAAQ,KAAK,OAAO,iBACnB/C,OAAA;YAAAqQ,QAAA,gBACErQ,OAAA;cAAI2L,KAAK,EAAE;gBACToH,YAAY,EAAE,MAAM;gBACpBxN,KAAK,EAAE;cACT,CAAE;cAAA8K,QAAA,EAAC;YAAS;cAAA7P,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjBX,OAAA;cAAK2L,KAAK,EAAE;gBACVvG,eAAe,EAAE,SAAS;gBAC1BQ,MAAM,EAAE,mBAAmB;gBAC3B6K,YAAY,EAAE,KAAK;gBACnBD,OAAO,EAAE;cACX,CAAE;cAAAH,QAAA,EACCvN,QAAQ,CAACzC,GAAG,CAAC,CAAC+B,IAAI,EAAEoT,GAAG,kBACtBxV,OAAA;gBAAe2L,KAAK,EAAE;kBACpB6E,OAAO,EAAE,MAAM;kBACf7K,YAAY,EAAE,gBAAgB;kBAC9BJ,KAAK,EAAE;gBACT,CAAE;gBAAA8K,QAAA,EACCjO,IAAI,CAACwP;cAAK,GALH4D,GAAG;gBAAAhV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMR,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEAoC,QAAQ,KAAK,WAAW,iBACvB/C,OAAA;YAAAqQ,QAAA,gBACErQ,OAAA;cAAI2L,KAAK,EAAE;gBACToH,YAAY,EAAE,MAAM;gBACpBxN,KAAK,EAAE;cACT,CAAE;cAAA8K,QAAA,EAAC;YAAa;cAAA7P,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBX,OAAA;cAAG2L,KAAK,EAAE;gBACR4F,OAAO,EAAE,GAAG;gBACZhM,KAAK,EAAE;cACT,CAAE;cAAA8K,QAAA,EAAC;YAA+B;cAAA7P,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,EAGNsC,YAAY,iBACXjD,OAAA;MAAK2L,KAAK,EAAE;QACV,GAAGhE,QAAQ,CAAC,cAAc,CAAC;QAC3BvC,eAAe,EAAEnC,YAAY,CAAC8K,IAAI,KAAK,OAAO,GAAG,SAAS,GAC3C9K,YAAY,CAAC8K,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS;QACtExI,KAAK,EAAE,OAAO;QACdK,MAAM,EAAE;MACV,CAAE;MAAAyK,QAAA,EACCpN,YAAY,CAAC6K;IAAG;MAAAtN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CACN,eAGDX,OAAA;MAAAqQ,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAA7P,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACM,EAAA,CA/gGID,YAAY;AAAA0W,EAAA,GAAZ1W,YAAY;AAghGlB,eAAeA,YAAY;AAAC,IAAA0W,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}