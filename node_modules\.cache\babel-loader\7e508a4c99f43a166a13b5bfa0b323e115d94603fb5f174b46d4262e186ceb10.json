{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\quiz\\\\aich (4)\\\\aich (3)\\\\aich(5)\\\\src\\\\AuthPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { initializeApp } from 'firebase/app';\nimport { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword } from 'firebase/auth';\nimport { getFirestore, doc, setDoc } from 'firebase/firestore';\nimport emailjs from 'emailjs-com';\nimport styles from './styles';\n\n// Firebase Config\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst firebaseConfig = {\n  apiKey: \"AIzaSyDmRAxMJvEKmel3JpUF1dXbBNccQJibcGo\",\n  authDomain: \"exploit-f41eb.firebaseapp.com\",\n  projectId: \"exploit-f41eb\",\n  storageBucket: \"exploit-f41eb.firebasestorage.app\",\n  messagingSenderId: \"849491574761\",\n  appId: \"1:849491574761:web:22ddb4ea62063d7a709578\",\n  measurementId: \"G-R0SWYQKXCV\"\n};\nconst app = initializeApp(firebaseConfig);\nconst auth = getAuth(app);\nconst db = getFirestore(app);\n\n// EmailJS Config\nconst EMAILJS_SERVICE_ID = 'service_sthdzci';\nconst EMAILJS_TEMPLATE_ID = 'template_8mnpzqq';\nconst EMAILJS_PUBLIC_KEY = 'jEfQd05ItbAthM3QN';\nconst AuthPage = ({\n  onAuthSuccess\n}) => {\n  _s();\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState('');\n  const [otp, setOtp] = useState('');\n  const [generatedOtp, setGeneratedOtp] = useState('');\n  const [isOtpSent, setIsOtpSent] = useState(false);\n  const [profilePic, setProfilePic] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isSignupMode, setIsSignupMode] = useState(true);\n  const [showPassword, setShowPassword] = useState(false);\n  const [particles, setParticles] = useState([]);\n\n  // Create floating particles effect\n  useEffect(() => {\n    const createParticles = () => {\n      const newParticles = [];\n      for (let i = 0; i < 15; i++) {\n        newParticles.push({\n          id: i,\n          x: Math.random() * 100,\n          y: Math.random() * 100,\n          size: Math.random() * 4 + 2,\n          speed: Math.random() * 2 + 1,\n          opacity: Math.random() * 0.5 + 0.2\n        });\n      }\n      setParticles(newParticles);\n    };\n    createParticles();\n  }, []);\n  const generateOtp = () => {\n    const otp = Math.floor(100000 + Math.random() * 900000); // Generate a 6-digit OTP\n    setGeneratedOtp(otp);\n    return otp;\n  };\n  const sendOtpEmail = (email, otp) => {\n    const templateParams = {\n      to_email: email,\n      otp: otp\n    };\n    emailjs.send(EMAILJS_SERVICE_ID, EMAILJS_TEMPLATE_ID, templateParams, EMAILJS_PUBLIC_KEY).then(response => {\n      console.log('OTP sent!', response.status, response.text);\n    }, err => {\n      console.error('OTP sending failed:', err);\n    });\n  };\n  const handleProfilePicChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setProfilePic(reader.result); // Store image in base64\n      };\n      reader.readAsDataURL(file); // Convert file to base64\n    }\n  };\n  const handleSignup = async () => {\n    if (!email || !password) {\n      setError('Please fill in all fields');\n      return;\n    }\n    setIsLoading(true);\n    setError('');\n    try {\n      const userCredential = await createUserWithEmailAndPassword(auth, email, password);\n      const otp = generateOtp();\n      sendOtpEmail(email, otp);\n      setIsOtpSent(true);\n\n      // Save user data to Firestore\n      const userRef = doc(db, \"users\", userCredential.user.uid);\n      await setDoc(userRef, {\n        email: email,\n        uid: userCredential.user.uid,\n        createdAt: new Date(),\n        dp: profilePic\n      });\n    } catch (error) {\n      setError(error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleOtpVerification = () => {\n    if (otp === generatedOtp.toString()) {\n      setIsOtpSent(false);\n      setOtp('');\n      setIsSignupMode(false);\n      setError('');\n      alert('OTP Verified! You can now login with your credentials.');\n    } else {\n      setError('Invalid OTP. Please try again.');\n    }\n  };\n  const handleLogin = async () => {\n    if (!email || !password) {\n      setError('Please fill in all fields');\n      return;\n    }\n    setIsLoading(true);\n    setError('');\n    try {\n      const userCredential = await signInWithEmailAndPassword(auth, email, password);\n      onAuthSuccess();\n    } catch (error) {\n      setError(error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.authPageContainer,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.authBackgroundAnimation\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this), particles.map(particle => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        left: `${particle.x}%`,\n        top: `${particle.y}%`,\n        width: `${particle.size}px`,\n        height: `${particle.size}px`,\n        background: styles.currentTheme.primary,\n        borderRadius: '50%',\n        opacity: particle.opacity,\n        animation: `floatParticle${particle.id % 2 + 1} ${12 + particle.speed * 3}s infinite linear`,\n        zIndex: 1,\n        pointerEvents: 'none'\n      }\n    }, particle.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 9\n    }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.authFormContainer,\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        style: styles.authFormTitle,\n        children: isSignupMode ? '✨ Create Account' : '🎯 Welcome Back'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: styles.authFormSubtitle,\n        children: isSignupMode ? '🚀 Join our learning platform and start your amazing journey' : '💫 Sign in to continue your learning adventure'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), !isOtpSent ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.authInputGroup,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'relative'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                position: 'absolute',\n                left: '1.25rem',\n                top: '50%',\n                transform: 'translateY(-50%)',\n                fontSize: '1.2rem',\n                color: styles.currentTheme.textLight,\n                zIndex: 1\n              },\n              children: \"\\uD83D\\uDCE7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              value: email,\n              onChange: e => setEmail(e.target.value),\n              placeholder: \"Enter your email address\",\n              style: {\n                ...styles.authInput,\n                paddingLeft: '3.5rem'\n              },\n              disabled: isLoading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.authInputGroup,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'relative'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                position: 'absolute',\n                left: '1.25rem',\n                top: '50%',\n                transform: 'translateY(-50%)',\n                fontSize: '1.2rem',\n                color: styles.currentTheme.textLight,\n                zIndex: 1\n              },\n              children: \"\\uD83D\\uDD12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: showPassword ? 'text' : 'password',\n              value: password,\n              onChange: e => setPassword(e.target.value),\n              placeholder: \"Enter your password\",\n              style: {\n                ...styles.authInput,\n                paddingLeft: '3.5rem',\n                paddingRight: '3.5rem'\n              },\n              disabled: isLoading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => setShowPassword(!showPassword),\n              style: {\n                position: 'absolute',\n                right: '1.25rem',\n                top: '50%',\n                transform: 'translateY(-50%)',\n                background: 'none',\n                border: 'none',\n                fontSize: '1.2rem',\n                cursor: 'pointer',\n                color: styles.currentTheme.textLight,\n                zIndex: 1\n              },\n              disabled: isLoading,\n              children: showPassword ? '🙈' : '👁️'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 13\n        }, this), isSignupMode && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.authInputGroup,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                padding: '1.5rem',\n                border: `2px dashed ${styles.currentTheme.primary}`,\n                borderRadius: '16px',\n                background: 'rgba(255, 255, 255, 0.5)',\n                cursor: 'pointer',\n                transition: 'all 0.3s ease',\n                ':hover': {\n                  background: 'rgba(255, 255, 255, 0.8)',\n                  transform: 'translateY(-2px)'\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '2rem',\n                  display: 'block',\n                  marginBottom: '0.5rem'\n                },\n                children: \"\\uD83D\\uDCF8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: styles.currentTheme.textLight,\n                  fontSize: '0.9rem'\n                },\n                children: profilePic ? 'Change Profile Picture' : 'Upload Profile Picture'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                onChange: handleProfilePicChange,\n                accept: \"image/*\",\n                style: {\n                  display: 'none'\n                },\n                disabled: isLoading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 19\n            }, this), profilePic && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '1rem',\n                position: 'relative',\n                display: 'inline-block'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: profilePic,\n                alt: \"Profile Preview\",\n                style: {\n                  ...styles.authProfilePreview,\n                  animation: 'fadeIn 0.5s ease'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: '-5px',\n                  right: '-5px',\n                  background: styles.currentTheme.primary,\n                  color: 'white',\n                  borderRadius: '50%',\n                  width: '24px',\n                  height: '24px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  fontSize: '0.8rem',\n                  animation: 'pulse 2s infinite'\n                },\n                children: \"\\u2713\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.authButtonGroup,\n          children: isSignupMode ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSignup,\n              disabled: isLoading,\n              style: styles.authButtonPrimary,\n              children: [isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.authLoadingSpinner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 35\n              }, this), isLoading ? 'Creating Account...' : 'Sign Up']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsSignupMode(false),\n              disabled: isLoading,\n              style: styles.authButtonSecondary,\n              children: \"Login Instead\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleLogin,\n              disabled: isLoading,\n              style: styles.authButtonPrimary,\n              children: [isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.authLoadingSpinner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 35\n              }, this), isLoading ? 'Signing In...' : 'Sign In']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsSignupMode(true),\n              disabled: isLoading,\n              style: styles.authButtonSecondary,\n              children: \"Create Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true) :\n      /*#__PURE__*/\n      /* OTP Verification Section */\n      _jsxDEV(\"div\", {\n        style: styles.authOtpContainer,\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: styles.authOtpTitle,\n          children: \"Verify Your Email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: styles.currentTheme.textLight,\n            marginBottom: '1rem'\n          },\n          children: [\"We've sent a 6-digit code to \", email]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: otp,\n          onChange: e => setOtp(e.target.value),\n          placeholder: \"Enter OTP\",\n          maxLength: \"6\",\n          style: styles.authOtpInput\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleOtpVerification,\n          style: styles.authOtpButton,\n          children: \"Verify OTP\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 11\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.authErrorMessage,\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 399,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 154,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthPage, \"RnwRUaX4FFNdnqE7BPeTZqfh50I=\");\n_c = AuthPage;\nexport default AuthPage;\nvar _c;\n$RefreshReg$(_c, \"AuthPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "initializeApp", "getAuth", "createUserWithEmailAndPassword", "signInWithEmailAndPassword", "getFirestore", "doc", "setDoc", "emailjs", "styles", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "measurementId", "app", "auth", "db", "EMAILJS_SERVICE_ID", "EMAILJS_TEMPLATE_ID", "EMAILJS_PUBLIC_KEY", "AuthPage", "onAuthSuccess", "_s", "email", "setEmail", "password", "setPassword", "error", "setError", "otp", "setOtp", "generatedOtp", "setGeneratedOtp", "isOtpSent", "setIsOtpSent", "profilePic", "setProfilePic", "isLoading", "setIsLoading", "isSignupMode", "setIsSignupMode", "showPassword", "setShowPassword", "particles", "setParticles", "createParticles", "newParticles", "i", "push", "id", "x", "Math", "random", "y", "size", "speed", "opacity", "generateOtp", "floor", "sendOtpEmail", "templateParams", "to_email", "send", "then", "response", "console", "log", "status", "text", "err", "handleProfilePicChange", "e", "file", "target", "files", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "handleSignup", "userCredential", "userRef", "user", "uid", "createdAt", "Date", "dp", "message", "handleOtpVerification", "toString", "alert", "handleLogin", "style", "authPageContainer", "children", "authBackgroundAnimation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "particle", "position", "left", "top", "width", "height", "background", "currentTheme", "primary", "borderRadius", "animation", "zIndex", "pointerEvents", "authFormContainer", "authFormTitle", "authFormSubtitle", "authInputGroup", "transform", "fontSize", "color", "textLight", "type", "value", "onChange", "placeholder", "authInput", "paddingLeft", "disabled", "paddingRight", "onClick", "right", "border", "cursor", "textAlign", "display", "padding", "transition", "marginBottom", "accept", "marginTop", "src", "alt", "authProfilePreview", "alignItems", "justifyContent", "authButtonGroup", "authButtonPrimary", "authLoading<PERSON><PERSON>ner", "authButtonSecondary", "authOtpContainer", "authOtpTitle", "max<PERSON><PERSON><PERSON>", "authOtpInput", "authOtpButton", "authErrorMessage", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(5)/src/AuthPage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { initializeApp } from 'firebase/app';\nimport { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword } from 'firebase/auth';\nimport { getFirestore, doc, setDoc } from 'firebase/firestore';\nimport emailjs from 'emailjs-com';\nimport styles from './styles';\n\n// Firebase Config\nconst firebaseConfig = {\n  apiKey: \"AIzaSyDmRAxMJvEKmel3JpUF1dXbBNccQJibcGo\",\n  authDomain: \"exploit-f41eb.firebaseapp.com\",\n  projectId: \"exploit-f41eb\",\n  storageBucket: \"exploit-f41eb.firebasestorage.app\",\n  messagingSenderId: \"849491574761\",\n  appId: \"1:849491574761:web:22ddb4ea62063d7a709578\",\n  measurementId: \"G-R0SWYQKXCV\"\n};\n\nconst app = initializeApp(firebaseConfig);\nconst auth = getAuth(app);\nconst db = getFirestore(app);\n\n// EmailJS Config\nconst EMAILJS_SERVICE_ID = 'service_sthdzci';\nconst EMAILJS_TEMPLATE_ID = 'template_8mnpzqq';\nconst EMAILJS_PUBLIC_KEY = 'jEfQd05ItbAthM3QN';\n\nconst AuthPage = ({ onAuthSuccess }) => {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState('');\n  const [otp, setOtp] = useState('');\n  const [generatedOtp, setGeneratedOtp] = useState('');\n  const [isOtpSent, setIsOtpSent] = useState(false);\n  const [profilePic, setProfilePic] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isSignupMode, setIsSignupMode] = useState(true);\n  const [showPassword, setShowPassword] = useState(false);\n  const [particles, setParticles] = useState([]);\n\n  // Create floating particles effect\n  useEffect(() => {\n    const createParticles = () => {\n      const newParticles = [];\n      for (let i = 0; i < 15; i++) {\n        newParticles.push({\n          id: i,\n          x: Math.random() * 100,\n          y: Math.random() * 100,\n          size: Math.random() * 4 + 2,\n          speed: Math.random() * 2 + 1,\n          opacity: Math.random() * 0.5 + 0.2,\n        });\n      }\n      setParticles(newParticles);\n    };\n    createParticles();\n  }, []);\n\n  const generateOtp = () => {\n    const otp = Math.floor(100000 + Math.random() * 900000); // Generate a 6-digit OTP\n    setGeneratedOtp(otp);\n    return otp;\n  };\n\n  const sendOtpEmail = (email, otp) => {\n    const templateParams = {\n      to_email: email,\n      otp: otp\n    };\n\n    emailjs.send(EMAILJS_SERVICE_ID, EMAILJS_TEMPLATE_ID, templateParams, EMAILJS_PUBLIC_KEY)\n      .then((response) => {\n        console.log('OTP sent!', response.status, response.text);\n      }, (err) => {\n        console.error('OTP sending failed:', err);\n      });\n  };\n\n  const handleProfilePicChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setProfilePic(reader.result); // Store image in base64\n      };\n      reader.readAsDataURL(file); // Convert file to base64\n    }\n  };\n\n  const handleSignup = async () => {\n    if (!email || !password) {\n      setError('Please fill in all fields');\n      return;\n    }\n\n    setIsLoading(true);\n    setError('');\n\n    try {\n      const userCredential = await createUserWithEmailAndPassword(auth, email, password);\n      const otp = generateOtp();\n      sendOtpEmail(email, otp);\n      setIsOtpSent(true);\n\n      // Save user data to Firestore\n      const userRef = doc(db, \"users\", userCredential.user.uid);\n      await setDoc(userRef, {\n        email: email,\n        uid: userCredential.user.uid,\n        createdAt: new Date(),\n        dp: profilePic,\n      });\n\n    } catch (error) {\n      setError(error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleOtpVerification = () => {\n    if (otp === generatedOtp.toString()) {\n      setIsOtpSent(false);\n      setOtp('');\n      setIsSignupMode(false);\n      setError('');\n      alert('OTP Verified! You can now login with your credentials.');\n    } else {\n      setError('Invalid OTP. Please try again.');\n    }\n  };\n\n  const handleLogin = async () => {\n    if (!email || !password) {\n      setError('Please fill in all fields');\n      return;\n    }\n\n    setIsLoading(true);\n    setError('');\n\n    try {\n      const userCredential = await signInWithEmailAndPassword(auth, email, password);\n      onAuthSuccess();\n    } catch (error) {\n      setError(error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div style={styles.authPageContainer}>\n      {/* Animated Background */}\n      <div style={styles.authBackgroundAnimation}></div>\n\n      {/* Floating Particles */}\n      {particles.map((particle) => (\n        <div\n          key={particle.id}\n          style={{\n            position: 'absolute',\n            left: `${particle.x}%`,\n            top: `${particle.y}%`,\n            width: `${particle.size}px`,\n            height: `${particle.size}px`,\n            background: styles.currentTheme.primary,\n            borderRadius: '50%',\n            opacity: particle.opacity,\n            animation: `floatParticle${(particle.id % 2) + 1} ${12 + particle.speed * 3}s infinite linear`,\n            zIndex: 1,\n            pointerEvents: 'none',\n          }}\n        />\n      ))}\n\n      {/* Main Form Container */}\n      <div style={styles.authFormContainer}>\n        <h1 style={styles.authFormTitle}>\n          {isSignupMode ? '✨ Create Account' : '🎯 Welcome Back'}\n        </h1>\n        <p style={styles.authFormSubtitle}>\n          {isSignupMode\n            ? '🚀 Join our learning platform and start your amazing journey'\n            : '💫 Sign in to continue your learning adventure'\n          }\n        </p>\n\n        {!isOtpSent ? (\n          <>\n            {/* Email Input */}\n            <div style={styles.authInputGroup}>\n              <div style={{ position: 'relative' }}>\n                <span style={{\n                  position: 'absolute',\n                  left: '1.25rem',\n                  top: '50%',\n                  transform: 'translateY(-50%)',\n                  fontSize: '1.2rem',\n                  color: styles.currentTheme.textLight,\n                  zIndex: 1,\n                }}>\n                  📧\n                </span>\n                <input\n                  type=\"email\"\n                  value={email}\n                  onChange={(e) => setEmail(e.target.value)}\n                  placeholder=\"Enter your email address\"\n                  style={{\n                    ...styles.authInput,\n                    paddingLeft: '3.5rem',\n                  }}\n                  disabled={isLoading}\n                />\n              </div>\n            </div>\n\n            {/* Password Input */}\n            <div style={styles.authInputGroup}>\n              <div style={{ position: 'relative' }}>\n                <span style={{\n                  position: 'absolute',\n                  left: '1.25rem',\n                  top: '50%',\n                  transform: 'translateY(-50%)',\n                  fontSize: '1.2rem',\n                  color: styles.currentTheme.textLight,\n                  zIndex: 1,\n                }}>\n                  🔒\n                </span>\n                <input\n                  type={showPassword ? 'text' : 'password'}\n                  value={password}\n                  onChange={(e) => setPassword(e.target.value)}\n                  placeholder=\"Enter your password\"\n                  style={{\n                    ...styles.authInput,\n                    paddingLeft: '3.5rem',\n                    paddingRight: '3.5rem',\n                  }}\n                  disabled={isLoading}\n                />\n                <button\n                  type=\"button\"\n                  onClick={() => setShowPassword(!showPassword)}\n                  style={{\n                    position: 'absolute',\n                    right: '1.25rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    background: 'none',\n                    border: 'none',\n                    fontSize: '1.2rem',\n                    cursor: 'pointer',\n                    color: styles.currentTheme.textLight,\n                    zIndex: 1,\n                  }}\n                  disabled={isLoading}\n                >\n                  {showPassword ? '🙈' : '👁️'}\n                </button>\n              </div>\n            </div>\n\n            {/* Profile Picture Upload (only for signup) */}\n            {isSignupMode && (\n              <div style={styles.authInputGroup}>\n                <div style={{ textAlign: 'center' }}>\n                  <label style={{\n                    display: 'block',\n                    padding: '1.5rem',\n                    border: `2px dashed ${styles.currentTheme.primary}`,\n                    borderRadius: '16px',\n                    background: 'rgba(255, 255, 255, 0.5)',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    ':hover': {\n                      background: 'rgba(255, 255, 255, 0.8)',\n                      transform: 'translateY(-2px)',\n                    }\n                  }}>\n                    <span style={{ fontSize: '2rem', display: 'block', marginBottom: '0.5rem' }}>\n                      📸\n                    </span>\n                    <span style={{ color: styles.currentTheme.textLight, fontSize: '0.9rem' }}>\n                      {profilePic ? 'Change Profile Picture' : 'Upload Profile Picture'}\n                    </span>\n                    <input\n                      type=\"file\"\n                      onChange={handleProfilePicChange}\n                      accept=\"image/*\"\n                      style={{ display: 'none' }}\n                      disabled={isLoading}\n                    />\n                  </label>\n                  {profilePic && (\n                    <div style={{ marginTop: '1rem', position: 'relative', display: 'inline-block' }}>\n                      <img\n                        src={profilePic}\n                        alt=\"Profile Preview\"\n                        style={{\n                          ...styles.authProfilePreview,\n                          animation: 'fadeIn 0.5s ease',\n                        }}\n                      />\n                      <div style={{\n                        position: 'absolute',\n                        top: '-5px',\n                        right: '-5px',\n                        background: styles.currentTheme.primary,\n                        color: 'white',\n                        borderRadius: '50%',\n                        width: '24px',\n                        height: '24px',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        fontSize: '0.8rem',\n                        animation: 'pulse 2s infinite',\n                      }}>\n                        ✓\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            )}\n\n            {/* Action Buttons */}\n            <div style={styles.authButtonGroup}>\n              {isSignupMode ? (\n                <>\n                  <button\n                    onClick={handleSignup}\n                    disabled={isLoading}\n                    style={styles.authButtonPrimary}\n                  >\n                    {isLoading && <div style={styles.authLoadingSpinner}></div>}\n                    {isLoading ? 'Creating Account...' : 'Sign Up'}\n                  </button>\n                  <button\n                    onClick={() => setIsSignupMode(false)}\n                    disabled={isLoading}\n                    style={styles.authButtonSecondary}\n                  >\n                    Login Instead\n                  </button>\n                </>\n              ) : (\n                <>\n                  <button\n                    onClick={handleLogin}\n                    disabled={isLoading}\n                    style={styles.authButtonPrimary}\n                  >\n                    {isLoading && <div style={styles.authLoadingSpinner}></div>}\n                    {isLoading ? 'Signing In...' : 'Sign In'}\n                  </button>\n                  <button\n                    onClick={() => setIsSignupMode(true)}\n                    disabled={isLoading}\n                    style={styles.authButtonSecondary}\n                  >\n                    Create Account\n                  </button>\n                </>\n              )}\n            </div>\n          </>\n        ) : (\n          /* OTP Verification Section */\n          <div style={styles.authOtpContainer}>\n            <h3 style={styles.authOtpTitle}>Verify Your Email</h3>\n            <p style={{ color: styles.currentTheme.textLight, marginBottom: '1rem' }}>\n              We've sent a 6-digit code to {email}\n            </p>\n            <input\n              type=\"text\"\n              value={otp}\n              onChange={(e) => setOtp(e.target.value)}\n              placeholder=\"Enter OTP\"\n              maxLength=\"6\"\n              style={styles.authOtpInput}\n            />\n            <button\n              onClick={handleOtpVerification}\n              style={styles.authOtpButton}\n            >\n              Verify OTP\n            </button>\n          </div>\n        )}\n\n        {/* Error Message */}\n        {error && (\n          <div style={styles.authErrorMessage}>\n            {error}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default AuthPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,QAAQ,cAAc;AAC5C,SAASC,OAAO,EAAEC,8BAA8B,EAAEC,0BAA0B,QAAQ,eAAe;AACnG,SAASC,YAAY,EAAEC,GAAG,EAAEC,MAAM,QAAQ,oBAAoB;AAC9D,OAAOC,OAAO,MAAM,aAAa;AACjC,OAAOC,MAAM,MAAM,UAAU;;AAE7B;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAc,GAAG;EACrBC,MAAM,EAAE,yCAAyC;EACjDC,UAAU,EAAE,+BAA+B;EAC3CC,SAAS,EAAE,eAAe;EAC1BC,aAAa,EAAE,mCAAmC;EAClDC,iBAAiB,EAAE,cAAc;EACjCC,KAAK,EAAE,2CAA2C;EAClDC,aAAa,EAAE;AACjB,CAAC;AAED,MAAMC,GAAG,GAAGrB,aAAa,CAACa,cAAc,CAAC;AACzC,MAAMS,IAAI,GAAGrB,OAAO,CAACoB,GAAG,CAAC;AACzB,MAAME,EAAE,GAAGnB,YAAY,CAACiB,GAAG,CAAC;;AAE5B;AACA,MAAMG,kBAAkB,GAAG,iBAAiB;AAC5C,MAAMC,mBAAmB,GAAG,kBAAkB;AAC9C,MAAMC,kBAAkB,GAAG,mBAAmB;AAE9C,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EACtC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkC,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoC,KAAK,EAAEC,QAAQ,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsC,GAAG,EAAEC,MAAM,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC0C,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC8C,SAAS,EAAEC,YAAY,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoD,SAAS,EAAEC,YAAY,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACAC,SAAS,CAAC,MAAM;IACd,MAAMqD,eAAe,GAAGA,CAAA,KAAM;MAC5B,MAAMC,YAAY,GAAG,EAAE;MACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3BD,YAAY,CAACE,IAAI,CAAC;UAChBC,EAAE,EAAEF,CAAC;UACLG,CAAC,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACtBC,CAAC,EAAEF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACtBE,IAAI,EAAEH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;UAC3BG,KAAK,EAAEJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;UAC5BI,OAAO,EAAEL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;QACjC,CAAC,CAAC;MACJ;MACAR,YAAY,CAACE,YAAY,CAAC;IAC5B,CAAC;IACDD,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMY,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAM5B,GAAG,GAAGsB,IAAI,CAACO,KAAK,CAAC,MAAM,GAAGP,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;IACzDpB,eAAe,CAACH,GAAG,CAAC;IACpB,OAAOA,GAAG;EACZ,CAAC;EAED,MAAM8B,YAAY,GAAGA,CAACpC,KAAK,EAAEM,GAAG,KAAK;IACnC,MAAM+B,cAAc,GAAG;MACrBC,QAAQ,EAAEtC,KAAK;MACfM,GAAG,EAAEA;IACP,CAAC;IAED7B,OAAO,CAAC8D,IAAI,CAAC7C,kBAAkB,EAAEC,mBAAmB,EAAE0C,cAAc,EAAEzC,kBAAkB,CAAC,CACtF4C,IAAI,CAAEC,QAAQ,IAAK;MAClBC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEF,QAAQ,CAACG,MAAM,EAAEH,QAAQ,CAACI,IAAI,CAAC;IAC1D,CAAC,EAAGC,GAAG,IAAK;MACVJ,OAAO,CAACtC,KAAK,CAAC,qBAAqB,EAAE0C,GAAG,CAAC;IAC3C,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,sBAAsB,GAAIC,CAAC,IAAK;IACpC,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAIF,IAAI,EAAE;MACR,MAAMG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACvBzC,aAAa,CAACuC,MAAM,CAACG,MAAM,CAAC,CAAC,CAAC;MAChC,CAAC;MACDH,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC,CAAC,CAAC;IAC9B;EACF,CAAC;EAED,MAAMQ,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACzD,KAAK,IAAI,CAACE,QAAQ,EAAE;MACvBG,QAAQ,CAAC,2BAA2B,CAAC;MACrC;IACF;IAEAU,YAAY,CAAC,IAAI,CAAC;IAClBV,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMqD,cAAc,GAAG,MAAMtF,8BAA8B,CAACoB,IAAI,EAAEQ,KAAK,EAAEE,QAAQ,CAAC;MAClF,MAAMI,GAAG,GAAG4B,WAAW,CAAC,CAAC;MACzBE,YAAY,CAACpC,KAAK,EAAEM,GAAG,CAAC;MACxBK,YAAY,CAAC,IAAI,CAAC;;MAElB;MACA,MAAMgD,OAAO,GAAGpF,GAAG,CAACkB,EAAE,EAAE,OAAO,EAAEiE,cAAc,CAACE,IAAI,CAACC,GAAG,CAAC;MACzD,MAAMrF,MAAM,CAACmF,OAAO,EAAE;QACpB3D,KAAK,EAAEA,KAAK;QACZ6D,GAAG,EAAEH,cAAc,CAACE,IAAI,CAACC,GAAG;QAC5BC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBC,EAAE,EAAEpD;MACN,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdC,QAAQ,CAACD,KAAK,CAAC6D,OAAO,CAAC;IACzB,CAAC,SAAS;MACRlD,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMmD,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI5D,GAAG,KAAKE,YAAY,CAAC2D,QAAQ,CAAC,CAAC,EAAE;MACnCxD,YAAY,CAAC,KAAK,CAAC;MACnBJ,MAAM,CAAC,EAAE,CAAC;MACVU,eAAe,CAAC,KAAK,CAAC;MACtBZ,QAAQ,CAAC,EAAE,CAAC;MACZ+D,KAAK,CAAC,wDAAwD,CAAC;IACjE,CAAC,MAAM;MACL/D,QAAQ,CAAC,gCAAgC,CAAC;IAC5C;EACF,CAAC;EAED,MAAMgE,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAACrE,KAAK,IAAI,CAACE,QAAQ,EAAE;MACvBG,QAAQ,CAAC,2BAA2B,CAAC;MACrC;IACF;IAEAU,YAAY,CAAC,IAAI,CAAC;IAClBV,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMqD,cAAc,GAAG,MAAMrF,0BAA0B,CAACmB,IAAI,EAAEQ,KAAK,EAAEE,QAAQ,CAAC;MAC9EJ,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,QAAQ,CAACD,KAAK,CAAC6D,OAAO,CAAC;IACzB,CAAC,SAAS;MACRlD,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,oBACEnC,OAAA;IAAK0F,KAAK,EAAE5F,MAAM,CAAC6F,iBAAkB;IAAAC,QAAA,gBAEnC5F,OAAA;MAAK0F,KAAK,EAAE5F,MAAM,CAAC+F;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAGjDzD,SAAS,CAAC0D,GAAG,CAAEC,QAAQ,iBACtBnG,OAAA;MAEE0F,KAAK,EAAE;QACLU,QAAQ,EAAE,UAAU;QACpBC,IAAI,EAAE,GAAGF,QAAQ,CAACpD,CAAC,GAAG;QACtBuD,GAAG,EAAE,GAAGH,QAAQ,CAACjD,CAAC,GAAG;QACrBqD,KAAK,EAAE,GAAGJ,QAAQ,CAAChD,IAAI,IAAI;QAC3BqD,MAAM,EAAE,GAAGL,QAAQ,CAAChD,IAAI,IAAI;QAC5BsD,UAAU,EAAE3G,MAAM,CAAC4G,YAAY,CAACC,OAAO;QACvCC,YAAY,EAAE,KAAK;QACnBvD,OAAO,EAAE8C,QAAQ,CAAC9C,OAAO;QACzBwD,SAAS,EAAE,gBAAiBV,QAAQ,CAACrD,EAAE,GAAG,CAAC,GAAI,CAAC,IAAI,EAAE,GAAGqD,QAAQ,CAAC/C,KAAK,GAAG,CAAC,mBAAmB;QAC9F0D,MAAM,EAAE,CAAC;QACTC,aAAa,EAAE;MACjB;IAAE,GAbGZ,QAAQ,CAACrD,EAAE;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAcjB,CACF,CAAC,eAGFjG,OAAA;MAAK0F,KAAK,EAAE5F,MAAM,CAACkH,iBAAkB;MAAApB,QAAA,gBACnC5F,OAAA;QAAI0F,KAAK,EAAE5F,MAAM,CAACmH,aAAc;QAAArB,QAAA,EAC7BxD,YAAY,GAAG,kBAAkB,GAAG;MAAiB;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eACLjG,OAAA;QAAG0F,KAAK,EAAE5F,MAAM,CAACoH,gBAAiB;QAAAtB,QAAA,EAC/BxD,YAAY,GACT,8DAA8D,GAC9D;MAAgD;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEnD,CAAC,EAEH,CAACnE,SAAS,gBACT9B,OAAA,CAAAE,SAAA;QAAA0F,QAAA,gBAEE5F,OAAA;UAAK0F,KAAK,EAAE5F,MAAM,CAACqH,cAAe;UAAAvB,QAAA,eAChC5F,OAAA;YAAK0F,KAAK,EAAE;cAAEU,QAAQ,EAAE;YAAW,CAAE;YAAAR,QAAA,gBACnC5F,OAAA;cAAM0F,KAAK,EAAE;gBACXU,QAAQ,EAAE,UAAU;gBACpBC,IAAI,EAAE,SAAS;gBACfC,GAAG,EAAE,KAAK;gBACVc,SAAS,EAAE,kBAAkB;gBAC7BC,QAAQ,EAAE,QAAQ;gBAClBC,KAAK,EAAExH,MAAM,CAAC4G,YAAY,CAACa,SAAS;gBACpCT,MAAM,EAAE;cACV,CAAE;cAAAlB,QAAA,EAAC;YAEH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPjG,OAAA;cACEwH,IAAI,EAAC,OAAO;cACZC,KAAK,EAAErG,KAAM;cACbsG,QAAQ,EAAGtD,CAAC,IAAK/C,QAAQ,CAAC+C,CAAC,CAACE,MAAM,CAACmD,KAAK,CAAE;cAC1CE,WAAW,EAAC,0BAA0B;cACtCjC,KAAK,EAAE;gBACL,GAAG5F,MAAM,CAAC8H,SAAS;gBACnBC,WAAW,EAAE;cACf,CAAE;cACFC,QAAQ,EAAE5F;YAAU;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNjG,OAAA;UAAK0F,KAAK,EAAE5F,MAAM,CAACqH,cAAe;UAAAvB,QAAA,eAChC5F,OAAA;YAAK0F,KAAK,EAAE;cAAEU,QAAQ,EAAE;YAAW,CAAE;YAAAR,QAAA,gBACnC5F,OAAA;cAAM0F,KAAK,EAAE;gBACXU,QAAQ,EAAE,UAAU;gBACpBC,IAAI,EAAE,SAAS;gBACfC,GAAG,EAAE,KAAK;gBACVc,SAAS,EAAE,kBAAkB;gBAC7BC,QAAQ,EAAE,QAAQ;gBAClBC,KAAK,EAAExH,MAAM,CAAC4G,YAAY,CAACa,SAAS;gBACpCT,MAAM,EAAE;cACV,CAAE;cAAAlB,QAAA,EAAC;YAEH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPjG,OAAA;cACEwH,IAAI,EAAElF,YAAY,GAAG,MAAM,GAAG,UAAW;cACzCmF,KAAK,EAAEnG,QAAS;cAChBoG,QAAQ,EAAGtD,CAAC,IAAK7C,WAAW,CAAC6C,CAAC,CAACE,MAAM,CAACmD,KAAK,CAAE;cAC7CE,WAAW,EAAC,qBAAqB;cACjCjC,KAAK,EAAE;gBACL,GAAG5F,MAAM,CAAC8H,SAAS;gBACnBC,WAAW,EAAE,QAAQ;gBACrBE,YAAY,EAAE;cAChB,CAAE;cACFD,QAAQ,EAAE5F;YAAU;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACFjG,OAAA;cACEwH,IAAI,EAAC,QAAQ;cACbQ,OAAO,EAAEA,CAAA,KAAMzF,eAAe,CAAC,CAACD,YAAY,CAAE;cAC9CoD,KAAK,EAAE;gBACLU,QAAQ,EAAE,UAAU;gBACpB6B,KAAK,EAAE,SAAS;gBAChB3B,GAAG,EAAE,KAAK;gBACVc,SAAS,EAAE,kBAAkB;gBAC7BX,UAAU,EAAE,MAAM;gBAClByB,MAAM,EAAE,MAAM;gBACdb,QAAQ,EAAE,QAAQ;gBAClBc,MAAM,EAAE,SAAS;gBACjBb,KAAK,EAAExH,MAAM,CAAC4G,YAAY,CAACa,SAAS;gBACpCT,MAAM,EAAE;cACV,CAAE;cACFgB,QAAQ,EAAE5F,SAAU;cAAA0D,QAAA,EAEnBtD,YAAY,GAAG,IAAI,GAAG;YAAK;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL7D,YAAY,iBACXpC,OAAA;UAAK0F,KAAK,EAAE5F,MAAM,CAACqH,cAAe;UAAAvB,QAAA,eAChC5F,OAAA;YAAK0F,KAAK,EAAE;cAAE0C,SAAS,EAAE;YAAS,CAAE;YAAAxC,QAAA,gBAClC5F,OAAA;cAAO0F,KAAK,EAAE;gBACZ2C,OAAO,EAAE,OAAO;gBAChBC,OAAO,EAAE,QAAQ;gBACjBJ,MAAM,EAAE,cAAcpI,MAAM,CAAC4G,YAAY,CAACC,OAAO,EAAE;gBACnDC,YAAY,EAAE,MAAM;gBACpBH,UAAU,EAAE,0BAA0B;gBACtC0B,MAAM,EAAE,SAAS;gBACjBI,UAAU,EAAE,eAAe;gBAC3B,QAAQ,EAAE;kBACR9B,UAAU,EAAE,0BAA0B;kBACtCW,SAAS,EAAE;gBACb;cACF,CAAE;cAAAxB,QAAA,gBACA5F,OAAA;gBAAM0F,KAAK,EAAE;kBAAE2B,QAAQ,EAAE,MAAM;kBAAEgB,OAAO,EAAE,OAAO;kBAAEG,YAAY,EAAE;gBAAS,CAAE;gBAAA5C,QAAA,EAAC;cAE7E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPjG,OAAA;gBAAM0F,KAAK,EAAE;kBAAE4B,KAAK,EAAExH,MAAM,CAAC4G,YAAY,CAACa,SAAS;kBAAEF,QAAQ,EAAE;gBAAS,CAAE;gBAAAzB,QAAA,EACvE5D,UAAU,GAAG,wBAAwB,GAAG;cAAwB;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACPjG,OAAA;gBACEwH,IAAI,EAAC,MAAM;gBACXE,QAAQ,EAAEvD,sBAAuB;gBACjCsE,MAAM,EAAC,SAAS;gBAChB/C,KAAK,EAAE;kBAAE2C,OAAO,EAAE;gBAAO,CAAE;gBAC3BP,QAAQ,EAAE5F;cAAU;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,EACPjE,UAAU,iBACThC,OAAA;cAAK0F,KAAK,EAAE;gBAAEgD,SAAS,EAAE,MAAM;gBAAEtC,QAAQ,EAAE,UAAU;gBAAEiC,OAAO,EAAE;cAAe,CAAE;cAAAzC,QAAA,gBAC/E5F,OAAA;gBACE2I,GAAG,EAAE3G,UAAW;gBAChB4G,GAAG,EAAC,iBAAiB;gBACrBlD,KAAK,EAAE;kBACL,GAAG5F,MAAM,CAAC+I,kBAAkB;kBAC5BhC,SAAS,EAAE;gBACb;cAAE;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFjG,OAAA;gBAAK0F,KAAK,EAAE;kBACVU,QAAQ,EAAE,UAAU;kBACpBE,GAAG,EAAE,MAAM;kBACX2B,KAAK,EAAE,MAAM;kBACbxB,UAAU,EAAE3G,MAAM,CAAC4G,YAAY,CAACC,OAAO;kBACvCW,KAAK,EAAE,OAAO;kBACdV,YAAY,EAAE,KAAK;kBACnBL,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACd6B,OAAO,EAAE,MAAM;kBACfS,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE,QAAQ;kBACxB1B,QAAQ,EAAE,QAAQ;kBAClBR,SAAS,EAAE;gBACb,CAAE;gBAAAjB,QAAA,EAAC;cAEH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDjG,OAAA;UAAK0F,KAAK,EAAE5F,MAAM,CAACkJ,eAAgB;UAAApD,QAAA,EAChCxD,YAAY,gBACXpC,OAAA,CAAAE,SAAA;YAAA0F,QAAA,gBACE5F,OAAA;cACEgI,OAAO,EAAEnD,YAAa;cACtBiD,QAAQ,EAAE5F,SAAU;cACpBwD,KAAK,EAAE5F,MAAM,CAACmJ,iBAAkB;cAAArD,QAAA,GAE/B1D,SAAS,iBAAIlC,OAAA;gBAAK0F,KAAK,EAAE5F,MAAM,CAACoJ;cAAmB;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAC1D/D,SAAS,GAAG,qBAAqB,GAAG,SAAS;YAAA;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACTjG,OAAA;cACEgI,OAAO,EAAEA,CAAA,KAAM3F,eAAe,CAAC,KAAK,CAAE;cACtCyF,QAAQ,EAAE5F,SAAU;cACpBwD,KAAK,EAAE5F,MAAM,CAACqJ,mBAAoB;cAAAvD,QAAA,EACnC;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT,CAAC,gBAEHjG,OAAA,CAAAE,SAAA;YAAA0F,QAAA,gBACE5F,OAAA;cACEgI,OAAO,EAAEvC,WAAY;cACrBqC,QAAQ,EAAE5F,SAAU;cACpBwD,KAAK,EAAE5F,MAAM,CAACmJ,iBAAkB;cAAArD,QAAA,GAE/B1D,SAAS,iBAAIlC,OAAA;gBAAK0F,KAAK,EAAE5F,MAAM,CAACoJ;cAAmB;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAC1D/D,SAAS,GAAG,eAAe,GAAG,SAAS;YAAA;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACTjG,OAAA;cACEgI,OAAO,EAAEA,CAAA,KAAM3F,eAAe,CAAC,IAAI,CAAE;cACrCyF,QAAQ,EAAE5F,SAAU;cACpBwD,KAAK,EAAE5F,MAAM,CAACqJ,mBAAoB;cAAAvD,QAAA,EACnC;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA,eACN,CAAC;MAAA;MAEH;MACAjG,OAAA;QAAK0F,KAAK,EAAE5F,MAAM,CAACsJ,gBAAiB;QAAAxD,QAAA,gBAClC5F,OAAA;UAAI0F,KAAK,EAAE5F,MAAM,CAACuJ,YAAa;UAAAzD,QAAA,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtDjG,OAAA;UAAG0F,KAAK,EAAE;YAAE4B,KAAK,EAAExH,MAAM,CAAC4G,YAAY,CAACa,SAAS;YAAEiB,YAAY,EAAE;UAAO,CAAE;UAAA5C,QAAA,GAAC,+BAC3C,EAACxE,KAAK;QAAA;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACJjG,OAAA;UACEwH,IAAI,EAAC,MAAM;UACXC,KAAK,EAAE/F,GAAI;UACXgG,QAAQ,EAAGtD,CAAC,IAAKzC,MAAM,CAACyC,CAAC,CAACE,MAAM,CAACmD,KAAK,CAAE;UACxCE,WAAW,EAAC,WAAW;UACvB2B,SAAS,EAAC,GAAG;UACb5D,KAAK,EAAE5F,MAAM,CAACyJ;QAAa;UAAAzD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACFjG,OAAA;UACEgI,OAAO,EAAE1C,qBAAsB;UAC/BI,KAAK,EAAE5F,MAAM,CAAC0J,aAAc;UAAA5D,QAAA,EAC7B;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGAzE,KAAK,iBACJxB,OAAA;QAAK0F,KAAK,EAAE5F,MAAM,CAAC2J,gBAAiB;QAAA7D,QAAA,EACjCpE;MAAK;QAAAsE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9E,EAAA,CA1XIF,QAAQ;AAAAyI,EAAA,GAARzI,QAAQ;AA4Xd,eAAeA,QAAQ;AAAC,IAAAyI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}