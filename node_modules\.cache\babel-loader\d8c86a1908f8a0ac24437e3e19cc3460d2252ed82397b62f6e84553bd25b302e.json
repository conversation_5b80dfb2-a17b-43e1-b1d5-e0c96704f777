{"ast": null, "code": "// styles.js - Centralized Theme System\n\n// Color Themes Configuration\nconst themes = {\n  mustardGreen: {\n    primary: '#8B7355',\n    primaryDark: '#6B5B47',\n    primaryLight: '#A0956B',\n    secondary: '#F5F3F0',\n    accent: '#9A8B73',\n    background: '#f0f4f8',\n    surface: '#ffffff',\n    text: '#2d3748',\n    textLight: '#718096',\n    textDark: '#1a202c',\n    border: '#e2e8f0',\n    shadow: 'rgba(139, 115, 85, 0.1)',\n    shadowDark: 'rgba(139, 115, 85, 0.3)',\n    gradient: 'linear-gradient(135deg, #8B7355 0%, #A0956B 100%)',\n    gradientReverse: 'linear-gradient(135deg, #A0956B 0%, #8B7355 100%)',\n    gradientLight: 'linear-gradient(135deg, #F5F3F0 0%, #E8E2D5 100%)'\n  },\n  redTheme: {\n    primary: '#DC2626',\n    primaryDark: '#B91C1C',\n    primaryLight: '#EF4444',\n    secondary: '#FEE2E2',\n    accent: '#F87171',\n    background: '#FEF2F2',\n    surface: '#ffffff',\n    text: '#2d3748',\n    textLight: '#718096',\n    textDark: '#1a202c',\n    border: '#e2e8f0',\n    shadow: 'rgba(220, 38, 38, 0.1)',\n    shadowDark: 'rgba(220, 38, 38, 0.3)',\n    gradient: 'linear-gradient(135deg, #DC2626 0%, #EF4444 100%)',\n    gradientReverse: 'linear-gradient(135deg, #EF4444 0%, #DC2626 100%)',\n    gradientLight: 'linear-gradient(135deg, #FEE2E2 0%, #FECACA 100%)'\n  },\n  softRedGradient: {\n    primary: '#FFB6B6',\n    primaryDark: '#FF99CC',\n    primaryLight: '#FFC5C5',\n    secondary: '#FFF0F0',\n    accent: '#FFE0E0',\n    background: '#FFF5F5',\n    surface: '#ffffff',\n    text: '#2d3748',\n    textLight: '#718096',\n    textDark: '#1a202c',\n    border: '#e2e8f0',\n    shadow: 'rgba(255, 182, 182, 0.1)',\n    shadowDark: 'rgba(255, 182, 182, 0.3)',\n    gradient: 'linear-gradient(135deg, #FFC5C5 0%, #FFB6B6 100%)',\n    gradientReverse: 'linear-gradient(135deg, #FFB6B6 0%, #FF99CC 100%)',\n    gradientLight: 'linear-gradient(135deg, #FFF0F0 0%, #FFE0E0 100%)'\n  },\n  purpleTheme: {\n    primary: '#7C3AED',\n    primaryDark: '#5B21B6',\n    primaryLight: '#8B5CF6',\n    secondary: '#F3E8FF',\n    accent: '#A78BFA',\n    background: '#FEFBFF',\n    surface: '#ffffff',\n    text: '#2d3748',\n    textLight: '#718096',\n    textDark: '#1a202c',\n    border: '#e2e8f0',\n    shadow: 'rgba(124, 58, 237, 0.1)',\n    shadowDark: 'rgba(124, 58, 237, 0.3)',\n    gradient: 'linear-gradient(135deg, #7C3AED 0%, #8B5CF6 100%)',\n    gradientReverse: 'linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%)',\n    gradientLight: 'linear-gradient(135deg, #F3E8FF 0%, #E9D5FF 100%)'\n  },\n  darkMode: {\n    primary: '#8B7355',\n    primaryDark: '#6B5B47',\n    primaryLight: '#A0956B',\n    secondary: '#252525',\n    accent: '#333333',\n    background: '#121212',\n    surface: '#1e1e1e',\n    text: '#e0e0e0',\n    textLight: '#a0a0a0',\n    textDark: '#ffffff',\n    border: '#333333',\n    shadow: 'rgba(0, 0, 0, 0.3)',\n    shadowDark: 'rgba(0, 0, 0, 0.5)',\n    gradient: 'linear-gradient(135deg, #8B7355 0%, #A0956B 100%)',\n    gradientReverse: 'linear-gradient(135deg, #A0956B 0%, #8B7355 100%)',\n    gradientLight: 'linear-gradient(135deg, #252525 0%, #333333 100%)'\n  }\n};\n\n// Current theme selector - can be changed to switch themes\nconst currentTheme = themes.purpleTheme; // Change this to switch themes globally\n\n// Helper function to get theme colors\nconst getTheme = (themeName = 'purpleTheme') => {\n  return themes[themeName] || themes.purpleTheme;\n};\n\n// Helper function to switch themes globally\nconst switchTheme = themeName => {\n  if (themes[themeName]) {\n    // Update currentTheme reference\n    Object.assign(currentTheme, themes[themeName]);\n    return true;\n  }\n  return false;\n};\n\n// Helper function to get all available theme names\nconst getAvailableThemes = () => {\n  return Object.keys(themes);\n};\nconst styles = {\n  // Theme configuration\n  themes,\n  currentTheme,\n  getTheme,\n  switchTheme,\n  getAvailableThemes,\n  // Base styles\n  background: {\n    minHeight: \"100vh\",\n    backgroundColor: currentTheme.background,\n    display: \"flex\",\n    flexDirection: \"column\",\n    fontFamily: \"'Inter', -apple-system, BlinkMacSystemFont, sans-serif\",\n    position: \"relative\",\n    overflowX: \"hidden\"\n  },\n  animatedBackground: {\n    position: \"fixed\",\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    background: \"linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)\",\n    zIndex: 0,\n    \"::before\": {\n      content: '\"\"',\n      position: \"absolute\",\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      backgroundImage: `\n          radial-gradient(circle at 10% 20%, rgba(255, 94, 94, 0.1) 0%, transparent 20%),\n          radial-gradient(circle at 90% 30%, rgba(94, 163, 255, 0.1) 0%, transparent 25%),\n          radial-gradient(circle at 50% 80%, rgba(255, 215, 94, 0.1) 0%, transparent 20%)\n        `,\n      animation: \"moveBackground 20s infinite alternate\"\n    }\n  },\n  navbar: {\n    width: \"100%\",\n    background: currentTheme.gradient,\n    boxShadow: `0 4px 6px ${currentTheme.shadow}`,\n    padding: \"0.75rem 2rem\",\n    position: \"sticky\",\n    top: 0,\n    zIndex: 1000,\n    backdropFilter: \"blur(8px)\",\n    borderBottom: `1px solid ${currentTheme.border}`\n  },\n  navContainer: {\n    width: \"100%\",\n    maxWidth: \"1400px\",\n    margin: \"0 auto\",\n    display: \"flex\",\n    justifyContent: \"space-between\",\n    alignItems: \"center\"\n  },\n  menuButton: {\n    background: \"transparent\",\n    border: \"none\",\n    cursor: \"pointer\",\n    color: \"#4a5568\",\n    padding: \"0.5rem\",\n    borderRadius: \"8px\",\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    transition: \"all 0.2s ease\",\n    \":hover\": {\n      background: \"rgba(0, 0, 0, 0.05)\",\n      color: \"#3182ce\"\n    },\n    \"@media (min-width: 768px)\": {\n      display: \"none\"\n    }\n  },\n  centerTitleContainer: {\n    display: \"flex\",\n    flexDirection: \"column\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    flex: 1,\n    textAlign: \"center\"\n  },\n  mainTitle: {\n    fontSize: \"1.25rem\",\n    fontWeight: \"bold\",\n    background: currentTheme.gradient,\n    WebkitBackgroundClip: \"text\",\n    WebkitTextFillColor: \"transparent\",\n    letterSpacing: \"0.5px\",\n    textTransform: \"uppercase\"\n  },\n  subTitle: {\n    fontSize: \"0.75rem\",\n    color: currentTheme.textLight,\n    marginTop: \"0.25rem\",\n    fontWeight: 500,\n    letterSpacing: \"0.5px\"\n  },\n  rightLogoContainer: {\n    display: \"flex\",\n    alignItems: \"center\"\n  },\n  logoImage: {\n    width: \"40px\",\n    height: \"40px\",\n    borderRadius: \"50%\",\n    objectFit: \"cover\",\n    border: `2px solid ${currentTheme.primary}`,\n    boxShadow: `0 2px 4px ${currentTheme.shadow}`\n  },\n  sidebar: {\n    position: \"fixed\",\n    top: 0,\n    left: 0,\n    width: \"280px\",\n    height: \"100vh\",\n    backgroundColor: currentTheme.surface,\n    boxShadow: `4px 0 15px ${currentTheme.shadow}`,\n    zIndex: 1100,\n    transition: \"transform 0.3s ease-in-out\",\n    display: \"flex\",\n    flexDirection: \"column\",\n    overflowY: \"auto\"\n  },\n  sidebarHeader: {\n    padding: \"1.5rem 1.5rem 1rem\",\n    display: \"flex\",\n    justifyContent: \"space-between\",\n    alignItems: \"center\",\n    borderBottom: `1px solid ${currentTheme.border}`\n  },\n  sidebarTitle: {\n    fontSize: \"1.25rem\",\n    fontWeight: \"600\",\n    color: currentTheme.text\n  },\n  closeSidebarButton: {\n    background: \"transparent\",\n    border: \"none\",\n    cursor: \"pointer\",\n    color: \"#4a5568\",\n    padding: \"0.25rem\",\n    borderRadius: \"4px\",\n    transition: \"all 0.2s ease\",\n    \":hover\": {\n      background: \"rgba(0, 0, 0, 0.05)\",\n      color: \"#3182ce\"\n    }\n  },\n  sidebarContent: {\n    flex: 1,\n    padding: \"1rem 0\",\n    overflowY: \"auto\"\n  },\n  sidebarItemGroup: {\n    marginBottom: \"0.5rem\"\n  },\n  sidebarItem: {\n    display: \"flex\",\n    alignItems: \"center\",\n    padding: \"0.75rem 1.5rem\",\n    cursor: \"pointer\",\n    transition: \"all 0.2s ease\",\n    position: \"relative\",\n    \":hover\": {\n      background: `${currentTheme.shadow}`,\n      color: currentTheme.primary\n    }\n  },\n  sidebarItemActive: {\n    background: `${currentTheme.shadow}`,\n    color: currentTheme.primary,\n    fontWeight: \"500\"\n  },\n  sidebarIcon: {\n    marginRight: \"1rem\",\n    fontSize: \"1.1rem\",\n    display: \"flex\",\n    alignItems: \"center\"\n  },\n  sidebarItemText: {\n    flex: 1\n  },\n  sidebarExpandIcon: {\n    transition: \"transform 0.2s ease\",\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    padding: \"0.25rem\",\n    borderRadius: \"4px\",\n    \":hover\": {\n      background: \"rgba(0, 0, 0, 0.05)\"\n    }\n  },\n  subItemsContainer: {\n    paddingLeft: \"2.5rem\",\n    overflow: \"hidden\",\n    transition: \"all 0.3s ease\"\n  },\n  subItem: {\n    padding: \"0.65rem 1.5rem\",\n    fontSize: \"0.9rem\",\n    color: currentTheme.textLight,\n    cursor: \"pointer\",\n    transition: \"all 0.2s ease\",\n    \":hover\": {\n      background: `${currentTheme.shadow}`,\n      color: currentTheme.primary\n    }\n  },\n  sidebarFooter: {\n    padding: \"1.5rem\",\n    borderTop: \"1px solid rgba(0, 0, 0, 0.05)\",\n    textAlign: \"center\"\n  },\n  sidebarFooterText: {\n    fontSize: \"0.8rem\",\n    color: \"#718096\"\n  },\n  overlay: {\n    position: \"fixed\",\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    backgroundColor: \"rgba(0, 0, 0, 0.5)\",\n    zIndex: 1090,\n    \"@media (min-width: 768px)\": {\n      display: \"none\"\n    }\n  },\n  mainContainer: {\n    width: \"100%\",\n    maxWidth: \"1400px\",\n    margin: \"2rem auto\",\n    padding: \"0 2rem\",\n    flex: 1,\n    display: \"flex\",\n    justifyContent: \"center\",\n    alignItems: \"flex-start\",\n    position: \"relative\",\n    zIndex: 1,\n    \"@media (max-width: 768px)\": {\n      margin: \"1rem auto\",\n      padding: \"0 1rem\"\n    }\n  },\n  resumeContainer: {\n    width: \"100%\",\n    maxWidth: \"800px\",\n    backgroundColor: currentTheme.surface,\n    borderRadius: \"16px\",\n    overflow: \"hidden\",\n    boxShadow: `0 10px 25px ${currentTheme.shadow}`,\n    position: \"relative\",\n    border: `1px solid ${currentTheme.border}`,\n    display: \"flex\",\n    flexDirection: \"column\",\n    height: \"calc(100vh - 120px)\",\n    \"@media (max-width: 768px)\": {\n      height: \"calc(100vh - 100px)\",\n      borderRadius: \"12px\"\n    },\n    \"@media (max-width: 480px)\": {\n      height: \"calc(100vh - 80px)\",\n      borderRadius: \"8px\"\n    }\n  },\n  gridOverlay: {\n    position: \"absolute\",\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    backgroundImage: `\n        linear-gradient(rgba(139, 115, 85, 0.03) 1px, transparent 1px),\n        linear-gradient(90deg, rgba(139, 115, 85, 0.03) 1px, transparent 1px)\n      `,\n    backgroundSize: \"20px 20px\",\n    pointerEvents: \"none\"\n  },\n  header: {\n    background: currentTheme.gradient,\n    color: \"white\",\n    padding: \"1.75rem 2rem\",\n    textAlign: \"center\",\n    position: \"relative\",\n    overflow: \"hidden\",\n    \"@media (max-width: 768px)\": {\n      padding: \"1.5rem 1.5rem\"\n    },\n    \"@media (max-width: 480px)\": {\n      padding: \"1.25rem 1rem\"\n    },\n    \"::before\": {\n      content: '\"\"',\n      position: \"absolute\",\n      top: \"-50%\",\n      left: \"-50%\",\n      right: \"-50%\",\n      bottom: \"-50%\",\n      background: `\n              radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)\n          `,\n      transform: \"rotate(30deg)\",\n      animation: \"shine 3s infinite\"\n    }\n  },\n  title: {\n    margin: 0,\n    fontSize: \"1.75rem\",\n    fontWeight: 700,\n    position: \"relative\",\n    textShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n  },\n  subtitle: {\n    marginTop: \"0.75rem\",\n    fontSize: \"1rem\",\n    opacity: 0.9,\n    position: \"relative\",\n    fontWeight: 400\n  },\n  chatContainer: {\n    height: \"calc(100vh - 200px)\",\n    maxHeight: \"800px\",\n    minHeight: \"400px\",\n    overflowY: \"auto\",\n    padding: \"1.5rem\",\n    background: currentTheme.surface,\n    position: \"relative\",\n    display: \"flex\",\n    flexDirection: \"column\",\n    width: \"100%\",\n    boxSizing: \"border-box\",\n    \"@media (max-width: 1024px)\": {\n      height: \"calc(100vh - 160px)\",\n      padding: \"1rem\"\n    },\n    \"@media (max-width: 768px)\": {\n      height: \"calc(100vh - 140px)\",\n      padding: \"0.75rem\",\n      minHeight: \"300px\"\n    },\n    \"@media (max-width: 480px)\": {\n      height: \"calc(100vh - 120px)\",\n      padding: \"0.5rem\",\n      minHeight: \"180px\"\n    }\n  },\n  chatMessages: {\n    display: \"flex\",\n    flexDirection: \"column\",\n    gap: \"1rem\",\n    position: \"relative\",\n    flex: 1,\n    overflowY: \"auto\",\n    paddingRight: \"0.5rem\",\n    scrollBehavior: \"smooth\",\n    width: \"100%\",\n    boxSizing: \"border-box\",\n    \"@media (max-width: 768px)\": {\n      gap: \"0.5rem\",\n      paddingRight: \"0.25rem\"\n    },\n    \"@media (max-width: 480px)\": {\n      gap: \"0.25rem\",\n      paddingRight: 0\n    }\n  },\n  message: {\n    padding: \"1rem 1.25rem\",\n    borderRadius: \"12px\",\n    maxWidth: \"85%\",\n    width: \"fit-content\",\n    boxShadow: \"0 1px 3px rgba(0,0,0,0.1)\",\n    lineHeight: 1.6,\n    position: \"relative\",\n    transition: \"all 0.3s ease\",\n    fontSize: \"1rem\",\n    animation: \"fadeIn 0.3s ease-out\",\n    wordWrap: \"break-word\",\n    \"@media (max-width: 768px)\": {\n      maxWidth: \"90%\",\n      padding: \"0.875rem 1rem\"\n    },\n    \"@media (max-width: 480px)\": {\n      maxWidth: \"95%\",\n      padding: \"0.75rem 0.875rem\",\n      fontSize: \"0.95rem\"\n    }\n  },\n  welcomeMessage: {\n    background: currentTheme.secondary,\n    padding: \"1.5rem\",\n    borderRadius: \"12px\",\n    textAlign: \"center\",\n    margin: \"0.5rem 0 1.5rem\",\n    border: `1px dashed ${currentTheme.primary}`,\n    backdropFilter: \"blur(5px)\",\n    animation: \"fadeIn 0.8s ease-out\"\n  },\n  welcomeTitle: {\n    fontSize: \"1.25rem\",\n    fontWeight: 600,\n    color: currentTheme.primary,\n    marginBottom: \"0.75rem\"\n  },\n  welcomeText: {\n    fontSize: \"1rem\",\n    color: currentTheme.textLight,\n    lineHeight: 1.6\n  },\n  userMessage: {\n    alignSelf: \"flex-end\",\n    background: currentTheme.gradient,\n    color: \"white\",\n    borderBottomRightRadius: \"4px\"\n  },\n  botMessage: {\n    alignSelf: \"flex-start\",\n    background: currentTheme.secondary,\n    color: currentTheme.text,\n    borderBottomLeftRadius: \"4px\",\n    border: `1px solid ${currentTheme.border}`,\n    backdropFilter: \"blur(5px)\"\n  },\n  messageRole: {\n    fontSize: \"0.75rem\",\n    fontWeight: 600,\n    marginBottom: \"0.5rem\",\n    opacity: 0.8,\n    textTransform: \"uppercase\",\n    letterSpacing: \"0.5px\"\n  },\n  messageContent: {\n    fontSize: \"1rem\",\n    whiteSpace: \"pre-wrap\"\n  },\n  inputContainer: {\n    display: \"flex\",\n    padding: \"1.25rem\",\n    background: currentTheme.secondary,\n    borderTop: `1px solid ${currentTheme.border}`,\n    position: \"relative\",\n    backdropFilter: \"blur(5px)\",\n    transition: \"all 0.3s ease\",\n    transform: \"translateY(0)\",\n    maxHeight: \"120px\",\n    width: \"100%\",\n    boxSizing: \"border-box\",\n    \"&.focused\": {\n      background: currentTheme.surface,\n      boxShadow: `0 -4px 10px ${currentTheme.shadow}`\n    },\n    \"@media (max-width: 1024px)\": {\n      padding: \"1rem\",\n      maxHeight: \"100px\"\n    },\n    \"@media (max-width: 768px)\": {\n      padding: \"0.75rem\",\n      flexDirection: \"column\",\n      gap: \"0.75rem\",\n      maxHeight: \"90px\"\n    },\n    \"@media (max-width: 480px)\": {\n      padding: \"0.5rem\",\n      flexDirection: \"column\",\n      gap: \"0.5rem\",\n      maxHeight: \"70px\"\n    }\n  },\n  input: {\n    flex: 1,\n    padding: \"1rem 1.5rem\",\n    borderRadius: \"8px\",\n    border: `1px solid ${currentTheme.border}`,\n    outline: \"none\",\n    fontSize: \"1rem\",\n    transition: \"all 0.3s ease\",\n    background: currentTheme.surface,\n    color: currentTheme.text,\n    boxShadow: `0 1px 2px ${currentTheme.shadow}`,\n    \"&:focus\": {\n      borderColor: currentTheme.primary,\n      boxShadow: `0 0 0 3px ${currentTheme.shadow}`,\n      background: currentTheme.surface\n    },\n    \"&::placeholder\": {\n      color: currentTheme.textLight,\n      transition: \"opacity 0.3s ease\"\n    },\n    \"&:focus::placeholder\": {\n      opacity: 0.7\n    },\n    \"@media (max-width: 768px)\": {\n      padding: \"0.875rem 1.25rem\",\n      fontSize: \"0.95rem\"\n    },\n    \"@media (max-width: 480px)\": {\n      padding: \"0.75rem 1rem\",\n      fontSize: \"0.9rem\",\n      width: \"100%\"\n    }\n  },\n  sendButton: {\n    background: currentTheme.gradient,\n    color: \"white\",\n    border: \"none\",\n    borderRadius: \"8px\",\n    padding: \"0 1.75rem\",\n    marginLeft: \"1rem\",\n    cursor: \"pointer\",\n    fontWeight: 600,\n    fontSize: \"1rem\",\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    transition: \"all 0.3s ease\",\n    position: \"relative\",\n    overflow: \"hidden\",\n    boxShadow: `0 2px 5px ${currentTheme.shadowDark}`,\n    minWidth: \"80px\",\n    height: \"42px\",\n    \"&:hover:not(:disabled)\": {\n      transform: \"translateY(-2px)\",\n      boxShadow: `0 4px 8px ${currentTheme.shadowDark}`\n    },\n    \"&:disabled\": {\n      background: currentTheme.textLight,\n      cursor: \"not-allowed\",\n      transform: \"none\",\n      boxShadow: \"none\",\n      opacity: 0.7\n    },\n    \"@media (max-width: 768px)\": {\n      padding: \"0 1.5rem\",\n      fontSize: \"0.95rem\",\n      height: \"40px\",\n      marginLeft: \"0.75rem\"\n    },\n    \"@media (max-width: 480px)\": {\n      marginLeft: 0,\n      width: \"100%\",\n      height: \"38px\",\n      transform: \"translateY(0) !important\",\n      opacity: \"1 !important\"\n    }\n  },\n  sendButtonLoading: {\n    width: \"24px\",\n    height: \"24px\",\n    border: \"3px solid rgba(255,255,255,0.3)\",\n    borderTopColor: \"white\",\n    borderRadius: \"50%\",\n    animation: \"spin 1s linear infinite\"\n  },\n  loadingContainer: {\n    display: \"flex\",\n    justifyContent: \"center\",\n    padding: \"1.5rem\"\n  },\n  loadingDots: {\n    display: \"flex\",\n    gap: \"0.75rem\",\n    alignItems: \"center\"\n  },\n  loadingDot: {\n    width: \"12px\",\n    height: \"12px\",\n    borderRadius: \"50%\",\n    background: currentTheme.primary,\n    animation: \"bounce 1.4s infinite ease-in-out\",\n    \":nth-child(1)\": {\n      animationDelay: \"0s\"\n    },\n    \":nth-child(2)\": {\n      animationDelay: \"0.2s\"\n    },\n    \":nth-child(3)\": {\n      animationDelay: \"0.4s\"\n    }\n  },\n  dsaContainer: {\n    width: \"100%\",\n    backgroundColor: \"white\",\n    borderRadius: \"16px\",\n    overflow: \"hidden\",\n    boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.08)\",\n    position: \"relative\",\n    border: \"1px solid rgba(0, 0, 0, 0.05)\"\n  },\n  dsaHeader: {\n    padding: \"1.75rem 2rem\",\n    background: \"linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%)\",\n    borderBottom: \"1px solid rgba(0, 0, 0, 0.05)\",\n    textAlign: \"center\"\n  },\n  dsaTitle: {\n    margin: 0,\n    fontSize: \"1.75rem\",\n    fontWeight: 700,\n    color: \"#2d3748\"\n  },\n  dsaSubtitle: {\n    marginTop: \"0.75rem\",\n    fontSize: \"1rem\",\n    color: \"#4a5568\",\n    fontWeight: 400\n  },\n  searchBox: {\n    position: \"relative\",\n    maxWidth: \"600px\",\n    margin: \"1.5rem auto 0\",\n    display: \"flex\",\n    alignItems: \"center\"\n  },\n  searchIcon: {\n    position: \"absolute\",\n    left: \"1rem\",\n    top: \"50%\",\n    transform: \"translateY(-50%)\",\n    color: \"#718096\"\n  },\n  searchInput: {\n    width: \"100%\",\n    padding: \"1rem 1rem 1rem 3rem\",\n    borderRadius: \"8px\",\n    border: \"1px solid #e2e8f0\",\n    outline: \"none\",\n    fontSize: \"1rem\",\n    transition: \"all 0.2s ease\",\n    background: \"white\",\n    color: \"#2d3748\",\n    boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n    \":focus\": {\n      borderColor: \"#8B7355\",\n      boxShadow: \"0 0 0 3px rgba(139, 115, 85, 0.1)\"\n    }\n  },\n  clearSearchButton: {\n    position: \"absolute\",\n    right: \"1rem\",\n    top: \"50%\",\n    transform: \"translateY(-50%)\",\n    background: \"transparent\",\n    border: \"none\",\n    cursor: \"pointer\",\n    color: \"#718096\",\n    padding: \"0.25rem\",\n    borderRadius: \"50%\",\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    transition: \"all 0.2s ease\",\n    \":hover\": {\n      background: \"rgba(0, 0, 0, 0.05)\"\n    }\n  },\n  companiesGrid: {\n    display: \"grid\",\n    gridTemplateColumns: \"repeat(auto-fill, minmax(200px, 1fr))\",\n    gap: \"1rem\",\n    padding: \"2rem\",\n    background: \"white\"\n  },\n  companyCard: {\n    background: currentTheme.gradient,\n    border: `1px solid ${currentTheme.border}`,\n    borderRadius: \"12px\",\n    padding: \"1.25rem\",\n    cursor: \"pointer\",\n    transition: \"all 0.3s ease\",\n    position: \"relative\",\n    overflow: \"hidden\",\n    boxShadow: `0 2px 4px ${currentTheme.shadow}`,\n    \":hover\": {\n      transform: \"translateY(-5px)\",\n      boxShadow: `0 8px 16px ${currentTheme.shadow}`,\n      borderColor: currentTheme.primary\n    }\n  },\n  companyInitial: {\n    width: \"40px\",\n    height: \"40px\",\n    borderRadius: \"50%\",\n    background: currentTheme.gradientReverse,\n    color: \"white\",\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    fontWeight: \"bold\",\n    fontSize: \"1.2rem\",\n    marginBottom: \"1rem\"\n  },\n  companyName: {\n    fontSize: \"1rem\",\n    fontWeight: \"600\",\n    color: currentTheme.text,\n    transition: \"all 0.3s ease\"\n  },\n  companyHoverEffect: {\n    position: \"absolute\",\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    background: \"linear-gradient(135deg, rgba(139, 115, 85, 0.1) 0%, rgba(160, 149, 107, 0.1) 100%)\",\n    opacity: 0,\n    transition: \"opacity 0.3s ease\",\n    \":hover\": {\n      opacity: 1\n    }\n  },\n  noResults: {\n    textAlign: \"center\",\n    padding: \"3rem\",\n    gridColumn: \"1 / -1\",\n    display: \"flex\",\n    flexDirection: \"column\",\n    alignItems: \"center\",\n    justifyContent: \"center\"\n  },\n  noResultsIcon: {\n    marginBottom: \"1rem\",\n    color: \"#a0aec0\"\n  },\n  noResultsText: {\n    fontSize: \"1.1rem\",\n    color: \"#4a5568\",\n    marginBottom: \"1.5rem\"\n  },\n  clearSearchButtonLarge: {\n    background: \"linear-gradient(135deg, #8B7355 0%, #6B5B47 100%)\",\n    color: \"white\",\n    border: \"none\",\n    borderRadius: \"8px\",\n    padding: \"0.75rem 1.5rem\",\n    cursor: \"pointer\",\n    fontWeight: 600,\n    fontSize: \"1rem\",\n    transition: \"all 0.2s ease\",\n    boxShadow: \"0 2px 5px rgba(139, 115, 85, 0.3)\",\n    \":hover\": {\n      transform: \"translateY(-2px)\",\n      boxShadow: \"0 4px 8px rgba(139, 115, 85, 0.4)\"\n    }\n  },\n  quizzesContainer: {\n    width: \"100%\",\n    backgroundColor: \"white\",\n    borderRadius: \"16px\",\n    overflow: \"hidden\",\n    boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.08)\",\n    position: \"relative\",\n    border: \"1px solid rgba(0, 0, 0, 0.05)\"\n  },\n  quizzesHeader: {\n    padding: \"1.75rem 2rem\",\n    background: \"linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%)\",\n    borderBottom: \"1px solid rgba(0, 0, 0, 0.05)\",\n    textAlign: \"center\"\n  },\n  quizzesTitle: {\n    margin: 0,\n    fontSize: \"1.75rem\",\n    fontWeight: 700,\n    color: \"#2d3748\"\n  },\n  quizzesSubtitle: {\n    marginTop: \"0.75rem\",\n    fontSize: \"1rem\",\n    color: \"#4a5568\",\n    fontWeight: 400\n  },\n  quizCards: {\n    display: \"grid\",\n    gridTemplateColumns: \"repeat(auto-fill, minmax(280px, 1fr))\",\n    gap: \"1.5rem\",\n    padding: \"2rem\",\n    background: \"white\"\n  },\n  quizCard: {\n    background: \"linear-gradient(135deg, #8B7355 0%, #A0956B 100%)\",\n    // mustard green gradient\n    border: \"1px solid #e2e8f0\",\n    borderRadius: \"12px\",\n    padding: \"1.5rem\",\n    cursor: \"pointer\",\n    transition: \"all 0.3s ease\",\n    position: \"relative\",\n    overflow: \"hidden\",\n    display: \"flex\",\n    justifyContent: \"space-between\",\n    alignItems: \"center\",\n    boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.03)\",\n    \":hover\": {\n      transform: \"translateY(-5px)\",\n      boxShadow: \"0 8px 16px rgba(0, 0, 0, 0.1)\",\n      borderColor: \"#8B7355\"\n    }\n  },\n  quizCardContent: {\n    flex: 1\n  },\n  quizCardTitle: {\n    fontSize: \"1.1rem\",\n    fontWeight: \"600\",\n    color: \"#2d3748\",\n    marginBottom: \"0.5rem\"\n  },\n  quizCardDescription: {\n    fontSize: \"0.9rem\",\n    color: \"#718096\",\n    lineHeight: 1.5\n  },\n  quizCardArrow: {\n    color: \"#8B7355\",\n    marginLeft: \"1rem\"\n  },\n  quizCardHover: {\n    position: \"absolute\",\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    background: \"linear-gradient(135deg, rgba(139, 115, 85, 0.1) 0%, rgba(160, 149, 107, 0.1) 100%)\",\n    opacity: 0,\n    transition: \"opacity 0.3s ease\",\n    \":hover\": {\n      opacity: 1\n    }\n  },\n  examsContainer: {\n    width: \"100%\",\n    backgroundColor: \"white\",\n    borderRadius: \"16px\",\n    overflow: \"hidden\",\n    boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.08)\",\n    position: \"relative\",\n    border: \"1px solid rgba(0, 0, 0, 0.05)\",\n    padding: \"2rem\",\n    textAlign: \"center\"\n  },\n  examsTitle: {\n    margin: 0,\n    fontSize: \"1.75rem\",\n    fontWeight: 700,\n    color: \"#2d3748\"\n  },\n  examsSubtitle: {\n    marginTop: \"0.75rem\",\n    fontSize: \"1rem\",\n    color: \"#4a5568\",\n    fontWeight: 400,\n    marginBottom: \"2rem\"\n  },\n  aptitudeContainer: {\n    width: \"100%\",\n    backgroundColor: \"white\",\n    borderRadius: \"16px\",\n    overflow: \"hidden\",\n    boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.08)\",\n    position: \"relative\",\n    border: \"1px solid rgba(0, 0, 0, 0.05)\",\n    padding: \"2rem\",\n    textAlign: \"center\"\n  },\n  aptitudeTitle: {\n    margin: 0,\n    fontSize: \"1.75rem\",\n    fontWeight: 700,\n    color: \"#2d3748\"\n  },\n  aptitudeSubtitle: {\n    marginTop: \"0.75rem\",\n    fontSize: \"1rem\",\n    color: \"#4a5568\",\n    fontWeight: 400,\n    marginBottom: \"2rem\"\n  },\n  faqContainer: {\n    width: \"100%\",\n    backgroundColor: \"white\",\n    borderRadius: \"16px\",\n    overflow: \"hidden\",\n    boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.08)\",\n    position: \"relative\",\n    border: \"1px solid rgba(0, 0, 0, 0.05)\",\n    padding: \"2rem\",\n    textAlign: \"center\"\n  },\n  faqTitle: {\n    margin: 0,\n    fontSize: \"1.75rem\",\n    fontWeight: 700,\n    color: \"#2d3748\"\n  },\n  faqSubtitle: {\n    marginTop: \"0.75rem\",\n    fontSize: \"1rem\",\n    color: \"#4a5568\",\n    fontWeight: 400,\n    marginBottom: \"2rem\"\n  },\n  comingSoon: {\n    display: \"flex\",\n    justifyContent: \"center\",\n    alignItems: \"center\",\n    minHeight: \"300px\"\n  },\n  comingSoonContent: {\n    maxWidth: \"400px\",\n    textAlign: \"center\"\n  },\n  comingSoonIcon: {\n    fontSize: \"3rem\",\n    marginBottom: \"1rem\",\n    color: \"#FFB6B6\"\n  },\n  comingSoonText: {\n    fontSize: \"1.5rem\",\n    fontWeight: 600,\n    color: \"#2d3748\",\n    marginBottom: \"0.5rem\"\n  },\n  comingSoonDescription: {\n    fontSize: \"1rem\",\n    color: \"#718096\",\n    lineHeight: 1.6\n  },\n  \"@keyframes fadeIn\": {\n    from: {\n      opacity: 0,\n      transform: \"translateY(10px)\"\n    },\n    to: {\n      opacity: 1,\n      transform: \"translateY(0)\"\n    }\n  },\n  \"@keyframes spin\": {\n    to: {\n      transform: \"rotate(360deg)\"\n    }\n  },\n  \"@keyframes bounce\": {\n    \"0%, 80%, 100%\": {\n      transform: \"scale(0.6)\"\n    },\n    \"40%\": {\n      transform: \"scale(1)\"\n    }\n  },\n  \"@keyframes moveBackground\": {\n    \"0%\": {\n      transform: \"translate(0, 0)\"\n    },\n    \"100%\": {\n      transform: \"translate(50px, 50px)\"\n    }\n  },\n  \"@keyframes shine\": {\n    \"0%\": {\n      transform: \"rotate(30deg) translate(-30%, -30%)\"\n    },\n    \"100%\": {\n      transform: \"rotate(30deg) translate(30%, 30%)\"\n    }\n  },\n  examButtonsGrid: {\n    display: \"grid\",\n    gridTemplateColumns: \"repeat(auto-fit, minmax(280px, 1fr))\",\n    gap: \"1.5rem\",\n    padding: \"1.5rem 0\",\n    \"@media (max-width: 768px)\": {\n      gridTemplateColumns: \"1fr\",\n      padding: \"1rem 0\"\n    }\n  },\n  examButton: {\n    background: \"white\",\n    borderRadius: \"12px\",\n    padding: \"1.5rem\",\n    border: \"1px solid #e2e8f0\",\n    transition: \"all 0.3s ease\",\n    display: \"flex\",\n    flexDirection: \"column\",\n    gap: \"1rem\",\n    cursor: \"pointer\",\n    boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.05)\",\n    \"&:hover\": {\n      transform: \"translateY(-4px)\",\n      boxShadow: \"0 8px 16px rgba(0, 0, 0, 0.1)\",\n      borderColor: \"#FFB6B6\"\n    }\n  },\n  examIconContainer: {\n    width: \"48px\",\n    height: \"48px\",\n    borderRadius: \"12px\",\n    background: \"linear-gradient(135deg, #FFC5C5 0%, #FFB6B6 100%)\",\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    marginBottom: \"0.5rem\"\n  },\n  examIcon: {\n    fontSize: \"24px\"\n  },\n  examContent: {\n    flex: 1\n  },\n  examTitle: {\n    fontSize: \"1.25rem\",\n    fontWeight: \"600\",\n    color: \"#2d3748\",\n    marginBottom: \"0.5rem\"\n  },\n  examDescription: {\n    fontSize: \"0.9rem\",\n    color: \"#718096\",\n    lineHeight: \"1.5\"\n  },\n  resourcesList: {\n    display: \"flex\",\n    flexDirection: \"column\",\n    gap: \"0.5rem\",\n    marginTop: \"1rem\"\n  },\n  resourceLink: {\n    color: \"#FFB6B6\",\n    textDecoration: \"none\",\n    fontSize: \"0.9rem\",\n    padding: \"0.5rem\",\n    borderRadius: \"6px\",\n    background: \"#FFF0F0\",\n    transition: \"all 0.2s ease\",\n    \"&:hover\": {\n      background: \"#FFE0E0\",\n      color: \"#FF99CC\"\n    }\n  },\n  comingSoonBadge: {\n    display: \"inline-block\",\n    padding: \"0.5rem 1rem\",\n    borderRadius: \"20px\",\n    background: \"#edf2f7\",\n    color: \"#718096\",\n    fontSize: \"0.85rem\",\n    fontWeight: \"500\",\n    marginTop: \"0.5rem\"\n  },\n  selectedExamContainer: {\n    width: \"100%\",\n    padding: \"1.5rem\"\n  },\n  examHeader: {\n    marginBottom: \"2rem\",\n    display: \"flex\",\n    flexDirection: \"column\",\n    gap: \"1rem\"\n  },\n  backButton: {\n    background: \"none\",\n    border: \"none\",\n    color: \"#FFB6B6\",\n    cursor: \"pointer\",\n    fontSize: \"1rem\",\n    padding: \"0.75rem 0\",\n    display: \"flex\",\n    alignItems: \"center\",\n    gap: \"0.5rem\",\n    transition: \"all 0.2s ease\",\n    marginBottom: \"1rem\",\n    \"&:hover\": {\n      color: \"#FF99CC\",\n      transform: \"translateX(-4px)\"\n    }\n  },\n  selectedExamTitle: {\n    fontSize: \"2rem\",\n    fontWeight: \"700\",\n    color: \"#2d3748\",\n    display: \"flex\",\n    alignItems: \"center\",\n    gap: \"0.75rem\"\n  },\n  resourceTypeFilter: {\n    display: \"flex\",\n    gap: \"0.75rem\",\n    flexWrap: \"wrap\",\n    marginBottom: \"2rem\",\n    padding: \"1rem\",\n    background: \"#f7fafc\",\n    borderRadius: \"12px\"\n  },\n  filterButton: {\n    background: \"white\",\n    border: \"1px solid #e2e8f0\",\n    borderRadius: \"8px\",\n    padding: \"0.5rem 1rem\",\n    cursor: \"pointer\",\n    transition: \"all 0.2s ease\",\n    fontSize: \"0.9rem\",\n    display: \"flex\",\n    alignItems: \"center\",\n    gap: \"0.5rem\",\n    \"&:hover\": {\n      background: \"#FFF0F0\",\n      borderColor: \"#FFB6B6\"\n    }\n  },\n  filterButtonActive: {\n    background: \"#FFB6B6\",\n    color: \"white\",\n    borderColor: \"#FFB6B6\",\n    \"&:hover\": {\n      background: \"#FF99CC\"\n    }\n  },\n  resourcesGrid: {\n    display: \"grid\",\n    gridTemplateColumns: \"repeat(auto-fit, minmax(300px, 1fr))\",\n    gap: \"2rem\"\n  },\n  resourceCategory: {\n    background: \"white\",\n    borderRadius: \"12px\",\n    padding: \"1.5rem\",\n    border: \"1px solid #e2e8f0\",\n    boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.05)\"\n  },\n  categoryTitle: {\n    fontSize: \"1.25rem\",\n    fontWeight: \"600\",\n    color: \"#2d3748\",\n    marginBottom: \"1rem\",\n    display: \"flex\",\n    alignItems: \"center\",\n    gap: \"0.5rem\"\n  },\n  resourceCard: {\n    display: \"flex\",\n    justifyContent: \"space-between\",\n    alignItems: \"center\",\n    padding: \"1rem\",\n    background: \"#f7fafc\",\n    borderRadius: \"8px\",\n    marginBottom: \"0.75rem\",\n    textDecoration: \"none\",\n    color: \"inherit\",\n    transition: \"all 0.2s ease\",\n    \"&:hover\": {\n      background: \"#FFF0F0\",\n      transform: \"translateY(-2px)\"\n    }\n  },\n  resourceInfo: {\n    flex: 1\n  },\n  resourceTitle: {\n    fontSize: \"1rem\",\n    fontWeight: \"500\",\n    color: \"#2d3748\",\n    marginBottom: \"0.25rem\"\n  },\n  resourceDescription: {\n    fontSize: \"0.875rem\",\n    color: \"#718096\",\n    marginBottom: \"0.5rem\"\n  },\n  resourceYear: {\n    fontSize: \"0.75rem\",\n    color: \"#4a5568\",\n    background: \"#edf2f7\",\n    padding: \"0.25rem 0.5rem\",\n    borderRadius: \"4px\"\n  },\n  downloadIcon: {\n    fontSize: \"1.25rem\",\n    color: \"#FFB6B6\",\n    marginLeft: \"1rem\"\n  },\n  categoryIcon: {\n    fontSize: \"1.25rem\",\n    marginRight: \"0.5rem\"\n  },\n  categoryPreview: {\n    display: \"flex\",\n    gap: \"0.5rem\",\n    marginTop: \"1rem\",\n    color: \"#718096\"\n  },\n  gateContainer: {\n    width: \"100%\",\n    padding: \"1.5rem\"\n  },\n  gateHeader: {\n    marginBottom: \"2rem\"\n  },\n  gateStats: {\n    display: \"flex\",\n    gap: \"1.5rem\",\n    marginBottom: \"2rem\",\n    \"@media (max-width: 768px)\": {\n      flexDirection: \"column\",\n      gap: \"1rem\"\n    }\n  },\n  statCard: {\n    flex: 1,\n    background: \"white\",\n    padding: \"1.5rem\",\n    borderRadius: \"12px\",\n    display: \"flex\",\n    flexDirection: \"column\",\n    alignItems: \"center\",\n    gap: \"0.5rem\",\n    boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.05)\",\n    transition: \"transform 0.2s ease\",\n    \"&:hover\": {\n      transform: \"translateY(-4px)\"\n    }\n  },\n  statIcon: {\n    fontSize: \"2rem\"\n  },\n  statValue: {\n    fontSize: \"1.5rem\",\n    fontWeight: \"bold\",\n    color: \"#2d3748\"\n  },\n  statLabel: {\n    fontSize: \"0.9rem\",\n    color: \"#718096\"\n  },\n  gateFilters: {\n    display: \"flex\",\n    gap: \"1rem\",\n    marginBottom: \"2rem\",\n    \"@media (max-width: 768px)\": {\n      flexDirection: \"column\"\n    }\n  },\n  gateSelect: {\n    padding: \"0.75rem 1rem\",\n    borderRadius: \"8px\",\n    border: \"1px solid #e2e8f0\",\n    background: \"white\",\n    fontSize: \"0.95rem\",\n    color: \"#2d3748\",\n    cursor: \"pointer\",\n    transition: \"all 0.2s ease\",\n    \"&:focus\": {\n      outline: \"none\",\n      borderColor: \"#FFB6B6\",\n      boxShadow: \"0 0 0 3px rgba(255, 182, 182, 0.1)\"\n    }\n  },\n  gateContent: {\n    display: \"flex\",\n    gap: \"2rem\",\n    \"@media (max-width: 1024px)\": {\n      flexDirection: \"column\"\n    }\n  },\n  gateSidebar: {\n    width: \"300px\",\n    flexShrink: 0,\n    \"@media (max-width: 1024px)\": {\n      width: \"100%\"\n    }\n  },\n  sidebarSection: {\n    background: \"white\",\n    borderRadius: \"12px\",\n    padding: \"1.5rem\",\n    marginBottom: \"1.5rem\",\n    boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.05)\"\n  },\n  sidebarTitleGate: {\n    fontSize: \"1.1rem\",\n    fontWeight: \"600\",\n    color: currentTheme.text,\n    marginBottom: \"1rem\"\n  },\n  quickLinks: {\n    display: \"flex\",\n    flexDirection: \"column\",\n    gap: \"0.75rem\"\n  },\n  quickLink: {\n    color: \"#FFB6B6\",\n    textDecoration: \"none\",\n    fontSize: \"0.95rem\",\n    display: \"flex\",\n    alignItems: \"center\",\n    gap: \"0.5rem\",\n    padding: \"0.5rem\",\n    borderRadius: \"6px\",\n    transition: \"all 0.2s ease\",\n    \"&:hover\": {\n      background: \"#FFF0F0\"\n    }\n  },\n  datesList: {\n    display: \"flex\",\n    flexDirection: \"column\",\n    gap: \"1rem\"\n  },\n  dateItem: {\n    display: \"flex\",\n    justifyContent: \"space-between\",\n    alignItems: \"center\",\n    padding: \"0.75rem\",\n    background: \"#f7fafc\",\n    borderRadius: \"8px\"\n  },\n  dateLabel: {\n    fontSize: \"0.9rem\",\n    color: \"#4a5568\"\n  },\n  dateValue: {\n    fontSize: \"0.9rem\",\n    fontWeight: \"500\",\n    color: \"#2d3748\"\n  },\n  gateMainContent: {\n    flex: 1\n  },\n  subjectsGrid: {\n    display: \"grid\",\n    gridTemplateColumns: \"repeat(auto-fill, minmax(250px, 1fr))\",\n    gap: \"1.5rem\"\n  },\n  subjectCard: {\n    background: \"white\",\n    borderRadius: \"12px\",\n    padding: \"1.5rem\",\n    display: \"flex\",\n    flexDirection: \"column\",\n    alignItems: \"center\",\n    gap: \"1rem\",\n    cursor: \"pointer\",\n    transition: \"all 0.3s ease\",\n    boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.05)\",\n    \"&:hover\": {\n      transform: \"translateY(-4px)\",\n      boxShadow: \"0 8px 16px rgba(0, 0, 0, 0.1)\"\n    }\n  },\n  subjectIcon: {\n    fontSize: \"2.5rem\"\n  },\n  subjectName: {\n    fontSize: \"1.1rem\",\n    fontWeight: \"600\",\n    color: \"#2d3748\",\n    textAlign: \"center\"\n  },\n  subjectTopics: {\n    fontSize: \"0.9rem\",\n    color: \"#718096\"\n  },\n  subjectContent: {\n    background: \"white\",\n    borderRadius: \"12px\",\n    padding: \"2rem\",\n    boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.05)\"\n  },\n  subjectTitle: {\n    fontSize: \"1.5rem\",\n    fontWeight: \"700\",\n    color: \"#2d3748\",\n    marginBottom: \"2rem\"\n  },\n  topicsList: {\n    display: \"grid\",\n    gridTemplateColumns: \"repeat(auto-fill, minmax(300px, 1fr))\",\n    gap: \"1.5rem\"\n  },\n  topicCard: {\n    background: \"#f7fafc\",\n    borderRadius: \"10px\",\n    padding: \"1.25rem\",\n    transition: \"all 0.2s ease\",\n    \"&:hover\": {\n      background: \"#FFF0F0\"\n    }\n  },\n  topicTitle: {\n    fontSize: \"1rem\",\n    fontWeight: \"500\",\n    color: \"#2d3748\",\n    marginBottom: \"1rem\"\n  },\n  topicResources: {\n    display: \"flex\",\n    gap: \"0.75rem\"\n  },\n  resourceButton: {\n    background: \"white\",\n    border: \"1px solid #e2e8f0\",\n    borderRadius: \"6px\",\n    padding: \"0.5rem 0.75rem\",\n    fontSize: \"0.9rem\",\n    color: \"#4a5568\",\n    cursor: \"pointer\",\n    transition: \"all 0.2s ease\",\n    \"&:hover\": {\n      background: \"#FFB6B6\",\n      color: \"white\",\n      borderColor: \"#FFB6B6\"\n    }\n  },\n  leftLogoContainer: {\n    display: \"flex\",\n    alignItems: \"center\",\n    marginRight: \"1rem\"\n  },\n  eduaiLogoImage: {\n    width: \"40px\",\n    height: \"40px\",\n    borderRadius: \"50%\",\n    objectFit: \"cover\",\n    border: \"2px solid #FF99CC\",\n    boxShadow: \"0 2px 4px rgba(255, 153, 204, 0.2)\"\n  },\n  userAvatar: {\n    width: '40px',\n    height: '40px',\n    borderRadius: '50%',\n    background: '#f0f4f8',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    fontWeight: 700,\n    fontSize: '1.1rem',\n    marginRight: '0.75rem',\n    border: '2px solid #FF99CC',\n    boxShadow: '0 2px 4px rgba(255,153,204,0.1)'\n  },\n  userAvatarLarge: {\n    width: '56px',\n    height: '56px',\n    borderRadius: '50%',\n    background: '#f0f4f8',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    fontWeight: 700,\n    fontSize: '2rem',\n    border: '2px solid #FF99CC',\n    boxShadow: '0 2px 4px rgba(255,153,204,0.1)'\n  },\n  // Authentication Page Styles\n  authPageContainer: {\n    minHeight: '100vh',\n    background: currentTheme.gradientLight,\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    padding: '2rem',\n    position: 'relative',\n    overflow: 'hidden',\n    fontFamily: \"'Inter', -apple-system, BlinkMacSystemFont, sans-serif\",\n    '@media (max-width: 768px)': {\n      padding: '1rem'\n    },\n    '@media (max-width: 480px)': {\n      padding: '0.5rem'\n    }\n  },\n  authBackgroundAnimation: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    background: `\n        radial-gradient(circle at 20% 80%, ${currentTheme.primary}20 0%, transparent 50%),\n        radial-gradient(circle at 80% 20%, ${currentTheme.accent}20 0%, transparent 50%),\n        radial-gradient(circle at 40% 40%, ${currentTheme.primaryLight}15 0%, transparent 50%)\n      `,\n    animation: 'authFloat 6s ease-in-out infinite alternate',\n    zIndex: 1\n  },\n  authFormContainer: {\n    background: 'rgba(255, 255, 255, 0.95)',\n    backdropFilter: 'blur(20px)',\n    borderRadius: '24px',\n    padding: '3rem',\n    width: '100%',\n    maxWidth: '480px',\n    boxShadow: `\n        0 20px 25px -5px ${currentTheme.shadow},\n        0 10px 10px -5px ${currentTheme.shadow},\n        inset 0 1px 0 rgba(255, 255, 255, 0.1)\n      `,\n    border: `1px solid rgba(255, 255, 255, 0.2)`,\n    position: 'relative',\n    zIndex: 2,\n    transition: 'all 0.3s ease',\n    '@media (max-width: 768px)': {\n      padding: '2rem',\n      borderRadius: '20px'\n    },\n    '@media (max-width: 480px)': {\n      padding: '1.5rem',\n      borderRadius: '16px',\n      maxWidth: '100%'\n    }\n  },\n  authFormTitle: {\n    fontSize: '2.5rem',\n    fontWeight: '700',\n    textAlign: 'center',\n    marginBottom: '0.5rem',\n    background: currentTheme.gradient,\n    WebkitBackgroundClip: 'text',\n    WebkitTextFillColor: 'transparent',\n    backgroundClip: 'text',\n    letterSpacing: '-0.02em',\n    '@media (max-width: 768px)': {\n      fontSize: '2rem'\n    },\n    '@media (max-width: 480px)': {\n      fontSize: '1.75rem'\n    }\n  },\n  authFormSubtitle: {\n    fontSize: '1rem',\n    color: currentTheme.textLight,\n    textAlign: 'center',\n    marginBottom: '2.5rem',\n    fontWeight: '400',\n    '@media (max-width: 480px)': {\n      fontSize: '0.9rem',\n      marginBottom: '2rem'\n    }\n  },\n  authInputGroup: {\n    marginBottom: '1.5rem',\n    position: 'relative'\n  },\n  authInput: {\n    width: '100%',\n    padding: '1rem 1.25rem',\n    fontSize: '1rem',\n    border: `2px solid ${currentTheme.border}`,\n    borderRadius: '12px',\n    background: 'rgba(255, 255, 255, 0.8)',\n    transition: 'all 0.3s ease',\n    outline: 'none',\n    fontFamily: 'inherit',\n    boxSizing: 'border-box',\n    '::placeholder': {\n      color: currentTheme.textLight,\n      opacity: 0.7\n    },\n    ':focus': {\n      borderColor: currentTheme.primary,\n      background: 'rgba(255, 255, 255, 1)',\n      boxShadow: `0 0 0 3px ${currentTheme.primary}20`,\n      transform: 'translateY(-1px)'\n    },\n    '@media (max-width: 480px)': {\n      padding: '0.875rem 1rem',\n      fontSize: '0.95rem'\n    }\n  },\n  // EduAIChatBot specific styles\n  appContainer: {\n    minHeight: '100vh',\n    backgroundColor: currentTheme.background,\n    color: currentTheme.text,\n    transition: 'all 0.3s ease'\n  },\n  navbarFixed: {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    right: 0,\n    height: '64px',\n    background: currentTheme.gradient,\n    boxShadow: `0 2px 10px ${currentTheme.shadow}`,\n    display: 'flex',\n    alignItems: 'center',\n    padding: '0 24px',\n    zIndex: 1000,\n    borderBottom: `1px solid ${currentTheme.border}`\n  },\n  sidebarFixed: {\n    position: 'fixed',\n    top: '64px',\n    left: 0,\n    bottom: 0,\n    width: '280px',\n    backgroundColor: currentTheme.surface,\n    transform: 'translateX(-100%)',\n    transition: 'transform 0.3s ease',\n    zIndex: 900,\n    boxShadow: `2px 0 10px ${currentTheme.shadow}`,\n    overflowY: 'auto'\n  },\n  sidebarOpen: {\n    transform: 'translateX(0)'\n  },\n  sidebarItemEdu: {\n    padding: '12px 16px',\n    display: 'flex',\n    alignItems: 'center',\n    cursor: 'pointer',\n    borderRadius: '8px',\n    margin: '4px 8px',\n    transition: 'all 0.2s ease',\n    color: currentTheme.text,\n    background: currentTheme.gradientLight,\n    '&:hover': {\n      background: currentTheme.gradient,\n      color: 'white'\n    }\n  },\n  sidebarItemActiveEdu: {\n    background: currentTheme.gradient,\n    color: 'white',\n    fontWeight: 500\n  },\n  mainContentEdu: {\n    paddingTop: '80px',\n    paddingBottom: '40px',\n    transition: 'margin-left 0.3s ease',\n    minHeight: 'calc(100vh - 120px)'\n  },\n  mainContentWithSidebar: {\n    marginLeft: '280px'\n  },\n  cardEdu: {\n    backgroundColor: currentTheme.surface,\n    borderRadius: '12px',\n    boxShadow: `0 4px 6px ${currentTheme.shadow}`,\n    padding: '24px',\n    marginBottom: '24px',\n    transition: 'all 0.3s ease'\n  },\n  buttonPrimary: {\n    background: currentTheme.gradient,\n    color: '#fff',\n    border: 'none',\n    borderRadius: '8px',\n    padding: '12px 24px',\n    fontWeight: 500,\n    fontSize: '16px',\n    cursor: 'pointer',\n    display: 'inline-flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    gap: '8px',\n    transition: 'all 0.2s ease',\n    '&:hover': {\n      transform: 'translateY(-2px)',\n      boxShadow: `0 4px 12px ${currentTheme.shadowDark}`\n    },\n    '&:disabled': {\n      opacity: 0.7,\n      cursor: 'not-allowed'\n    }\n  },\n  inputField: {\n    width: '100%',\n    padding: '14px 16px',\n    borderRadius: '8px',\n    border: `1.5px solid ${currentTheme.primary}`,\n    fontSize: '16px',\n    backgroundColor: currentTheme.secondary,\n    color: currentTheme.primary,\n    transition: 'all 0.3s ease',\n    '&:focus': {\n      outline: 'none',\n      borderColor: currentTheme.primary,\n      boxShadow: `0 0 0 2px ${currentTheme.shadow}`\n    }\n  },\n  chatBubbleUser: {\n    backgroundColor: currentTheme.primary,\n    color: '#fff',\n    padding: '12px 16px',\n    borderRadius: '18px 18px 4px 18px',\n    maxWidth: '80%',\n    marginLeft: 'auto',\n    marginBottom: '8px',\n    boxShadow: `0 2px 4px ${currentTheme.shadow}`\n  },\n  chatBubbleBot: {\n    backgroundColor: currentTheme.secondary,\n    color: currentTheme.text,\n    padding: '12px 16px',\n    borderRadius: '18px 18px 18px 4px',\n    maxWidth: '80%',\n    marginRight: 'auto',\n    marginBottom: '8px',\n    boxShadow: `0 2px 4px ${currentTheme.shadow}`\n  },\n  companyCardEdu: {\n    background: currentTheme.gradient,\n    borderRadius: '12px',\n    padding: '20px',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.3s ease',\n    boxShadow: `0 4px 6px ${currentTheme.shadow}`,\n    '&:hover': {\n      transform: 'translateY(-4px)',\n      boxShadow: `0 8px 15px ${currentTheme.shadowDark}`,\n      background: currentTheme.gradientReverse\n    }\n  },\n  quizCardEdu: {\n    backgroundColor: currentTheme.surface,\n    borderRadius: '12px',\n    padding: '20px',\n    display: 'flex',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.3s ease',\n    boxShadow: `0 4px 6px ${currentTheme.shadow}`,\n    '&:hover': {\n      transform: 'translateY(-4px)',\n      boxShadow: `0 8px 15px ${currentTheme.shadowDark}`,\n      backgroundColor: currentTheme.secondary\n    }\n  },\n  notification: {\n    position: 'fixed',\n    bottom: '24px',\n    right: '24px',\n    padding: '12px 24px',\n    borderRadius: '8px',\n    boxShadow: `0 4px 12px ${currentTheme.shadowDark}`,\n    zIndex: 2000,\n    animation: 'slideIn 0.3s ease-out',\n    color: '#fff'\n  },\n  // Academic Dashboard specific styles\n  academicBackground: {\n    minHeight: '100vh',\n    background: currentTheme.gradientLight,\n    padding: '2rem 1rem'\n  },\n  academicHeader: {\n    display: 'flex',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    marginBottom: '2rem',\n    flexWrap: 'wrap',\n    gap: '1rem'\n  },\n  academicTitle: {\n    fontSize: '2.5rem',\n    fontWeight: 'bold',\n    color: currentTheme.primary,\n    margin: 0,\n    marginBottom: '0.5rem'\n  },\n  academicSubtitle: {\n    color: currentTheme.textLight,\n    margin: 0\n  },\n  academicButton: {\n    background: currentTheme.primary,\n    color: 'white',\n    padding: '0.75rem 1.5rem',\n    borderRadius: '0.5rem',\n    border: 'none',\n    cursor: 'pointer',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '0.5rem',\n    fontSize: '1rem',\n    transition: 'all 0.3s ease',\n    '&:hover': {\n      background: currentTheme.primaryDark\n    }\n  },\n  academicButtonSecondary: {\n    background: 'white',\n    color: currentTheme.primary,\n    border: `2px solid ${currentTheme.primary}`,\n    padding: '0.75rem 1.5rem',\n    borderRadius: '0.5rem',\n    cursor: 'pointer',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '0.5rem',\n    fontSize: '1rem',\n    transition: 'all 0.3s ease',\n    '&:hover': {\n      background: currentTheme.secondary\n    }\n  },\n  academicStatsGrid: {\n    display: 'grid',\n    gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n    gap: '1.5rem',\n    marginBottom: '2rem'\n  },\n  academicStatCard: {\n    background: 'white',\n    borderRadius: '1rem',\n    boxShadow: `0 4px 6px ${currentTheme.shadow}`,\n    padding: '1.5rem',\n    display: 'flex',\n    alignItems: 'center',\n    transition: 'transform 0.3s ease',\n    cursor: 'pointer',\n    '&:hover': {\n      transform: 'translateY(-5px)'\n    }\n  },\n  academicStatIcon: {\n    padding: '0.75rem',\n    borderRadius: '50%',\n    marginRight: '1rem'\n  },\n  academicMainGrid: {\n    display: 'grid',\n    gridTemplateColumns: '2fr 1fr',\n    gap: '2rem',\n    '@media (max-width: 1024px)': {\n      gridTemplateColumns: '1fr'\n    }\n  },\n  academicCoursesSection: {\n    background: 'white',\n    borderRadius: '1rem',\n    boxShadow: `0 4px 6px ${currentTheme.shadow}`,\n    overflow: 'hidden'\n  },\n  academicSectionHeader: {\n    background: currentTheme.primary,\n    color: 'white',\n    padding: '1.5rem',\n    display: 'flex',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    flexWrap: 'wrap',\n    gap: '1rem'\n  },\n  academicSectionTitle: {\n    fontSize: '1.25rem',\n    fontWeight: 'bold',\n    margin: 0\n  },\n  academicFilterButton: {\n    background: 'white',\n    color: currentTheme.primary,\n    padding: '0.5rem 1rem',\n    borderRadius: '0.375rem',\n    border: 'none',\n    fontSize: '0.875rem',\n    cursor: 'pointer',\n    transition: 'all 0.3s ease'\n  },\n  academicFilterButtonActive: {\n    background: currentTheme.primaryDark,\n    color: 'white'\n  },\n  academicCourseCard: {\n    border: `1px solid ${currentTheme.border}`,\n    borderRadius: '0.5rem',\n    padding: '1rem',\n    transition: 'all 0.3s ease',\n    cursor: 'pointer',\n    '&:hover': {\n      borderColor: currentTheme.primary,\n      transform: 'translateY(-2px)'\n    }\n  },\n  // Academics component styles\n  academicsContainer: {\n    width: '100%',\n    maxWidth: '1200px',\n    margin: '0 auto',\n    padding: '2rem 1rem'\n  },\n  academicsHeaderSection: {\n    textAlign: 'center',\n    marginBottom: '2rem'\n  },\n  academicsMainTitle: {\n    fontSize: '2rem',\n    fontWeight: '700',\n    color: currentTheme.text,\n    marginBottom: '0.5rem'\n  },\n  academicsMainSubtitle: {\n    fontSize: '1.1rem',\n    color: currentTheme.textLight,\n    maxWidth: '600px',\n    margin: '0 auto'\n  },\n  academicsGrid: {\n    display: 'grid',\n    gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',\n    gap: '1.5rem',\n    marginBottom: '3rem'\n  },\n  academicCardBasic: {\n    background: currentTheme.surface,\n    borderRadius: '12px',\n    padding: '1.5rem',\n    boxShadow: `0 2px 8px ${currentTheme.shadow}`,\n    transition: 'transform 0.2s, box-shadow 0.2s',\n    cursor: 'pointer',\n    position: 'relative',\n    overflow: 'hidden',\n    '&:hover': {\n      transform: 'translateY(-4px)',\n      boxShadow: `0 4px 12px ${currentTheme.shadowDark}`\n    }\n  },\n  academicIcon: {\n    fontSize: '2rem',\n    marginBottom: '1rem'\n  },\n  academicCardTitleBasic: {\n    fontSize: '1.25rem',\n    fontWeight: '600',\n    color: currentTheme.text,\n    marginBottom: '0.5rem'\n  },\n  academicCardDescriptionBasic: {\n    fontSize: '1rem',\n    color: currentTheme.textLight,\n    lineHeight: '1.5'\n  },\n  subjectSection: {\n    marginTop: '2rem'\n  },\n  subjectTitleMain: {\n    fontSize: '1.5rem',\n    fontWeight: '600',\n    color: currentTheme.text,\n    marginBottom: '1.5rem',\n    textAlign: 'center'\n  },\n  subjectGrid: {\n    display: 'grid',\n    gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',\n    gap: '1rem'\n  },\n  subjectCardBasic: {\n    background: currentTheme.surface,\n    borderRadius: '8px',\n    padding: '1.25rem 1rem',\n    textAlign: 'center',\n    fontWeight: '500',\n    color: currentTheme.textLight,\n    boxShadow: `0 1px 3px ${currentTheme.shadow}`,\n    transition: 'all 0.2s',\n    '&:hover': {\n      background: currentTheme.primary,\n      color: '#fff',\n      transform: 'translateY(-2px)'\n    }\n  }\n};\nexport default styles;", "map": {"version": 3, "names": ["themes", "mustardGreen", "primary", "primaryDark", "primaryLight", "secondary", "accent", "background", "surface", "text", "textLight", "textDark", "border", "shadow", "shadowDark", "gradient", "gradientReverse", "gradientLight", "redTheme", "softRedGradient", "purpleTheme", "darkMode", "currentTheme", "getTheme", "themeName", "switchTheme", "Object", "assign", "getAvailableThemes", "keys", "styles", "minHeight", "backgroundColor", "display", "flexDirection", "fontFamily", "position", "overflowX", "animatedBackground", "top", "left", "right", "bottom", "zIndex", "content", "backgroundImage", "animation", "navbar", "width", "boxShadow", "padding", "<PERSON><PERSON>ilter", "borderBottom", "navContainer", "max<PERSON><PERSON><PERSON>", "margin", "justifyContent", "alignItems", "menuButton", "cursor", "color", "borderRadius", "transition", "centerTitleContainer", "flex", "textAlign", "mainTitle", "fontSize", "fontWeight", "WebkitBackgroundClip", "WebkitTextFillColor", "letterSpacing", "textTransform", "subTitle", "marginTop", "rightLogoContainer", "logoImage", "height", "objectFit", "sidebar", "overflowY", "sidebarHeader", "sidebarTitle", "closeSidebarButton", "sidebarContent", "sidebarItemGroup", "marginBottom", "sidebarItem", "sidebarItemActive", "sidebarIcon", "marginRight", "sidebarItemText", "sidebarExpandIcon", "subItemsContainer", "paddingLeft", "overflow", "subItem", "sidebarFooter", "borderTop", "sidebarFooterText", "overlay", "mainContainer", "<PERSON><PERSON><PERSON><PERSON>", "gridOverlay", "backgroundSize", "pointerEvents", "header", "transform", "title", "textShadow", "subtitle", "opacity", "chatContainer", "maxHeight", "boxSizing", "chatMessages", "gap", "paddingRight", "scroll<PERSON>eh<PERSON>or", "message", "lineHeight", "wordWrap", "welcomeMessage", "welcomeTitle", "welcomeText", "userMessage", "alignSelf", "borderBottomRightRadius", "botMessage", "borderBottomLeftRadius", "messageRole", "messageContent", "whiteSpace", "inputContainer", "input", "outline", "borderColor", "sendButton", "marginLeft", "min<PERSON><PERSON><PERSON>", "sendButtonLoading", "borderTopColor", "loadingContainer", "loadingDots", "loadingDot", "animationDelay", "dsa<PERSON><PERSON><PERSON>", "d<PERSON><PERSON><PERSON><PERSON>", "dsa<PERSON>itle", "dsaSubtitle", "searchBox", "searchIcon", "searchInput", "clearSearchButton", "companiesGrid", "gridTemplateColumns", "companyCard", "companyInitial", "companyName", "companyHoverEffect", "noResults", "gridColumn", "noResultsIcon", "noResultsText", "clearSearchButtonLarge", "quizzes<PERSON><PERSON><PERSON>", "quizzes<PERSON><PERSON><PERSON>", "quizzesTitle", "quizzesSubtitle", "quizCards", "quizCard", "quiz<PERSON><PERSON><PERSON><PERSON><PERSON>", "quizCardTitle", "quizCardDescription", "quizCardArrow", "quizCardHover", "<PERSON><PERSON><PERSON><PERSON>", "examsTitle", "examsSubtitle", "aptitudeC<PERSON>r", "aptitudeTitle", "aptitudeSubtitle", "faq<PERSON><PERSON><PERSON>", "faqTitle", "faqSubtitle", "comingSoon", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "comingSoonIcon", "comingSoonText", "comingSoonDescription", "from", "to", "examButtonsGrid", "examButton", "examIconContainer", "examIcon", "examContent", "examTitle", "examDescription", "resourcesList", "resourceLink", "textDecoration", "comingSoonBadge", "selected<PERSON><PERSON>mContainer", "<PERSON><PERSON><PERSON><PERSON>", "backButton", "selectedExamTitle", "resourceTypeFilter", "flexWrap", "filterButton", "filterButtonActive", "resourcesGrid", "resourceCategory", "categoryTitle", "resourceCard", "resourceInfo", "resourceTitle", "resourceDescription", "resourceYear", "downloadIcon", "categoryIcon", "categoryPreview", "gateContainer", "gateHeader", "gateStats", "statCard", "statIcon", "statValue", "statLabel", "gateFilters", "gateSelect", "gateContent", "gateSidebar", "flexShrink", "sidebarSection", "sidebarTitleGate", "quickLinks", "quickLink", "datesList", "dateItem", "<PERSON><PERSON><PERSON><PERSON>", "dateValue", "gateMain<PERSON><PERSON>nt", "subjectsGrid", "subjectCard", "subjectIcon", "subjectName", "subjectTopics", "subjectContent", "subjectTitle", "topicsList", "topicCard", "topicTitle", "topicResources", "resourceButton", "leftLogoContainer", "eduaiLogoImage", "userAvatar", "userAvatarLarge", "authPageContainer", "authBackgroundAnimation", "authFormContainer", "authFormTitle", "backgroundClip", "authFormSubtitle", "authInputGroup", "authInput", "appContainer", "navbarFixed", "sidebarFixed", "sidebarOpen", "sidebarItemEdu", "sidebarItemActiveEdu", "mainContentEdu", "paddingTop", "paddingBottom", "mainContentWithSidebar", "cardEdu", "buttonPrimary", "inputField", "chatBubbleUser", "chatBubbleBot", "companyCardEdu", "quizCardEdu", "notification", "academicBackground", "<PERSON><PERSON><PERSON><PERSON>", "academicTitle", "academicSubtitle", "academic<PERSON><PERSON><PERSON>", "academicButtonSecondary", "academicStatsGrid", "academicStatCard", "academicStatIcon", "academicMain<PERSON><PERSON>", "academicCoursesSection", "academicSection<PERSON><PERSON>er", "academicSectionTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "academicFilterButtonActive", "academicCourseCard", "<PERSON><PERSON><PERSON><PERSON>", "academicsHeaderSection", "academics<PERSON><PERSON><PERSON><PERSON><PERSON>", "academicsMainSubtitle", "academics<PERSON><PERSON>", "academic<PERSON><PERSON><PERSON><PERSON><PERSON>", "academicIcon", "academicCardTitleBasic", "academicCardDescriptionBasic", "subjectSection", "subjectTitleMain", "subjectGrid", "subjectCardBasic"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(5)/src/styles.js"], "sourcesContent": ["// styles.js - Centralized Theme System\n\n// Color Themes Configuration\nconst themes = {\n  mustardGreen: {\n    primary: '#8B7355',\n    primaryDark: '#6B5B47',\n    primaryLight: '#A0956B',\n    secondary: '#F5F3F0',\n    accent: '#9A8B73',\n    background: '#f0f4f8',\n    surface: '#ffffff',\n    text: '#2d3748',\n    textLight: '#718096',\n    textDark: '#1a202c',\n    border: '#e2e8f0',\n    shadow: 'rgba(139, 115, 85, 0.1)',\n    shadowDark: 'rgba(139, 115, 85, 0.3)',\n    gradient: 'linear-gradient(135deg, #8B7355 0%, #A0956B 100%)',\n    gradientReverse: 'linear-gradient(135deg, #A0956B 0%, #8B7355 100%)',\n    gradientLight: 'linear-gradient(135deg, #F5F3F0 0%, #E8E2D5 100%)',\n  },\n  redTheme: {\n    primary: '#DC2626',\n    primaryDark: '#B91C1C',\n    primaryLight: '#EF4444',\n    secondary: '#FEE2E2',\n    accent: '#F87171',\n    background: '#FEF2F2',\n    surface: '#ffffff',\n    text: '#2d3748',\n    textLight: '#718096',\n    textDark: '#1a202c',\n    border: '#e2e8f0',\n    shadow: 'rgba(220, 38, 38, 0.1)',\n    shadowDark: 'rgba(220, 38, 38, 0.3)',\n    gradient: 'linear-gradient(135deg, #DC2626 0%, #EF4444 100%)',\n    gradientReverse: 'linear-gradient(135deg, #EF4444 0%, #DC2626 100%)',\n    gradientLight: 'linear-gradient(135deg, #FEE2E2 0%, #FECACA 100%)',\n  },\n  softRedGradient: {\n    primary: '#FFB6B6',\n    primaryDark: '#FF99CC',\n    primaryLight: '#FFC5C5',\n    secondary: '#FFF0F0',\n    accent: '#FFE0E0',\n    background: '#FFF5F5',\n    surface: '#ffffff',\n    text: '#2d3748',\n    textLight: '#718096',\n    textDark: '#1a202c',\n    border: '#e2e8f0',\n    shadow: 'rgba(255, 182, 182, 0.1)',\n    shadowDark: 'rgba(255, 182, 182, 0.3)',\n    gradient: 'linear-gradient(135deg, #FFC5C5 0%, #FFB6B6 100%)',\n    gradientReverse: 'linear-gradient(135deg, #FFB6B6 0%, #FF99CC 100%)',\n    gradientLight: 'linear-gradient(135deg, #FFF0F0 0%, #FFE0E0 100%)',\n  },\n  purpleTheme: {\n    primary: '#7C3AED',\n    primaryDark: '#5B21B6',\n    primaryLight: '#8B5CF6',\n    secondary: '#F3E8FF',\n    accent: '#A78BFA',\n    background: '#FEFBFF',\n    surface: '#ffffff',\n    text: '#2d3748',\n    textLight: '#718096',\n    textDark: '#1a202c',\n    border: '#e2e8f0',\n    shadow: 'rgba(124, 58, 237, 0.1)',\n    shadowDark: 'rgba(124, 58, 237, 0.3)',\n    gradient: 'linear-gradient(135deg, #7C3AED 0%, #8B5CF6 100%)',\n    gradientReverse: 'linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%)',\n    gradientLight: 'linear-gradient(135deg, #F3E8FF 0%, #E9D5FF 100%)',\n  },\n  darkMode: {\n    primary: '#8B7355',\n    primaryDark: '#6B5B47',\n    primaryLight: '#A0956B',\n    secondary: '#252525',\n    accent: '#333333',\n    background: '#121212',\n    surface: '#1e1e1e',\n    text: '#e0e0e0',\n    textLight: '#a0a0a0',\n    textDark: '#ffffff',\n    border: '#333333',\n    shadow: 'rgba(0, 0, 0, 0.3)',\n    shadowDark: 'rgba(0, 0, 0, 0.5)',\n    gradient: 'linear-gradient(135deg, #8B7355 0%, #A0956B 100%)',\n    gradientReverse: 'linear-gradient(135deg, #A0956B 0%, #8B7355 100%)',\n    gradientLight: 'linear-gradient(135deg, #252525 0%, #333333 100%)',\n  }\n};\n\n// Current theme selector - can be changed to switch themes\nconst currentTheme = themes.purpleTheme; // Change this to switch themes globally\n\n// Helper function to get theme colors\nconst getTheme = (themeName = 'purpleTheme') => {\n  return themes[themeName] || themes.purpleTheme;\n};\n\n// Helper function to switch themes globally\nconst switchTheme = (themeName) => {\n  if (themes[themeName]) {\n    // Update currentTheme reference\n    Object.assign(currentTheme, themes[themeName]);\n    return true;\n  }\n  return false;\n};\n\n// Helper function to get all available theme names\nconst getAvailableThemes = () => {\n  return Object.keys(themes);\n};\n\nconst styles = {\n  // Theme configuration\n  themes,\n  currentTheme,\n  getTheme,\n  switchTheme,\n  getAvailableThemes,\n\n  // Base styles\n  background: {\n    minHeight: \"100vh\",\n    backgroundColor: currentTheme.background,\n    display: \"flex\",\n    flexDirection: \"column\",\n    fontFamily: \"'Inter', -apple-system, BlinkMacSystemFont, sans-serif\",\n    position: \"relative\",\n    overflowX: \"hidden\",\n  },\n    animatedBackground: {\n      position: \"fixed\",\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      background: \"linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)\",\n      zIndex: 0,\n      \"::before\": {\n        content: '\"\"',\n        position: \"absolute\",\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundImage: `\n          radial-gradient(circle at 10% 20%, rgba(255, 94, 94, 0.1) 0%, transparent 20%),\n          radial-gradient(circle at 90% 30%, rgba(94, 163, 255, 0.1) 0%, transparent 25%),\n          radial-gradient(circle at 50% 80%, rgba(255, 215, 94, 0.1) 0%, transparent 20%)\n        `,\n        animation: \"moveBackground 20s infinite alternate\",\n      },\n    },\n    navbar: {\n      width: \"100%\",\n      background: currentTheme.gradient,\n      boxShadow: `0 4px 6px ${currentTheme.shadow}`,\n      padding: \"0.75rem 2rem\",\n      position: \"sticky\",\n      top: 0,\n      zIndex: 1000,\n      backdropFilter: \"blur(8px)\",\n      borderBottom: `1px solid ${currentTheme.border}`,\n    },\n    navContainer: {\n      width: \"100%\",\n      maxWidth: \"1400px\",\n      margin: \"0 auto\",\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n    },\n    menuButton: {\n      background: \"transparent\",\n      border: \"none\",\n      cursor: \"pointer\",\n      color: \"#4a5568\",\n      padding: \"0.5rem\",\n      borderRadius: \"8px\",\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      transition: \"all 0.2s ease\",\n      \":hover\": {\n        background: \"rgba(0, 0, 0, 0.05)\",\n        color: \"#3182ce\",\n      },\n      \"@media (min-width: 768px)\": {\n        display: \"none\",\n      },\n    },\n    centerTitleContainer: {\n      display: \"flex\",\n      flexDirection: \"column\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      flex: 1,\n      textAlign: \"center\",\n    },\n    mainTitle: {\n      fontSize: \"1.25rem\",\n      fontWeight: \"bold\",\n      background: currentTheme.gradient,\n      WebkitBackgroundClip: \"text\",\n      WebkitTextFillColor: \"transparent\",\n      letterSpacing: \"0.5px\",\n      textTransform: \"uppercase\",\n    },\n    subTitle: {\n      fontSize: \"0.75rem\",\n      color: currentTheme.textLight,\n      marginTop: \"0.25rem\",\n      fontWeight: 500,\n      letterSpacing: \"0.5px\",\n    },\n    rightLogoContainer: {\n      display: \"flex\",\n      alignItems: \"center\",\n    },\n    logoImage: {\n      width: \"40px\",\n      height: \"40px\",\n      borderRadius: \"50%\",\n      objectFit: \"cover\",\n      border: `2px solid ${currentTheme.primary}`,\n      boxShadow: `0 2px 4px ${currentTheme.shadow}`,\n    },\n    sidebar: {\n      position: \"fixed\",\n      top: 0,\n      left: 0,\n      width: \"280px\",\n      height: \"100vh\",\n      backgroundColor: currentTheme.surface,\n      boxShadow: `4px 0 15px ${currentTheme.shadow}`,\n      zIndex: 1100,\n      transition: \"transform 0.3s ease-in-out\",\n      display: \"flex\",\n      flexDirection: \"column\",\n      overflowY: \"auto\",\n    },\n    sidebarHeader: {\n      padding: \"1.5rem 1.5rem 1rem\",\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      borderBottom: `1px solid ${currentTheme.border}`,\n    },\n    sidebarTitle: {\n      fontSize: \"1.25rem\",\n      fontWeight: \"600\",\n      color: currentTheme.text,\n    },\n    closeSidebarButton: {\n      background: \"transparent\",\n      border: \"none\",\n      cursor: \"pointer\",\n      color: \"#4a5568\",\n      padding: \"0.25rem\",\n      borderRadius: \"4px\",\n      transition: \"all 0.2s ease\",\n      \":hover\": {\n        background: \"rgba(0, 0, 0, 0.05)\",\n        color: \"#3182ce\",\n      },\n    },\n    sidebarContent: {\n      flex: 1,\n      padding: \"1rem 0\",\n      overflowY: \"auto\",\n    },\n    sidebarItemGroup: {\n      marginBottom: \"0.5rem\",\n    },\n    sidebarItem: {\n      display: \"flex\",\n      alignItems: \"center\",\n      padding: \"0.75rem 1.5rem\",\n      cursor: \"pointer\",\n      transition: \"all 0.2s ease\",\n      position: \"relative\",\n      \":hover\": {\n        background: `${currentTheme.shadow}`,\n        color: currentTheme.primary,\n      },\n    },\n    sidebarItemActive: {\n      background: `${currentTheme.shadow}`,\n      color: currentTheme.primary,\n      fontWeight: \"500\",\n    },\n    sidebarIcon: {\n      marginRight: \"1rem\",\n      fontSize: \"1.1rem\",\n      display: \"flex\",\n      alignItems: \"center\",\n    },\n    sidebarItemText: {\n      flex: 1,\n    },\n    sidebarExpandIcon: {\n      transition: \"transform 0.2s ease\",\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      padding: \"0.25rem\",\n      borderRadius: \"4px\",\n      \":hover\": {\n        background: \"rgba(0, 0, 0, 0.05)\",\n      },\n    },\n    subItemsContainer: {\n      paddingLeft: \"2.5rem\",\n      overflow: \"hidden\",\n      transition: \"all 0.3s ease\",\n    },\n    subItem: {\n      padding: \"0.65rem 1.5rem\",\n      fontSize: \"0.9rem\",\n      color: currentTheme.textLight,\n      cursor: \"pointer\",\n      transition: \"all 0.2s ease\",\n      \":hover\": {\n        background: `${currentTheme.shadow}`,\n        color: currentTheme.primary,\n      },\n    },\n    sidebarFooter: {\n      padding: \"1.5rem\",\n      borderTop: \"1px solid rgba(0, 0, 0, 0.05)\",\n      textAlign: \"center\",\n    },\n    sidebarFooterText: {\n      fontSize: \"0.8rem\",\n      color: \"#718096\",\n    },\n    overlay: {\n      position: \"fixed\",\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      backgroundColor: \"rgba(0, 0, 0, 0.5)\",\n      zIndex: 1090,\n      \"@media (min-width: 768px)\": {\n        display: \"none\",\n      },\n    },\n    mainContainer: {\n      width: \"100%\",\n      maxWidth: \"1400px\",\n      margin: \"2rem auto\",\n      padding: \"0 2rem\",\n      flex: 1,\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"flex-start\",\n      position: \"relative\",\n      zIndex: 1,\n      \"@media (max-width: 768px)\": {\n        margin: \"1rem auto\",\n        padding: \"0 1rem\",\n      },\n    },\n    resumeContainer: {\n      width: \"100%\",\n      maxWidth: \"800px\",\n      backgroundColor: currentTheme.surface,\n      borderRadius: \"16px\",\n      overflow: \"hidden\",\n      boxShadow: `0 10px 25px ${currentTheme.shadow}`,\n      position: \"relative\",\n      border: `1px solid ${currentTheme.border}`,\n      display: \"flex\",\n      flexDirection: \"column\",\n      height: \"calc(100vh - 120px)\",\n      \"@media (max-width: 768px)\": {\n          height: \"calc(100vh - 100px)\",\n          borderRadius: \"12px\",\n      },\n      \"@media (max-width: 480px)\": {\n          height: \"calc(100vh - 80px)\",\n          borderRadius: \"8px\",\n      }\n  },\n  gridOverlay: {\n      position: \"absolute\",\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      backgroundImage: `\n        linear-gradient(rgba(139, 115, 85, 0.03) 1px, transparent 1px),\n        linear-gradient(90deg, rgba(139, 115, 85, 0.03) 1px, transparent 1px)\n      `,\n      backgroundSize: \"20px 20px\",\n      pointerEvents: \"none\",\n    },\n    header: {\n      background: currentTheme.gradient,\n      color: \"white\",\n      padding: \"1.75rem 2rem\",\n      textAlign: \"center\",\n      position: \"relative\",\n      overflow: \"hidden\",\n      \"@media (max-width: 768px)\": {\n          padding: \"1.5rem 1.5rem\",\n      },\n      \"@media (max-width: 480px)\": {\n          padding: \"1.25rem 1rem\",\n      },\n      \"::before\": {\n          content: '\"\"',\n          position: \"absolute\",\n          top: \"-50%\",\n          left: \"-50%\",\n          right: \"-50%\",\n          bottom: \"-50%\",\n          background: `\n              radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)\n          `,\n          transform: \"rotate(30deg)\",\n          animation: \"shine 3s infinite\",\n      }\n  },\n  title: {\n      margin: 0,\n      fontSize: \"1.75rem\",\n      fontWeight: 700,\n      position: \"relative\",\n      textShadow: \"0 2px 4px rgba(0,0,0,0.1)\",\n    },\n    subtitle: {\n      marginTop: \"0.75rem\",\n      fontSize: \"1rem\",\n      opacity: 0.9,\n      position: \"relative\",\n      fontWeight: 400,\n    },\n    chatContainer: {\n      height: \"calc(100vh - 200px)\",\n      maxHeight: \"800px\",\n      minHeight: \"400px\",\n      overflowY: \"auto\",\n      padding: \"1.5rem\",\n      background: currentTheme.surface,\n      position: \"relative\",\n      display: \"flex\",\n      flexDirection: \"column\",\n      width: \"100%\",\n      boxSizing: \"border-box\",\n      \"@media (max-width: 1024px)\": {\n        height: \"calc(100vh - 160px)\",\n        padding: \"1rem\",\n      },\n      \"@media (max-width: 768px)\": {\n        height: \"calc(100vh - 140px)\",\n        padding: \"0.75rem\",\n        minHeight: \"300px\",\n      },\n      \"@media (max-width: 480px)\": {\n        height: \"calc(100vh - 120px)\",\n        padding: \"0.5rem\",\n        minHeight: \"180px\",\n      }\n    },\n    chatMessages: {\n      display: \"flex\",\n      flexDirection: \"column\",\n      gap: \"1rem\",\n      position: \"relative\",\n      flex: 1,\n      overflowY: \"auto\",\n      paddingRight: \"0.5rem\",\n      scrollBehavior: \"smooth\",\n      width: \"100%\",\n      boxSizing: \"border-box\",\n      \"@media (max-width: 768px)\": {\n        gap: \"0.5rem\",\n        paddingRight: \"0.25rem\",\n      },\n      \"@media (max-width: 480px)\": {\n        gap: \"0.25rem\",\n        paddingRight: 0,\n      }\n    },\n    message: {\n      padding: \"1rem 1.25rem\",\n      borderRadius: \"12px\",\n      maxWidth: \"85%\",\n      width: \"fit-content\",\n      boxShadow: \"0 1px 3px rgba(0,0,0,0.1)\",\n      lineHeight: 1.6,\n      position: \"relative\",\n      transition: \"all 0.3s ease\",\n      fontSize: \"1rem\",\n      animation: \"fadeIn 0.3s ease-out\",\n      wordWrap: \"break-word\",\n      \"@media (max-width: 768px)\": {\n          maxWidth: \"90%\",\n          padding: \"0.875rem 1rem\",\n      },\n      \"@media (max-width: 480px)\": {\n          maxWidth: \"95%\",\n          padding: \"0.75rem 0.875rem\",\n          fontSize: \"0.95rem\",\n      }\n    },\n    welcomeMessage: {\n      background: currentTheme.secondary,\n      padding: \"1.5rem\",\n      borderRadius: \"12px\",\n      textAlign: \"center\",\n      margin: \"0.5rem 0 1.5rem\",\n      border: `1px dashed ${currentTheme.primary}`,\n      backdropFilter: \"blur(5px)\",\n      animation: \"fadeIn 0.8s ease-out\",\n    },\n    welcomeTitle: {\n      fontSize: \"1.25rem\",\n      fontWeight: 600,\n      color: currentTheme.primary,\n      marginBottom: \"0.75rem\",\n    },\n    welcomeText: {\n      fontSize: \"1rem\",\n      color: currentTheme.textLight,\n      lineHeight: 1.6,\n    },\n    userMessage: {\n      alignSelf: \"flex-end\",\n      background: currentTheme.gradient,\n      color: \"white\",\n      borderBottomRightRadius: \"4px\",\n    },\n    botMessage: {\n      alignSelf: \"flex-start\",\n      background: currentTheme.secondary,\n      color: currentTheme.text,\n      borderBottomLeftRadius: \"4px\",\n      border: `1px solid ${currentTheme.border}`,\n      backdropFilter: \"blur(5px)\",\n    },\n    messageRole: {\n      fontSize: \"0.75rem\",\n      fontWeight: 600,\n      marginBottom: \"0.5rem\",\n      opacity: 0.8,\n      textTransform: \"uppercase\",\n      letterSpacing: \"0.5px\",\n    },\n    messageContent: {\n      fontSize: \"1rem\",\n      whiteSpace: \"pre-wrap\",\n    },\n    inputContainer: {\n      display: \"flex\",\n      padding: \"1.25rem\",\n      background: currentTheme.secondary,\n      borderTop: `1px solid ${currentTheme.border}`,\n      position: \"relative\",\n      backdropFilter: \"blur(5px)\",\n      transition: \"all 0.3s ease\",\n      transform: \"translateY(0)\",\n      maxHeight: \"120px\",\n      width: \"100%\",\n      boxSizing: \"border-box\",\n      \"&.focused\": {\n        background: currentTheme.surface,\n        boxShadow: `0 -4px 10px ${currentTheme.shadow}`,\n      },\n      \"@media (max-width: 1024px)\": {\n        padding: \"1rem\",\n        maxHeight: \"100px\",\n      },\n      \"@media (max-width: 768px)\": {\n        padding: \"0.75rem\",\n        flexDirection: \"column\",\n        gap: \"0.75rem\",\n        maxHeight: \"90px\",\n      },\n      \"@media (max-width: 480px)\": {\n        padding: \"0.5rem\",\n        flexDirection: \"column\",\n        gap: \"0.5rem\",\n        maxHeight: \"70px\",\n      }\n    },\n    input: {\n      flex: 1,\n      padding: \"1rem 1.5rem\",\n      borderRadius: \"8px\",\n      border: `1px solid ${currentTheme.border}`,\n      outline: \"none\",\n      fontSize: \"1rem\",\n      transition: \"all 0.3s ease\",\n      background: currentTheme.surface,\n      color: currentTheme.text,\n      boxShadow: `0 1px 2px ${currentTheme.shadow}`,\n      \"&:focus\": {\n        borderColor: currentTheme.primary,\n        boxShadow: `0 0 0 3px ${currentTheme.shadow}`,\n        background: currentTheme.surface,\n      },\n      \"&::placeholder\": {\n        color: currentTheme.textLight,\n        transition: \"opacity 0.3s ease\",\n      },\n      \"&:focus::placeholder\": {\n        opacity: 0.7,\n      },\n      \"@media (max-width: 768px)\": {\n        padding: \"0.875rem 1.25rem\",\n        fontSize: \"0.95rem\",\n      },\n      \"@media (max-width: 480px)\": {\n        padding: \"0.75rem 1rem\",\n        fontSize: \"0.9rem\",\n        width: \"100%\",\n      }\n    },\n    sendButton: {\n      background: currentTheme.gradient,\n      color: \"white\",\n      border: \"none\",\n      borderRadius: \"8px\",\n      padding: \"0 1.75rem\",\n      marginLeft: \"1rem\",\n      cursor: \"pointer\",\n      fontWeight: 600,\n      fontSize: \"1rem\",\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      transition: \"all 0.3s ease\",\n      position: \"relative\",\n      overflow: \"hidden\",\n      boxShadow: `0 2px 5px ${currentTheme.shadowDark}`,\n      minWidth: \"80px\",\n      height: \"42px\",\n      \"&:hover:not(:disabled)\": {\n        transform: \"translateY(-2px)\",\n        boxShadow: `0 4px 8px ${currentTheme.shadowDark}`,\n      },\n      \"&:disabled\": {\n        background: currentTheme.textLight,\n        cursor: \"not-allowed\",\n        transform: \"none\",\n        boxShadow: \"none\",\n        opacity: 0.7,\n      },\n      \"@media (max-width: 768px)\": {\n        padding: \"0 1.5rem\",\n        fontSize: \"0.95rem\",\n        height: \"40px\",\n        marginLeft: \"0.75rem\",\n      },\n      \"@media (max-width: 480px)\": {\n        marginLeft: 0,\n        width: \"100%\",\n        height: \"38px\",\n        transform: \"translateY(0) !important\",\n        opacity: \"1 !important\",\n      }\n    },\n    sendButtonLoading: {\n      width: \"24px\",\n      height: \"24px\",\n      border: \"3px solid rgba(255,255,255,0.3)\",\n      borderTopColor: \"white\",\n      borderRadius: \"50%\",\n      animation: \"spin 1s linear infinite\",\n    },\n    loadingContainer: {\n      display: \"flex\",\n      justifyContent: \"center\",\n      padding: \"1.5rem\",\n    },\n    loadingDots: {\n      display: \"flex\",\n      gap: \"0.75rem\",\n      alignItems: \"center\",\n    },\n    loadingDot: {\n      width: \"12px\",\n      height: \"12px\",\n      borderRadius: \"50%\",\n      background: currentTheme.primary,\n      animation: \"bounce 1.4s infinite ease-in-out\",\n      \":nth-child(1)\": {\n        animationDelay: \"0s\",\n      },\n      \":nth-child(2)\": {\n        animationDelay: \"0.2s\",\n      },\n      \":nth-child(3)\": {\n        animationDelay: \"0.4s\",\n      },\n    },\n    dsaContainer: {\n      width: \"100%\",\n      backgroundColor: \"white\",\n      borderRadius: \"16px\",\n      overflow: \"hidden\",\n      boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.08)\",\n      position: \"relative\",\n      border: \"1px solid rgba(0, 0, 0, 0.05)\",\n    },\n    dsaHeader: {\n      padding: \"1.75rem 2rem\",\n      background: \"linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%)\",\n      borderBottom: \"1px solid rgba(0, 0, 0, 0.05)\",\n      textAlign: \"center\",\n    },\n    dsaTitle: {\n      margin: 0,\n      fontSize: \"1.75rem\",\n      fontWeight: 700,\n      color: \"#2d3748\",\n    },\n    dsaSubtitle: {\n      marginTop: \"0.75rem\",\n      fontSize: \"1rem\",\n      color: \"#4a5568\",\n      fontWeight: 400,\n    },\n    searchBox: {\n      position: \"relative\",\n      maxWidth: \"600px\",\n      margin: \"1.5rem auto 0\",\n      display: \"flex\",\n      alignItems: \"center\",\n    },\n    searchIcon: {\n      position: \"absolute\",\n      left: \"1rem\",\n      top: \"50%\",\n      transform: \"translateY(-50%)\",\n      color: \"#718096\",\n    },\n    searchInput: {\n      width: \"100%\",\n      padding: \"1rem 1rem 1rem 3rem\",\n      borderRadius: \"8px\",\n      border: \"1px solid #e2e8f0\",\n      outline: \"none\",\n      fontSize: \"1rem\",\n      transition: \"all 0.2s ease\",\n      background: \"white\",\n      color: \"#2d3748\",\n      boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n      \":focus\": {\n        borderColor: \"#8B7355\",\n        boxShadow: \"0 0 0 3px rgba(139, 115, 85, 0.1)\",\n      },\n    },\n    clearSearchButton: {\n      position: \"absolute\",\n      right: \"1rem\",\n      top: \"50%\",\n      transform: \"translateY(-50%)\",\n      background: \"transparent\",\n      border: \"none\",\n      cursor: \"pointer\",\n      color: \"#718096\",\n      padding: \"0.25rem\",\n      borderRadius: \"50%\",\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      transition: \"all 0.2s ease\",\n      \":hover\": {\n        background: \"rgba(0, 0, 0, 0.05)\",\n      },\n    },\n    companiesGrid: {\n      display: \"grid\",\n      gridTemplateColumns: \"repeat(auto-fill, minmax(200px, 1fr))\",\n      gap: \"1rem\",\n      padding: \"2rem\",\n      background: \"white\",\n    },\n    companyCard: {\n      background: currentTheme.gradient,\n      border: `1px solid ${currentTheme.border}`,\n      borderRadius: \"12px\",\n      padding: \"1.25rem\",\n      cursor: \"pointer\",\n      transition: \"all 0.3s ease\",\n      position: \"relative\",\n      overflow: \"hidden\",\n      boxShadow: `0 2px 4px ${currentTheme.shadow}`,\n      \":hover\": {\n        transform: \"translateY(-5px)\",\n        boxShadow: `0 8px 16px ${currentTheme.shadow}`,\n        borderColor: currentTheme.primary,\n      },\n    },\n    companyInitial: {\n      width: \"40px\",\n      height: \"40px\",\n      borderRadius: \"50%\",\n      background: currentTheme.gradientReverse,\n      color: \"white\",\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      fontWeight: \"bold\",\n      fontSize: \"1.2rem\",\n      marginBottom: \"1rem\",\n    },\n    companyName: {\n      fontSize: \"1rem\",\n      fontWeight: \"600\",\n      color: currentTheme.text,\n      transition: \"all 0.3s ease\",\n    },\n    companyHoverEffect: {\n      position: \"absolute\",\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      background: \"linear-gradient(135deg, rgba(139, 115, 85, 0.1) 0%, rgba(160, 149, 107, 0.1) 100%)\",\n      opacity: 0,\n      transition: \"opacity 0.3s ease\",\n      \":hover\": {\n        opacity: 1,\n      },\n    },\n    noResults: {\n      textAlign: \"center\",\n      padding: \"3rem\",\n      gridColumn: \"1 / -1\",\n      display: \"flex\",\n      flexDirection: \"column\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n    },\n    noResultsIcon: {\n      marginBottom: \"1rem\",\n      color: \"#a0aec0\",\n    },\n    noResultsText: {\n      fontSize: \"1.1rem\",\n      color: \"#4a5568\",\n      marginBottom: \"1.5rem\",\n    },\n    clearSearchButtonLarge: {\n      background: \"linear-gradient(135deg, #8B7355 0%, #6B5B47 100%)\",\n      color: \"white\",\n      border: \"none\",\n      borderRadius: \"8px\",\n      padding: \"0.75rem 1.5rem\",\n      cursor: \"pointer\",\n      fontWeight: 600,\n      fontSize: \"1rem\",\n      transition: \"all 0.2s ease\",\n      boxShadow: \"0 2px 5px rgba(139, 115, 85, 0.3)\",\n      \":hover\": {\n        transform: \"translateY(-2px)\",\n        boxShadow: \"0 4px 8px rgba(139, 115, 85, 0.4)\",\n      },\n    },\n    quizzesContainer: {\n      width: \"100%\",\n      backgroundColor: \"white\",\n      borderRadius: \"16px\",\n      overflow: \"hidden\",\n      boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.08)\",\n      position: \"relative\",\n      border: \"1px solid rgba(0, 0, 0, 0.05)\",\n    },\n    quizzesHeader: {\n      padding: \"1.75rem 2rem\",\n      background: \"linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%)\",\n      borderBottom: \"1px solid rgba(0, 0, 0, 0.05)\",\n      textAlign: \"center\",\n    },\n    quizzesTitle: {\n      margin: 0,\n      fontSize: \"1.75rem\",\n      fontWeight: 700,\n      color: \"#2d3748\",\n    },\n    quizzesSubtitle: {\n      marginTop: \"0.75rem\",\n      fontSize: \"1rem\",\n      color: \"#4a5568\",\n      fontWeight: 400,\n    },\n    quizCards: {\n      display: \"grid\",\n      gridTemplateColumns: \"repeat(auto-fill, minmax(280px, 1fr))\",\n      gap: \"1.5rem\",\n      padding: \"2rem\",\n      background: \"white\",\n    },\n    quizCard: {\n      background: \"linear-gradient(135deg, #8B7355 0%, #A0956B 100%)\", // mustard green gradient\n      border: \"1px solid #e2e8f0\",\n      borderRadius: \"12px\",\n      padding: \"1.5rem\",\n      cursor: \"pointer\",\n      transition: \"all 0.3s ease\",\n      position: \"relative\",\n      overflow: \"hidden\",\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.03)\",\n      \":hover\": {\n        transform: \"translateY(-5px)\",\n        boxShadow: \"0 8px 16px rgba(0, 0, 0, 0.1)\",\n        borderColor: \"#8B7355\",\n      },\n    },\n    quizCardContent: {\n      flex: 1,\n    },\n    quizCardTitle: {\n      fontSize: \"1.1rem\",\n      fontWeight: \"600\",\n      color: \"#2d3748\",\n      marginBottom: \"0.5rem\",\n    },\n    quizCardDescription: {\n      fontSize: \"0.9rem\",\n      color: \"#718096\",\n      lineHeight: 1.5,\n    },\n    quizCardArrow: {\n      color: \"#8B7355\",\n      marginLeft: \"1rem\",\n    },\n    quizCardHover: {\n      position: \"absolute\",\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      background: \"linear-gradient(135deg, rgba(139, 115, 85, 0.1) 0%, rgba(160, 149, 107, 0.1) 100%)\",\n      opacity: 0,\n      transition: \"opacity 0.3s ease\",\n      \":hover\": {\n        opacity: 1,\n      },\n    },\n    examsContainer: {\n      width: \"100%\",\n      backgroundColor: \"white\",\n      borderRadius: \"16px\",\n      overflow: \"hidden\",\n      boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.08)\",\n      position: \"relative\",\n      border: \"1px solid rgba(0, 0, 0, 0.05)\",\n      padding: \"2rem\",\n      textAlign: \"center\",\n    },\n    examsTitle: {\n      margin: 0,\n      fontSize: \"1.75rem\",\n      fontWeight: 700,\n      color: \"#2d3748\",\n    },\n    examsSubtitle: {\n      marginTop: \"0.75rem\",\n      fontSize: \"1rem\",\n      color: \"#4a5568\",\n      fontWeight: 400,\n      marginBottom: \"2rem\",\n    },\n    aptitudeContainer: {\n      width: \"100%\",\n      backgroundColor: \"white\",\n      borderRadius: \"16px\",\n      overflow: \"hidden\",\n      boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.08)\",\n      position: \"relative\",\n      border: \"1px solid rgba(0, 0, 0, 0.05)\",\n      padding: \"2rem\",\n      textAlign: \"center\",\n    },\n    aptitudeTitle: {\n      margin: 0,\n      fontSize: \"1.75rem\",\n      fontWeight: 700,\n      color: \"#2d3748\",\n    },\n    aptitudeSubtitle: {\n      marginTop: \"0.75rem\",\n      fontSize: \"1rem\",\n      color: \"#4a5568\",\n      fontWeight: 400,\n      marginBottom: \"2rem\",\n    },\n    faqContainer: {\n      width: \"100%\",\n      backgroundColor: \"white\",\n      borderRadius: \"16px\",\n      overflow: \"hidden\",\n      boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.08)\",\n      position: \"relative\",\n      border: \"1px solid rgba(0, 0, 0, 0.05)\",\n      padding: \"2rem\",\n      textAlign: \"center\",\n    },\n    faqTitle: {\n      margin: 0,\n      fontSize: \"1.75rem\",\n      fontWeight: 700,\n      color: \"#2d3748\",\n    },\n    faqSubtitle: {\n      marginTop: \"0.75rem\",\n      fontSize: \"1rem\",\n      color: \"#4a5568\",\n      fontWeight: 400,\n      marginBottom: \"2rem\",\n    },\n    comingSoon: {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"300px\",\n    },\n    comingSoonContent: {\n      maxWidth: \"400px\",\n      textAlign: \"center\",\n    },\n    comingSoonIcon: {\n      fontSize: \"3rem\",\n      marginBottom: \"1rem\",\n      color: \"#FFB6B6\",\n    },\n    comingSoonText: {\n      fontSize: \"1.5rem\",\n      fontWeight: 600,\n      color: \"#2d3748\",\n      marginBottom: \"0.5rem\",\n    },\n    comingSoonDescription: {\n      fontSize: \"1rem\",\n      color: \"#718096\",\n      lineHeight: 1.6,\n    },\n    \"@keyframes fadeIn\": {\n      from: { opacity: 0, transform: \"translateY(10px)\" },\n      to: { opacity: 1, transform: \"translateY(0)\" },\n    },\n    \"@keyframes spin\": {\n      to: { transform: \"rotate(360deg)\" },\n    },\n    \"@keyframes bounce\": {\n      \"0%, 80%, 100%\": { transform: \"scale(0.6)\" },\n      \"40%\": { transform: \"scale(1)\" },\n    },\n    \"@keyframes moveBackground\": {\n      \"0%\": { transform: \"translate(0, 0)\" },\n      \"100%\": { transform: \"translate(50px, 50px)\" },\n    },\n    \"@keyframes shine\": {\n      \"0%\": { transform: \"rotate(30deg) translate(-30%, -30%)\" },\n      \"100%\": { transform: \"rotate(30deg) translate(30%, 30%)\" },\n    },\n    examButtonsGrid: {\n      display: \"grid\",\n      gridTemplateColumns: \"repeat(auto-fit, minmax(280px, 1fr))\",\n      gap: \"1.5rem\",\n      padding: \"1.5rem 0\",\n      \"@media (max-width: 768px)\": {\n        gridTemplateColumns: \"1fr\",\n        padding: \"1rem 0\",\n      }\n    },\n    examButton: {\n      background: \"white\",\n      borderRadius: \"12px\",\n      padding: \"1.5rem\",\n      border: \"1px solid #e2e8f0\",\n      transition: \"all 0.3s ease\",\n      display: \"flex\",\n      flexDirection: \"column\",\n      gap: \"1rem\",\n      cursor: \"pointer\",\n      boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.05)\",\n      \"&:hover\": {\n        transform: \"translateY(-4px)\",\n        boxShadow: \"0 8px 16px rgba(0, 0, 0, 0.1)\",\n        borderColor: \"#FFB6B6\",\n      }\n    },\n    examIconContainer: {\n      width: \"48px\",\n      height: \"48px\",\n      borderRadius: \"12px\",\n      background: \"linear-gradient(135deg, #FFC5C5 0%, #FFB6B6 100%)\",\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      marginBottom: \"0.5rem\",\n    },\n    examIcon: {\n      fontSize: \"24px\",\n    },\n    examContent: {\n      flex: 1,\n    },\n    examTitle: {\n      fontSize: \"1.25rem\",\n      fontWeight: \"600\",\n      color: \"#2d3748\",\n      marginBottom: \"0.5rem\",\n    },\n    examDescription: {\n      fontSize: \"0.9rem\",\n      color: \"#718096\",\n      lineHeight: \"1.5\",\n    },\n    resourcesList: {\n      display: \"flex\",\n      flexDirection: \"column\",\n      gap: \"0.5rem\",\n      marginTop: \"1rem\",\n    },\n    resourceLink: {\n      color: \"#FFB6B6\",\n      textDecoration: \"none\",\n      fontSize: \"0.9rem\",\n      padding: \"0.5rem\",\n      borderRadius: \"6px\",\n      background: \"#FFF0F0\",\n      transition: \"all 0.2s ease\",\n      \"&:hover\": {\n        background: \"#FFE0E0\",\n        color: \"#FF99CC\",\n      }\n    },\n    comingSoonBadge: {\n      display: \"inline-block\",\n      padding: \"0.5rem 1rem\",\n      borderRadius: \"20px\",\n      background: \"#edf2f7\",\n      color: \"#718096\",\n      fontSize: \"0.85rem\",\n      fontWeight: \"500\",\n      marginTop: \"0.5rem\",\n    },\n    selectedExamContainer: {\n      width: \"100%\",\n      padding: \"1.5rem\",\n    },\n    examHeader: {\n      marginBottom: \"2rem\",\n      display: \"flex\",\n      flexDirection: \"column\",\n      gap: \"1rem\",\n    },\n    backButton: {\n      background: \"none\",\n      border: \"none\",\n      color: \"#FFB6B6\",\n      cursor: \"pointer\",\n      fontSize: \"1rem\",\n      padding: \"0.75rem 0\",\n      display: \"flex\",\n      alignItems: \"center\",\n      gap: \"0.5rem\",\n      transition: \"all 0.2s ease\",\n      marginBottom: \"1rem\",\n      \"&:hover\": {\n        color: \"#FF99CC\",\n        transform: \"translateX(-4px)\",\n      }\n    },\n    selectedExamTitle: {\n      fontSize: \"2rem\",\n      fontWeight: \"700\",\n      color: \"#2d3748\",\n      display: \"flex\",\n      alignItems: \"center\",\n      gap: \"0.75rem\",\n    },\n    resourceTypeFilter: {\n      display: \"flex\",\n      gap: \"0.75rem\",\n      flexWrap: \"wrap\",\n      marginBottom: \"2rem\",\n      padding: \"1rem\",\n      background: \"#f7fafc\",\n      borderRadius: \"12px\",\n    },\n    filterButton: {\n      background: \"white\",\n      border: \"1px solid #e2e8f0\",\n      borderRadius: \"8px\",\n      padding: \"0.5rem 1rem\",\n      cursor: \"pointer\",\n      transition: \"all 0.2s ease\",\n      fontSize: \"0.9rem\",\n      display: \"flex\",\n      alignItems: \"center\",\n      gap: \"0.5rem\",\n      \"&:hover\": {\n        background: \"#FFF0F0\",\n        borderColor: \"#FFB6B6\",\n      }\n    },\n    filterButtonActive: {\n      background: \"#FFB6B6\",\n      color: \"white\",\n      borderColor: \"#FFB6B6\",\n      \"&:hover\": {\n        background: \"#FF99CC\",\n      }\n    },\n    resourcesGrid: {\n      display: \"grid\",\n      gridTemplateColumns: \"repeat(auto-fit, minmax(300px, 1fr))\",\n      gap: \"2rem\",\n    },\n    resourceCategory: {\n      background: \"white\",\n      borderRadius: \"12px\",\n      padding: \"1.5rem\",\n      border: \"1px solid #e2e8f0\",\n      boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.05)\",\n    },\n    categoryTitle: {\n      fontSize: \"1.25rem\",\n      fontWeight: \"600\",\n      color: \"#2d3748\",\n      marginBottom: \"1rem\",\n      display: \"flex\",\n      alignItems: \"center\",\n      gap: \"0.5rem\",\n    },\n    resourceCard: {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      padding: \"1rem\",\n      background: \"#f7fafc\",\n      borderRadius: \"8px\",\n      marginBottom: \"0.75rem\",\n      textDecoration: \"none\",\n      color: \"inherit\",\n      transition: \"all 0.2s ease\",\n      \"&:hover\": {\n        background: \"#FFF0F0\",\n        transform: \"translateY(-2px)\",\n      }\n    },\n    resourceInfo: {\n      flex: 1,\n    },\n    resourceTitle: {\n      fontSize: \"1rem\",\n      fontWeight: \"500\",\n      color: \"#2d3748\",\n      marginBottom: \"0.25rem\",\n    },\n    resourceDescription: {\n      fontSize: \"0.875rem\",\n      color: \"#718096\",\n      marginBottom: \"0.5rem\",\n    },\n    resourceYear: {\n      fontSize: \"0.75rem\",\n      color: \"#4a5568\",\n      background: \"#edf2f7\",\n      padding: \"0.25rem 0.5rem\",\n      borderRadius: \"4px\",\n    },\n    downloadIcon: {\n      fontSize: \"1.25rem\",\n      color: \"#FFB6B6\",\n      marginLeft: \"1rem\",\n    },\n    categoryIcon: {\n      fontSize: \"1.25rem\",\n      marginRight: \"0.5rem\",\n    },\n    categoryPreview: {\n      display: \"flex\",\n      gap: \"0.5rem\",\n      marginTop: \"1rem\",\n      color: \"#718096\",\n    },\n    gateContainer: {\n      width: \"100%\",\n      padding: \"1.5rem\",\n    },\n    gateHeader: {\n      marginBottom: \"2rem\",\n    },\n    gateStats: {\n      display: \"flex\",\n      gap: \"1.5rem\",\n      marginBottom: \"2rem\",\n      \"@media (max-width: 768px)\": {\n        flexDirection: \"column\",\n        gap: \"1rem\",\n      }\n    },\n    statCard: {\n      flex: 1,\n      background: \"white\",\n      padding: \"1.5rem\",\n      borderRadius: \"12px\",\n      display: \"flex\",\n      flexDirection: \"column\",\n      alignItems: \"center\",\n      gap: \"0.5rem\",\n      boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.05)\",\n      transition: \"transform 0.2s ease\",\n      \"&:hover\": {\n        transform: \"translateY(-4px)\",\n      }\n    },\n    statIcon: {\n      fontSize: \"2rem\",\n    },\n    statValue: {\n      fontSize: \"1.5rem\",\n      fontWeight: \"bold\",\n      color: \"#2d3748\",\n    },\n    statLabel: {\n      fontSize: \"0.9rem\",\n      color: \"#718096\",\n    },\n    gateFilters: {\n      display: \"flex\",\n      gap: \"1rem\",\n      marginBottom: \"2rem\",\n      \"@media (max-width: 768px)\": {\n        flexDirection: \"column\",\n      }\n    },\n    gateSelect: {\n      padding: \"0.75rem 1rem\",\n      borderRadius: \"8px\",\n      border: \"1px solid #e2e8f0\",\n      background: \"white\",\n      fontSize: \"0.95rem\",\n      color: \"#2d3748\",\n      cursor: \"pointer\",\n      transition: \"all 0.2s ease\",\n      \"&:focus\": {\n        outline: \"none\",\n        borderColor: \"#FFB6B6\",\n        boxShadow: \"0 0 0 3px rgba(255, 182, 182, 0.1)\",\n      }\n    },\n    gateContent: {\n      display: \"flex\",\n      gap: \"2rem\",\n      \"@media (max-width: 1024px)\": {\n        flexDirection: \"column\",\n      }\n    },\n    gateSidebar: {\n      width: \"300px\",\n      flexShrink: 0,\n      \"@media (max-width: 1024px)\": {\n        width: \"100%\",\n      }\n    },\n    sidebarSection: {\n      background: \"white\",\n      borderRadius: \"12px\",\n      padding: \"1.5rem\",\n      marginBottom: \"1.5rem\",\n      boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.05)\",\n    },\n    sidebarTitleGate: {\n      fontSize: \"1.1rem\",\n      fontWeight: \"600\",\n      color: currentTheme.text,\n      marginBottom: \"1rem\",\n    },\n    quickLinks: {\n      display: \"flex\",\n      flexDirection: \"column\",\n      gap: \"0.75rem\",\n    },\n    quickLink: {\n      color: \"#FFB6B6\",\n      textDecoration: \"none\",\n      fontSize: \"0.95rem\",\n      display: \"flex\",\n      alignItems: \"center\",\n      gap: \"0.5rem\",\n      padding: \"0.5rem\",\n      borderRadius: \"6px\",\n      transition: \"all 0.2s ease\",\n      \"&:hover\": {\n        background: \"#FFF0F0\",\n      }\n    },\n    datesList: {\n      display: \"flex\",\n      flexDirection: \"column\",\n      gap: \"1rem\",\n    },\n    dateItem: {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      padding: \"0.75rem\",\n      background: \"#f7fafc\",\n      borderRadius: \"8px\",\n    },\n    dateLabel: {\n      fontSize: \"0.9rem\",\n      color: \"#4a5568\",\n    },\n    dateValue: {\n      fontSize: \"0.9rem\",\n      fontWeight: \"500\",\n      color: \"#2d3748\",\n    },\n    gateMainContent: {\n      flex: 1,\n    },\n    subjectsGrid: {\n      display: \"grid\",\n      gridTemplateColumns: \"repeat(auto-fill, minmax(250px, 1fr))\",\n      gap: \"1.5rem\",\n    },\n    subjectCard: {\n      background: \"white\",\n      borderRadius: \"12px\",\n      padding: \"1.5rem\",\n      display: \"flex\",\n      flexDirection: \"column\",\n      alignItems: \"center\",\n      gap: \"1rem\",\n      cursor: \"pointer\",\n      transition: \"all 0.3s ease\",\n      boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.05)\",\n      \"&:hover\": {\n        transform: \"translateY(-4px)\",\n        boxShadow: \"0 8px 16px rgba(0, 0, 0, 0.1)\",\n      }\n    },\n    subjectIcon: {\n      fontSize: \"2.5rem\",\n    },\n    subjectName: {\n      fontSize: \"1.1rem\",\n      fontWeight: \"600\",\n      color: \"#2d3748\",\n      textAlign: \"center\",\n    },\n    subjectTopics: {\n      fontSize: \"0.9rem\",\n      color: \"#718096\",\n    },\n    subjectContent: {\n      background: \"white\",\n      borderRadius: \"12px\",\n      padding: \"2rem\",\n      boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.05)\",\n    },\n    subjectTitle: {\n      fontSize: \"1.5rem\",\n      fontWeight: \"700\",\n      color: \"#2d3748\",\n      marginBottom: \"2rem\",\n    },\n    topicsList: {\n      display: \"grid\",\n      gridTemplateColumns: \"repeat(auto-fill, minmax(300px, 1fr))\",\n      gap: \"1.5rem\",\n    },\n    topicCard: {\n      background: \"#f7fafc\",\n      borderRadius: \"10px\",\n      padding: \"1.25rem\",\n      transition: \"all 0.2s ease\",\n      \"&:hover\": {\n        background: \"#FFF0F0\",\n      }\n    },\n    topicTitle: {\n      fontSize: \"1rem\",\n      fontWeight: \"500\",\n      color: \"#2d3748\",\n      marginBottom: \"1rem\",\n    },\n    topicResources: {\n      display: \"flex\",\n      gap: \"0.75rem\",\n    },\n    resourceButton: {\n      background: \"white\",\n      border: \"1px solid #e2e8f0\",\n      borderRadius: \"6px\",\n      padding: \"0.5rem 0.75rem\",\n      fontSize: \"0.9rem\",\n      color: \"#4a5568\",\n      cursor: \"pointer\",\n      transition: \"all 0.2s ease\",\n      \"&:hover\": {\n        background: \"#FFB6B6\",\n        color: \"white\",\n        borderColor: \"#FFB6B6\",\n      }\n    },\n    leftLogoContainer: {\n      display: \"flex\",\n      alignItems: \"center\",\n      marginRight: \"1rem\",\n    },\n    eduaiLogoImage: {\n      width: \"40px\",\n      height: \"40px\",\n      borderRadius: \"50%\",\n      objectFit: \"cover\",\n      border: \"2px solid #FF99CC\",\n      boxShadow: \"0 2px 4px rgba(255, 153, 204, 0.2)\",\n    },\n    userAvatar: {\n      width: '40px', height: '40px', borderRadius: '50%', background: '#f0f4f8', display: 'flex', alignItems: 'center', justifyContent: 'center', fontWeight: 700, fontSize: '1.1rem', marginRight: '0.75rem', border: '2px solid #FF99CC', boxShadow: '0 2px 4px rgba(255,153,204,0.1)'\n    },\n    userAvatarLarge: {\n      width: '56px', height: '56px', borderRadius: '50%', background: '#f0f4f8', display: 'flex', alignItems: 'center', justifyContent: 'center', fontWeight: 700, fontSize: '2rem', border: '2px solid #FF99CC', boxShadow: '0 2px 4px rgba(255,153,204,0.1)'\n    },\n\n    // Authentication Page Styles\n    authPageContainer: {\n      minHeight: '100vh',\n      background: currentTheme.gradientLight,\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      padding: '2rem',\n      position: 'relative',\n      overflow: 'hidden',\n      fontFamily: \"'Inter', -apple-system, BlinkMacSystemFont, sans-serif\",\n      '@media (max-width: 768px)': {\n        padding: '1rem',\n      },\n      '@media (max-width: 480px)': {\n        padding: '0.5rem',\n      }\n    },\n\n    authBackgroundAnimation: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      background: `\n        radial-gradient(circle at 20% 80%, ${currentTheme.primary}20 0%, transparent 50%),\n        radial-gradient(circle at 80% 20%, ${currentTheme.accent}20 0%, transparent 50%),\n        radial-gradient(circle at 40% 40%, ${currentTheme.primaryLight}15 0%, transparent 50%)\n      `,\n      animation: 'authFloat 6s ease-in-out infinite alternate',\n      zIndex: 1,\n    },\n\n    authFormContainer: {\n      background: 'rgba(255, 255, 255, 0.95)',\n      backdropFilter: 'blur(20px)',\n      borderRadius: '24px',\n      padding: '3rem',\n      width: '100%',\n      maxWidth: '480px',\n      boxShadow: `\n        0 20px 25px -5px ${currentTheme.shadow},\n        0 10px 10px -5px ${currentTheme.shadow},\n        inset 0 1px 0 rgba(255, 255, 255, 0.1)\n      `,\n      border: `1px solid rgba(255, 255, 255, 0.2)`,\n      position: 'relative',\n      zIndex: 2,\n      transition: 'all 0.3s ease',\n      '@media (max-width: 768px)': {\n        padding: '2rem',\n        borderRadius: '20px',\n      },\n      '@media (max-width: 480px)': {\n        padding: '1.5rem',\n        borderRadius: '16px',\n        maxWidth: '100%',\n      }\n    },\n\n    authFormTitle: {\n      fontSize: '2.5rem',\n      fontWeight: '700',\n      textAlign: 'center',\n      marginBottom: '0.5rem',\n      background: currentTheme.gradient,\n      WebkitBackgroundClip: 'text',\n      WebkitTextFillColor: 'transparent',\n      backgroundClip: 'text',\n      letterSpacing: '-0.02em',\n      '@media (max-width: 768px)': {\n        fontSize: '2rem',\n      },\n      '@media (max-width: 480px)': {\n        fontSize: '1.75rem',\n      }\n    },\n\n    authFormSubtitle: {\n      fontSize: '1rem',\n      color: currentTheme.textLight,\n      textAlign: 'center',\n      marginBottom: '2.5rem',\n      fontWeight: '400',\n      '@media (max-width: 480px)': {\n        fontSize: '0.9rem',\n        marginBottom: '2rem',\n      }\n    },\n\n    authInputGroup: {\n      marginBottom: '1.5rem',\n      position: 'relative',\n    },\n\n    authInput: {\n      width: '100%',\n      padding: '1rem 1.25rem',\n      fontSize: '1rem',\n      border: `2px solid ${currentTheme.border}`,\n      borderRadius: '12px',\n      background: 'rgba(255, 255, 255, 0.8)',\n      transition: 'all 0.3s ease',\n      outline: 'none',\n      fontFamily: 'inherit',\n      boxSizing: 'border-box',\n      '::placeholder': {\n        color: currentTheme.textLight,\n        opacity: 0.7,\n      },\n      ':focus': {\n        borderColor: currentTheme.primary,\n        background: 'rgba(255, 255, 255, 1)',\n        boxShadow: `0 0 0 3px ${currentTheme.primary}20`,\n        transform: 'translateY(-1px)',\n      },\n      '@media (max-width: 480px)': {\n        padding: '0.875rem 1rem',\n        fontSize: '0.95rem',\n      }\n    },\n\n    // EduAIChatBot specific styles\n    appContainer: {\n      minHeight: '100vh',\n      backgroundColor: currentTheme.background,\n      color: currentTheme.text,\n      transition: 'all 0.3s ease'\n    },\n    navbarFixed: {\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      right: 0,\n      height: '64px',\n      background: currentTheme.gradient,\n      boxShadow: `0 2px 10px ${currentTheme.shadow}`,\n      display: 'flex',\n      alignItems: 'center',\n      padding: '0 24px',\n      zIndex: 1000,\n      borderBottom: `1px solid ${currentTheme.border}`\n    },\n    sidebarFixed: {\n      position: 'fixed',\n      top: '64px',\n      left: 0,\n      bottom: 0,\n      width: '280px',\n      backgroundColor: currentTheme.surface,\n      transform: 'translateX(-100%)',\n      transition: 'transform 0.3s ease',\n      zIndex: 900,\n      boxShadow: `2px 0 10px ${currentTheme.shadow}`,\n      overflowY: 'auto'\n    },\n    sidebarOpen: {\n      transform: 'translateX(0)'\n    },\n    sidebarItemEdu: {\n      padding: '12px 16px',\n      display: 'flex',\n      alignItems: 'center',\n      cursor: 'pointer',\n      borderRadius: '8px',\n      margin: '4px 8px',\n      transition: 'all 0.2s ease',\n      color: currentTheme.text,\n      background: currentTheme.gradientLight,\n      '&:hover': {\n        background: currentTheme.gradient,\n        color: 'white'\n      }\n    },\n    sidebarItemActiveEdu: {\n      background: currentTheme.gradient,\n      color: 'white',\n      fontWeight: 500\n    },\n    mainContentEdu: {\n      paddingTop: '80px',\n      paddingBottom: '40px',\n      transition: 'margin-left 0.3s ease',\n      minHeight: 'calc(100vh - 120px)'\n    },\n    mainContentWithSidebar: {\n      marginLeft: '280px'\n    },\n    cardEdu: {\n      backgroundColor: currentTheme.surface,\n      borderRadius: '12px',\n      boxShadow: `0 4px 6px ${currentTheme.shadow}`,\n      padding: '24px',\n      marginBottom: '24px',\n      transition: 'all 0.3s ease'\n    },\n    buttonPrimary: {\n      background: currentTheme.gradient,\n      color: '#fff',\n      border: 'none',\n      borderRadius: '8px',\n      padding: '12px 24px',\n      fontWeight: 500,\n      fontSize: '16px',\n      cursor: 'pointer',\n      display: 'inline-flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      gap: '8px',\n      transition: 'all 0.2s ease',\n      '&:hover': {\n        transform: 'translateY(-2px)',\n        boxShadow: `0 4px 12px ${currentTheme.shadowDark}`\n      },\n      '&:disabled': {\n        opacity: 0.7,\n        cursor: 'not-allowed'\n      }\n    },\n    inputField: {\n      width: '100%',\n      padding: '14px 16px',\n      borderRadius: '8px',\n      border: `1.5px solid ${currentTheme.primary}`,\n      fontSize: '16px',\n      backgroundColor: currentTheme.secondary,\n      color: currentTheme.primary,\n      transition: 'all 0.3s ease',\n      '&:focus': {\n        outline: 'none',\n        borderColor: currentTheme.primary,\n        boxShadow: `0 0 0 2px ${currentTheme.shadow}`\n      }\n    },\n    chatBubbleUser: {\n      backgroundColor: currentTheme.primary,\n      color: '#fff',\n      padding: '12px 16px',\n      borderRadius: '18px 18px 4px 18px',\n      maxWidth: '80%',\n      marginLeft: 'auto',\n      marginBottom: '8px',\n      boxShadow: `0 2px 4px ${currentTheme.shadow}`\n    },\n    chatBubbleBot: {\n      backgroundColor: currentTheme.secondary,\n      color: currentTheme.text,\n      padding: '12px 16px',\n      borderRadius: '18px 18px 18px 4px',\n      maxWidth: '80%',\n      marginRight: 'auto',\n      marginBottom: '8px',\n      boxShadow: `0 2px 4px ${currentTheme.shadow}`\n    },\n    companyCardEdu: {\n      background: currentTheme.gradient,\n      borderRadius: '12px',\n      padding: '20px',\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      cursor: 'pointer',\n      transition: 'all 0.3s ease',\n      boxShadow: `0 4px 6px ${currentTheme.shadow}`,\n      '&:hover': {\n        transform: 'translateY(-4px)',\n        boxShadow: `0 8px 15px ${currentTheme.shadowDark}`,\n        background: currentTheme.gradientReverse\n      }\n    },\n    quizCardEdu: {\n      backgroundColor: currentTheme.surface,\n      borderRadius: '12px',\n      padding: '20px',\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      cursor: 'pointer',\n      transition: 'all 0.3s ease',\n      boxShadow: `0 4px 6px ${currentTheme.shadow}`,\n      '&:hover': {\n        transform: 'translateY(-4px)',\n        boxShadow: `0 8px 15px ${currentTheme.shadowDark}`,\n        backgroundColor: currentTheme.secondary\n      }\n    },\n    notification: {\n      position: 'fixed',\n      bottom: '24px',\n      right: '24px',\n      padding: '12px 24px',\n      borderRadius: '8px',\n      boxShadow: `0 4px 12px ${currentTheme.shadowDark}`,\n      zIndex: 2000,\n      animation: 'slideIn 0.3s ease-out',\n      color: '#fff'\n    },\n\n    // Academic Dashboard specific styles\n    academicBackground: {\n      minHeight: '100vh',\n      background: currentTheme.gradientLight,\n      padding: '2rem 1rem'\n    },\n    academicHeader: {\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      marginBottom: '2rem',\n      flexWrap: 'wrap',\n      gap: '1rem'\n    },\n    academicTitle: {\n      fontSize: '2.5rem',\n      fontWeight: 'bold',\n      color: currentTheme.primary,\n      margin: 0,\n      marginBottom: '0.5rem'\n    },\n    academicSubtitle: {\n      color: currentTheme.textLight,\n      margin: 0\n    },\n    academicButton: {\n      background: currentTheme.primary,\n      color: 'white',\n      padding: '0.75rem 1.5rem',\n      borderRadius: '0.5rem',\n      border: 'none',\n      cursor: 'pointer',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '0.5rem',\n      fontSize: '1rem',\n      transition: 'all 0.3s ease',\n      '&:hover': {\n        background: currentTheme.primaryDark\n      }\n    },\n    academicButtonSecondary: {\n      background: 'white',\n      color: currentTheme.primary,\n      border: `2px solid ${currentTheme.primary}`,\n      padding: '0.75rem 1.5rem',\n      borderRadius: '0.5rem',\n      cursor: 'pointer',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '0.5rem',\n      fontSize: '1rem',\n      transition: 'all 0.3s ease',\n      '&:hover': {\n        background: currentTheme.secondary\n      }\n    },\n    academicStatsGrid: {\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n      gap: '1.5rem',\n      marginBottom: '2rem'\n    },\n    academicStatCard: {\n      background: 'white',\n      borderRadius: '1rem',\n      boxShadow: `0 4px 6px ${currentTheme.shadow}`,\n      padding: '1.5rem',\n      display: 'flex',\n      alignItems: 'center',\n      transition: 'transform 0.3s ease',\n      cursor: 'pointer',\n      '&:hover': {\n        transform: 'translateY(-5px)'\n      }\n    },\n    academicStatIcon: {\n      padding: '0.75rem',\n      borderRadius: '50%',\n      marginRight: '1rem'\n    },\n    academicMainGrid: {\n      display: 'grid',\n      gridTemplateColumns: '2fr 1fr',\n      gap: '2rem',\n      '@media (max-width: 1024px)': {\n        gridTemplateColumns: '1fr'\n      }\n    },\n    academicCoursesSection: {\n      background: 'white',\n      borderRadius: '1rem',\n      boxShadow: `0 4px 6px ${currentTheme.shadow}`,\n      overflow: 'hidden'\n    },\n    academicSectionHeader: {\n      background: currentTheme.primary,\n      color: 'white',\n      padding: '1.5rem',\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      flexWrap: 'wrap',\n      gap: '1rem'\n    },\n    academicSectionTitle: {\n      fontSize: '1.25rem',\n      fontWeight: 'bold',\n      margin: 0\n    },\n    academicFilterButton: {\n      background: 'white',\n      color: currentTheme.primary,\n      padding: '0.5rem 1rem',\n      borderRadius: '0.375rem',\n      border: 'none',\n      fontSize: '0.875rem',\n      cursor: 'pointer',\n      transition: 'all 0.3s ease'\n    },\n    academicFilterButtonActive: {\n      background: currentTheme.primaryDark,\n      color: 'white'\n    },\n    academicCourseCard: {\n      border: `1px solid ${currentTheme.border}`,\n      borderRadius: '0.5rem',\n      padding: '1rem',\n      transition: 'all 0.3s ease',\n      cursor: 'pointer',\n      '&:hover': {\n        borderColor: currentTheme.primary,\n        transform: 'translateY(-2px)'\n      }\n    },\n\n    // Academics component styles\n    academicsContainer: {\n      width: '100%',\n      maxWidth: '1200px',\n      margin: '0 auto',\n      padding: '2rem 1rem'\n    },\n    academicsHeaderSection: {\n      textAlign: 'center',\n      marginBottom: '2rem'\n    },\n    academicsMainTitle: {\n      fontSize: '2rem',\n      fontWeight: '700',\n      color: currentTheme.text,\n      marginBottom: '0.5rem'\n    },\n    academicsMainSubtitle: {\n      fontSize: '1.1rem',\n      color: currentTheme.textLight,\n      maxWidth: '600px',\n      margin: '0 auto'\n    },\n    academicsGrid: {\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',\n      gap: '1.5rem',\n      marginBottom: '3rem'\n    },\n    academicCardBasic: {\n      background: currentTheme.surface,\n      borderRadius: '12px',\n      padding: '1.5rem',\n      boxShadow: `0 2px 8px ${currentTheme.shadow}`,\n      transition: 'transform 0.2s, box-shadow 0.2s',\n      cursor: 'pointer',\n      position: 'relative',\n      overflow: 'hidden',\n      '&:hover': {\n        transform: 'translateY(-4px)',\n        boxShadow: `0 4px 12px ${currentTheme.shadowDark}`\n      }\n    },\n    academicIcon: {\n      fontSize: '2rem',\n      marginBottom: '1rem'\n    },\n    academicCardTitleBasic: {\n      fontSize: '1.25rem',\n      fontWeight: '600',\n      color: currentTheme.text,\n      marginBottom: '0.5rem'\n    },\n    academicCardDescriptionBasic: {\n      fontSize: '1rem',\n      color: currentTheme.textLight,\n      lineHeight: '1.5'\n    },\n    subjectSection: {\n      marginTop: '2rem'\n    },\n    subjectTitleMain: {\n      fontSize: '1.5rem',\n      fontWeight: '600',\n      color: currentTheme.text,\n      marginBottom: '1.5rem',\n      textAlign: 'center'\n    },\n    subjectGrid: {\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',\n      gap: '1rem'\n    },\n    subjectCardBasic: {\n      background: currentTheme.surface,\n      borderRadius: '8px',\n      padding: '1.25rem 1rem',\n      textAlign: 'center',\n      fontWeight: '500',\n      color: currentTheme.textLight,\n      boxShadow: `0 1px 3px ${currentTheme.shadow}`,\n      transition: 'all 0.2s',\n      '&:hover': {\n        background: currentTheme.primary,\n        color: '#fff',\n        transform: 'translateY(-2px)'\n      }\n    }\n};\n\nexport default styles;"], "mappings": "AAAA;;AAEA;AACA,MAAMA,MAAM,GAAG;EACbC,YAAY,EAAE;IACZC,OAAO,EAAE,SAAS;IAClBC,WAAW,EAAE,SAAS;IACtBC,YAAY,EAAE,SAAS;IACvBC,SAAS,EAAE,SAAS;IACpBC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,yBAAyB;IACjCC,UAAU,EAAE,yBAAyB;IACrCC,QAAQ,EAAE,mDAAmD;IAC7DC,eAAe,EAAE,mDAAmD;IACpEC,aAAa,EAAE;EACjB,CAAC;EACDC,QAAQ,EAAE;IACRhB,OAAO,EAAE,SAAS;IAClBC,WAAW,EAAE,SAAS;IACtBC,YAAY,EAAE,SAAS;IACvBC,SAAS,EAAE,SAAS;IACpBC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,wBAAwB;IAChCC,UAAU,EAAE,wBAAwB;IACpCC,QAAQ,EAAE,mDAAmD;IAC7DC,eAAe,EAAE,mDAAmD;IACpEC,aAAa,EAAE;EACjB,CAAC;EACDE,eAAe,EAAE;IACfjB,OAAO,EAAE,SAAS;IAClBC,WAAW,EAAE,SAAS;IACtBC,YAAY,EAAE,SAAS;IACvBC,SAAS,EAAE,SAAS;IACpBC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,0BAA0B;IAClCC,UAAU,EAAE,0BAA0B;IACtCC,QAAQ,EAAE,mDAAmD;IAC7DC,eAAe,EAAE,mDAAmD;IACpEC,aAAa,EAAE;EACjB,CAAC;EACDG,WAAW,EAAE;IACXlB,OAAO,EAAE,SAAS;IAClBC,WAAW,EAAE,SAAS;IACtBC,YAAY,EAAE,SAAS;IACvBC,SAAS,EAAE,SAAS;IACpBC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,yBAAyB;IACjCC,UAAU,EAAE,yBAAyB;IACrCC,QAAQ,EAAE,mDAAmD;IAC7DC,eAAe,EAAE,mDAAmD;IACpEC,aAAa,EAAE;EACjB,CAAC;EACDI,QAAQ,EAAE;IACRnB,OAAO,EAAE,SAAS;IAClBC,WAAW,EAAE,SAAS;IACtBC,YAAY,EAAE,SAAS;IACvBC,SAAS,EAAE,SAAS;IACpBC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,oBAAoB;IAC5BC,UAAU,EAAE,oBAAoB;IAChCC,QAAQ,EAAE,mDAAmD;IAC7DC,eAAe,EAAE,mDAAmD;IACpEC,aAAa,EAAE;EACjB;AACF,CAAC;;AAED;AACA,MAAMK,YAAY,GAAGtB,MAAM,CAACoB,WAAW,CAAC,CAAC;;AAEzC;AACA,MAAMG,QAAQ,GAAGA,CAACC,SAAS,GAAG,aAAa,KAAK;EAC9C,OAAOxB,MAAM,CAACwB,SAAS,CAAC,IAAIxB,MAAM,CAACoB,WAAW;AAChD,CAAC;;AAED;AACA,MAAMK,WAAW,GAAID,SAAS,IAAK;EACjC,IAAIxB,MAAM,CAACwB,SAAS,CAAC,EAAE;IACrB;IACAE,MAAM,CAACC,MAAM,CAACL,YAAY,EAAEtB,MAAM,CAACwB,SAAS,CAAC,CAAC;IAC9C,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACA,MAAMI,kBAAkB,GAAGA,CAAA,KAAM;EAC/B,OAAOF,MAAM,CAACG,IAAI,CAAC7B,MAAM,CAAC;AAC5B,CAAC;AAED,MAAM8B,MAAM,GAAG;EACb;EACA9B,MAAM;EACNsB,YAAY;EACZC,QAAQ;EACRE,WAAW;EACXG,kBAAkB;EAElB;EACArB,UAAU,EAAE;IACVwB,SAAS,EAAE,OAAO;IAClBC,eAAe,EAAEV,YAAY,CAACf,UAAU;IACxC0B,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBC,UAAU,EAAE,wDAAwD;IACpEC,QAAQ,EAAE,UAAU;IACpBC,SAAS,EAAE;EACb,CAAC;EACCC,kBAAkB,EAAE;IAClBF,QAAQ,EAAE,OAAO;IACjBG,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTnC,UAAU,EAAE,mDAAmD;IAC/DoC,MAAM,EAAE,CAAC;IACT,UAAU,EAAE;MACVC,OAAO,EAAE,IAAI;MACbR,QAAQ,EAAE,UAAU;MACpBG,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTG,eAAe,EAAE;AACzB;AACA;AACA;AACA,SAAS;MACDC,SAAS,EAAE;IACb;EACF,CAAC;EACDC,MAAM,EAAE;IACNC,KAAK,EAAE,MAAM;IACbzC,UAAU,EAAEe,YAAY,CAACP,QAAQ;IACjCkC,SAAS,EAAE,aAAa3B,YAAY,CAACT,MAAM,EAAE;IAC7CqC,OAAO,EAAE,cAAc;IACvBd,QAAQ,EAAE,QAAQ;IAClBG,GAAG,EAAE,CAAC;IACNI,MAAM,EAAE,IAAI;IACZQ,cAAc,EAAE,WAAW;IAC3BC,YAAY,EAAE,aAAa9B,YAAY,CAACV,MAAM;EAChD,CAAC;EACDyC,YAAY,EAAE;IACZL,KAAK,EAAE,MAAM;IACbM,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE,QAAQ;IAChBtB,OAAO,EAAE,MAAM;IACfuB,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE;EACd,CAAC;EACDC,UAAU,EAAE;IACVnD,UAAU,EAAE,aAAa;IACzBK,MAAM,EAAE,MAAM;IACd+C,MAAM,EAAE,SAAS;IACjBC,KAAK,EAAE,SAAS;IAChBV,OAAO,EAAE,QAAQ;IACjBW,YAAY,EAAE,KAAK;IACnB5B,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxBM,UAAU,EAAE,eAAe;IAC3B,QAAQ,EAAE;MACRvD,UAAU,EAAE,qBAAqB;MACjCqD,KAAK,EAAE;IACT,CAAC;IACD,2BAA2B,EAAE;MAC3B3B,OAAO,EAAE;IACX;EACF,CAAC;EACD8B,oBAAoB,EAAE;IACpB9B,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBuB,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxBQ,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE;EACb,CAAC;EACDC,SAAS,EAAE;IACTC,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,MAAM;IAClB7D,UAAU,EAAEe,YAAY,CAACP,QAAQ;IACjCsD,oBAAoB,EAAE,MAAM;IAC5BC,mBAAmB,EAAE,aAAa;IAClCC,aAAa,EAAE,OAAO;IACtBC,aAAa,EAAE;EACjB,CAAC;EACDC,QAAQ,EAAE;IACRN,QAAQ,EAAE,SAAS;IACnBP,KAAK,EAAEtC,YAAY,CAACZ,SAAS;IAC7BgE,SAAS,EAAE,SAAS;IACpBN,UAAU,EAAE,GAAG;IACfG,aAAa,EAAE;EACjB,CAAC;EACDI,kBAAkB,EAAE;IAClB1C,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE;EACd,CAAC;EACDmB,SAAS,EAAE;IACT5B,KAAK,EAAE,MAAM;IACb6B,MAAM,EAAE,MAAM;IACdhB,YAAY,EAAE,KAAK;IACnBiB,SAAS,EAAE,OAAO;IAClBlE,MAAM,EAAE,aAAaU,YAAY,CAACpB,OAAO,EAAE;IAC3C+C,SAAS,EAAE,aAAa3B,YAAY,CAACT,MAAM;EAC7C,CAAC;EACDkE,OAAO,EAAE;IACP3C,QAAQ,EAAE,OAAO;IACjBG,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPQ,KAAK,EAAE,OAAO;IACd6B,MAAM,EAAE,OAAO;IACf7C,eAAe,EAAEV,YAAY,CAACd,OAAO;IACrCyC,SAAS,EAAE,cAAc3B,YAAY,CAACT,MAAM,EAAE;IAC9C8B,MAAM,EAAE,IAAI;IACZmB,UAAU,EAAE,4BAA4B;IACxC7B,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvB8C,SAAS,EAAE;EACb,CAAC;EACDC,aAAa,EAAE;IACb/B,OAAO,EAAE,oBAAoB;IAC7BjB,OAAO,EAAE,MAAM;IACfuB,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBL,YAAY,EAAE,aAAa9B,YAAY,CAACV,MAAM;EAChD,CAAC;EACDsE,YAAY,EAAE;IACZf,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAEtC,YAAY,CAACb;EACtB,CAAC;EACD0E,kBAAkB,EAAE;IAClB5E,UAAU,EAAE,aAAa;IACzBK,MAAM,EAAE,MAAM;IACd+C,MAAM,EAAE,SAAS;IACjBC,KAAK,EAAE,SAAS;IAChBV,OAAO,EAAE,SAAS;IAClBW,YAAY,EAAE,KAAK;IACnBC,UAAU,EAAE,eAAe;IAC3B,QAAQ,EAAE;MACRvD,UAAU,EAAE,qBAAqB;MACjCqD,KAAK,EAAE;IACT;EACF,CAAC;EACDwB,cAAc,EAAE;IACdpB,IAAI,EAAE,CAAC;IACPd,OAAO,EAAE,QAAQ;IACjB8B,SAAS,EAAE;EACb,CAAC;EACDK,gBAAgB,EAAE;IAChBC,YAAY,EAAE;EAChB,CAAC;EACDC,WAAW,EAAE;IACXtD,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpBP,OAAO,EAAE,gBAAgB;IACzBS,MAAM,EAAE,SAAS;IACjBG,UAAU,EAAE,eAAe;IAC3B1B,QAAQ,EAAE,UAAU;IACpB,QAAQ,EAAE;MACR7B,UAAU,EAAE,GAAGe,YAAY,CAACT,MAAM,EAAE;MACpC+C,KAAK,EAAEtC,YAAY,CAACpB;IACtB;EACF,CAAC;EACDsF,iBAAiB,EAAE;IACjBjF,UAAU,EAAE,GAAGe,YAAY,CAACT,MAAM,EAAE;IACpC+C,KAAK,EAAEtC,YAAY,CAACpB,OAAO;IAC3BkE,UAAU,EAAE;EACd,CAAC;EACDqB,WAAW,EAAE;IACXC,WAAW,EAAE,MAAM;IACnBvB,QAAQ,EAAE,QAAQ;IAClBlC,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE;EACd,CAAC;EACDkC,eAAe,EAAE;IACf3B,IAAI,EAAE;EACR,CAAC;EACD4B,iBAAiB,EAAE;IACjB9B,UAAU,EAAE,qBAAqB;IACjC7B,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxBN,OAAO,EAAE,SAAS;IAClBW,YAAY,EAAE,KAAK;IACnB,QAAQ,EAAE;MACRtD,UAAU,EAAE;IACd;EACF,CAAC;EACDsF,iBAAiB,EAAE;IACjBC,WAAW,EAAE,QAAQ;IACrBC,QAAQ,EAAE,QAAQ;IAClBjC,UAAU,EAAE;EACd,CAAC;EACDkC,OAAO,EAAE;IACP9C,OAAO,EAAE,gBAAgB;IACzBiB,QAAQ,EAAE,QAAQ;IAClBP,KAAK,EAAEtC,YAAY,CAACZ,SAAS;IAC7BiD,MAAM,EAAE,SAAS;IACjBG,UAAU,EAAE,eAAe;IAC3B,QAAQ,EAAE;MACRvD,UAAU,EAAE,GAAGe,YAAY,CAACT,MAAM,EAAE;MACpC+C,KAAK,EAAEtC,YAAY,CAACpB;IACtB;EACF,CAAC;EACD+F,aAAa,EAAE;IACb/C,OAAO,EAAE,QAAQ;IACjBgD,SAAS,EAAE,+BAA+B;IAC1CjC,SAAS,EAAE;EACb,CAAC;EACDkC,iBAAiB,EAAE;IACjBhC,QAAQ,EAAE,QAAQ;IAClBP,KAAK,EAAE;EACT,CAAC;EACDwC,OAAO,EAAE;IACPhE,QAAQ,EAAE,OAAO;IACjBG,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTV,eAAe,EAAE,oBAAoB;IACrCW,MAAM,EAAE,IAAI;IACZ,2BAA2B,EAAE;MAC3BV,OAAO,EAAE;IACX;EACF,CAAC;EACDoE,aAAa,EAAE;IACbrD,KAAK,EAAE,MAAM;IACbM,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE,WAAW;IACnBL,OAAO,EAAE,QAAQ;IACjBc,IAAI,EAAE,CAAC;IACP/B,OAAO,EAAE,MAAM;IACfuB,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,YAAY;IACxBrB,QAAQ,EAAE,UAAU;IACpBO,MAAM,EAAE,CAAC;IACT,2BAA2B,EAAE;MAC3BY,MAAM,EAAE,WAAW;MACnBL,OAAO,EAAE;IACX;EACF,CAAC;EACDoD,eAAe,EAAE;IACftD,KAAK,EAAE,MAAM;IACbM,QAAQ,EAAE,OAAO;IACjBtB,eAAe,EAAEV,YAAY,CAACd,OAAO;IACrCqD,YAAY,EAAE,MAAM;IACpBkC,QAAQ,EAAE,QAAQ;IAClB9C,SAAS,EAAE,eAAe3B,YAAY,CAACT,MAAM,EAAE;IAC/CuB,QAAQ,EAAE,UAAU;IACpBxB,MAAM,EAAE,aAAaU,YAAY,CAACV,MAAM,EAAE;IAC1CqB,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvB2C,MAAM,EAAE,qBAAqB;IAC7B,2BAA2B,EAAE;MACzBA,MAAM,EAAE,qBAAqB;MAC7BhB,YAAY,EAAE;IAClB,CAAC;IACD,2BAA2B,EAAE;MACzBgB,MAAM,EAAE,oBAAoB;MAC5BhB,YAAY,EAAE;IAClB;EACJ,CAAC;EACD0C,WAAW,EAAE;IACTnE,QAAQ,EAAE,UAAU;IACpBG,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTG,eAAe,EAAE;AACvB;AACA;AACA,OAAO;IACD2D,cAAc,EAAE,WAAW;IAC3BC,aAAa,EAAE;EACjB,CAAC;EACDC,MAAM,EAAE;IACNnG,UAAU,EAAEe,YAAY,CAACP,QAAQ;IACjC6C,KAAK,EAAE,OAAO;IACdV,OAAO,EAAE,cAAc;IACvBe,SAAS,EAAE,QAAQ;IACnB7B,QAAQ,EAAE,UAAU;IACpB2D,QAAQ,EAAE,QAAQ;IAClB,2BAA2B,EAAE;MACzB7C,OAAO,EAAE;IACb,CAAC;IACD,2BAA2B,EAAE;MACzBA,OAAO,EAAE;IACb,CAAC;IACD,UAAU,EAAE;MACRN,OAAO,EAAE,IAAI;MACbR,QAAQ,EAAE,UAAU;MACpBG,GAAG,EAAE,MAAM;MACXC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdnC,UAAU,EAAE;AACtB;AACA,WAAW;MACDoG,SAAS,EAAE,eAAe;MAC1B7D,SAAS,EAAE;IACf;EACJ,CAAC;EACD8D,KAAK,EAAE;IACHrD,MAAM,EAAE,CAAC;IACTY,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,GAAG;IACfhC,QAAQ,EAAE,UAAU;IACpByE,UAAU,EAAE;EACd,CAAC;EACDC,QAAQ,EAAE;IACRpC,SAAS,EAAE,SAAS;IACpBP,QAAQ,EAAE,MAAM;IAChB4C,OAAO,EAAE,GAAG;IACZ3E,QAAQ,EAAE,UAAU;IACpBgC,UAAU,EAAE;EACd,CAAC;EACD4C,aAAa,EAAE;IACbnC,MAAM,EAAE,qBAAqB;IAC7BoC,SAAS,EAAE,OAAO;IAClBlF,SAAS,EAAE,OAAO;IAClBiD,SAAS,EAAE,MAAM;IACjB9B,OAAO,EAAE,QAAQ;IACjB3C,UAAU,EAAEe,YAAY,CAACd,OAAO;IAChC4B,QAAQ,EAAE,UAAU;IACpBH,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBc,KAAK,EAAE,MAAM;IACbkE,SAAS,EAAE,YAAY;IACvB,4BAA4B,EAAE;MAC5BrC,MAAM,EAAE,qBAAqB;MAC7B3B,OAAO,EAAE;IACX,CAAC;IACD,2BAA2B,EAAE;MAC3B2B,MAAM,EAAE,qBAAqB;MAC7B3B,OAAO,EAAE,SAAS;MAClBnB,SAAS,EAAE;IACb,CAAC;IACD,2BAA2B,EAAE;MAC3B8C,MAAM,EAAE,qBAAqB;MAC7B3B,OAAO,EAAE,QAAQ;MACjBnB,SAAS,EAAE;IACb;EACF,CAAC;EACDoF,YAAY,EAAE;IACZlF,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBkF,GAAG,EAAE,MAAM;IACXhF,QAAQ,EAAE,UAAU;IACpB4B,IAAI,EAAE,CAAC;IACPgB,SAAS,EAAE,MAAM;IACjBqC,YAAY,EAAE,QAAQ;IACtBC,cAAc,EAAE,QAAQ;IACxBtE,KAAK,EAAE,MAAM;IACbkE,SAAS,EAAE,YAAY;IACvB,2BAA2B,EAAE;MAC3BE,GAAG,EAAE,QAAQ;MACbC,YAAY,EAAE;IAChB,CAAC;IACD,2BAA2B,EAAE;MAC3BD,GAAG,EAAE,SAAS;MACdC,YAAY,EAAE;IAChB;EACF,CAAC;EACDE,OAAO,EAAE;IACPrE,OAAO,EAAE,cAAc;IACvBW,YAAY,EAAE,MAAM;IACpBP,QAAQ,EAAE,KAAK;IACfN,KAAK,EAAE,aAAa;IACpBC,SAAS,EAAE,2BAA2B;IACtCuE,UAAU,EAAE,GAAG;IACfpF,QAAQ,EAAE,UAAU;IACpB0B,UAAU,EAAE,eAAe;IAC3BK,QAAQ,EAAE,MAAM;IAChBrB,SAAS,EAAE,sBAAsB;IACjC2E,QAAQ,EAAE,YAAY;IACtB,2BAA2B,EAAE;MACzBnE,QAAQ,EAAE,KAAK;MACfJ,OAAO,EAAE;IACb,CAAC;IACD,2BAA2B,EAAE;MACzBI,QAAQ,EAAE,KAAK;MACfJ,OAAO,EAAE,kBAAkB;MAC3BiB,QAAQ,EAAE;IACd;EACF,CAAC;EACDuD,cAAc,EAAE;IACdnH,UAAU,EAAEe,YAAY,CAACjB,SAAS;IAClC6C,OAAO,EAAE,QAAQ;IACjBW,YAAY,EAAE,MAAM;IACpBI,SAAS,EAAE,QAAQ;IACnBV,MAAM,EAAE,iBAAiB;IACzB3C,MAAM,EAAE,cAAcU,YAAY,CAACpB,OAAO,EAAE;IAC5CiD,cAAc,EAAE,WAAW;IAC3BL,SAAS,EAAE;EACb,CAAC;EACD6E,YAAY,EAAE;IACZxD,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,GAAG;IACfR,KAAK,EAAEtC,YAAY,CAACpB,OAAO;IAC3BoF,YAAY,EAAE;EAChB,CAAC;EACDsC,WAAW,EAAE;IACXzD,QAAQ,EAAE,MAAM;IAChBP,KAAK,EAAEtC,YAAY,CAACZ,SAAS;IAC7B8G,UAAU,EAAE;EACd,CAAC;EACDK,WAAW,EAAE;IACXC,SAAS,EAAE,UAAU;IACrBvH,UAAU,EAAEe,YAAY,CAACP,QAAQ;IACjC6C,KAAK,EAAE,OAAO;IACdmE,uBAAuB,EAAE;EAC3B,CAAC;EACDC,UAAU,EAAE;IACVF,SAAS,EAAE,YAAY;IACvBvH,UAAU,EAAEe,YAAY,CAACjB,SAAS;IAClCuD,KAAK,EAAEtC,YAAY,CAACb,IAAI;IACxBwH,sBAAsB,EAAE,KAAK;IAC7BrH,MAAM,EAAE,aAAaU,YAAY,CAACV,MAAM,EAAE;IAC1CuC,cAAc,EAAE;EAClB,CAAC;EACD+E,WAAW,EAAE;IACX/D,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,GAAG;IACfkB,YAAY,EAAE,QAAQ;IACtByB,OAAO,EAAE,GAAG;IACZvC,aAAa,EAAE,WAAW;IAC1BD,aAAa,EAAE;EACjB,CAAC;EACD4D,cAAc,EAAE;IACdhE,QAAQ,EAAE,MAAM;IAChBiE,UAAU,EAAE;EACd,CAAC;EACDC,cAAc,EAAE;IACdpG,OAAO,EAAE,MAAM;IACfiB,OAAO,EAAE,SAAS;IAClB3C,UAAU,EAAEe,YAAY,CAACjB,SAAS;IAClC6F,SAAS,EAAE,aAAa5E,YAAY,CAACV,MAAM,EAAE;IAC7CwB,QAAQ,EAAE,UAAU;IACpBe,cAAc,EAAE,WAAW;IAC3BW,UAAU,EAAE,eAAe;IAC3B6C,SAAS,EAAE,eAAe;IAC1BM,SAAS,EAAE,OAAO;IAClBjE,KAAK,EAAE,MAAM;IACbkE,SAAS,EAAE,YAAY;IACvB,WAAW,EAAE;MACX3G,UAAU,EAAEe,YAAY,CAACd,OAAO;MAChCyC,SAAS,EAAE,eAAe3B,YAAY,CAACT,MAAM;IAC/C,CAAC;IACD,4BAA4B,EAAE;MAC5BqC,OAAO,EAAE,MAAM;MACf+D,SAAS,EAAE;IACb,CAAC;IACD,2BAA2B,EAAE;MAC3B/D,OAAO,EAAE,SAAS;MAClBhB,aAAa,EAAE,QAAQ;MACvBkF,GAAG,EAAE,SAAS;MACdH,SAAS,EAAE;IACb,CAAC;IACD,2BAA2B,EAAE;MAC3B/D,OAAO,EAAE,QAAQ;MACjBhB,aAAa,EAAE,QAAQ;MACvBkF,GAAG,EAAE,QAAQ;MACbH,SAAS,EAAE;IACb;EACF,CAAC;EACDqB,KAAK,EAAE;IACLtE,IAAI,EAAE,CAAC;IACPd,OAAO,EAAE,aAAa;IACtBW,YAAY,EAAE,KAAK;IACnBjD,MAAM,EAAE,aAAaU,YAAY,CAACV,MAAM,EAAE;IAC1C2H,OAAO,EAAE,MAAM;IACfpE,QAAQ,EAAE,MAAM;IAChBL,UAAU,EAAE,eAAe;IAC3BvD,UAAU,EAAEe,YAAY,CAACd,OAAO;IAChCoD,KAAK,EAAEtC,YAAY,CAACb,IAAI;IACxBwC,SAAS,EAAE,aAAa3B,YAAY,CAACT,MAAM,EAAE;IAC7C,SAAS,EAAE;MACT2H,WAAW,EAAElH,YAAY,CAACpB,OAAO;MACjC+C,SAAS,EAAE,aAAa3B,YAAY,CAACT,MAAM,EAAE;MAC7CN,UAAU,EAAEe,YAAY,CAACd;IAC3B,CAAC;IACD,gBAAgB,EAAE;MAChBoD,KAAK,EAAEtC,YAAY,CAACZ,SAAS;MAC7BoD,UAAU,EAAE;IACd,CAAC;IACD,sBAAsB,EAAE;MACtBiD,OAAO,EAAE;IACX,CAAC;IACD,2BAA2B,EAAE;MAC3B7D,OAAO,EAAE,kBAAkB;MAC3BiB,QAAQ,EAAE;IACZ,CAAC;IACD,2BAA2B,EAAE;MAC3BjB,OAAO,EAAE,cAAc;MACvBiB,QAAQ,EAAE,QAAQ;MAClBnB,KAAK,EAAE;IACT;EACF,CAAC;EACDyF,UAAU,EAAE;IACVlI,UAAU,EAAEe,YAAY,CAACP,QAAQ;IACjC6C,KAAK,EAAE,OAAO;IACdhD,MAAM,EAAE,MAAM;IACdiD,YAAY,EAAE,KAAK;IACnBX,OAAO,EAAE,WAAW;IACpBwF,UAAU,EAAE,MAAM;IAClB/E,MAAM,EAAE,SAAS;IACjBS,UAAU,EAAE,GAAG;IACfD,QAAQ,EAAE,MAAM;IAChBlC,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxBM,UAAU,EAAE,eAAe;IAC3B1B,QAAQ,EAAE,UAAU;IACpB2D,QAAQ,EAAE,QAAQ;IAClB9C,SAAS,EAAE,aAAa3B,YAAY,CAACR,UAAU,EAAE;IACjD6H,QAAQ,EAAE,MAAM;IAChB9D,MAAM,EAAE,MAAM;IACd,wBAAwB,EAAE;MACxB8B,SAAS,EAAE,kBAAkB;MAC7B1D,SAAS,EAAE,aAAa3B,YAAY,CAACR,UAAU;IACjD,CAAC;IACD,YAAY,EAAE;MACZP,UAAU,EAAEe,YAAY,CAACZ,SAAS;MAClCiD,MAAM,EAAE,aAAa;MACrBgD,SAAS,EAAE,MAAM;MACjB1D,SAAS,EAAE,MAAM;MACjB8D,OAAO,EAAE;IACX,CAAC;IACD,2BAA2B,EAAE;MAC3B7D,OAAO,EAAE,UAAU;MACnBiB,QAAQ,EAAE,SAAS;MACnBU,MAAM,EAAE,MAAM;MACd6D,UAAU,EAAE;IACd,CAAC;IACD,2BAA2B,EAAE;MAC3BA,UAAU,EAAE,CAAC;MACb1F,KAAK,EAAE,MAAM;MACb6B,MAAM,EAAE,MAAM;MACd8B,SAAS,EAAE,0BAA0B;MACrCI,OAAO,EAAE;IACX;EACF,CAAC;EACD6B,iBAAiB,EAAE;IACjB5F,KAAK,EAAE,MAAM;IACb6B,MAAM,EAAE,MAAM;IACdjE,MAAM,EAAE,iCAAiC;IACzCiI,cAAc,EAAE,OAAO;IACvBhF,YAAY,EAAE,KAAK;IACnBf,SAAS,EAAE;EACb,CAAC;EACDgG,gBAAgB,EAAE;IAChB7G,OAAO,EAAE,MAAM;IACfuB,cAAc,EAAE,QAAQ;IACxBN,OAAO,EAAE;EACX,CAAC;EACD6F,WAAW,EAAE;IACX9G,OAAO,EAAE,MAAM;IACfmF,GAAG,EAAE,SAAS;IACd3D,UAAU,EAAE;EACd,CAAC;EACDuF,UAAU,EAAE;IACVhG,KAAK,EAAE,MAAM;IACb6B,MAAM,EAAE,MAAM;IACdhB,YAAY,EAAE,KAAK;IACnBtD,UAAU,EAAEe,YAAY,CAACpB,OAAO;IAChC4C,SAAS,EAAE,kCAAkC;IAC7C,eAAe,EAAE;MACfmG,cAAc,EAAE;IAClB,CAAC;IACD,eAAe,EAAE;MACfA,cAAc,EAAE;IAClB,CAAC;IACD,eAAe,EAAE;MACfA,cAAc,EAAE;IAClB;EACF,CAAC;EACDC,YAAY,EAAE;IACZlG,KAAK,EAAE,MAAM;IACbhB,eAAe,EAAE,OAAO;IACxB6B,YAAY,EAAE,MAAM;IACpBkC,QAAQ,EAAE,QAAQ;IAClB9C,SAAS,EAAE,iCAAiC;IAC5Cb,QAAQ,EAAE,UAAU;IACpBxB,MAAM,EAAE;EACV,CAAC;EACDuI,SAAS,EAAE;IACTjG,OAAO,EAAE,cAAc;IACvB3C,UAAU,EAAE,mDAAmD;IAC/D6C,YAAY,EAAE,+BAA+B;IAC7Ca,SAAS,EAAE;EACb,CAAC;EACDmF,QAAQ,EAAE;IACR7F,MAAM,EAAE,CAAC;IACTY,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,GAAG;IACfR,KAAK,EAAE;EACT,CAAC;EACDyF,WAAW,EAAE;IACX3E,SAAS,EAAE,SAAS;IACpBP,QAAQ,EAAE,MAAM;IAChBP,KAAK,EAAE,SAAS;IAChBQ,UAAU,EAAE;EACd,CAAC;EACDkF,SAAS,EAAE;IACTlH,QAAQ,EAAE,UAAU;IACpBkB,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,eAAe;IACvBtB,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE;EACd,CAAC;EACD8F,UAAU,EAAE;IACVnH,QAAQ,EAAE,UAAU;IACpBI,IAAI,EAAE,MAAM;IACZD,GAAG,EAAE,KAAK;IACVoE,SAAS,EAAE,kBAAkB;IAC7B/C,KAAK,EAAE;EACT,CAAC;EACD4F,WAAW,EAAE;IACXxG,KAAK,EAAE,MAAM;IACbE,OAAO,EAAE,qBAAqB;IAC9BW,YAAY,EAAE,KAAK;IACnBjD,MAAM,EAAE,mBAAmB;IAC3B2H,OAAO,EAAE,MAAM;IACfpE,QAAQ,EAAE,MAAM;IAChBL,UAAU,EAAE,eAAe;IAC3BvD,UAAU,EAAE,OAAO;IACnBqD,KAAK,EAAE,SAAS;IAChBX,SAAS,EAAE,8BAA8B;IACzC,QAAQ,EAAE;MACRuF,WAAW,EAAE,SAAS;MACtBvF,SAAS,EAAE;IACb;EACF,CAAC;EACDwG,iBAAiB,EAAE;IACjBrH,QAAQ,EAAE,UAAU;IACpBK,KAAK,EAAE,MAAM;IACbF,GAAG,EAAE,KAAK;IACVoE,SAAS,EAAE,kBAAkB;IAC7BpG,UAAU,EAAE,aAAa;IACzBK,MAAM,EAAE,MAAM;IACd+C,MAAM,EAAE,SAAS;IACjBC,KAAK,EAAE,SAAS;IAChBV,OAAO,EAAE,SAAS;IAClBW,YAAY,EAAE,KAAK;IACnB5B,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxBM,UAAU,EAAE,eAAe;IAC3B,QAAQ,EAAE;MACRvD,UAAU,EAAE;IACd;EACF,CAAC;EACDmJ,aAAa,EAAE;IACbzH,OAAO,EAAE,MAAM;IACf0H,mBAAmB,EAAE,uCAAuC;IAC5DvC,GAAG,EAAE,MAAM;IACXlE,OAAO,EAAE,MAAM;IACf3C,UAAU,EAAE;EACd,CAAC;EACDqJ,WAAW,EAAE;IACXrJ,UAAU,EAAEe,YAAY,CAACP,QAAQ;IACjCH,MAAM,EAAE,aAAaU,YAAY,CAACV,MAAM,EAAE;IAC1CiD,YAAY,EAAE,MAAM;IACpBX,OAAO,EAAE,SAAS;IAClBS,MAAM,EAAE,SAAS;IACjBG,UAAU,EAAE,eAAe;IAC3B1B,QAAQ,EAAE,UAAU;IACpB2D,QAAQ,EAAE,QAAQ;IAClB9C,SAAS,EAAE,aAAa3B,YAAY,CAACT,MAAM,EAAE;IAC7C,QAAQ,EAAE;MACR8F,SAAS,EAAE,kBAAkB;MAC7B1D,SAAS,EAAE,cAAc3B,YAAY,CAACT,MAAM,EAAE;MAC9C2H,WAAW,EAAElH,YAAY,CAACpB;IAC5B;EACF,CAAC;EACD2J,cAAc,EAAE;IACd7G,KAAK,EAAE,MAAM;IACb6B,MAAM,EAAE,MAAM;IACdhB,YAAY,EAAE,KAAK;IACnBtD,UAAU,EAAEe,YAAY,CAACN,eAAe;IACxC4C,KAAK,EAAE,OAAO;IACd3B,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxBY,UAAU,EAAE,MAAM;IAClBD,QAAQ,EAAE,QAAQ;IAClBmB,YAAY,EAAE;EAChB,CAAC;EACDwE,WAAW,EAAE;IACX3F,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAEtC,YAAY,CAACb,IAAI;IACxBqD,UAAU,EAAE;EACd,CAAC;EACDiG,kBAAkB,EAAE;IAClB3H,QAAQ,EAAE,UAAU;IACpBG,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTnC,UAAU,EAAE,oFAAoF;IAChGwG,OAAO,EAAE,CAAC;IACVjD,UAAU,EAAE,mBAAmB;IAC/B,QAAQ,EAAE;MACRiD,OAAO,EAAE;IACX;EACF,CAAC;EACDiD,SAAS,EAAE;IACT/F,SAAS,EAAE,QAAQ;IACnBf,OAAO,EAAE,MAAM;IACf+G,UAAU,EAAE,QAAQ;IACpBhI,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBuB,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE;EAClB,CAAC;EACD0G,aAAa,EAAE;IACb5E,YAAY,EAAE,MAAM;IACpB1B,KAAK,EAAE;EACT,CAAC;EACDuG,aAAa,EAAE;IACbhG,QAAQ,EAAE,QAAQ;IAClBP,KAAK,EAAE,SAAS;IAChB0B,YAAY,EAAE;EAChB,CAAC;EACD8E,sBAAsB,EAAE;IACtB7J,UAAU,EAAE,mDAAmD;IAC/DqD,KAAK,EAAE,OAAO;IACdhD,MAAM,EAAE,MAAM;IACdiD,YAAY,EAAE,KAAK;IACnBX,OAAO,EAAE,gBAAgB;IACzBS,MAAM,EAAE,SAAS;IACjBS,UAAU,EAAE,GAAG;IACfD,QAAQ,EAAE,MAAM;IAChBL,UAAU,EAAE,eAAe;IAC3Bb,SAAS,EAAE,mCAAmC;IAC9C,QAAQ,EAAE;MACR0D,SAAS,EAAE,kBAAkB;MAC7B1D,SAAS,EAAE;IACb;EACF,CAAC;EACDoH,gBAAgB,EAAE;IAChBrH,KAAK,EAAE,MAAM;IACbhB,eAAe,EAAE,OAAO;IACxB6B,YAAY,EAAE,MAAM;IACpBkC,QAAQ,EAAE,QAAQ;IAClB9C,SAAS,EAAE,iCAAiC;IAC5Cb,QAAQ,EAAE,UAAU;IACpBxB,MAAM,EAAE;EACV,CAAC;EACD0J,aAAa,EAAE;IACbpH,OAAO,EAAE,cAAc;IACvB3C,UAAU,EAAE,mDAAmD;IAC/D6C,YAAY,EAAE,+BAA+B;IAC7Ca,SAAS,EAAE;EACb,CAAC;EACDsG,YAAY,EAAE;IACZhH,MAAM,EAAE,CAAC;IACTY,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,GAAG;IACfR,KAAK,EAAE;EACT,CAAC;EACD4G,eAAe,EAAE;IACf9F,SAAS,EAAE,SAAS;IACpBP,QAAQ,EAAE,MAAM;IAChBP,KAAK,EAAE,SAAS;IAChBQ,UAAU,EAAE;EACd,CAAC;EACDqG,SAAS,EAAE;IACTxI,OAAO,EAAE,MAAM;IACf0H,mBAAmB,EAAE,uCAAuC;IAC5DvC,GAAG,EAAE,QAAQ;IACblE,OAAO,EAAE,MAAM;IACf3C,UAAU,EAAE;EACd,CAAC;EACDmK,QAAQ,EAAE;IACRnK,UAAU,EAAE,mDAAmD;IAAE;IACjEK,MAAM,EAAE,mBAAmB;IAC3BiD,YAAY,EAAE,MAAM;IACpBX,OAAO,EAAE,QAAQ;IACjBS,MAAM,EAAE,SAAS;IACjBG,UAAU,EAAE,eAAe;IAC3B1B,QAAQ,EAAE,UAAU;IACpB2D,QAAQ,EAAE,QAAQ;IAClB9D,OAAO,EAAE,MAAM;IACfuB,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBR,SAAS,EAAE,+BAA+B;IAC1C,QAAQ,EAAE;MACR0D,SAAS,EAAE,kBAAkB;MAC7B1D,SAAS,EAAE,+BAA+B;MAC1CuF,WAAW,EAAE;IACf;EACF,CAAC;EACDmC,eAAe,EAAE;IACf3G,IAAI,EAAE;EACR,CAAC;EACD4G,aAAa,EAAE;IACbzG,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAE,SAAS;IAChB0B,YAAY,EAAE;EAChB,CAAC;EACDuF,mBAAmB,EAAE;IACnB1G,QAAQ,EAAE,QAAQ;IAClBP,KAAK,EAAE,SAAS;IAChB4D,UAAU,EAAE;EACd,CAAC;EACDsD,aAAa,EAAE;IACblH,KAAK,EAAE,SAAS;IAChB8E,UAAU,EAAE;EACd,CAAC;EACDqC,aAAa,EAAE;IACb3I,QAAQ,EAAE,UAAU;IACpBG,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTnC,UAAU,EAAE,oFAAoF;IAChGwG,OAAO,EAAE,CAAC;IACVjD,UAAU,EAAE,mBAAmB;IAC/B,QAAQ,EAAE;MACRiD,OAAO,EAAE;IACX;EACF,CAAC;EACDiE,cAAc,EAAE;IACdhI,KAAK,EAAE,MAAM;IACbhB,eAAe,EAAE,OAAO;IACxB6B,YAAY,EAAE,MAAM;IACpBkC,QAAQ,EAAE,QAAQ;IAClB9C,SAAS,EAAE,iCAAiC;IAC5Cb,QAAQ,EAAE,UAAU;IACpBxB,MAAM,EAAE,+BAA+B;IACvCsC,OAAO,EAAE,MAAM;IACfe,SAAS,EAAE;EACb,CAAC;EACDgH,UAAU,EAAE;IACV1H,MAAM,EAAE,CAAC;IACTY,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,GAAG;IACfR,KAAK,EAAE;EACT,CAAC;EACDsH,aAAa,EAAE;IACbxG,SAAS,EAAE,SAAS;IACpBP,QAAQ,EAAE,MAAM;IAChBP,KAAK,EAAE,SAAS;IAChBQ,UAAU,EAAE,GAAG;IACfkB,YAAY,EAAE;EAChB,CAAC;EACD6F,iBAAiB,EAAE;IACjBnI,KAAK,EAAE,MAAM;IACbhB,eAAe,EAAE,OAAO;IACxB6B,YAAY,EAAE,MAAM;IACpBkC,QAAQ,EAAE,QAAQ;IAClB9C,SAAS,EAAE,iCAAiC;IAC5Cb,QAAQ,EAAE,UAAU;IACpBxB,MAAM,EAAE,+BAA+B;IACvCsC,OAAO,EAAE,MAAM;IACfe,SAAS,EAAE;EACb,CAAC;EACDmH,aAAa,EAAE;IACb7H,MAAM,EAAE,CAAC;IACTY,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,GAAG;IACfR,KAAK,EAAE;EACT,CAAC;EACDyH,gBAAgB,EAAE;IAChB3G,SAAS,EAAE,SAAS;IACpBP,QAAQ,EAAE,MAAM;IAChBP,KAAK,EAAE,SAAS;IAChBQ,UAAU,EAAE,GAAG;IACfkB,YAAY,EAAE;EAChB,CAAC;EACDgG,YAAY,EAAE;IACZtI,KAAK,EAAE,MAAM;IACbhB,eAAe,EAAE,OAAO;IACxB6B,YAAY,EAAE,MAAM;IACpBkC,QAAQ,EAAE,QAAQ;IAClB9C,SAAS,EAAE,iCAAiC;IAC5Cb,QAAQ,EAAE,UAAU;IACpBxB,MAAM,EAAE,+BAA+B;IACvCsC,OAAO,EAAE,MAAM;IACfe,SAAS,EAAE;EACb,CAAC;EACDsH,QAAQ,EAAE;IACRhI,MAAM,EAAE,CAAC;IACTY,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,GAAG;IACfR,KAAK,EAAE;EACT,CAAC;EACD4H,WAAW,EAAE;IACX9G,SAAS,EAAE,SAAS;IACpBP,QAAQ,EAAE,MAAM;IAChBP,KAAK,EAAE,SAAS;IAChBQ,UAAU,EAAE,GAAG;IACfkB,YAAY,EAAE;EAChB,CAAC;EACDmG,UAAU,EAAE;IACVxJ,OAAO,EAAE,MAAM;IACfuB,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpB1B,SAAS,EAAE;EACb,CAAC;EACD2J,iBAAiB,EAAE;IACjBpI,QAAQ,EAAE,OAAO;IACjBW,SAAS,EAAE;EACb,CAAC;EACD0H,cAAc,EAAE;IACdxH,QAAQ,EAAE,MAAM;IAChBmB,YAAY,EAAE,MAAM;IACpB1B,KAAK,EAAE;EACT,CAAC;EACDgI,cAAc,EAAE;IACdzH,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,GAAG;IACfR,KAAK,EAAE,SAAS;IAChB0B,YAAY,EAAE;EAChB,CAAC;EACDuG,qBAAqB,EAAE;IACrB1H,QAAQ,EAAE,MAAM;IAChBP,KAAK,EAAE,SAAS;IAChB4D,UAAU,EAAE;EACd,CAAC;EACD,mBAAmB,EAAE;IACnBsE,IAAI,EAAE;MAAE/E,OAAO,EAAE,CAAC;MAAEJ,SAAS,EAAE;IAAmB,CAAC;IACnDoF,EAAE,EAAE;MAAEhF,OAAO,EAAE,CAAC;MAAEJ,SAAS,EAAE;IAAgB;EAC/C,CAAC;EACD,iBAAiB,EAAE;IACjBoF,EAAE,EAAE;MAAEpF,SAAS,EAAE;IAAiB;EACpC,CAAC;EACD,mBAAmB,EAAE;IACnB,eAAe,EAAE;MAAEA,SAAS,EAAE;IAAa,CAAC;IAC5C,KAAK,EAAE;MAAEA,SAAS,EAAE;IAAW;EACjC,CAAC;EACD,2BAA2B,EAAE;IAC3B,IAAI,EAAE;MAAEA,SAAS,EAAE;IAAkB,CAAC;IACtC,MAAM,EAAE;MAAEA,SAAS,EAAE;IAAwB;EAC/C,CAAC;EACD,kBAAkB,EAAE;IAClB,IAAI,EAAE;MAAEA,SAAS,EAAE;IAAsC,CAAC;IAC1D,MAAM,EAAE;MAAEA,SAAS,EAAE;IAAoC;EAC3D,CAAC;EACDqF,eAAe,EAAE;IACf/J,OAAO,EAAE,MAAM;IACf0H,mBAAmB,EAAE,sCAAsC;IAC3DvC,GAAG,EAAE,QAAQ;IACblE,OAAO,EAAE,UAAU;IACnB,2BAA2B,EAAE;MAC3ByG,mBAAmB,EAAE,KAAK;MAC1BzG,OAAO,EAAE;IACX;EACF,CAAC;EACD+I,UAAU,EAAE;IACV1L,UAAU,EAAE,OAAO;IACnBsD,YAAY,EAAE,MAAM;IACpBX,OAAO,EAAE,QAAQ;IACjBtC,MAAM,EAAE,mBAAmB;IAC3BkD,UAAU,EAAE,eAAe;IAC3B7B,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBkF,GAAG,EAAE,MAAM;IACXzD,MAAM,EAAE,SAAS;IACjBV,SAAS,EAAE,+BAA+B;IAC1C,SAAS,EAAE;MACT0D,SAAS,EAAE,kBAAkB;MAC7B1D,SAAS,EAAE,+BAA+B;MAC1CuF,WAAW,EAAE;IACf;EACF,CAAC;EACD0D,iBAAiB,EAAE;IACjBlJ,KAAK,EAAE,MAAM;IACb6B,MAAM,EAAE,MAAM;IACdhB,YAAY,EAAE,MAAM;IACpBtD,UAAU,EAAE,mDAAmD;IAC/D0B,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxB8B,YAAY,EAAE;EAChB,CAAC;EACD6G,QAAQ,EAAE;IACRhI,QAAQ,EAAE;EACZ,CAAC;EACDiI,WAAW,EAAE;IACXpI,IAAI,EAAE;EACR,CAAC;EACDqI,SAAS,EAAE;IACTlI,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAE,SAAS;IAChB0B,YAAY,EAAE;EAChB,CAAC;EACDgH,eAAe,EAAE;IACfnI,QAAQ,EAAE,QAAQ;IAClBP,KAAK,EAAE,SAAS;IAChB4D,UAAU,EAAE;EACd,CAAC;EACD+E,aAAa,EAAE;IACbtK,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBkF,GAAG,EAAE,QAAQ;IACb1C,SAAS,EAAE;EACb,CAAC;EACD8H,YAAY,EAAE;IACZ5I,KAAK,EAAE,SAAS;IAChB6I,cAAc,EAAE,MAAM;IACtBtI,QAAQ,EAAE,QAAQ;IAClBjB,OAAO,EAAE,QAAQ;IACjBW,YAAY,EAAE,KAAK;IACnBtD,UAAU,EAAE,SAAS;IACrBuD,UAAU,EAAE,eAAe;IAC3B,SAAS,EAAE;MACTvD,UAAU,EAAE,SAAS;MACrBqD,KAAK,EAAE;IACT;EACF,CAAC;EACD8I,eAAe,EAAE;IACfzK,OAAO,EAAE,cAAc;IACvBiB,OAAO,EAAE,aAAa;IACtBW,YAAY,EAAE,MAAM;IACpBtD,UAAU,EAAE,SAAS;IACrBqD,KAAK,EAAE,SAAS;IAChBO,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,KAAK;IACjBM,SAAS,EAAE;EACb,CAAC;EACDiI,qBAAqB,EAAE;IACrB3J,KAAK,EAAE,MAAM;IACbE,OAAO,EAAE;EACX,CAAC;EACD0J,UAAU,EAAE;IACVtH,YAAY,EAAE,MAAM;IACpBrD,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBkF,GAAG,EAAE;EACP,CAAC;EACDyF,UAAU,EAAE;IACVtM,UAAU,EAAE,MAAM;IAClBK,MAAM,EAAE,MAAM;IACdgD,KAAK,EAAE,SAAS;IAChBD,MAAM,EAAE,SAAS;IACjBQ,QAAQ,EAAE,MAAM;IAChBjB,OAAO,EAAE,WAAW;IACpBjB,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpB2D,GAAG,EAAE,QAAQ;IACbtD,UAAU,EAAE,eAAe;IAC3BwB,YAAY,EAAE,MAAM;IACpB,SAAS,EAAE;MACT1B,KAAK,EAAE,SAAS;MAChB+C,SAAS,EAAE;IACb;EACF,CAAC;EACDmG,iBAAiB,EAAE;IACjB3I,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAE,SAAS;IAChB3B,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpB2D,GAAG,EAAE;EACP,CAAC;EACD2F,kBAAkB,EAAE;IAClB9K,OAAO,EAAE,MAAM;IACfmF,GAAG,EAAE,SAAS;IACd4F,QAAQ,EAAE,MAAM;IAChB1H,YAAY,EAAE,MAAM;IACpBpC,OAAO,EAAE,MAAM;IACf3C,UAAU,EAAE,SAAS;IACrBsD,YAAY,EAAE;EAChB,CAAC;EACDoJ,YAAY,EAAE;IACZ1M,UAAU,EAAE,OAAO;IACnBK,MAAM,EAAE,mBAAmB;IAC3BiD,YAAY,EAAE,KAAK;IACnBX,OAAO,EAAE,aAAa;IACtBS,MAAM,EAAE,SAAS;IACjBG,UAAU,EAAE,eAAe;IAC3BK,QAAQ,EAAE,QAAQ;IAClBlC,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpB2D,GAAG,EAAE,QAAQ;IACb,SAAS,EAAE;MACT7G,UAAU,EAAE,SAAS;MACrBiI,WAAW,EAAE;IACf;EACF,CAAC;EACD0E,kBAAkB,EAAE;IAClB3M,UAAU,EAAE,SAAS;IACrBqD,KAAK,EAAE,OAAO;IACd4E,WAAW,EAAE,SAAS;IACtB,SAAS,EAAE;MACTjI,UAAU,EAAE;IACd;EACF,CAAC;EACD4M,aAAa,EAAE;IACblL,OAAO,EAAE,MAAM;IACf0H,mBAAmB,EAAE,sCAAsC;IAC3DvC,GAAG,EAAE;EACP,CAAC;EACDgG,gBAAgB,EAAE;IAChB7M,UAAU,EAAE,OAAO;IACnBsD,YAAY,EAAE,MAAM;IACpBX,OAAO,EAAE,QAAQ;IACjBtC,MAAM,EAAE,mBAAmB;IAC3BqC,SAAS,EAAE;EACb,CAAC;EACDoK,aAAa,EAAE;IACblJ,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAE,SAAS;IAChB0B,YAAY,EAAE,MAAM;IACpBrD,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpB2D,GAAG,EAAE;EACP,CAAC;EACDkG,YAAY,EAAE;IACZrL,OAAO,EAAE,MAAM;IACfuB,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBP,OAAO,EAAE,MAAM;IACf3C,UAAU,EAAE,SAAS;IACrBsD,YAAY,EAAE,KAAK;IACnByB,YAAY,EAAE,SAAS;IACvBmH,cAAc,EAAE,MAAM;IACtB7I,KAAK,EAAE,SAAS;IAChBE,UAAU,EAAE,eAAe;IAC3B,SAAS,EAAE;MACTvD,UAAU,EAAE,SAAS;MACrBoG,SAAS,EAAE;IACb;EACF,CAAC;EACD4G,YAAY,EAAE;IACZvJ,IAAI,EAAE;EACR,CAAC;EACDwJ,aAAa,EAAE;IACbrJ,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAE,SAAS;IAChB0B,YAAY,EAAE;EAChB,CAAC;EACDmI,mBAAmB,EAAE;IACnBtJ,QAAQ,EAAE,UAAU;IACpBP,KAAK,EAAE,SAAS;IAChB0B,YAAY,EAAE;EAChB,CAAC;EACDoI,YAAY,EAAE;IACZvJ,QAAQ,EAAE,SAAS;IACnBP,KAAK,EAAE,SAAS;IAChBrD,UAAU,EAAE,SAAS;IACrB2C,OAAO,EAAE,gBAAgB;IACzBW,YAAY,EAAE;EAChB,CAAC;EACD8J,YAAY,EAAE;IACZxJ,QAAQ,EAAE,SAAS;IACnBP,KAAK,EAAE,SAAS;IAChB8E,UAAU,EAAE;EACd,CAAC;EACDkF,YAAY,EAAE;IACZzJ,QAAQ,EAAE,SAAS;IACnBuB,WAAW,EAAE;EACf,CAAC;EACDmI,eAAe,EAAE;IACf5L,OAAO,EAAE,MAAM;IACfmF,GAAG,EAAE,QAAQ;IACb1C,SAAS,EAAE,MAAM;IACjBd,KAAK,EAAE;EACT,CAAC;EACDkK,aAAa,EAAE;IACb9K,KAAK,EAAE,MAAM;IACbE,OAAO,EAAE;EACX,CAAC;EACD6K,UAAU,EAAE;IACVzI,YAAY,EAAE;EAChB,CAAC;EACD0I,SAAS,EAAE;IACT/L,OAAO,EAAE,MAAM;IACfmF,GAAG,EAAE,QAAQ;IACb9B,YAAY,EAAE,MAAM;IACpB,2BAA2B,EAAE;MAC3BpD,aAAa,EAAE,QAAQ;MACvBkF,GAAG,EAAE;IACP;EACF,CAAC;EACD6G,QAAQ,EAAE;IACRjK,IAAI,EAAE,CAAC;IACPzD,UAAU,EAAE,OAAO;IACnB2C,OAAO,EAAE,QAAQ;IACjBW,YAAY,EAAE,MAAM;IACpB5B,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBuB,UAAU,EAAE,QAAQ;IACpB2D,GAAG,EAAE,QAAQ;IACbnE,SAAS,EAAE,+BAA+B;IAC1Ca,UAAU,EAAE,qBAAqB;IACjC,SAAS,EAAE;MACT6C,SAAS,EAAE;IACb;EACF,CAAC;EACDuH,QAAQ,EAAE;IACR/J,QAAQ,EAAE;EACZ,CAAC;EACDgK,SAAS,EAAE;IACThK,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,MAAM;IAClBR,KAAK,EAAE;EACT,CAAC;EACDwK,SAAS,EAAE;IACTjK,QAAQ,EAAE,QAAQ;IAClBP,KAAK,EAAE;EACT,CAAC;EACDyK,WAAW,EAAE;IACXpM,OAAO,EAAE,MAAM;IACfmF,GAAG,EAAE,MAAM;IACX9B,YAAY,EAAE,MAAM;IACpB,2BAA2B,EAAE;MAC3BpD,aAAa,EAAE;IACjB;EACF,CAAC;EACDoM,UAAU,EAAE;IACVpL,OAAO,EAAE,cAAc;IACvBW,YAAY,EAAE,KAAK;IACnBjD,MAAM,EAAE,mBAAmB;IAC3BL,UAAU,EAAE,OAAO;IACnB4D,QAAQ,EAAE,SAAS;IACnBP,KAAK,EAAE,SAAS;IAChBD,MAAM,EAAE,SAAS;IACjBG,UAAU,EAAE,eAAe;IAC3B,SAAS,EAAE;MACTyE,OAAO,EAAE,MAAM;MACfC,WAAW,EAAE,SAAS;MACtBvF,SAAS,EAAE;IACb;EACF,CAAC;EACDsL,WAAW,EAAE;IACXtM,OAAO,EAAE,MAAM;IACfmF,GAAG,EAAE,MAAM;IACX,4BAA4B,EAAE;MAC5BlF,aAAa,EAAE;IACjB;EACF,CAAC;EACDsM,WAAW,EAAE;IACXxL,KAAK,EAAE,OAAO;IACdyL,UAAU,EAAE,CAAC;IACb,4BAA4B,EAAE;MAC5BzL,KAAK,EAAE;IACT;EACF,CAAC;EACD0L,cAAc,EAAE;IACdnO,UAAU,EAAE,OAAO;IACnBsD,YAAY,EAAE,MAAM;IACpBX,OAAO,EAAE,QAAQ;IACjBoC,YAAY,EAAE,QAAQ;IACtBrC,SAAS,EAAE;EACb,CAAC;EACD0L,gBAAgB,EAAE;IAChBxK,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAEtC,YAAY,CAACb,IAAI;IACxB6E,YAAY,EAAE;EAChB,CAAC;EACDsJ,UAAU,EAAE;IACV3M,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBkF,GAAG,EAAE;EACP,CAAC;EACDyH,SAAS,EAAE;IACTjL,KAAK,EAAE,SAAS;IAChB6I,cAAc,EAAE,MAAM;IACtBtI,QAAQ,EAAE,SAAS;IACnBlC,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpB2D,GAAG,EAAE,QAAQ;IACblE,OAAO,EAAE,QAAQ;IACjBW,YAAY,EAAE,KAAK;IACnBC,UAAU,EAAE,eAAe;IAC3B,SAAS,EAAE;MACTvD,UAAU,EAAE;IACd;EACF,CAAC;EACDuO,SAAS,EAAE;IACT7M,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBkF,GAAG,EAAE;EACP,CAAC;EACD2H,QAAQ,EAAE;IACR9M,OAAO,EAAE,MAAM;IACfuB,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBP,OAAO,EAAE,SAAS;IAClB3C,UAAU,EAAE,SAAS;IACrBsD,YAAY,EAAE;EAChB,CAAC;EACDmL,SAAS,EAAE;IACT7K,QAAQ,EAAE,QAAQ;IAClBP,KAAK,EAAE;EACT,CAAC;EACDqL,SAAS,EAAE;IACT9K,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAE;EACT,CAAC;EACDsL,eAAe,EAAE;IACflL,IAAI,EAAE;EACR,CAAC;EACDmL,YAAY,EAAE;IACZlN,OAAO,EAAE,MAAM;IACf0H,mBAAmB,EAAE,uCAAuC;IAC5DvC,GAAG,EAAE;EACP,CAAC;EACDgI,WAAW,EAAE;IACX7O,UAAU,EAAE,OAAO;IACnBsD,YAAY,EAAE,MAAM;IACpBX,OAAO,EAAE,QAAQ;IACjBjB,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBuB,UAAU,EAAE,QAAQ;IACpB2D,GAAG,EAAE,MAAM;IACXzD,MAAM,EAAE,SAAS;IACjBG,UAAU,EAAE,eAAe;IAC3Bb,SAAS,EAAE,+BAA+B;IAC1C,SAAS,EAAE;MACT0D,SAAS,EAAE,kBAAkB;MAC7B1D,SAAS,EAAE;IACb;EACF,CAAC;EACDoM,WAAW,EAAE;IACXlL,QAAQ,EAAE;EACZ,CAAC;EACDmL,WAAW,EAAE;IACXnL,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAE,SAAS;IAChBK,SAAS,EAAE;EACb,CAAC;EACDsL,aAAa,EAAE;IACbpL,QAAQ,EAAE,QAAQ;IAClBP,KAAK,EAAE;EACT,CAAC;EACD4L,cAAc,EAAE;IACdjP,UAAU,EAAE,OAAO;IACnBsD,YAAY,EAAE,MAAM;IACpBX,OAAO,EAAE,MAAM;IACfD,SAAS,EAAE;EACb,CAAC;EACDwM,YAAY,EAAE;IACZtL,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAE,SAAS;IAChB0B,YAAY,EAAE;EAChB,CAAC;EACDoK,UAAU,EAAE;IACVzN,OAAO,EAAE,MAAM;IACf0H,mBAAmB,EAAE,uCAAuC;IAC5DvC,GAAG,EAAE;EACP,CAAC;EACDuI,SAAS,EAAE;IACTpP,UAAU,EAAE,SAAS;IACrBsD,YAAY,EAAE,MAAM;IACpBX,OAAO,EAAE,SAAS;IAClBY,UAAU,EAAE,eAAe;IAC3B,SAAS,EAAE;MACTvD,UAAU,EAAE;IACd;EACF,CAAC;EACDqP,UAAU,EAAE;IACVzL,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAE,SAAS;IAChB0B,YAAY,EAAE;EAChB,CAAC;EACDuK,cAAc,EAAE;IACd5N,OAAO,EAAE,MAAM;IACfmF,GAAG,EAAE;EACP,CAAC;EACD0I,cAAc,EAAE;IACdvP,UAAU,EAAE,OAAO;IACnBK,MAAM,EAAE,mBAAmB;IAC3BiD,YAAY,EAAE,KAAK;IACnBX,OAAO,EAAE,gBAAgB;IACzBiB,QAAQ,EAAE,QAAQ;IAClBP,KAAK,EAAE,SAAS;IAChBD,MAAM,EAAE,SAAS;IACjBG,UAAU,EAAE,eAAe;IAC3B,SAAS,EAAE;MACTvD,UAAU,EAAE,SAAS;MACrBqD,KAAK,EAAE,OAAO;MACd4E,WAAW,EAAE;IACf;EACF,CAAC;EACDuH,iBAAiB,EAAE;IACjB9N,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpBiC,WAAW,EAAE;EACf,CAAC;EACDsK,cAAc,EAAE;IACdhN,KAAK,EAAE,MAAM;IACb6B,MAAM,EAAE,MAAM;IACdhB,YAAY,EAAE,KAAK;IACnBiB,SAAS,EAAE,OAAO;IAClBlE,MAAM,EAAE,mBAAmB;IAC3BqC,SAAS,EAAE;EACb,CAAC;EACDgN,UAAU,EAAE;IACVjN,KAAK,EAAE,MAAM;IAAE6B,MAAM,EAAE,MAAM;IAAEhB,YAAY,EAAE,KAAK;IAAEtD,UAAU,EAAE,SAAS;IAAE0B,OAAO,EAAE,MAAM;IAAEwB,UAAU,EAAE,QAAQ;IAAED,cAAc,EAAE,QAAQ;IAAEY,UAAU,EAAE,GAAG;IAAED,QAAQ,EAAE,QAAQ;IAAEuB,WAAW,EAAE,SAAS;IAAE9E,MAAM,EAAE,mBAAmB;IAAEqC,SAAS,EAAE;EACnP,CAAC;EACDiN,eAAe,EAAE;IACflN,KAAK,EAAE,MAAM;IAAE6B,MAAM,EAAE,MAAM;IAAEhB,YAAY,EAAE,KAAK;IAAEtD,UAAU,EAAE,SAAS;IAAE0B,OAAO,EAAE,MAAM;IAAEwB,UAAU,EAAE,QAAQ;IAAED,cAAc,EAAE,QAAQ;IAAEY,UAAU,EAAE,GAAG;IAAED,QAAQ,EAAE,MAAM;IAAEvD,MAAM,EAAE,mBAAmB;IAAEqC,SAAS,EAAE;EACzN,CAAC;EAED;EACAkN,iBAAiB,EAAE;IACjBpO,SAAS,EAAE,OAAO;IAClBxB,UAAU,EAAEe,YAAY,CAACL,aAAa;IACtCgB,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxBN,OAAO,EAAE,MAAM;IACfd,QAAQ,EAAE,UAAU;IACpB2D,QAAQ,EAAE,QAAQ;IAClB5D,UAAU,EAAE,wDAAwD;IACpE,2BAA2B,EAAE;MAC3Be,OAAO,EAAE;IACX,CAAC;IACD,2BAA2B,EAAE;MAC3BA,OAAO,EAAE;IACX;EACF,CAAC;EAEDkN,uBAAuB,EAAE;IACvBhO,QAAQ,EAAE,UAAU;IACpBG,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTnC,UAAU,EAAE;AAClB,6CAA6Ce,YAAY,CAACpB,OAAO;AACjE,6CAA6CoB,YAAY,CAAChB,MAAM;AAChE,6CAA6CgB,YAAY,CAAClB,YAAY;AACtE,OAAO;IACD0C,SAAS,EAAE,6CAA6C;IACxDH,MAAM,EAAE;EACV,CAAC;EAED0N,iBAAiB,EAAE;IACjB9P,UAAU,EAAE,2BAA2B;IACvC4C,cAAc,EAAE,YAAY;IAC5BU,YAAY,EAAE,MAAM;IACpBX,OAAO,EAAE,MAAM;IACfF,KAAK,EAAE,MAAM;IACbM,QAAQ,EAAE,OAAO;IACjBL,SAAS,EAAE;AACjB,2BAA2B3B,YAAY,CAACT,MAAM;AAC9C,2BAA2BS,YAAY,CAACT,MAAM;AAC9C;AACA,OAAO;IACDD,MAAM,EAAE,oCAAoC;IAC5CwB,QAAQ,EAAE,UAAU;IACpBO,MAAM,EAAE,CAAC;IACTmB,UAAU,EAAE,eAAe;IAC3B,2BAA2B,EAAE;MAC3BZ,OAAO,EAAE,MAAM;MACfW,YAAY,EAAE;IAChB,CAAC;IACD,2BAA2B,EAAE;MAC3BX,OAAO,EAAE,QAAQ;MACjBW,YAAY,EAAE,MAAM;MACpBP,QAAQ,EAAE;IACZ;EACF,CAAC;EAEDgN,aAAa,EAAE;IACbnM,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,KAAK;IACjBH,SAAS,EAAE,QAAQ;IACnBqB,YAAY,EAAE,QAAQ;IACtB/E,UAAU,EAAEe,YAAY,CAACP,QAAQ;IACjCsD,oBAAoB,EAAE,MAAM;IAC5BC,mBAAmB,EAAE,aAAa;IAClCiM,cAAc,EAAE,MAAM;IACtBhM,aAAa,EAAE,SAAS;IACxB,2BAA2B,EAAE;MAC3BJ,QAAQ,EAAE;IACZ,CAAC;IACD,2BAA2B,EAAE;MAC3BA,QAAQ,EAAE;IACZ;EACF,CAAC;EAEDqM,gBAAgB,EAAE;IAChBrM,QAAQ,EAAE,MAAM;IAChBP,KAAK,EAAEtC,YAAY,CAACZ,SAAS;IAC7BuD,SAAS,EAAE,QAAQ;IACnBqB,YAAY,EAAE,QAAQ;IACtBlB,UAAU,EAAE,KAAK;IACjB,2BAA2B,EAAE;MAC3BD,QAAQ,EAAE,QAAQ;MAClBmB,YAAY,EAAE;IAChB;EACF,CAAC;EAEDmL,cAAc,EAAE;IACdnL,YAAY,EAAE,QAAQ;IACtBlD,QAAQ,EAAE;EACZ,CAAC;EAEDsO,SAAS,EAAE;IACT1N,KAAK,EAAE,MAAM;IACbE,OAAO,EAAE,cAAc;IACvBiB,QAAQ,EAAE,MAAM;IAChBvD,MAAM,EAAE,aAAaU,YAAY,CAACV,MAAM,EAAE;IAC1CiD,YAAY,EAAE,MAAM;IACpBtD,UAAU,EAAE,0BAA0B;IACtCuD,UAAU,EAAE,eAAe;IAC3ByE,OAAO,EAAE,MAAM;IACfpG,UAAU,EAAE,SAAS;IACrB+E,SAAS,EAAE,YAAY;IACvB,eAAe,EAAE;MACftD,KAAK,EAAEtC,YAAY,CAACZ,SAAS;MAC7BqG,OAAO,EAAE;IACX,CAAC;IACD,QAAQ,EAAE;MACRyB,WAAW,EAAElH,YAAY,CAACpB,OAAO;MACjCK,UAAU,EAAE,wBAAwB;MACpC0C,SAAS,EAAE,aAAa3B,YAAY,CAACpB,OAAO,IAAI;MAChDyG,SAAS,EAAE;IACb,CAAC;IACD,2BAA2B,EAAE;MAC3BzD,OAAO,EAAE,eAAe;MACxBiB,QAAQ,EAAE;IACZ;EACF,CAAC;EAED;EACAwM,YAAY,EAAE;IACZ5O,SAAS,EAAE,OAAO;IAClBC,eAAe,EAAEV,YAAY,CAACf,UAAU;IACxCqD,KAAK,EAAEtC,YAAY,CAACb,IAAI;IACxBqD,UAAU,EAAE;EACd,CAAC;EACD8M,WAAW,EAAE;IACXxO,QAAQ,EAAE,OAAO;IACjBG,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRoC,MAAM,EAAE,MAAM;IACdtE,UAAU,EAAEe,YAAY,CAACP,QAAQ;IACjCkC,SAAS,EAAE,cAAc3B,YAAY,CAACT,MAAM,EAAE;IAC9CoB,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpBP,OAAO,EAAE,QAAQ;IACjBP,MAAM,EAAE,IAAI;IACZS,YAAY,EAAE,aAAa9B,YAAY,CAACV,MAAM;EAChD,CAAC;EACDiQ,YAAY,EAAE;IACZzO,QAAQ,EAAE,OAAO;IACjBG,GAAG,EAAE,MAAM;IACXC,IAAI,EAAE,CAAC;IACPE,MAAM,EAAE,CAAC;IACTM,KAAK,EAAE,OAAO;IACdhB,eAAe,EAAEV,YAAY,CAACd,OAAO;IACrCmG,SAAS,EAAE,mBAAmB;IAC9B7C,UAAU,EAAE,qBAAqB;IACjCnB,MAAM,EAAE,GAAG;IACXM,SAAS,EAAE,cAAc3B,YAAY,CAACT,MAAM,EAAE;IAC9CmE,SAAS,EAAE;EACb,CAAC;EACD8L,WAAW,EAAE;IACXnK,SAAS,EAAE;EACb,CAAC;EACDoK,cAAc,EAAE;IACd7N,OAAO,EAAE,WAAW;IACpBjB,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpBE,MAAM,EAAE,SAAS;IACjBE,YAAY,EAAE,KAAK;IACnBN,MAAM,EAAE,SAAS;IACjBO,UAAU,EAAE,eAAe;IAC3BF,KAAK,EAAEtC,YAAY,CAACb,IAAI;IACxBF,UAAU,EAAEe,YAAY,CAACL,aAAa;IACtC,SAAS,EAAE;MACTV,UAAU,EAAEe,YAAY,CAACP,QAAQ;MACjC6C,KAAK,EAAE;IACT;EACF,CAAC;EACDoN,oBAAoB,EAAE;IACpBzQ,UAAU,EAAEe,YAAY,CAACP,QAAQ;IACjC6C,KAAK,EAAE,OAAO;IACdQ,UAAU,EAAE;EACd,CAAC;EACD6M,cAAc,EAAE;IACdC,UAAU,EAAE,MAAM;IAClBC,aAAa,EAAE,MAAM;IACrBrN,UAAU,EAAE,uBAAuB;IACnC/B,SAAS,EAAE;EACb,CAAC;EACDqP,sBAAsB,EAAE;IACtB1I,UAAU,EAAE;EACd,CAAC;EACD2I,OAAO,EAAE;IACPrP,eAAe,EAAEV,YAAY,CAACd,OAAO;IACrCqD,YAAY,EAAE,MAAM;IACpBZ,SAAS,EAAE,aAAa3B,YAAY,CAACT,MAAM,EAAE;IAC7CqC,OAAO,EAAE,MAAM;IACfoC,YAAY,EAAE,MAAM;IACpBxB,UAAU,EAAE;EACd,CAAC;EACDwN,aAAa,EAAE;IACb/Q,UAAU,EAAEe,YAAY,CAACP,QAAQ;IACjC6C,KAAK,EAAE,MAAM;IACbhD,MAAM,EAAE,MAAM;IACdiD,YAAY,EAAE,KAAK;IACnBX,OAAO,EAAE,WAAW;IACpBkB,UAAU,EAAE,GAAG;IACfD,QAAQ,EAAE,MAAM;IAChBR,MAAM,EAAE,SAAS;IACjB1B,OAAO,EAAE,aAAa;IACtBwB,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxB4D,GAAG,EAAE,KAAK;IACVtD,UAAU,EAAE,eAAe;IAC3B,SAAS,EAAE;MACT6C,SAAS,EAAE,kBAAkB;MAC7B1D,SAAS,EAAE,cAAc3B,YAAY,CAACR,UAAU;IAClD,CAAC;IACD,YAAY,EAAE;MACZiG,OAAO,EAAE,GAAG;MACZpD,MAAM,EAAE;IACV;EACF,CAAC;EACD4N,UAAU,EAAE;IACVvO,KAAK,EAAE,MAAM;IACbE,OAAO,EAAE,WAAW;IACpBW,YAAY,EAAE,KAAK;IACnBjD,MAAM,EAAE,eAAeU,YAAY,CAACpB,OAAO,EAAE;IAC7CiE,QAAQ,EAAE,MAAM;IAChBnC,eAAe,EAAEV,YAAY,CAACjB,SAAS;IACvCuD,KAAK,EAAEtC,YAAY,CAACpB,OAAO;IAC3B4D,UAAU,EAAE,eAAe;IAC3B,SAAS,EAAE;MACTyE,OAAO,EAAE,MAAM;MACfC,WAAW,EAAElH,YAAY,CAACpB,OAAO;MACjC+C,SAAS,EAAE,aAAa3B,YAAY,CAACT,MAAM;IAC7C;EACF,CAAC;EACD2Q,cAAc,EAAE;IACdxP,eAAe,EAAEV,YAAY,CAACpB,OAAO;IACrC0D,KAAK,EAAE,MAAM;IACbV,OAAO,EAAE,WAAW;IACpBW,YAAY,EAAE,oBAAoB;IAClCP,QAAQ,EAAE,KAAK;IACfoF,UAAU,EAAE,MAAM;IAClBpD,YAAY,EAAE,KAAK;IACnBrC,SAAS,EAAE,aAAa3B,YAAY,CAACT,MAAM;EAC7C,CAAC;EACD4Q,aAAa,EAAE;IACbzP,eAAe,EAAEV,YAAY,CAACjB,SAAS;IACvCuD,KAAK,EAAEtC,YAAY,CAACb,IAAI;IACxByC,OAAO,EAAE,WAAW;IACpBW,YAAY,EAAE,oBAAoB;IAClCP,QAAQ,EAAE,KAAK;IACfoC,WAAW,EAAE,MAAM;IACnBJ,YAAY,EAAE,KAAK;IACnBrC,SAAS,EAAE,aAAa3B,YAAY,CAACT,MAAM;EAC7C,CAAC;EACD6Q,cAAc,EAAE;IACdnR,UAAU,EAAEe,YAAY,CAACP,QAAQ;IACjC8C,YAAY,EAAE,MAAM;IACpBX,OAAO,EAAE,MAAM;IACfjB,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBuB,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxBG,MAAM,EAAE,SAAS;IACjBG,UAAU,EAAE,eAAe;IAC3Bb,SAAS,EAAE,aAAa3B,YAAY,CAACT,MAAM,EAAE;IAC7C,SAAS,EAAE;MACT8F,SAAS,EAAE,kBAAkB;MAC7B1D,SAAS,EAAE,cAAc3B,YAAY,CAACR,UAAU,EAAE;MAClDP,UAAU,EAAEe,YAAY,CAACN;IAC3B;EACF,CAAC;EACD2Q,WAAW,EAAE;IACX3P,eAAe,EAAEV,YAAY,CAACd,OAAO;IACrCqD,YAAY,EAAE,MAAM;IACpBX,OAAO,EAAE,MAAM;IACfjB,OAAO,EAAE,MAAM;IACfuB,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBE,MAAM,EAAE,SAAS;IACjBG,UAAU,EAAE,eAAe;IAC3Bb,SAAS,EAAE,aAAa3B,YAAY,CAACT,MAAM,EAAE;IAC7C,SAAS,EAAE;MACT8F,SAAS,EAAE,kBAAkB;MAC7B1D,SAAS,EAAE,cAAc3B,YAAY,CAACR,UAAU,EAAE;MAClDkB,eAAe,EAAEV,YAAY,CAACjB;IAChC;EACF,CAAC;EACDuR,YAAY,EAAE;IACZxP,QAAQ,EAAE,OAAO;IACjBM,MAAM,EAAE,MAAM;IACdD,KAAK,EAAE,MAAM;IACbS,OAAO,EAAE,WAAW;IACpBW,YAAY,EAAE,KAAK;IACnBZ,SAAS,EAAE,cAAc3B,YAAY,CAACR,UAAU,EAAE;IAClD6B,MAAM,EAAE,IAAI;IACZG,SAAS,EAAE,uBAAuB;IAClCc,KAAK,EAAE;EACT,CAAC;EAED;EACAiO,kBAAkB,EAAE;IAClB9P,SAAS,EAAE,OAAO;IAClBxB,UAAU,EAAEe,YAAY,CAACL,aAAa;IACtCiC,OAAO,EAAE;EACX,CAAC;EACD4O,cAAc,EAAE;IACd7P,OAAO,EAAE,MAAM;IACfuB,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpB6B,YAAY,EAAE,MAAM;IACpB0H,QAAQ,EAAE,MAAM;IAChB5F,GAAG,EAAE;EACP,CAAC;EACD2K,aAAa,EAAE;IACb5N,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,MAAM;IAClBR,KAAK,EAAEtC,YAAY,CAACpB,OAAO;IAC3BqD,MAAM,EAAE,CAAC;IACT+B,YAAY,EAAE;EAChB,CAAC;EACD0M,gBAAgB,EAAE;IAChBpO,KAAK,EAAEtC,YAAY,CAACZ,SAAS;IAC7B6C,MAAM,EAAE;EACV,CAAC;EACD0O,cAAc,EAAE;IACd1R,UAAU,EAAEe,YAAY,CAACpB,OAAO;IAChC0D,KAAK,EAAE,OAAO;IACdV,OAAO,EAAE,gBAAgB;IACzBW,YAAY,EAAE,QAAQ;IACtBjD,MAAM,EAAE,MAAM;IACd+C,MAAM,EAAE,SAAS;IACjB1B,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpB2D,GAAG,EAAE,QAAQ;IACbjD,QAAQ,EAAE,MAAM;IAChBL,UAAU,EAAE,eAAe;IAC3B,SAAS,EAAE;MACTvD,UAAU,EAAEe,YAAY,CAACnB;IAC3B;EACF,CAAC;EACD+R,uBAAuB,EAAE;IACvB3R,UAAU,EAAE,OAAO;IACnBqD,KAAK,EAAEtC,YAAY,CAACpB,OAAO;IAC3BU,MAAM,EAAE,aAAaU,YAAY,CAACpB,OAAO,EAAE;IAC3CgD,OAAO,EAAE,gBAAgB;IACzBW,YAAY,EAAE,QAAQ;IACtBF,MAAM,EAAE,SAAS;IACjB1B,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpB2D,GAAG,EAAE,QAAQ;IACbjD,QAAQ,EAAE,MAAM;IAChBL,UAAU,EAAE,eAAe;IAC3B,SAAS,EAAE;MACTvD,UAAU,EAAEe,YAAY,CAACjB;IAC3B;EACF,CAAC;EACD8R,iBAAiB,EAAE;IACjBlQ,OAAO,EAAE,MAAM;IACf0H,mBAAmB,EAAE,sCAAsC;IAC3DvC,GAAG,EAAE,QAAQ;IACb9B,YAAY,EAAE;EAChB,CAAC;EACD8M,gBAAgB,EAAE;IAChB7R,UAAU,EAAE,OAAO;IACnBsD,YAAY,EAAE,MAAM;IACpBZ,SAAS,EAAE,aAAa3B,YAAY,CAACT,MAAM,EAAE;IAC7CqC,OAAO,EAAE,QAAQ;IACjBjB,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpBK,UAAU,EAAE,qBAAqB;IACjCH,MAAM,EAAE,SAAS;IACjB,SAAS,EAAE;MACTgD,SAAS,EAAE;IACb;EACF,CAAC;EACD0L,gBAAgB,EAAE;IAChBnP,OAAO,EAAE,SAAS;IAClBW,YAAY,EAAE,KAAK;IACnB6B,WAAW,EAAE;EACf,CAAC;EACD4M,gBAAgB,EAAE;IAChBrQ,OAAO,EAAE,MAAM;IACf0H,mBAAmB,EAAE,SAAS;IAC9BvC,GAAG,EAAE,MAAM;IACX,4BAA4B,EAAE;MAC5BuC,mBAAmB,EAAE;IACvB;EACF,CAAC;EACD4I,sBAAsB,EAAE;IACtBhS,UAAU,EAAE,OAAO;IACnBsD,YAAY,EAAE,MAAM;IACpBZ,SAAS,EAAE,aAAa3B,YAAY,CAACT,MAAM,EAAE;IAC7CkF,QAAQ,EAAE;EACZ,CAAC;EACDyM,qBAAqB,EAAE;IACrBjS,UAAU,EAAEe,YAAY,CAACpB,OAAO;IAChC0D,KAAK,EAAE,OAAO;IACdV,OAAO,EAAE,QAAQ;IACjBjB,OAAO,EAAE,MAAM;IACfuB,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBuJ,QAAQ,EAAE,MAAM;IAChB5F,GAAG,EAAE;EACP,CAAC;EACDqL,oBAAoB,EAAE;IACpBtO,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,MAAM;IAClBb,MAAM,EAAE;EACV,CAAC;EACDmP,oBAAoB,EAAE;IACpBnS,UAAU,EAAE,OAAO;IACnBqD,KAAK,EAAEtC,YAAY,CAACpB,OAAO;IAC3BgD,OAAO,EAAE,aAAa;IACtBW,YAAY,EAAE,UAAU;IACxBjD,MAAM,EAAE,MAAM;IACduD,QAAQ,EAAE,UAAU;IACpBR,MAAM,EAAE,SAAS;IACjBG,UAAU,EAAE;EACd,CAAC;EACD6O,0BAA0B,EAAE;IAC1BpS,UAAU,EAAEe,YAAY,CAACnB,WAAW;IACpCyD,KAAK,EAAE;EACT,CAAC;EACDgP,kBAAkB,EAAE;IAClBhS,MAAM,EAAE,aAAaU,YAAY,CAACV,MAAM,EAAE;IAC1CiD,YAAY,EAAE,QAAQ;IACtBX,OAAO,EAAE,MAAM;IACfY,UAAU,EAAE,eAAe;IAC3BH,MAAM,EAAE,SAAS;IACjB,SAAS,EAAE;MACT6E,WAAW,EAAElH,YAAY,CAACpB,OAAO;MACjCyG,SAAS,EAAE;IACb;EACF,CAAC;EAED;EACAkM,kBAAkB,EAAE;IAClB7P,KAAK,EAAE,MAAM;IACbM,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE,QAAQ;IAChBL,OAAO,EAAE;EACX,CAAC;EACD4P,sBAAsB,EAAE;IACtB7O,SAAS,EAAE,QAAQ;IACnBqB,YAAY,EAAE;EAChB,CAAC;EACDyN,kBAAkB,EAAE;IAClB5O,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAEtC,YAAY,CAACb,IAAI;IACxB6E,YAAY,EAAE;EAChB,CAAC;EACD0N,qBAAqB,EAAE;IACrB7O,QAAQ,EAAE,QAAQ;IAClBP,KAAK,EAAEtC,YAAY,CAACZ,SAAS;IAC7B4C,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE;EACV,CAAC;EACD0P,aAAa,EAAE;IACbhR,OAAO,EAAE,MAAM;IACf0H,mBAAmB,EAAE,uCAAuC;IAC5DvC,GAAG,EAAE,QAAQ;IACb9B,YAAY,EAAE;EAChB,CAAC;EACD4N,iBAAiB,EAAE;IACjB3S,UAAU,EAAEe,YAAY,CAACd,OAAO;IAChCqD,YAAY,EAAE,MAAM;IACpBX,OAAO,EAAE,QAAQ;IACjBD,SAAS,EAAE,aAAa3B,YAAY,CAACT,MAAM,EAAE;IAC7CiD,UAAU,EAAE,iCAAiC;IAC7CH,MAAM,EAAE,SAAS;IACjBvB,QAAQ,EAAE,UAAU;IACpB2D,QAAQ,EAAE,QAAQ;IAClB,SAAS,EAAE;MACTY,SAAS,EAAE,kBAAkB;MAC7B1D,SAAS,EAAE,cAAc3B,YAAY,CAACR,UAAU;IAClD;EACF,CAAC;EACDqS,YAAY,EAAE;IACZhP,QAAQ,EAAE,MAAM;IAChBmB,YAAY,EAAE;EAChB,CAAC;EACD8N,sBAAsB,EAAE;IACtBjP,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAEtC,YAAY,CAACb,IAAI;IACxB6E,YAAY,EAAE;EAChB,CAAC;EACD+N,4BAA4B,EAAE;IAC5BlP,QAAQ,EAAE,MAAM;IAChBP,KAAK,EAAEtC,YAAY,CAACZ,SAAS;IAC7B8G,UAAU,EAAE;EACd,CAAC;EACD8L,cAAc,EAAE;IACd5O,SAAS,EAAE;EACb,CAAC;EACD6O,gBAAgB,EAAE;IAChBpP,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAEtC,YAAY,CAACb,IAAI;IACxB6E,YAAY,EAAE,QAAQ;IACtBrB,SAAS,EAAE;EACb,CAAC;EACDuP,WAAW,EAAE;IACXvR,OAAO,EAAE,MAAM;IACf0H,mBAAmB,EAAE,uCAAuC;IAC5DvC,GAAG,EAAE;EACP,CAAC;EACDqM,gBAAgB,EAAE;IAChBlT,UAAU,EAAEe,YAAY,CAACd,OAAO;IAChCqD,YAAY,EAAE,KAAK;IACnBX,OAAO,EAAE,cAAc;IACvBe,SAAS,EAAE,QAAQ;IACnBG,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAEtC,YAAY,CAACZ,SAAS;IAC7BuC,SAAS,EAAE,aAAa3B,YAAY,CAACT,MAAM,EAAE;IAC7CiD,UAAU,EAAE,UAAU;IACtB,SAAS,EAAE;MACTvD,UAAU,EAAEe,YAAY,CAACpB,OAAO;MAChC0D,KAAK,EAAE,MAAM;MACb+C,SAAS,EAAE;IACb;EACF;AACJ,CAAC;AAED,eAAe7E,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}