{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\quiz\\\\aich (4)\\\\aich (3)\\\\aich(5)\\\\src\\\\AuthPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { initializeApp } from 'firebase/app';\nimport { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword } from 'firebase/auth';\nimport { getFirestore, doc, setDoc } from 'firebase/firestore';\nimport emailjs from 'emailjs-com';\nimport styles from './styles';\n\n// Firebase Config\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst firebaseConfig = {\n  apiKey: \"AIzaSyDmRAxMJvEKmel3JpUF1dXbBNccQJibcGo\",\n  authDomain: \"exploit-f41eb.firebaseapp.com\",\n  projectId: \"exploit-f41eb\",\n  storageBucket: \"exploit-f41eb.firebasestorage.app\",\n  messagingSenderId: \"849491574761\",\n  appId: \"1:849491574761:web:22ddb4ea62063d7a709578\",\n  measurementId: \"G-R0SWYQKXCV\"\n};\nconst app = initializeApp(firebaseConfig);\nconst auth = getAuth(app);\nconst db = getFirestore(app);\n\n// EmailJS Config\nconst EMAILJS_SERVICE_ID = 'service_sthdzci';\nconst EMAILJS_TEMPLATE_ID = 'template_8mnpzqq';\nconst EMAILJS_PUBLIC_KEY = 'jEfQd05ItbAthM3QN';\nconst AuthPage = ({\n  onAuthSuccess\n}) => {\n  _s();\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState('');\n  const [otp, setOtp] = useState('');\n  const [generatedOtp, setGeneratedOtp] = useState('');\n  const [isOtpSent, setIsOtpSent] = useState(false);\n  const [profilePic, setProfilePic] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isSignupMode, setIsSignupMode] = useState(true);\n  const [showPassword, setShowPassword] = useState(false);\n  const [particles, setParticles] = useState([]);\n  const [mousePosition, setMousePosition] = useState({\n    x: 0,\n    y: 0\n  });\n  const [isFormHovered, setIsFormHovered] = useState(false);\n\n  // Create floating particles effect\n  useEffect(() => {\n    const createParticles = () => {\n      const newParticles = [];\n      for (let i = 0; i < 15; i++) {\n        newParticles.push({\n          id: i,\n          x: Math.random() * 100,\n          y: Math.random() * 100,\n          size: Math.random() * 4 + 2,\n          speed: Math.random() * 2 + 1,\n          opacity: Math.random() * 0.5 + 0.2\n        });\n      }\n      setParticles(newParticles);\n    };\n    createParticles();\n\n    // Mouse tracking for interactive effects\n    const handleMouseMove = e => {\n      setMousePosition({\n        x: e.clientX / window.innerWidth * 100,\n        y: e.clientY / window.innerHeight * 100\n      });\n    };\n    window.addEventListener('mousemove', handleMouseMove);\n    return () => window.removeEventListener('mousemove', handleMouseMove);\n  }, []);\n  const generateOtp = () => {\n    const otp = Math.floor(100000 + Math.random() * 900000); // Generate a 6-digit OTP\n    setGeneratedOtp(otp);\n    return otp;\n  };\n  const sendOtpEmail = (email, otp) => {\n    const templateParams = {\n      to_email: email,\n      otp: otp\n    };\n    emailjs.send(EMAILJS_SERVICE_ID, EMAILJS_TEMPLATE_ID, templateParams, EMAILJS_PUBLIC_KEY).then(response => {\n      console.log('OTP sent!', response.status, response.text);\n    }, err => {\n      console.error('OTP sending failed:', err);\n    });\n  };\n  const handleProfilePicChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setProfilePic(reader.result); // Store image in base64\n      };\n      reader.readAsDataURL(file); // Convert file to base64\n    }\n  };\n  const handleSignup = async () => {\n    if (!email || !password) {\n      setError('Please fill in all fields');\n      return;\n    }\n    setIsLoading(true);\n    setError('');\n    try {\n      const userCredential = await createUserWithEmailAndPassword(auth, email, password);\n      const otp = generateOtp();\n      sendOtpEmail(email, otp);\n      setIsOtpSent(true);\n\n      // Save user data to Firestore\n      const userRef = doc(db, \"users\", userCredential.user.uid);\n      await setDoc(userRef, {\n        email: email,\n        uid: userCredential.user.uid,\n        createdAt: new Date(),\n        dp: profilePic\n      });\n    } catch (error) {\n      setError(error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleOtpVerification = () => {\n    if (otp === generatedOtp.toString()) {\n      setIsOtpSent(false);\n      setOtp('');\n      setIsSignupMode(false);\n      setError('');\n      alert('OTP Verified! You can now login with your credentials.');\n    } else {\n      setError('Invalid OTP. Please try again.');\n    }\n  };\n  const handleLogin = async () => {\n    if (!email || !password) {\n      setError('Please fill in all fields');\n      return;\n    }\n    setIsLoading(true);\n    setError('');\n    try {\n      const userCredential = await signInWithEmailAndPassword(auth, email, password);\n      onAuthSuccess();\n    } catch (error) {\n      setError(error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      ...styles.authPageContainer,\n      background: `\n        linear-gradient(135deg, ${styles.currentTheme.primary}15 0%, ${styles.currentTheme.accent}10 25%, ${styles.currentTheme.primaryLight}20 50%, ${styles.currentTheme.secondary}30 75%, ${styles.currentTheme.primary}10 100%),\n        radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, ${styles.currentTheme.primary}20 0%, transparent 50%),\n        radial-gradient(circle at 80% 20%, ${styles.currentTheme.accent}20 0%, transparent 50%),\n        radial-gradient(circle at 40% 40%, ${styles.currentTheme.primaryLight}15 0%, transparent 50%)\n      `\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...styles.authBackgroundAnimation,\n        transform: `translate(${mousePosition.x * 0.02}px, ${mousePosition.y * 0.02}px)`\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this), particles.map(particle => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        left: `${particle.x}%`,\n        top: `${particle.y}%`,\n        width: `${particle.size}px`,\n        height: `${particle.size}px`,\n        background: styles.currentTheme.primary,\n        borderRadius: '50%',\n        opacity: particle.opacity,\n        animation: `floatParticle${particle.id % 2 + 1} ${12 + particle.speed * 3}s infinite linear`,\n        zIndex: 1,\n        pointerEvents: 'none'\n      }\n    }, particle.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 9\n    }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...styles.authFormContainer,\n        transform: isFormHovered ? `translateY(-8px) scale(1.02) rotateX(${mousePosition.y * 0.01}deg) rotateY(${mousePosition.x * 0.01}deg)` : 'translateY(0) scale(1)',\n        boxShadow: isFormHovered ? `\n              0 35px 60px -12px ${styles.currentTheme.shadow},\n              0 20px 30px -8px ${styles.currentTheme.shadow},\n              inset 0 1px 0 rgba(255, 255, 255, 0.3),\n              0 0 0 1px rgba(255, 255, 255, 0.2)\n            ` : styles.authFormContainer.boxShadow\n      },\n      onMouseEnter: () => setIsFormHovered(true),\n      onMouseLeave: () => setIsFormHovered(false),\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        style: styles.authFormTitle,\n        children: isSignupMode ? '✨ Create Account' : '🎯 Welcome Back'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: styles.authFormSubtitle,\n        children: isSignupMode ? '🚀 Join our learning platform and start your amazing journey' : '💫 Sign in to continue your learning adventure'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this), isSignupMode && !isOtpSent && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'center',\n          marginBottom: '2rem',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            background: email && password ? styles.currentTheme.primary : styles.currentTheme.border,\n            transition: 'all 0.3s ease',\n            animation: email && password ? 'pulse 2s infinite' : 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            background: profilePic ? styles.currentTheme.primary : styles.currentTheme.border,\n            transition: 'all 0.3s ease',\n            animation: profilePic ? 'pulse 2s infinite' : 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            background: styles.currentTheme.border,\n            transition: 'all 0.3s ease'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 11\n      }, this), !isOtpSent ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.authInputGroup,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'relative'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                position: 'absolute',\n                left: '1.25rem',\n                top: '50%',\n                transform: 'translateY(-50%)',\n                fontSize: '1.2rem',\n                color: styles.currentTheme.textLight,\n                zIndex: 1\n              },\n              children: \"\\uD83D\\uDCE7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              value: email,\n              onChange: e => setEmail(e.target.value),\n              placeholder: \"Enter your email address\",\n              style: {\n                ...styles.authInput,\n                paddingLeft: '3.5rem'\n              },\n              disabled: isLoading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.authInputGroup,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'relative'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                position: 'absolute',\n                left: '1.25rem',\n                top: '50%',\n                transform: 'translateY(-50%)',\n                fontSize: '1.2rem',\n                color: styles.currentTheme.textLight,\n                zIndex: 1\n              },\n              children: \"\\uD83D\\uDD12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: showPassword ? 'text' : 'password',\n              value: password,\n              onChange: e => setPassword(e.target.value),\n              placeholder: \"Enter your password\",\n              style: {\n                ...styles.authInput,\n                paddingLeft: '3.5rem',\n                paddingRight: '3.5rem'\n              },\n              disabled: isLoading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => setShowPassword(!showPassword),\n              style: {\n                position: 'absolute',\n                right: '1.25rem',\n                top: '50%',\n                transform: 'translateY(-50%)',\n                background: 'none',\n                border: 'none',\n                fontSize: '1.2rem',\n                cursor: 'pointer',\n                color: styles.currentTheme.textLight,\n                zIndex: 1\n              },\n              disabled: isLoading,\n              children: showPassword ? '🙈' : '👁️'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 13\n        }, this), isSignupMode && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.authInputGroup,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                padding: '1.5rem',\n                border: `2px dashed ${styles.currentTheme.primary}`,\n                borderRadius: '16px',\n                background: 'rgba(255, 255, 255, 0.5)',\n                cursor: 'pointer',\n                transition: 'all 0.3s ease',\n                ':hover': {\n                  background: 'rgba(255, 255, 255, 0.8)',\n                  transform: 'translateY(-2px)'\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '2rem',\n                  display: 'block',\n                  marginBottom: '0.5rem'\n                },\n                children: \"\\uD83D\\uDCF8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: styles.currentTheme.textLight,\n                  fontSize: '0.9rem'\n                },\n                children: profilePic ? 'Change Profile Picture' : 'Upload Profile Picture'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                onChange: handleProfilePicChange,\n                accept: \"image/*\",\n                style: {\n                  display: 'none'\n                },\n                disabled: isLoading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 19\n            }, this), profilePic && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '1rem',\n                position: 'relative',\n                display: 'inline-block'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: profilePic,\n                alt: \"Profile Preview\",\n                style: {\n                  ...styles.authProfilePreview,\n                  animation: 'fadeIn 0.5s ease'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: '-5px',\n                  right: '-5px',\n                  background: styles.currentTheme.primary,\n                  color: 'white',\n                  borderRadius: '50%',\n                  width: '24px',\n                  height: '24px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  fontSize: '0.8rem',\n                  animation: 'pulse 2s infinite'\n                },\n                children: \"\\u2713\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.authButtonGroup,\n          children: isSignupMode ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSignup,\n              disabled: isLoading,\n              style: styles.authButtonPrimary,\n              children: [isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.authLoadingSpinner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 35\n              }, this), isLoading ? '🚀 Creating Magic...' : '✨ Create Account']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsSignupMode(false),\n              disabled: isLoading,\n              style: styles.authButtonSecondary,\n              children: \"\\uD83D\\uDD11 Login Instead\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleLogin,\n              disabled: isLoading,\n              style: styles.authButtonPrimary,\n              children: [isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.authLoadingSpinner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 35\n              }, this), isLoading ? '🎯 Signing In...' : '🚀 Sign In']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsSignupMode(true),\n              disabled: isLoading,\n              style: styles.authButtonSecondary,\n              children: \"\\u2728 Create Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true) :\n      /*#__PURE__*/\n      /* OTP Verification Section */\n      _jsxDEV(\"div\", {\n        style: styles.authOtpContainer,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '3rem',\n              marginBottom: '1rem',\n              animation: 'pulse 2s infinite'\n            },\n            children: \"\\uD83D\\uDCE7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            style: styles.authOtpTitle,\n            children: \"\\uD83D\\uDD10 Verify Your Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: styles.currentTheme.textLight,\n              marginBottom: '1rem'\n            },\n            children: [\"\\u2728 We've sent a magical 6-digit code to\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 55\n            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n              style: {\n                color: styles.currentTheme.primary\n              },\n              children: email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              position: 'absolute',\n              left: '1rem',\n              top: '50%',\n              transform: 'translateY(-50%)',\n              fontSize: '1.2rem',\n              color: styles.currentTheme.textLight,\n              zIndex: 1\n            },\n            children: \"\\uD83D\\uDD22\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: otp,\n            onChange: e => setOtp(e.target.value),\n            placeholder: \"Enter 6-digit code\",\n            maxLength: \"6\",\n            style: {\n              ...styles.authOtpInput,\n              paddingLeft: '3rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleOtpVerification,\n          style: {\n            ...styles.authOtpButton,\n            background: styles.currentTheme.gradient,\n            animation: otp.length === 6 ? 'buttonPulse 2s infinite' : 'none'\n          },\n          children: \"\\uD83C\\uDFAF Verify & Continue\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 11\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          ...styles.authErrorMessage,\n          animation: 'slideIn 0.3s ease'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            marginRight: '0.5rem'\n          },\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 13\n        }, this), error]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 504,\n        columnNumber: 11\n      }, this), email && password && !error && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginTop: '1rem',\n          color: styles.currentTheme.primary,\n          fontSize: '0.9rem',\n          animation: 'fadeIn 0.5s ease'\n        },\n        children: [\"\\u2705 Ready to \", isSignupMode ? 'create your account' : 'sign in', \"!\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 167,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthPage, \"5LBKpYanwp/eeqUOg9cwg7FUi44=\");\n_c = AuthPage;\nexport default AuthPage;\nvar _c;\n$RefreshReg$(_c, \"AuthPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "initializeApp", "getAuth", "createUserWithEmailAndPassword", "signInWithEmailAndPassword", "getFirestore", "doc", "setDoc", "emailjs", "styles", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "measurementId", "app", "auth", "db", "EMAILJS_SERVICE_ID", "EMAILJS_TEMPLATE_ID", "EMAILJS_PUBLIC_KEY", "AuthPage", "onAuthSuccess", "_s", "email", "setEmail", "password", "setPassword", "error", "setError", "otp", "setOtp", "generatedOtp", "setGeneratedOtp", "isOtpSent", "setIsOtpSent", "profilePic", "setProfilePic", "isLoading", "setIsLoading", "isSignupMode", "setIsSignupMode", "showPassword", "setShowPassword", "particles", "setParticles", "mousePosition", "setMousePosition", "x", "y", "isFormHovered", "setIsFormHovered", "createParticles", "newParticles", "i", "push", "id", "Math", "random", "size", "speed", "opacity", "handleMouseMove", "e", "clientX", "window", "innerWidth", "clientY", "innerHeight", "addEventListener", "removeEventListener", "generateOtp", "floor", "sendOtpEmail", "templateParams", "to_email", "send", "then", "response", "console", "log", "status", "text", "err", "handleProfilePicChange", "file", "target", "files", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "handleSignup", "userCredential", "userRef", "user", "uid", "createdAt", "Date", "dp", "message", "handleOtpVerification", "toString", "alert", "handleLogin", "style", "authPageContainer", "background", "currentTheme", "primary", "accent", "primaryLight", "secondary", "children", "authBackgroundAnimation", "transform", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "particle", "position", "left", "top", "width", "height", "borderRadius", "animation", "zIndex", "pointerEvents", "authFormContainer", "boxShadow", "shadow", "onMouseEnter", "onMouseLeave", "authFormTitle", "authFormSubtitle", "display", "justifyContent", "marginBottom", "gap", "border", "transition", "authInputGroup", "fontSize", "color", "textLight", "type", "value", "onChange", "placeholder", "authInput", "paddingLeft", "disabled", "paddingRight", "onClick", "right", "cursor", "textAlign", "padding", "accept", "marginTop", "src", "alt", "authProfilePreview", "alignItems", "authButtonGroup", "authButtonPrimary", "authLoading<PERSON><PERSON>ner", "authButtonSecondary", "authOtpContainer", "authOtpTitle", "max<PERSON><PERSON><PERSON>", "authOtpInput", "authOtpButton", "gradient", "length", "authErrorMessage", "marginRight", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(5)/src/AuthPage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { initializeApp } from 'firebase/app';\nimport { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword } from 'firebase/auth';\nimport { getFirestore, doc, setDoc } from 'firebase/firestore';\nimport emailjs from 'emailjs-com';\nimport styles from './styles';\n\n// Firebase Config\nconst firebaseConfig = {\n  apiKey: \"AIzaSyDmRAxMJvEKmel3JpUF1dXbBNccQJibcGo\",\n  authDomain: \"exploit-f41eb.firebaseapp.com\",\n  projectId: \"exploit-f41eb\",\n  storageBucket: \"exploit-f41eb.firebasestorage.app\",\n  messagingSenderId: \"849491574761\",\n  appId: \"1:849491574761:web:22ddb4ea62063d7a709578\",\n  measurementId: \"G-R0SWYQKXCV\"\n};\n\nconst app = initializeApp(firebaseConfig);\nconst auth = getAuth(app);\nconst db = getFirestore(app);\n\n// EmailJS Config\nconst EMAILJS_SERVICE_ID = 'service_sthdzci';\nconst EMAILJS_TEMPLATE_ID = 'template_8mnpzqq';\nconst EMAILJS_PUBLIC_KEY = 'jEfQd05ItbAthM3QN';\n\nconst AuthPage = ({ onAuthSuccess }) => {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState('');\n  const [otp, setOtp] = useState('');\n  const [generatedOtp, setGeneratedOtp] = useState('');\n  const [isOtpSent, setIsOtpSent] = useState(false);\n  const [profilePic, setProfilePic] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isSignupMode, setIsSignupMode] = useState(true);\n  const [showPassword, setShowPassword] = useState(false);\n  const [particles, setParticles] = useState([]);\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n  const [isFormHovered, setIsFormHovered] = useState(false);\n\n  // Create floating particles effect\n  useEffect(() => {\n    const createParticles = () => {\n      const newParticles = [];\n      for (let i = 0; i < 15; i++) {\n        newParticles.push({\n          id: i,\n          x: Math.random() * 100,\n          y: Math.random() * 100,\n          size: Math.random() * 4 + 2,\n          speed: Math.random() * 2 + 1,\n          opacity: Math.random() * 0.5 + 0.2,\n        });\n      }\n      setParticles(newParticles);\n    };\n    createParticles();\n\n    // Mouse tracking for interactive effects\n    const handleMouseMove = (e) => {\n      setMousePosition({\n        x: (e.clientX / window.innerWidth) * 100,\n        y: (e.clientY / window.innerHeight) * 100,\n      });\n    };\n\n    window.addEventListener('mousemove', handleMouseMove);\n    return () => window.removeEventListener('mousemove', handleMouseMove);\n  }, []);\n\n  const generateOtp = () => {\n    const otp = Math.floor(100000 + Math.random() * 900000); // Generate a 6-digit OTP\n    setGeneratedOtp(otp);\n    return otp;\n  };\n\n  const sendOtpEmail = (email, otp) => {\n    const templateParams = {\n      to_email: email,\n      otp: otp\n    };\n\n    emailjs.send(EMAILJS_SERVICE_ID, EMAILJS_TEMPLATE_ID, templateParams, EMAILJS_PUBLIC_KEY)\n      .then((response) => {\n        console.log('OTP sent!', response.status, response.text);\n      }, (err) => {\n        console.error('OTP sending failed:', err);\n      });\n  };\n\n  const handleProfilePicChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setProfilePic(reader.result); // Store image in base64\n      };\n      reader.readAsDataURL(file); // Convert file to base64\n    }\n  };\n\n  const handleSignup = async () => {\n    if (!email || !password) {\n      setError('Please fill in all fields');\n      return;\n    }\n\n    setIsLoading(true);\n    setError('');\n\n    try {\n      const userCredential = await createUserWithEmailAndPassword(auth, email, password);\n      const otp = generateOtp();\n      sendOtpEmail(email, otp);\n      setIsOtpSent(true);\n\n      // Save user data to Firestore\n      const userRef = doc(db, \"users\", userCredential.user.uid);\n      await setDoc(userRef, {\n        email: email,\n        uid: userCredential.user.uid,\n        createdAt: new Date(),\n        dp: profilePic,\n      });\n\n    } catch (error) {\n      setError(error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleOtpVerification = () => {\n    if (otp === generatedOtp.toString()) {\n      setIsOtpSent(false);\n      setOtp('');\n      setIsSignupMode(false);\n      setError('');\n      alert('OTP Verified! You can now login with your credentials.');\n    } else {\n      setError('Invalid OTP. Please try again.');\n    }\n  };\n\n  const handleLogin = async () => {\n    if (!email || !password) {\n      setError('Please fill in all fields');\n      return;\n    }\n\n    setIsLoading(true);\n    setError('');\n\n    try {\n      const userCredential = await signInWithEmailAndPassword(auth, email, password);\n      onAuthSuccess();\n    } catch (error) {\n      setError(error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div style={{\n      ...styles.authPageContainer,\n      background: `\n        linear-gradient(135deg, ${styles.currentTheme.primary}15 0%, ${styles.currentTheme.accent}10 25%, ${styles.currentTheme.primaryLight}20 50%, ${styles.currentTheme.secondary}30 75%, ${styles.currentTheme.primary}10 100%),\n        radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, ${styles.currentTheme.primary}20 0%, transparent 50%),\n        radial-gradient(circle at 80% 20%, ${styles.currentTheme.accent}20 0%, transparent 50%),\n        radial-gradient(circle at 40% 40%, ${styles.currentTheme.primaryLight}15 0%, transparent 50%)\n      `,\n    }}>\n      {/* Animated Background */}\n      <div style={{\n        ...styles.authBackgroundAnimation,\n        transform: `translate(${mousePosition.x * 0.02}px, ${mousePosition.y * 0.02}px)`,\n      }}></div>\n\n      {/* Floating Particles */}\n      {particles.map((particle) => (\n        <div\n          key={particle.id}\n          style={{\n            position: 'absolute',\n            left: `${particle.x}%`,\n            top: `${particle.y}%`,\n            width: `${particle.size}px`,\n            height: `${particle.size}px`,\n            background: styles.currentTheme.primary,\n            borderRadius: '50%',\n            opacity: particle.opacity,\n            animation: `floatParticle${(particle.id % 2) + 1} ${12 + particle.speed * 3}s infinite linear`,\n            zIndex: 1,\n            pointerEvents: 'none',\n          }}\n        />\n      ))}\n\n      {/* Main Form Container */}\n      <div\n        style={{\n          ...styles.authFormContainer,\n          transform: isFormHovered\n            ? `translateY(-8px) scale(1.02) rotateX(${mousePosition.y * 0.01}deg) rotateY(${mousePosition.x * 0.01}deg)`\n            : 'translateY(0) scale(1)',\n          boxShadow: isFormHovered\n            ? `\n              0 35px 60px -12px ${styles.currentTheme.shadow},\n              0 20px 30px -8px ${styles.currentTheme.shadow},\n              inset 0 1px 0 rgba(255, 255, 255, 0.3),\n              0 0 0 1px rgba(255, 255, 255, 0.2)\n            `\n            : styles.authFormContainer.boxShadow,\n        }}\n        onMouseEnter={() => setIsFormHovered(true)}\n        onMouseLeave={() => setIsFormHovered(false)}\n      >\n        <h1 style={styles.authFormTitle}>\n          {isSignupMode ? '✨ Create Account' : '🎯 Welcome Back'}\n        </h1>\n        <p style={styles.authFormSubtitle}>\n          {isSignupMode\n            ? '🚀 Join our learning platform and start your amazing journey'\n            : '💫 Sign in to continue your learning adventure'\n          }\n        </p>\n\n        {/* Progress Indicator */}\n        {isSignupMode && !isOtpSent && (\n          <div style={{\n            display: 'flex',\n            justifyContent: 'center',\n            marginBottom: '2rem',\n            gap: '0.5rem',\n          }}>\n            <div style={{\n              width: '12px',\n              height: '12px',\n              borderRadius: '50%',\n              background: email && password ? styles.currentTheme.primary : styles.currentTheme.border,\n              transition: 'all 0.3s ease',\n              animation: email && password ? 'pulse 2s infinite' : 'none',\n            }}></div>\n            <div style={{\n              width: '12px',\n              height: '12px',\n              borderRadius: '50%',\n              background: profilePic ? styles.currentTheme.primary : styles.currentTheme.border,\n              transition: 'all 0.3s ease',\n              animation: profilePic ? 'pulse 2s infinite' : 'none',\n            }}></div>\n            <div style={{\n              width: '12px',\n              height: '12px',\n              borderRadius: '50%',\n              background: styles.currentTheme.border,\n              transition: 'all 0.3s ease',\n            }}></div>\n          </div>\n        )}\n\n        {!isOtpSent ? (\n          <>\n            {/* Email Input */}\n            <div style={styles.authInputGroup}>\n              <div style={{ position: 'relative' }}>\n                <span style={{\n                  position: 'absolute',\n                  left: '1.25rem',\n                  top: '50%',\n                  transform: 'translateY(-50%)',\n                  fontSize: '1.2rem',\n                  color: styles.currentTheme.textLight,\n                  zIndex: 1,\n                }}>\n                  📧\n                </span>\n                <input\n                  type=\"email\"\n                  value={email}\n                  onChange={(e) => setEmail(e.target.value)}\n                  placeholder=\"Enter your email address\"\n                  style={{\n                    ...styles.authInput,\n                    paddingLeft: '3.5rem',\n                  }}\n                  disabled={isLoading}\n                />\n              </div>\n            </div>\n\n            {/* Password Input */}\n            <div style={styles.authInputGroup}>\n              <div style={{ position: 'relative' }}>\n                <span style={{\n                  position: 'absolute',\n                  left: '1.25rem',\n                  top: '50%',\n                  transform: 'translateY(-50%)',\n                  fontSize: '1.2rem',\n                  color: styles.currentTheme.textLight,\n                  zIndex: 1,\n                }}>\n                  🔒\n                </span>\n                <input\n                  type={showPassword ? 'text' : 'password'}\n                  value={password}\n                  onChange={(e) => setPassword(e.target.value)}\n                  placeholder=\"Enter your password\"\n                  style={{\n                    ...styles.authInput,\n                    paddingLeft: '3.5rem',\n                    paddingRight: '3.5rem',\n                  }}\n                  disabled={isLoading}\n                />\n                <button\n                  type=\"button\"\n                  onClick={() => setShowPassword(!showPassword)}\n                  style={{\n                    position: 'absolute',\n                    right: '1.25rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    background: 'none',\n                    border: 'none',\n                    fontSize: '1.2rem',\n                    cursor: 'pointer',\n                    color: styles.currentTheme.textLight,\n                    zIndex: 1,\n                  }}\n                  disabled={isLoading}\n                >\n                  {showPassword ? '🙈' : '👁️'}\n                </button>\n              </div>\n            </div>\n\n            {/* Profile Picture Upload (only for signup) */}\n            {isSignupMode && (\n              <div style={styles.authInputGroup}>\n                <div style={{ textAlign: 'center' }}>\n                  <label style={{\n                    display: 'block',\n                    padding: '1.5rem',\n                    border: `2px dashed ${styles.currentTheme.primary}`,\n                    borderRadius: '16px',\n                    background: 'rgba(255, 255, 255, 0.5)',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    ':hover': {\n                      background: 'rgba(255, 255, 255, 0.8)',\n                      transform: 'translateY(-2px)',\n                    }\n                  }}>\n                    <span style={{ fontSize: '2rem', display: 'block', marginBottom: '0.5rem' }}>\n                      📸\n                    </span>\n                    <span style={{ color: styles.currentTheme.textLight, fontSize: '0.9rem' }}>\n                      {profilePic ? 'Change Profile Picture' : 'Upload Profile Picture'}\n                    </span>\n                    <input\n                      type=\"file\"\n                      onChange={handleProfilePicChange}\n                      accept=\"image/*\"\n                      style={{ display: 'none' }}\n                      disabled={isLoading}\n                    />\n                  </label>\n                  {profilePic && (\n                    <div style={{ marginTop: '1rem', position: 'relative', display: 'inline-block' }}>\n                      <img\n                        src={profilePic}\n                        alt=\"Profile Preview\"\n                        style={{\n                          ...styles.authProfilePreview,\n                          animation: 'fadeIn 0.5s ease',\n                        }}\n                      />\n                      <div style={{\n                        position: 'absolute',\n                        top: '-5px',\n                        right: '-5px',\n                        background: styles.currentTheme.primary,\n                        color: 'white',\n                        borderRadius: '50%',\n                        width: '24px',\n                        height: '24px',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        fontSize: '0.8rem',\n                        animation: 'pulse 2s infinite',\n                      }}>\n                        ✓\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            )}\n\n            {/* Action Buttons */}\n            <div style={styles.authButtonGroup}>\n              {isSignupMode ? (\n                <>\n                  <button\n                    onClick={handleSignup}\n                    disabled={isLoading}\n                    style={styles.authButtonPrimary}\n                  >\n                    {isLoading && <div style={styles.authLoadingSpinner}></div>}\n                    {isLoading ? '🚀 Creating Magic...' : '✨ Create Account'}\n                  </button>\n                  <button\n                    onClick={() => setIsSignupMode(false)}\n                    disabled={isLoading}\n                    style={styles.authButtonSecondary}\n                  >\n                    🔑 Login Instead\n                  </button>\n                </>\n              ) : (\n                <>\n                  <button\n                    onClick={handleLogin}\n                    disabled={isLoading}\n                    style={styles.authButtonPrimary}\n                  >\n                    {isLoading && <div style={styles.authLoadingSpinner}></div>}\n                    {isLoading ? '🎯 Signing In...' : '🚀 Sign In'}\n                  </button>\n                  <button\n                    onClick={() => setIsSignupMode(true)}\n                    disabled={isLoading}\n                    style={styles.authButtonSecondary}\n                  >\n                    ✨ Create Account\n                  </button>\n                </>\n              )}\n            </div>\n          </>\n        ) : (\n          /* OTP Verification Section */\n          <div style={styles.authOtpContainer}>\n            <div style={{ textAlign: 'center', marginBottom: '1.5rem' }}>\n              <div style={{\n                fontSize: '3rem',\n                marginBottom: '1rem',\n                animation: 'pulse 2s infinite',\n              }}>\n                📧\n              </div>\n              <h3 style={styles.authOtpTitle}>🔐 Verify Your Email</h3>\n              <p style={{ color: styles.currentTheme.textLight, marginBottom: '1rem' }}>\n                ✨ We've sent a magical 6-digit code to<br />\n                <strong style={{ color: styles.currentTheme.primary }}>{email}</strong>\n              </p>\n            </div>\n            <div style={{ position: 'relative' }}>\n              <span style={{\n                position: 'absolute',\n                left: '1rem',\n                top: '50%',\n                transform: 'translateY(-50%)',\n                fontSize: '1.2rem',\n                color: styles.currentTheme.textLight,\n                zIndex: 1,\n              }}>\n                🔢\n              </span>\n              <input\n                type=\"text\"\n                value={otp}\n                onChange={(e) => setOtp(e.target.value)}\n                placeholder=\"Enter 6-digit code\"\n                maxLength=\"6\"\n                style={{\n                  ...styles.authOtpInput,\n                  paddingLeft: '3rem',\n                }}\n              />\n            </div>\n            <button\n              onClick={handleOtpVerification}\n              style={{\n                ...styles.authOtpButton,\n                background: styles.currentTheme.gradient,\n                animation: otp.length === 6 ? 'buttonPulse 2s infinite' : 'none',\n              }}\n            >\n              🎯 Verify & Continue\n            </button>\n          </div>\n        )}\n\n        {/* Error Message */}\n        {error && (\n          <div style={{\n            ...styles.authErrorMessage,\n            animation: 'slideIn 0.3s ease',\n          }}>\n            <span style={{ marginRight: '0.5rem' }}>⚠️</span>\n            {error}\n          </div>\n        )}\n\n        {/* Success Indicators */}\n        {email && password && !error && (\n          <div style={{\n            textAlign: 'center',\n            marginTop: '1rem',\n            color: styles.currentTheme.primary,\n            fontSize: '0.9rem',\n            animation: 'fadeIn 0.5s ease',\n          }}>\n            ✅ Ready to {isSignupMode ? 'create your account' : 'sign in'}!\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default AuthPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,QAAQ,cAAc;AAC5C,SAASC,OAAO,EAAEC,8BAA8B,EAAEC,0BAA0B,QAAQ,eAAe;AACnG,SAASC,YAAY,EAAEC,GAAG,EAAEC,MAAM,QAAQ,oBAAoB;AAC9D,OAAOC,OAAO,MAAM,aAAa;AACjC,OAAOC,MAAM,MAAM,UAAU;;AAE7B;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAc,GAAG;EACrBC,MAAM,EAAE,yCAAyC;EACjDC,UAAU,EAAE,+BAA+B;EAC3CC,SAAS,EAAE,eAAe;EAC1BC,aAAa,EAAE,mCAAmC;EAClDC,iBAAiB,EAAE,cAAc;EACjCC,KAAK,EAAE,2CAA2C;EAClDC,aAAa,EAAE;AACjB,CAAC;AAED,MAAMC,GAAG,GAAGrB,aAAa,CAACa,cAAc,CAAC;AACzC,MAAMS,IAAI,GAAGrB,OAAO,CAACoB,GAAG,CAAC;AACzB,MAAME,EAAE,GAAGnB,YAAY,CAACiB,GAAG,CAAC;;AAE5B;AACA,MAAMG,kBAAkB,GAAG,iBAAiB;AAC5C,MAAMC,mBAAmB,GAAG,kBAAkB;AAC9C,MAAMC,kBAAkB,GAAG,mBAAmB;AAE9C,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EACtC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkC,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoC,KAAK,EAAEC,QAAQ,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsC,GAAG,EAAEC,MAAM,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC0C,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC8C,SAAS,EAAEC,YAAY,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoD,SAAS,EAAEC,YAAY,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACsD,aAAa,EAAEC,gBAAgB,CAAC,GAAGvD,QAAQ,CAAC;IAAEwD,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAClE,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACAC,SAAS,CAAC,MAAM;IACd,MAAM2D,eAAe,GAAGA,CAAA,KAAM;MAC5B,MAAMC,YAAY,GAAG,EAAE;MACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3BD,YAAY,CAACE,IAAI,CAAC;UAChBC,EAAE,EAAEF,CAAC;UACLN,CAAC,EAAES,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACtBT,CAAC,EAAEQ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACtBC,IAAI,EAAEF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;UAC3BE,KAAK,EAAEH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;UAC5BG,OAAO,EAAEJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;QACjC,CAAC,CAAC;MACJ;MACAb,YAAY,CAACQ,YAAY,CAAC;IAC5B,CAAC;IACDD,eAAe,CAAC,CAAC;;IAEjB;IACA,MAAMU,eAAe,GAAIC,CAAC,IAAK;MAC7BhB,gBAAgB,CAAC;QACfC,CAAC,EAAGe,CAAC,CAACC,OAAO,GAAGC,MAAM,CAACC,UAAU,GAAI,GAAG;QACxCjB,CAAC,EAAGc,CAAC,CAACI,OAAO,GAAGF,MAAM,CAACG,WAAW,GAAI;MACxC,CAAC,CAAC;IACJ,CAAC;IAEDH,MAAM,CAACI,gBAAgB,CAAC,WAAW,EAAEP,eAAe,CAAC;IACrD,OAAO,MAAMG,MAAM,CAACK,mBAAmB,CAAC,WAAW,EAAER,eAAe,CAAC;EACvE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMS,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMzC,GAAG,GAAG2B,IAAI,CAACe,KAAK,CAAC,MAAM,GAAGf,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;IACzDzB,eAAe,CAACH,GAAG,CAAC;IACpB,OAAOA,GAAG;EACZ,CAAC;EAED,MAAM2C,YAAY,GAAGA,CAACjD,KAAK,EAAEM,GAAG,KAAK;IACnC,MAAM4C,cAAc,GAAG;MACrBC,QAAQ,EAAEnD,KAAK;MACfM,GAAG,EAAEA;IACP,CAAC;IAED7B,OAAO,CAAC2E,IAAI,CAAC1D,kBAAkB,EAAEC,mBAAmB,EAAEuD,cAAc,EAAEtD,kBAAkB,CAAC,CACtFyD,IAAI,CAAEC,QAAQ,IAAK;MAClBC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEF,QAAQ,CAACG,MAAM,EAAEH,QAAQ,CAACI,IAAI,CAAC;IAC1D,CAAC,EAAGC,GAAG,IAAK;MACVJ,OAAO,CAACnD,KAAK,CAAC,qBAAqB,EAAEuD,GAAG,CAAC;IAC3C,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,sBAAsB,GAAIrB,CAAC,IAAK;IACpC,MAAMsB,IAAI,GAAGtB,CAAC,CAACuB,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAIF,IAAI,EAAE;MACR,MAAMG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACvBrD,aAAa,CAACmD,MAAM,CAACG,MAAM,CAAC,CAAC,CAAC;MAChC,CAAC;MACDH,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC,CAAC,CAAC;IAC9B;EACF,CAAC;EAED,MAAMQ,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACrE,KAAK,IAAI,CAACE,QAAQ,EAAE;MACvBG,QAAQ,CAAC,2BAA2B,CAAC;MACrC;IACF;IAEAU,YAAY,CAAC,IAAI,CAAC;IAClBV,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMiE,cAAc,GAAG,MAAMlG,8BAA8B,CAACoB,IAAI,EAAEQ,KAAK,EAAEE,QAAQ,CAAC;MAClF,MAAMI,GAAG,GAAGyC,WAAW,CAAC,CAAC;MACzBE,YAAY,CAACjD,KAAK,EAAEM,GAAG,CAAC;MACxBK,YAAY,CAAC,IAAI,CAAC;;MAElB;MACA,MAAM4D,OAAO,GAAGhG,GAAG,CAACkB,EAAE,EAAE,OAAO,EAAE6E,cAAc,CAACE,IAAI,CAACC,GAAG,CAAC;MACzD,MAAMjG,MAAM,CAAC+F,OAAO,EAAE;QACpBvE,KAAK,EAAEA,KAAK;QACZyE,GAAG,EAAEH,cAAc,CAACE,IAAI,CAACC,GAAG;QAC5BC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBC,EAAE,EAAEhE;MACN,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdC,QAAQ,CAACD,KAAK,CAACyE,OAAO,CAAC;IACzB,CAAC,SAAS;MACR9D,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM+D,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAIxE,GAAG,KAAKE,YAAY,CAACuE,QAAQ,CAAC,CAAC,EAAE;MACnCpE,YAAY,CAAC,KAAK,CAAC;MACnBJ,MAAM,CAAC,EAAE,CAAC;MACVU,eAAe,CAAC,KAAK,CAAC;MACtBZ,QAAQ,CAAC,EAAE,CAAC;MACZ2E,KAAK,CAAC,wDAAwD,CAAC;IACjE,CAAC,MAAM;MACL3E,QAAQ,CAAC,gCAAgC,CAAC;IAC5C;EACF,CAAC;EAED,MAAM4E,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAACjF,KAAK,IAAI,CAACE,QAAQ,EAAE;MACvBG,QAAQ,CAAC,2BAA2B,CAAC;MACrC;IACF;IAEAU,YAAY,CAAC,IAAI,CAAC;IAClBV,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMiE,cAAc,GAAG,MAAMjG,0BAA0B,CAACmB,IAAI,EAAEQ,KAAK,EAAEE,QAAQ,CAAC;MAC9EJ,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,QAAQ,CAACD,KAAK,CAACyE,OAAO,CAAC;IACzB,CAAC,SAAS;MACR9D,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,oBACEnC,OAAA;IAAKsG,KAAK,EAAE;MACV,GAAGxG,MAAM,CAACyG,iBAAiB;MAC3BC,UAAU,EAAE;AAClB,kCAAkC1G,MAAM,CAAC2G,YAAY,CAACC,OAAO,UAAU5G,MAAM,CAAC2G,YAAY,CAACE,MAAM,WAAW7G,MAAM,CAAC2G,YAAY,CAACG,YAAY,WAAW9G,MAAM,CAAC2G,YAAY,CAACI,SAAS,WAAW/G,MAAM,CAAC2G,YAAY,CAACC,OAAO;AAC1N,oCAAoChE,aAAa,CAACE,CAAC,KAAKF,aAAa,CAACG,CAAC,MAAM/C,MAAM,CAAC2G,YAAY,CAACC,OAAO;AACxG,6CAA6C5G,MAAM,CAAC2G,YAAY,CAACE,MAAM;AACvE,6CAA6C7G,MAAM,CAAC2G,YAAY,CAACG,YAAY;AAC7E;IACI,CAAE;IAAAE,QAAA,gBAEA9G,OAAA;MAAKsG,KAAK,EAAE;QACV,GAAGxG,MAAM,CAACiH,uBAAuB;QACjCC,SAAS,EAAE,aAAatE,aAAa,CAACE,CAAC,GAAG,IAAI,OAAOF,aAAa,CAACG,CAAC,GAAG,IAAI;MAC7E;IAAE;MAAAoE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAGR5E,SAAS,CAAC6E,GAAG,CAAEC,QAAQ,iBACtBtH,OAAA;MAEEsG,KAAK,EAAE;QACLiB,QAAQ,EAAE,UAAU;QACpBC,IAAI,EAAE,GAAGF,QAAQ,CAAC1E,CAAC,GAAG;QACtB6E,GAAG,EAAE,GAAGH,QAAQ,CAACzE,CAAC,GAAG;QACrB6E,KAAK,EAAE,GAAGJ,QAAQ,CAAC/D,IAAI,IAAI;QAC3BoE,MAAM,EAAE,GAAGL,QAAQ,CAAC/D,IAAI,IAAI;QAC5BiD,UAAU,EAAE1G,MAAM,CAAC2G,YAAY,CAACC,OAAO;QACvCkB,YAAY,EAAE,KAAK;QACnBnE,OAAO,EAAE6D,QAAQ,CAAC7D,OAAO;QACzBoE,SAAS,EAAE,gBAAiBP,QAAQ,CAAClE,EAAE,GAAG,CAAC,GAAI,CAAC,IAAI,EAAE,GAAGkE,QAAQ,CAAC9D,KAAK,GAAG,CAAC,mBAAmB;QAC9FsE,MAAM,EAAE,CAAC;QACTC,aAAa,EAAE;MACjB;IAAE,GAbGT,QAAQ,CAAClE,EAAE;MAAA6D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAcjB,CACF,CAAC,eAGFpH,OAAA;MACEsG,KAAK,EAAE;QACL,GAAGxG,MAAM,CAACkI,iBAAiB;QAC3BhB,SAAS,EAAElE,aAAa,GACpB,wCAAwCJ,aAAa,CAACG,CAAC,GAAG,IAAI,gBAAgBH,aAAa,CAACE,CAAC,GAAG,IAAI,MAAM,GAC1G,wBAAwB;QAC5BqF,SAAS,EAAEnF,aAAa,GACpB;AACd,kCAAkChD,MAAM,CAAC2G,YAAY,CAACyB,MAAM;AAC5D,iCAAiCpI,MAAM,CAAC2G,YAAY,CAACyB,MAAM;AAC3D;AACA;AACA,aAAa,GACCpI,MAAM,CAACkI,iBAAiB,CAACC;MAC/B,CAAE;MACFE,YAAY,EAAEA,CAAA,KAAMpF,gBAAgB,CAAC,IAAI,CAAE;MAC3CqF,YAAY,EAAEA,CAAA,KAAMrF,gBAAgB,CAAC,KAAK,CAAE;MAAA+D,QAAA,gBAE5C9G,OAAA;QAAIsG,KAAK,EAAExG,MAAM,CAACuI,aAAc;QAAAvB,QAAA,EAC7B1E,YAAY,GAAG,kBAAkB,GAAG;MAAiB;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eACLpH,OAAA;QAAGsG,KAAK,EAAExG,MAAM,CAACwI,gBAAiB;QAAAxB,QAAA,EAC/B1E,YAAY,GACT,8DAA8D,GAC9D;MAAgD;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEnD,CAAC,EAGHhF,YAAY,IAAI,CAACN,SAAS,iBACzB9B,OAAA;QAAKsG,KAAK,EAAE;UACViC,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,QAAQ;UACxBC,YAAY,EAAE,MAAM;UACpBC,GAAG,EAAE;QACP,CAAE;QAAA5B,QAAA,gBACA9G,OAAA;UAAKsG,KAAK,EAAE;YACVoB,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,KAAK;YACnBpB,UAAU,EAAEpF,KAAK,IAAIE,QAAQ,GAAGxB,MAAM,CAAC2G,YAAY,CAACC,OAAO,GAAG5G,MAAM,CAAC2G,YAAY,CAACkC,MAAM;YACxFC,UAAU,EAAE,eAAe;YAC3Bf,SAAS,EAAEzG,KAAK,IAAIE,QAAQ,GAAG,mBAAmB,GAAG;UACvD;QAAE;UAAA2F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACTpH,OAAA;UAAKsG,KAAK,EAAE;YACVoB,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,KAAK;YACnBpB,UAAU,EAAExE,UAAU,GAAGlC,MAAM,CAAC2G,YAAY,CAACC,OAAO,GAAG5G,MAAM,CAAC2G,YAAY,CAACkC,MAAM;YACjFC,UAAU,EAAE,eAAe;YAC3Bf,SAAS,EAAE7F,UAAU,GAAG,mBAAmB,GAAG;UAChD;QAAE;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACTpH,OAAA;UAAKsG,KAAK,EAAE;YACVoB,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,KAAK;YACnBpB,UAAU,EAAE1G,MAAM,CAAC2G,YAAY,CAACkC,MAAM;YACtCC,UAAU,EAAE;UACd;QAAE;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAEA,CAACtF,SAAS,gBACT9B,OAAA,CAAAE,SAAA;QAAA4G,QAAA,gBAEE9G,OAAA;UAAKsG,KAAK,EAAExG,MAAM,CAAC+I,cAAe;UAAA/B,QAAA,eAChC9G,OAAA;YAAKsG,KAAK,EAAE;cAAEiB,QAAQ,EAAE;YAAW,CAAE;YAAAT,QAAA,gBACnC9G,OAAA;cAAMsG,KAAK,EAAE;gBACXiB,QAAQ,EAAE,UAAU;gBACpBC,IAAI,EAAE,SAAS;gBACfC,GAAG,EAAE,KAAK;gBACVT,SAAS,EAAE,kBAAkB;gBAC7B8B,QAAQ,EAAE,QAAQ;gBAClBC,KAAK,EAAEjJ,MAAM,CAAC2G,YAAY,CAACuC,SAAS;gBACpClB,MAAM,EAAE;cACV,CAAE;cAAAhB,QAAA,EAAC;YAEH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPpH,OAAA;cACEiJ,IAAI,EAAC,OAAO;cACZC,KAAK,EAAE9H,KAAM;cACb+H,QAAQ,EAAGxF,CAAC,IAAKtC,QAAQ,CAACsC,CAAC,CAACuB,MAAM,CAACgE,KAAK,CAAE;cAC1CE,WAAW,EAAC,0BAA0B;cACtC9C,KAAK,EAAE;gBACL,GAAGxG,MAAM,CAACuJ,SAAS;gBACnBC,WAAW,EAAE;cACf,CAAE;cACFC,QAAQ,EAAErH;YAAU;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpH,OAAA;UAAKsG,KAAK,EAAExG,MAAM,CAAC+I,cAAe;UAAA/B,QAAA,eAChC9G,OAAA;YAAKsG,KAAK,EAAE;cAAEiB,QAAQ,EAAE;YAAW,CAAE;YAAAT,QAAA,gBACnC9G,OAAA;cAAMsG,KAAK,EAAE;gBACXiB,QAAQ,EAAE,UAAU;gBACpBC,IAAI,EAAE,SAAS;gBACfC,GAAG,EAAE,KAAK;gBACVT,SAAS,EAAE,kBAAkB;gBAC7B8B,QAAQ,EAAE,QAAQ;gBAClBC,KAAK,EAAEjJ,MAAM,CAAC2G,YAAY,CAACuC,SAAS;gBACpClB,MAAM,EAAE;cACV,CAAE;cAAAhB,QAAA,EAAC;YAEH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPpH,OAAA;cACEiJ,IAAI,EAAE3G,YAAY,GAAG,MAAM,GAAG,UAAW;cACzC4G,KAAK,EAAE5H,QAAS;cAChB6H,QAAQ,EAAGxF,CAAC,IAAKpC,WAAW,CAACoC,CAAC,CAACuB,MAAM,CAACgE,KAAK,CAAE;cAC7CE,WAAW,EAAC,qBAAqB;cACjC9C,KAAK,EAAE;gBACL,GAAGxG,MAAM,CAACuJ,SAAS;gBACnBC,WAAW,EAAE,QAAQ;gBACrBE,YAAY,EAAE;cAChB,CAAE;cACFD,QAAQ,EAAErH;YAAU;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACFpH,OAAA;cACEiJ,IAAI,EAAC,QAAQ;cACbQ,OAAO,EAAEA,CAAA,KAAMlH,eAAe,CAAC,CAACD,YAAY,CAAE;cAC9CgE,KAAK,EAAE;gBACLiB,QAAQ,EAAE,UAAU;gBACpBmC,KAAK,EAAE,SAAS;gBAChBjC,GAAG,EAAE,KAAK;gBACVT,SAAS,EAAE,kBAAkB;gBAC7BR,UAAU,EAAE,MAAM;gBAClBmC,MAAM,EAAE,MAAM;gBACdG,QAAQ,EAAE,QAAQ;gBAClBa,MAAM,EAAE,SAAS;gBACjBZ,KAAK,EAAEjJ,MAAM,CAAC2G,YAAY,CAACuC,SAAS;gBACpClB,MAAM,EAAE;cACV,CAAE;cACFyB,QAAQ,EAAErH,SAAU;cAAA4E,QAAA,EAEnBxE,YAAY,GAAG,IAAI,GAAG;YAAK;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLhF,YAAY,iBACXpC,OAAA;UAAKsG,KAAK,EAAExG,MAAM,CAAC+I,cAAe;UAAA/B,QAAA,eAChC9G,OAAA;YAAKsG,KAAK,EAAE;cAAEsD,SAAS,EAAE;YAAS,CAAE;YAAA9C,QAAA,gBAClC9G,OAAA;cAAOsG,KAAK,EAAE;gBACZiC,OAAO,EAAE,OAAO;gBAChBsB,OAAO,EAAE,QAAQ;gBACjBlB,MAAM,EAAE,cAAc7I,MAAM,CAAC2G,YAAY,CAACC,OAAO,EAAE;gBACnDkB,YAAY,EAAE,MAAM;gBACpBpB,UAAU,EAAE,0BAA0B;gBACtCmD,MAAM,EAAE,SAAS;gBACjBf,UAAU,EAAE,eAAe;gBAC3B,QAAQ,EAAE;kBACRpC,UAAU,EAAE,0BAA0B;kBACtCQ,SAAS,EAAE;gBACb;cACF,CAAE;cAAAF,QAAA,gBACA9G,OAAA;gBAAMsG,KAAK,EAAE;kBAAEwC,QAAQ,EAAE,MAAM;kBAAEP,OAAO,EAAE,OAAO;kBAAEE,YAAY,EAAE;gBAAS,CAAE;gBAAA3B,QAAA,EAAC;cAE7E;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPpH,OAAA;gBAAMsG,KAAK,EAAE;kBAAEyC,KAAK,EAAEjJ,MAAM,CAAC2G,YAAY,CAACuC,SAAS;kBAAEF,QAAQ,EAAE;gBAAS,CAAE;gBAAAhC,QAAA,EACvE9E,UAAU,GAAG,wBAAwB,GAAG;cAAwB;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACPpH,OAAA;gBACEiJ,IAAI,EAAC,MAAM;gBACXE,QAAQ,EAAEnE,sBAAuB;gBACjC8E,MAAM,EAAC,SAAS;gBAChBxD,KAAK,EAAE;kBAAEiC,OAAO,EAAE;gBAAO,CAAE;gBAC3BgB,QAAQ,EAAErH;cAAU;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,EACPpF,UAAU,iBACThC,OAAA;cAAKsG,KAAK,EAAE;gBAAEyD,SAAS,EAAE,MAAM;gBAAExC,QAAQ,EAAE,UAAU;gBAAEgB,OAAO,EAAE;cAAe,CAAE;cAAAzB,QAAA,gBAC/E9G,OAAA;gBACEgK,GAAG,EAAEhI,UAAW;gBAChBiI,GAAG,EAAC,iBAAiB;gBACrB3D,KAAK,EAAE;kBACL,GAAGxG,MAAM,CAACoK,kBAAkB;kBAC5BrC,SAAS,EAAE;gBACb;cAAE;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFpH,OAAA;gBAAKsG,KAAK,EAAE;kBACViB,QAAQ,EAAE,UAAU;kBACpBE,GAAG,EAAE,MAAM;kBACXiC,KAAK,EAAE,MAAM;kBACblD,UAAU,EAAE1G,MAAM,CAAC2G,YAAY,CAACC,OAAO;kBACvCqC,KAAK,EAAE,OAAO;kBACdnB,YAAY,EAAE,KAAK;kBACnBF,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdY,OAAO,EAAE,MAAM;kBACf4B,UAAU,EAAE,QAAQ;kBACpB3B,cAAc,EAAE,QAAQ;kBACxBM,QAAQ,EAAE,QAAQ;kBAClBjB,SAAS,EAAE;gBACb,CAAE;gBAAAf,QAAA,EAAC;cAEH;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDpH,OAAA;UAAKsG,KAAK,EAAExG,MAAM,CAACsK,eAAgB;UAAAtD,QAAA,EAChC1E,YAAY,gBACXpC,OAAA,CAAAE,SAAA;YAAA4G,QAAA,gBACE9G,OAAA;cACEyJ,OAAO,EAAEhE,YAAa;cACtB8D,QAAQ,EAAErH,SAAU;cACpBoE,KAAK,EAAExG,MAAM,CAACuK,iBAAkB;cAAAvD,QAAA,GAE/B5E,SAAS,iBAAIlC,OAAA;gBAAKsG,KAAK,EAAExG,MAAM,CAACwK;cAAmB;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAC1DlF,SAAS,GAAG,sBAAsB,GAAG,kBAAkB;YAAA;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACTpH,OAAA;cACEyJ,OAAO,EAAEA,CAAA,KAAMpH,eAAe,CAAC,KAAK,CAAE;cACtCkH,QAAQ,EAAErH,SAAU;cACpBoE,KAAK,EAAExG,MAAM,CAACyK,mBAAoB;cAAAzD,QAAA,EACnC;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT,CAAC,gBAEHpH,OAAA,CAAAE,SAAA;YAAA4G,QAAA,gBACE9G,OAAA;cACEyJ,OAAO,EAAEpD,WAAY;cACrBkD,QAAQ,EAAErH,SAAU;cACpBoE,KAAK,EAAExG,MAAM,CAACuK,iBAAkB;cAAAvD,QAAA,GAE/B5E,SAAS,iBAAIlC,OAAA;gBAAKsG,KAAK,EAAExG,MAAM,CAACwK;cAAmB;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAC1DlF,SAAS,GAAG,kBAAkB,GAAG,YAAY;YAAA;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACTpH,OAAA;cACEyJ,OAAO,EAAEA,CAAA,KAAMpH,eAAe,CAAC,IAAI,CAAE;cACrCkH,QAAQ,EAAErH,SAAU;cACpBoE,KAAK,EAAExG,MAAM,CAACyK,mBAAoB;cAAAzD,QAAA,EACnC;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA,eACN,CAAC;MAAA;MAEH;MACApH,OAAA;QAAKsG,KAAK,EAAExG,MAAM,CAAC0K,gBAAiB;QAAA1D,QAAA,gBAClC9G,OAAA;UAAKsG,KAAK,EAAE;YAAEsD,SAAS,EAAE,QAAQ;YAAEnB,YAAY,EAAE;UAAS,CAAE;UAAA3B,QAAA,gBAC1D9G,OAAA;YAAKsG,KAAK,EAAE;cACVwC,QAAQ,EAAE,MAAM;cAChBL,YAAY,EAAE,MAAM;cACpBZ,SAAS,EAAE;YACb,CAAE;YAAAf,QAAA,EAAC;UAEH;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNpH,OAAA;YAAIsG,KAAK,EAAExG,MAAM,CAAC2K,YAAa;YAAA3D,QAAA,EAAC;UAAoB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzDpH,OAAA;YAAGsG,KAAK,EAAE;cAAEyC,KAAK,EAAEjJ,MAAM,CAAC2G,YAAY,CAACuC,SAAS;cAAEP,YAAY,EAAE;YAAO,CAAE;YAAA3B,QAAA,GAAC,6CAClC,eAAA9G,OAAA;cAAAiH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5CpH,OAAA;cAAQsG,KAAK,EAAE;gBAAEyC,KAAK,EAAEjJ,MAAM,CAAC2G,YAAY,CAACC;cAAQ,CAAE;cAAAI,QAAA,EAAE1F;YAAK;cAAA6F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNpH,OAAA;UAAKsG,KAAK,EAAE;YAAEiB,QAAQ,EAAE;UAAW,CAAE;UAAAT,QAAA,gBACnC9G,OAAA;YAAMsG,KAAK,EAAE;cACXiB,QAAQ,EAAE,UAAU;cACpBC,IAAI,EAAE,MAAM;cACZC,GAAG,EAAE,KAAK;cACVT,SAAS,EAAE,kBAAkB;cAC7B8B,QAAQ,EAAE,QAAQ;cAClBC,KAAK,EAAEjJ,MAAM,CAAC2G,YAAY,CAACuC,SAAS;cACpClB,MAAM,EAAE;YACV,CAAE;YAAAhB,QAAA,EAAC;UAEH;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPpH,OAAA;YACEiJ,IAAI,EAAC,MAAM;YACXC,KAAK,EAAExH,GAAI;YACXyH,QAAQ,EAAGxF,CAAC,IAAKhC,MAAM,CAACgC,CAAC,CAACuB,MAAM,CAACgE,KAAK,CAAE;YACxCE,WAAW,EAAC,oBAAoB;YAChCsB,SAAS,EAAC,GAAG;YACbpE,KAAK,EAAE;cACL,GAAGxG,MAAM,CAAC6K,YAAY;cACtBrB,WAAW,EAAE;YACf;UAAE;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNpH,OAAA;UACEyJ,OAAO,EAAEvD,qBAAsB;UAC/BI,KAAK,EAAE;YACL,GAAGxG,MAAM,CAAC8K,aAAa;YACvBpE,UAAU,EAAE1G,MAAM,CAAC2G,YAAY,CAACoE,QAAQ;YACxChD,SAAS,EAAEnG,GAAG,CAACoJ,MAAM,KAAK,CAAC,GAAG,yBAAyB,GAAG;UAC5D,CAAE;UAAAhE,QAAA,EACH;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGA5F,KAAK,iBACJxB,OAAA;QAAKsG,KAAK,EAAE;UACV,GAAGxG,MAAM,CAACiL,gBAAgB;UAC1BlD,SAAS,EAAE;QACb,CAAE;QAAAf,QAAA,gBACA9G,OAAA;UAAMsG,KAAK,EAAE;YAAE0E,WAAW,EAAE;UAAS,CAAE;UAAAlE,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAChD5F,KAAK;MAAA;QAAAyF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAhG,KAAK,IAAIE,QAAQ,IAAI,CAACE,KAAK,iBAC1BxB,OAAA;QAAKsG,KAAK,EAAE;UACVsD,SAAS,EAAE,QAAQ;UACnBG,SAAS,EAAE,MAAM;UACjBhB,KAAK,EAAEjJ,MAAM,CAAC2G,YAAY,CAACC,OAAO;UAClCoC,QAAQ,EAAE,QAAQ;UAClBjB,SAAS,EAAE;QACb,CAAE;QAAAf,QAAA,GAAC,kBACU,EAAC1E,YAAY,GAAG,qBAAqB,GAAG,SAAS,EAAC,GAC/D;MAAA;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjG,EAAA,CApfIF,QAAQ;AAAAgK,EAAA,GAARhK,QAAQ;AAsfd,eAAeA,QAAQ;AAAC,IAAAgK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}