{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\quiz\\\\aich (4)\\\\aich (3)\\\\aich(5)\\\\src\\\\AuthPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { initializeApp } from 'firebase/app';\nimport { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword } from 'firebase/auth';\nimport { getFirestore, doc, setDoc } from 'firebase/firestore';\nimport emailjs from 'emailjs-com';\nimport styles from './styles';\n\n// Firebase Config\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst firebaseConfig = {\n  apiKey: \"AIzaSyDmRAxMJvEKmel3JpUF1dXbBNccQJibcGo\",\n  authDomain: \"exploit-f41eb.firebaseapp.com\",\n  projectId: \"exploit-f41eb\",\n  storageBucket: \"exploit-f41eb.firebasestorage.app\",\n  messagingSenderId: \"849491574761\",\n  appId: \"1:849491574761:web:22ddb4ea62063d7a709578\",\n  measurementId: \"G-R0SWYQKXCV\"\n};\nconst app = initializeApp(firebaseConfig);\nconst auth = getAuth(app);\nconst db = getFirestore(app);\n\n// EmailJS Config\nconst EMAILJS_SERVICE_ID = 'service_sthdzci';\nconst EMAILJS_TEMPLATE_ID = 'template_8mnpzqq';\nconst EMAILJS_PUBLIC_KEY = 'jEfQd05ItbAthM3QN';\nconst AuthPage = ({\n  onAuthSuccess\n}) => {\n  _s();\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState('');\n  const [otp, setOtp] = useState('');\n  const [generatedOtp, setGeneratedOtp] = useState('');\n  const [isOtpSent, setIsOtpSent] = useState(false);\n  const [profilePic, setProfilePic] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isSignupMode, setIsSignupMode] = useState(true);\n  const [showPassword, setShowPassword] = useState(false);\n  const [particles, setParticles] = useState([]);\n\n  // Create floating particles effect\n  useEffect(() => {\n    const createParticles = () => {\n      const newParticles = [];\n      for (let i = 0; i < 15; i++) {\n        newParticles.push({\n          id: i,\n          x: Math.random() * 100,\n          y: Math.random() * 100,\n          size: Math.random() * 4 + 2,\n          speed: Math.random() * 2 + 1,\n          opacity: Math.random() * 0.5 + 0.2\n        });\n      }\n      setParticles(newParticles);\n    };\n    createParticles();\n  }, []);\n  const generateOtp = () => {\n    const otp = Math.floor(100000 + Math.random() * 900000); // Generate a 6-digit OTP\n    setGeneratedOtp(otp);\n    return otp;\n  };\n  const sendOtpEmail = (email, otp) => {\n    const templateParams = {\n      to_email: email,\n      otp: otp\n    };\n    emailjs.send(EMAILJS_SERVICE_ID, EMAILJS_TEMPLATE_ID, templateParams, EMAILJS_PUBLIC_KEY).then(response => {\n      console.log('OTP sent!', response.status, response.text);\n    }, err => {\n      console.error('OTP sending failed:', err);\n    });\n  };\n  const handleProfilePicChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setProfilePic(reader.result); // Store image in base64\n      };\n      reader.readAsDataURL(file); // Convert file to base64\n    }\n  };\n  const handleSignup = async () => {\n    if (!email || !password) {\n      setError('Please fill in all fields');\n      return;\n    }\n    setIsLoading(true);\n    setError('');\n    try {\n      const userCredential = await createUserWithEmailAndPassword(auth, email, password);\n      const otp = generateOtp();\n      sendOtpEmail(email, otp);\n      setIsOtpSent(true);\n\n      // Save user data to Firestore\n      const userRef = doc(db, \"users\", userCredential.user.uid);\n      await setDoc(userRef, {\n        email: email,\n        uid: userCredential.user.uid,\n        createdAt: new Date(),\n        dp: profilePic\n      });\n    } catch (error) {\n      setError(error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleOtpVerification = () => {\n    if (otp === generatedOtp.toString()) {\n      setIsOtpSent(false);\n      setOtp('');\n      setIsSignupMode(false);\n      setError('');\n      alert('OTP Verified! You can now login with your credentials.');\n    } else {\n      setError('Invalid OTP. Please try again.');\n    }\n  };\n  const handleLogin = async () => {\n    if (!email || !password) {\n      setError('Please fill in all fields');\n      return;\n    }\n    setIsLoading(true);\n    setError('');\n    try {\n      const userCredential = await signInWithEmailAndPassword(auth, email, password);\n      onAuthSuccess();\n    } catch (error) {\n      setError(error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.authPageContainer,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.authBackgroundAnimation\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.authFormContainer,\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        style: styles.authFormTitle,\n        children: isSignupMode ? 'Create Account' : 'Welcome Back'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: styles.authFormSubtitle,\n        children: isSignupMode ? 'Join our learning platform and start your journey' : 'Sign in to continue your learning journey'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), !isOtpSent ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.authInputGroup,\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            value: email,\n            onChange: e => setEmail(e.target.value),\n            placeholder: \"Enter your email\",\n            style: styles.authInput,\n            disabled: isLoading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.authInputGroup,\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            value: password,\n            onChange: e => setPassword(e.target.value),\n            placeholder: \"Enter your password\",\n            style: styles.authInput,\n            disabled: isLoading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 13\n        }, this), isSignupMode && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.authInputGroup,\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            onChange: handleProfilePicChange,\n            accept: \"image/*\",\n            style: styles.authFileInput,\n            disabled: isLoading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 17\n          }, this), profilePic && /*#__PURE__*/_jsxDEV(\"img\", {\n            src: profilePic,\n            alt: \"Profile Preview\",\n            style: styles.authProfilePreview\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.authButtonGroup,\n          children: isSignupMode ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSignup,\n              disabled: isLoading,\n              style: styles.authButtonPrimary,\n              children: [isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.authLoadingSpinner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 35\n              }, this), isLoading ? 'Creating Account...' : 'Sign Up']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsSignupMode(false),\n              disabled: isLoading,\n              style: styles.authButtonSecondary,\n              children: \"Login Instead\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleLogin,\n              disabled: isLoading,\n              style: styles.authButtonPrimary,\n              children: [isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.authLoadingSpinner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 35\n              }, this), isLoading ? 'Signing In...' : 'Sign In']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsSignupMode(true),\n              disabled: isLoading,\n              style: styles.authButtonSecondary,\n              children: \"Create Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true) :\n      /*#__PURE__*/\n      /* OTP Verification Section */\n      _jsxDEV(\"div\", {\n        style: styles.authOtpContainer,\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: styles.authOtpTitle,\n          children: \"Verify Your Email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: styles.currentTheme.textLight,\n            marginBottom: '1rem'\n          },\n          children: [\"We've sent a 6-digit code to \", email]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: otp,\n          onChange: e => setOtp(e.target.value),\n          placeholder: \"Enter OTP\",\n          maxLength: \"6\",\n          style: styles.authOtpInput\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleOtpVerification,\n          style: styles.authOtpButton,\n          children: \"Verify OTP\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 11\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.authErrorMessage,\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 154,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthPage, \"RnwRUaX4FFNdnqE7BPeTZqfh50I=\");\n_c = AuthPage;\nexport default AuthPage;\nvar _c;\n$RefreshReg$(_c, \"AuthPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "initializeApp", "getAuth", "createUserWithEmailAndPassword", "signInWithEmailAndPassword", "getFirestore", "doc", "setDoc", "emailjs", "styles", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "measurementId", "app", "auth", "db", "EMAILJS_SERVICE_ID", "EMAILJS_TEMPLATE_ID", "EMAILJS_PUBLIC_KEY", "AuthPage", "onAuthSuccess", "_s", "email", "setEmail", "password", "setPassword", "error", "setError", "otp", "setOtp", "generatedOtp", "setGeneratedOtp", "isOtpSent", "setIsOtpSent", "profilePic", "setProfilePic", "isLoading", "setIsLoading", "isSignupMode", "setIsSignupMode", "showPassword", "setShowPassword", "particles", "setParticles", "createParticles", "newParticles", "i", "push", "id", "x", "Math", "random", "y", "size", "speed", "opacity", "generateOtp", "floor", "sendOtpEmail", "templateParams", "to_email", "send", "then", "response", "console", "log", "status", "text", "err", "handleProfilePicChange", "e", "file", "target", "files", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "handleSignup", "userCredential", "userRef", "user", "uid", "createdAt", "Date", "dp", "message", "handleOtpVerification", "toString", "alert", "handleLogin", "style", "authPageContainer", "children", "authBackgroundAnimation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "authFormContainer", "authFormTitle", "authFormSubtitle", "authInputGroup", "type", "value", "onChange", "placeholder", "authInput", "disabled", "accept", "authFileInput", "src", "alt", "authProfilePreview", "authButtonGroup", "onClick", "authButtonPrimary", "authLoading<PERSON><PERSON>ner", "authButtonSecondary", "authOtpContainer", "authOtpTitle", "color", "currentTheme", "textLight", "marginBottom", "max<PERSON><PERSON><PERSON>", "authOtpInput", "authOtpButton", "authErrorMessage", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(5)/src/AuthPage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { initializeApp } from 'firebase/app';\nimport { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword } from 'firebase/auth';\nimport { getFirestore, doc, setDoc } from 'firebase/firestore';\nimport emailjs from 'emailjs-com';\nimport styles from './styles';\n\n// Firebase Config\nconst firebaseConfig = {\n  apiKey: \"AIzaSyDmRAxMJvEKmel3JpUF1dXbBNccQJibcGo\",\n  authDomain: \"exploit-f41eb.firebaseapp.com\",\n  projectId: \"exploit-f41eb\",\n  storageBucket: \"exploit-f41eb.firebasestorage.app\",\n  messagingSenderId: \"849491574761\",\n  appId: \"1:849491574761:web:22ddb4ea62063d7a709578\",\n  measurementId: \"G-R0SWYQKXCV\"\n};\n\nconst app = initializeApp(firebaseConfig);\nconst auth = getAuth(app);\nconst db = getFirestore(app);\n\n// EmailJS Config\nconst EMAILJS_SERVICE_ID = 'service_sthdzci';\nconst EMAILJS_TEMPLATE_ID = 'template_8mnpzqq';\nconst EMAILJS_PUBLIC_KEY = 'jEfQd05ItbAthM3QN';\n\nconst AuthPage = ({ onAuthSuccess }) => {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState('');\n  const [otp, setOtp] = useState('');\n  const [generatedOtp, setGeneratedOtp] = useState('');\n  const [isOtpSent, setIsOtpSent] = useState(false);\n  const [profilePic, setProfilePic] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isSignupMode, setIsSignupMode] = useState(true);\n  const [showPassword, setShowPassword] = useState(false);\n  const [particles, setParticles] = useState([]);\n\n  // Create floating particles effect\n  useEffect(() => {\n    const createParticles = () => {\n      const newParticles = [];\n      for (let i = 0; i < 15; i++) {\n        newParticles.push({\n          id: i,\n          x: Math.random() * 100,\n          y: Math.random() * 100,\n          size: Math.random() * 4 + 2,\n          speed: Math.random() * 2 + 1,\n          opacity: Math.random() * 0.5 + 0.2,\n        });\n      }\n      setParticles(newParticles);\n    };\n    createParticles();\n  }, []);\n\n  const generateOtp = () => {\n    const otp = Math.floor(100000 + Math.random() * 900000); // Generate a 6-digit OTP\n    setGeneratedOtp(otp);\n    return otp;\n  };\n\n  const sendOtpEmail = (email, otp) => {\n    const templateParams = {\n      to_email: email,\n      otp: otp\n    };\n\n    emailjs.send(EMAILJS_SERVICE_ID, EMAILJS_TEMPLATE_ID, templateParams, EMAILJS_PUBLIC_KEY)\n      .then((response) => {\n        console.log('OTP sent!', response.status, response.text);\n      }, (err) => {\n        console.error('OTP sending failed:', err);\n      });\n  };\n\n  const handleProfilePicChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setProfilePic(reader.result); // Store image in base64\n      };\n      reader.readAsDataURL(file); // Convert file to base64\n    }\n  };\n\n  const handleSignup = async () => {\n    if (!email || !password) {\n      setError('Please fill in all fields');\n      return;\n    }\n\n    setIsLoading(true);\n    setError('');\n\n    try {\n      const userCredential = await createUserWithEmailAndPassword(auth, email, password);\n      const otp = generateOtp();\n      sendOtpEmail(email, otp);\n      setIsOtpSent(true);\n\n      // Save user data to Firestore\n      const userRef = doc(db, \"users\", userCredential.user.uid);\n      await setDoc(userRef, {\n        email: email,\n        uid: userCredential.user.uid,\n        createdAt: new Date(),\n        dp: profilePic,\n      });\n\n    } catch (error) {\n      setError(error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleOtpVerification = () => {\n    if (otp === generatedOtp.toString()) {\n      setIsOtpSent(false);\n      setOtp('');\n      setIsSignupMode(false);\n      setError('');\n      alert('OTP Verified! You can now login with your credentials.');\n    } else {\n      setError('Invalid OTP. Please try again.');\n    }\n  };\n\n  const handleLogin = async () => {\n    if (!email || !password) {\n      setError('Please fill in all fields');\n      return;\n    }\n\n    setIsLoading(true);\n    setError('');\n\n    try {\n      const userCredential = await signInWithEmailAndPassword(auth, email, password);\n      onAuthSuccess();\n    } catch (error) {\n      setError(error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div style={styles.authPageContainer}>\n      {/* Animated Background */}\n      <div style={styles.authBackgroundAnimation}></div>\n\n      {/* Main Form Container */}\n      <div style={styles.authFormContainer}>\n        <h1 style={styles.authFormTitle}>\n          {isSignupMode ? 'Create Account' : 'Welcome Back'}\n        </h1>\n        <p style={styles.authFormSubtitle}>\n          {isSignupMode\n            ? 'Join our learning platform and start your journey'\n            : 'Sign in to continue your learning journey'\n          }\n        </p>\n\n        {!isOtpSent ? (\n          <>\n            {/* Email Input */}\n            <div style={styles.authInputGroup}>\n              <input\n                type=\"email\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                placeholder=\"Enter your email\"\n                style={styles.authInput}\n                disabled={isLoading}\n              />\n            </div>\n\n            {/* Password Input */}\n            <div style={styles.authInputGroup}>\n              <input\n                type=\"password\"\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n                placeholder=\"Enter your password\"\n                style={styles.authInput}\n                disabled={isLoading}\n              />\n            </div>\n\n            {/* Profile Picture Upload (only for signup) */}\n            {isSignupMode && (\n              <div style={styles.authInputGroup}>\n                <input\n                  type=\"file\"\n                  onChange={handleProfilePicChange}\n                  accept=\"image/*\"\n                  style={styles.authFileInput}\n                  disabled={isLoading}\n                />\n                {profilePic && (\n                  <img\n                    src={profilePic}\n                    alt=\"Profile Preview\"\n                    style={styles.authProfilePreview}\n                  />\n                )}\n              </div>\n            )}\n\n            {/* Action Buttons */}\n            <div style={styles.authButtonGroup}>\n              {isSignupMode ? (\n                <>\n                  <button\n                    onClick={handleSignup}\n                    disabled={isLoading}\n                    style={styles.authButtonPrimary}\n                  >\n                    {isLoading && <div style={styles.authLoadingSpinner}></div>}\n                    {isLoading ? 'Creating Account...' : 'Sign Up'}\n                  </button>\n                  <button\n                    onClick={() => setIsSignupMode(false)}\n                    disabled={isLoading}\n                    style={styles.authButtonSecondary}\n                  >\n                    Login Instead\n                  </button>\n                </>\n              ) : (\n                <>\n                  <button\n                    onClick={handleLogin}\n                    disabled={isLoading}\n                    style={styles.authButtonPrimary}\n                  >\n                    {isLoading && <div style={styles.authLoadingSpinner}></div>}\n                    {isLoading ? 'Signing In...' : 'Sign In'}\n                  </button>\n                  <button\n                    onClick={() => setIsSignupMode(true)}\n                    disabled={isLoading}\n                    style={styles.authButtonSecondary}\n                  >\n                    Create Account\n                  </button>\n                </>\n              )}\n            </div>\n          </>\n        ) : (\n          /* OTP Verification Section */\n          <div style={styles.authOtpContainer}>\n            <h3 style={styles.authOtpTitle}>Verify Your Email</h3>\n            <p style={{ color: styles.currentTheme.textLight, marginBottom: '1rem' }}>\n              We've sent a 6-digit code to {email}\n            </p>\n            <input\n              type=\"text\"\n              value={otp}\n              onChange={(e) => setOtp(e.target.value)}\n              placeholder=\"Enter OTP\"\n              maxLength=\"6\"\n              style={styles.authOtpInput}\n            />\n            <button\n              onClick={handleOtpVerification}\n              style={styles.authOtpButton}\n            >\n              Verify OTP\n            </button>\n          </div>\n        )}\n\n        {/* Error Message */}\n        {error && (\n          <div style={styles.authErrorMessage}>\n            {error}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default AuthPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,QAAQ,cAAc;AAC5C,SAASC,OAAO,EAAEC,8BAA8B,EAAEC,0BAA0B,QAAQ,eAAe;AACnG,SAASC,YAAY,EAAEC,GAAG,EAAEC,MAAM,QAAQ,oBAAoB;AAC9D,OAAOC,OAAO,MAAM,aAAa;AACjC,OAAOC,MAAM,MAAM,UAAU;;AAE7B;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAc,GAAG;EACrBC,MAAM,EAAE,yCAAyC;EACjDC,UAAU,EAAE,+BAA+B;EAC3CC,SAAS,EAAE,eAAe;EAC1BC,aAAa,EAAE,mCAAmC;EAClDC,iBAAiB,EAAE,cAAc;EACjCC,KAAK,EAAE,2CAA2C;EAClDC,aAAa,EAAE;AACjB,CAAC;AAED,MAAMC,GAAG,GAAGrB,aAAa,CAACa,cAAc,CAAC;AACzC,MAAMS,IAAI,GAAGrB,OAAO,CAACoB,GAAG,CAAC;AACzB,MAAME,EAAE,GAAGnB,YAAY,CAACiB,GAAG,CAAC;;AAE5B;AACA,MAAMG,kBAAkB,GAAG,iBAAiB;AAC5C,MAAMC,mBAAmB,GAAG,kBAAkB;AAC9C,MAAMC,kBAAkB,GAAG,mBAAmB;AAE9C,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EACtC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkC,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoC,KAAK,EAAEC,QAAQ,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsC,GAAG,EAAEC,MAAM,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC0C,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC8C,SAAS,EAAEC,YAAY,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoD,SAAS,EAAEC,YAAY,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACAC,SAAS,CAAC,MAAM;IACd,MAAMqD,eAAe,GAAGA,CAAA,KAAM;MAC5B,MAAMC,YAAY,GAAG,EAAE;MACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3BD,YAAY,CAACE,IAAI,CAAC;UAChBC,EAAE,EAAEF,CAAC;UACLG,CAAC,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACtBC,CAAC,EAAEF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACtBE,IAAI,EAAEH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;UAC3BG,KAAK,EAAEJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;UAC5BI,OAAO,EAAEL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;QACjC,CAAC,CAAC;MACJ;MACAR,YAAY,CAACE,YAAY,CAAC;IAC5B,CAAC;IACDD,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMY,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAM5B,GAAG,GAAGsB,IAAI,CAACO,KAAK,CAAC,MAAM,GAAGP,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;IACzDpB,eAAe,CAACH,GAAG,CAAC;IACpB,OAAOA,GAAG;EACZ,CAAC;EAED,MAAM8B,YAAY,GAAGA,CAACpC,KAAK,EAAEM,GAAG,KAAK;IACnC,MAAM+B,cAAc,GAAG;MACrBC,QAAQ,EAAEtC,KAAK;MACfM,GAAG,EAAEA;IACP,CAAC;IAED7B,OAAO,CAAC8D,IAAI,CAAC7C,kBAAkB,EAAEC,mBAAmB,EAAE0C,cAAc,EAAEzC,kBAAkB,CAAC,CACtF4C,IAAI,CAAEC,QAAQ,IAAK;MAClBC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEF,QAAQ,CAACG,MAAM,EAAEH,QAAQ,CAACI,IAAI,CAAC;IAC1D,CAAC,EAAGC,GAAG,IAAK;MACVJ,OAAO,CAACtC,KAAK,CAAC,qBAAqB,EAAE0C,GAAG,CAAC;IAC3C,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,sBAAsB,GAAIC,CAAC,IAAK;IACpC,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAIF,IAAI,EAAE;MACR,MAAMG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACvBzC,aAAa,CAACuC,MAAM,CAACG,MAAM,CAAC,CAAC,CAAC;MAChC,CAAC;MACDH,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC,CAAC,CAAC;IAC9B;EACF,CAAC;EAED,MAAMQ,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACzD,KAAK,IAAI,CAACE,QAAQ,EAAE;MACvBG,QAAQ,CAAC,2BAA2B,CAAC;MACrC;IACF;IAEAU,YAAY,CAAC,IAAI,CAAC;IAClBV,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMqD,cAAc,GAAG,MAAMtF,8BAA8B,CAACoB,IAAI,EAAEQ,KAAK,EAAEE,QAAQ,CAAC;MAClF,MAAMI,GAAG,GAAG4B,WAAW,CAAC,CAAC;MACzBE,YAAY,CAACpC,KAAK,EAAEM,GAAG,CAAC;MACxBK,YAAY,CAAC,IAAI,CAAC;;MAElB;MACA,MAAMgD,OAAO,GAAGpF,GAAG,CAACkB,EAAE,EAAE,OAAO,EAAEiE,cAAc,CAACE,IAAI,CAACC,GAAG,CAAC;MACzD,MAAMrF,MAAM,CAACmF,OAAO,EAAE;QACpB3D,KAAK,EAAEA,KAAK;QACZ6D,GAAG,EAAEH,cAAc,CAACE,IAAI,CAACC,GAAG;QAC5BC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBC,EAAE,EAAEpD;MACN,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdC,QAAQ,CAACD,KAAK,CAAC6D,OAAO,CAAC;IACzB,CAAC,SAAS;MACRlD,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMmD,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI5D,GAAG,KAAKE,YAAY,CAAC2D,QAAQ,CAAC,CAAC,EAAE;MACnCxD,YAAY,CAAC,KAAK,CAAC;MACnBJ,MAAM,CAAC,EAAE,CAAC;MACVU,eAAe,CAAC,KAAK,CAAC;MACtBZ,QAAQ,CAAC,EAAE,CAAC;MACZ+D,KAAK,CAAC,wDAAwD,CAAC;IACjE,CAAC,MAAM;MACL/D,QAAQ,CAAC,gCAAgC,CAAC;IAC5C;EACF,CAAC;EAED,MAAMgE,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAACrE,KAAK,IAAI,CAACE,QAAQ,EAAE;MACvBG,QAAQ,CAAC,2BAA2B,CAAC;MACrC;IACF;IAEAU,YAAY,CAAC,IAAI,CAAC;IAClBV,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMqD,cAAc,GAAG,MAAMrF,0BAA0B,CAACmB,IAAI,EAAEQ,KAAK,EAAEE,QAAQ,CAAC;MAC9EJ,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,QAAQ,CAACD,KAAK,CAAC6D,OAAO,CAAC;IACzB,CAAC,SAAS;MACRlD,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,oBACEnC,OAAA;IAAK0F,KAAK,EAAE5F,MAAM,CAAC6F,iBAAkB;IAAAC,QAAA,gBAEnC5F,OAAA;MAAK0F,KAAK,EAAE5F,MAAM,CAAC+F;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAGlDjG,OAAA;MAAK0F,KAAK,EAAE5F,MAAM,CAACoG,iBAAkB;MAAAN,QAAA,gBACnC5F,OAAA;QAAI0F,KAAK,EAAE5F,MAAM,CAACqG,aAAc;QAAAP,QAAA,EAC7BxD,YAAY,GAAG,gBAAgB,GAAG;MAAc;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACLjG,OAAA;QAAG0F,KAAK,EAAE5F,MAAM,CAACsG,gBAAiB;QAAAR,QAAA,EAC/BxD,YAAY,GACT,mDAAmD,GACnD;MAA2C;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE9C,CAAC,EAEH,CAACnE,SAAS,gBACT9B,OAAA,CAAAE,SAAA;QAAA0F,QAAA,gBAEE5F,OAAA;UAAK0F,KAAK,EAAE5F,MAAM,CAACuG,cAAe;UAAAT,QAAA,eAChC5F,OAAA;YACEsG,IAAI,EAAC,OAAO;YACZC,KAAK,EAAEnF,KAAM;YACboF,QAAQ,EAAGpC,CAAC,IAAK/C,QAAQ,CAAC+C,CAAC,CAACE,MAAM,CAACiC,KAAK,CAAE;YAC1CE,WAAW,EAAC,kBAAkB;YAC9Bf,KAAK,EAAE5F,MAAM,CAAC4G,SAAU;YACxBC,QAAQ,EAAEzE;UAAU;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNjG,OAAA;UAAK0F,KAAK,EAAE5F,MAAM,CAACuG,cAAe;UAAAT,QAAA,eAChC5F,OAAA;YACEsG,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEjF,QAAS;YAChBkF,QAAQ,EAAGpC,CAAC,IAAK7C,WAAW,CAAC6C,CAAC,CAACE,MAAM,CAACiC,KAAK,CAAE;YAC7CE,WAAW,EAAC,qBAAqB;YACjCf,KAAK,EAAE5F,MAAM,CAAC4G,SAAU;YACxBC,QAAQ,EAAEzE;UAAU;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAGL7D,YAAY,iBACXpC,OAAA;UAAK0F,KAAK,EAAE5F,MAAM,CAACuG,cAAe;UAAAT,QAAA,gBAChC5F,OAAA;YACEsG,IAAI,EAAC,MAAM;YACXE,QAAQ,EAAErC,sBAAuB;YACjCyC,MAAM,EAAC,SAAS;YAChBlB,KAAK,EAAE5F,MAAM,CAAC+G,aAAc;YAC5BF,QAAQ,EAAEzE;UAAU;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,EACDjE,UAAU,iBACThC,OAAA;YACE8G,GAAG,EAAE9E,UAAW;YAChB+E,GAAG,EAAC,iBAAiB;YACrBrB,KAAK,EAAE5F,MAAM,CAACkH;UAAmB;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,eAGDjG,OAAA;UAAK0F,KAAK,EAAE5F,MAAM,CAACmH,eAAgB;UAAArB,QAAA,EAChCxD,YAAY,gBACXpC,OAAA,CAAAE,SAAA;YAAA0F,QAAA,gBACE5F,OAAA;cACEkH,OAAO,EAAErC,YAAa;cACtB8B,QAAQ,EAAEzE,SAAU;cACpBwD,KAAK,EAAE5F,MAAM,CAACqH,iBAAkB;cAAAvB,QAAA,GAE/B1D,SAAS,iBAAIlC,OAAA;gBAAK0F,KAAK,EAAE5F,MAAM,CAACsH;cAAmB;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAC1D/D,SAAS,GAAG,qBAAqB,GAAG,SAAS;YAAA;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACTjG,OAAA;cACEkH,OAAO,EAAEA,CAAA,KAAM7E,eAAe,CAAC,KAAK,CAAE;cACtCsE,QAAQ,EAAEzE,SAAU;cACpBwD,KAAK,EAAE5F,MAAM,CAACuH,mBAAoB;cAAAzB,QAAA,EACnC;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT,CAAC,gBAEHjG,OAAA,CAAAE,SAAA;YAAA0F,QAAA,gBACE5F,OAAA;cACEkH,OAAO,EAAEzB,WAAY;cACrBkB,QAAQ,EAAEzE,SAAU;cACpBwD,KAAK,EAAE5F,MAAM,CAACqH,iBAAkB;cAAAvB,QAAA,GAE/B1D,SAAS,iBAAIlC,OAAA;gBAAK0F,KAAK,EAAE5F,MAAM,CAACsH;cAAmB;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAC1D/D,SAAS,GAAG,eAAe,GAAG,SAAS;YAAA;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACTjG,OAAA;cACEkH,OAAO,EAAEA,CAAA,KAAM7E,eAAe,CAAC,IAAI,CAAE;cACrCsE,QAAQ,EAAEzE,SAAU;cACpBwD,KAAK,EAAE5F,MAAM,CAACuH,mBAAoB;cAAAzB,QAAA,EACnC;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA,eACN,CAAC;MAAA;MAEH;MACAjG,OAAA;QAAK0F,KAAK,EAAE5F,MAAM,CAACwH,gBAAiB;QAAA1B,QAAA,gBAClC5F,OAAA;UAAI0F,KAAK,EAAE5F,MAAM,CAACyH,YAAa;UAAA3B,QAAA,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtDjG,OAAA;UAAG0F,KAAK,EAAE;YAAE8B,KAAK,EAAE1H,MAAM,CAAC2H,YAAY,CAACC,SAAS;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAA/B,QAAA,GAAC,+BAC3C,EAACxE,KAAK;QAAA;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACJjG,OAAA;UACEsG,IAAI,EAAC,MAAM;UACXC,KAAK,EAAE7E,GAAI;UACX8E,QAAQ,EAAGpC,CAAC,IAAKzC,MAAM,CAACyC,CAAC,CAACE,MAAM,CAACiC,KAAK,CAAE;UACxCE,WAAW,EAAC,WAAW;UACvBmB,SAAS,EAAC,GAAG;UACblC,KAAK,EAAE5F,MAAM,CAAC+H;QAAa;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACFjG,OAAA;UACEkH,OAAO,EAAE5B,qBAAsB;UAC/BI,KAAK,EAAE5F,MAAM,CAACgI,aAAc;UAAAlC,QAAA,EAC7B;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGAzE,KAAK,iBACJxB,OAAA;QAAK0F,KAAK,EAAE5F,MAAM,CAACiI,gBAAiB;QAAAnC,QAAA,EACjCpE;MAAK;QAAAsE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9E,EAAA,CAtQIF,QAAQ;AAAA+G,EAAA,GAAR/G,QAAQ;AAwQd,eAAeA,QAAQ;AAAC,IAAA+G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}