{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\quiz\\\\aich (4)\\\\aich (3)\\\\aich(5)\\\\src\\\\AuthPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { initializeApp } from 'firebase/app';\nimport { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword } from 'firebase/auth';\nimport { getFirestore, doc, setDoc } from 'firebase/firestore';\nimport emailjs from 'emailjs-com';\nimport styles from './styles';\n\n// Firebase Config\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst firebaseConfig = {\n  apiKey: \"AIzaSyDmRAxMJvEKmel3JpUF1dXbBNccQJibcGo\",\n  authDomain: \"exploit-f41eb.firebaseapp.com\",\n  projectId: \"exploit-f41eb\",\n  storageBucket: \"exploit-f41eb.firebasestorage.app\",\n  messagingSenderId: \"849491574761\",\n  appId: \"1:849491574761:web:22ddb4ea62063d7a709578\",\n  measurementId: \"G-R0SWYQKXCV\"\n};\nconst app = initializeApp(firebaseConfig);\nconst auth = getAuth(app);\nconst db = getFirestore(app);\n\n// EmailJS Config\nconst EMAILJS_SERVICE_ID = 'service_sthdzci';\nconst EMAILJS_TEMPLATE_ID = 'template_8mnpzqq';\nconst EMAILJS_PUBLIC_KEY = 'jEfQd05ItbAthM3QN';\nconst AuthPage = ({\n  onAuthSuccess\n}) => {\n  _s();\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState('');\n  const [otp, setOtp] = useState('');\n  const [generatedOtp, setGeneratedOtp] = useState('');\n  const [isOtpSent, setIsOtpSent] = useState(false);\n  const [profilePic, setProfilePic] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isSignupMode, setIsSignupMode] = useState(true);\n  const [showPassword, setShowPassword] = useState(false);\n  const [particles, setParticles] = useState([]);\n  const [mousePosition, setMousePosition] = useState({\n    x: 0,\n    y: 0\n  });\n  const [showCelebration, setShowCelebration] = useState(false);\n\n  // Create floating particles effect\n  useEffect(() => {\n    const createParticles = () => {\n      const newParticles = [];\n      for (let i = 0; i < 15; i++) {\n        newParticles.push({\n          id: i,\n          x: Math.random() * 100,\n          y: Math.random() * 100,\n          size: Math.random() * 4 + 2,\n          speed: Math.random() * 2 + 1,\n          opacity: Math.random() * 0.5 + 0.2\n        });\n      }\n      setParticles(newParticles);\n    };\n    createParticles();\n\n    // Mouse tracking for interactive effects\n    const handleMouseMove = e => {\n      setMousePosition({\n        x: e.clientX / window.innerWidth * 100,\n        y: e.clientY / window.innerHeight * 100\n      });\n    };\n    window.addEventListener('mousemove', handleMouseMove);\n    return () => window.removeEventListener('mousemove', handleMouseMove);\n  }, []);\n  const generateOtp = () => {\n    const otp = Math.floor(100000 + Math.random() * 900000); // Generate a 6-digit OTP\n    setGeneratedOtp(otp);\n    return otp;\n  };\n  const sendOtpEmail = (email, otp) => {\n    const templateParams = {\n      to_email: email,\n      otp: otp\n    };\n    emailjs.send(EMAILJS_SERVICE_ID, EMAILJS_TEMPLATE_ID, templateParams, EMAILJS_PUBLIC_KEY).then(response => {\n      console.log('OTP sent!', response.status, response.text);\n    }, err => {\n      console.error('OTP sending failed:', err);\n    });\n  };\n  const handleProfilePicChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setProfilePic(reader.result); // Store image in base64\n      };\n      reader.readAsDataURL(file); // Convert file to base64\n    }\n  };\n  const handleSignup = async () => {\n    if (!email || !password) {\n      setError('Please fill in all fields');\n      return;\n    }\n    setIsLoading(true);\n    setError('');\n    try {\n      const userCredential = await createUserWithEmailAndPassword(auth, email, password);\n      const otp = generateOtp();\n      sendOtpEmail(email, otp);\n      setIsOtpSent(true);\n\n      // Save user data to Firestore\n      const userRef = doc(db, \"users\", userCredential.user.uid);\n      await setDoc(userRef, {\n        email: email,\n        uid: userCredential.user.uid,\n        createdAt: new Date(),\n        dp: profilePic\n      });\n    } catch (error) {\n      setError(error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleOtpVerification = () => {\n    if (otp === generatedOtp.toString()) {\n      setShowCelebration(true);\n      setTimeout(() => {\n        setIsOtpSent(false);\n        setOtp('');\n        setIsSignupMode(false);\n        setError('');\n        setShowCelebration(false);\n        alert('🎉 OTP Verified! You can now login with your credentials.');\n      }, 2000);\n    } else {\n      setError('Invalid OTP. Please try again.');\n    }\n  };\n  const handleLogin = async () => {\n    if (!email || !password) {\n      setError('Please fill in all fields');\n      return;\n    }\n    setIsLoading(true);\n    setError('');\n    try {\n      const userCredential = await signInWithEmailAndPassword(auth, email, password);\n      onAuthSuccess();\n    } catch (error) {\n      setError(error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      ...styles.authPageContainer,\n      background: `\n        linear-gradient(135deg, ${styles.currentTheme.primary}15 0%, ${styles.currentTheme.accent}10 25%, ${styles.currentTheme.primaryLight}20 50%, ${styles.currentTheme.secondary}30 75%, ${styles.currentTheme.primary}10 100%),\n        radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, ${styles.currentTheme.primary}20 0%, transparent 50%),\n        radial-gradient(circle at 80% 20%, ${styles.currentTheme.accent}20 0%, transparent 50%),\n        radial-gradient(circle at 40% 40%, ${styles.currentTheme.primaryLight}15 0%, transparent 50%)\n      `\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...styles.authBackgroundAnimation,\n        transform: `translate(${mousePosition.x * 0.02}px, ${mousePosition.y * 0.02}px)`\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), particles.map(particle => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        left: `${particle.x}%`,\n        top: `${particle.y}%`,\n        width: `${particle.size}px`,\n        height: `${particle.size}px`,\n        background: styles.currentTheme.primary,\n        borderRadius: '50%',\n        opacity: particle.opacity,\n        animation: `floatParticle${particle.id % 2 + 1} ${12 + particle.speed * 3}s infinite linear`,\n        zIndex: 1,\n        pointerEvents: 'none'\n      }\n    }, particle.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 9\n    }, this)), showCelebration && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        pointerEvents: 'none',\n        zIndex: 1000\n      },\n      children: [...Array(20)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          left: `${Math.random() * 100}%`,\n          top: `${Math.random() * 100}%`,\n          fontSize: '2rem',\n          animation: `confetti ${2 + Math.random() * 2}s ease-out forwards`,\n          animationDelay: `${Math.random() * 0.5}s`\n        },\n        children: ['🎉', '✨', '🎊', '🌟', '💫'][Math.floor(Math.random() * 5)]\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.authBrandingSection,\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: require('./eduai-logo.jpg'),\n        alt: \"EduNova Logo\",\n        style: styles.authBrandingLogo\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        style: styles.authBrandingTitle,\n        children: \"EDU NOVA\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: styles.authBrandingSubtitle,\n        children: \"AI POWERED LEARNING SYSTEM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.authBrandingFeatures,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.authBrandingFeature,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83C\\uDFAF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Personalized Learning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.authBrandingFeature,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83D\\uDE80\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"AI-Powered Assistance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.authBrandingFeature,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83D\\uDCCA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Progress Tracking\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.authBrandingFeature,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83C\\uDF93\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Expert Content\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.authWelcomeSection,\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: styles.authWelcomeTitle,\n        children: \"\\uD83C\\uDF1F Why Join EduNova?\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: styles.authWelcomeText,\n        children: \"Transform your learning journey with our cutting-edge AI platform\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.authBenefitsList,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.authBenefitItem,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u2728\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Smart Study Plans\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.authBenefitItem,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83D\\uDCA1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Instant Doubt Resolution\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.authBenefitItem,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83C\\uDFC6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Achievement Tracking\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.authBenefitItem,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83C\\uDF10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"24/7 Learning Support\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.authFormContainer,\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        style: styles.authFormTitle,\n        children: isSignupMode ? '✨ Create Account' : '🎯 Welcome Back'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: styles.authFormSubtitle,\n        children: isSignupMode ? '🚀 Join our learning platform and start your amazing journey' : '💫 Sign in to continue your learning adventure'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this), isSignupMode && !isOtpSent && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'center',\n          marginBottom: '2rem',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            background: email && password ? styles.currentTheme.primary : styles.currentTheme.border,\n            transition: 'all 0.3s ease',\n            animation: email && password ? 'pulse 2s infinite' : 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            background: profilePic ? styles.currentTheme.primary : styles.currentTheme.border,\n            transition: 'all 0.3s ease',\n            animation: profilePic ? 'pulse 2s infinite' : 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            background: styles.currentTheme.border,\n            transition: 'all 0.3s ease'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 11\n      }, this), !isOtpSent ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.authInputGroup,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'relative'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                position: 'absolute',\n                left: '1.25rem',\n                top: '50%',\n                transform: 'translateY(-50%)',\n                fontSize: '1.2rem',\n                color: styles.currentTheme.textLight,\n                zIndex: 1\n              },\n              children: \"\\uD83D\\uDCE7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              value: email,\n              onChange: e => setEmail(e.target.value),\n              placeholder: \"Enter your email address\",\n              style: {\n                ...styles.authInput,\n                paddingLeft: '3.5rem'\n              },\n              disabled: isLoading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.authInputGroup,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'relative'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                position: 'absolute',\n                left: '1.25rem',\n                top: '50%',\n                transform: 'translateY(-50%)',\n                fontSize: '1.2rem',\n                color: styles.currentTheme.textLight,\n                zIndex: 1\n              },\n              children: \"\\uD83D\\uDD12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: showPassword ? 'text' : 'password',\n              value: password,\n              onChange: e => setPassword(e.target.value),\n              placeholder: \"Enter your password\",\n              style: {\n                ...styles.authInput,\n                paddingLeft: '3.5rem',\n                paddingRight: '3.5rem'\n              },\n              disabled: isLoading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => setShowPassword(!showPassword),\n              style: {\n                position: 'absolute',\n                right: '1.25rem',\n                top: '50%',\n                transform: 'translateY(-50%)',\n                background: 'none',\n                border: 'none',\n                fontSize: '1.2rem',\n                cursor: 'pointer',\n                color: styles.currentTheme.textLight,\n                zIndex: 1\n              },\n              disabled: isLoading,\n              children: showPassword ? '🙈' : '👁️'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 13\n        }, this), isSignupMode && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.authInputGroup,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                padding: '1.5rem',\n                border: `2px dashed ${styles.currentTheme.primary}`,\n                borderRadius: '16px',\n                background: 'rgba(255, 255, 255, 0.5)',\n                cursor: 'pointer',\n                transition: 'all 0.3s ease',\n                ':hover': {\n                  background: 'rgba(255, 255, 255, 0.8)',\n                  transform: 'translateY(-2px)'\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '2rem',\n                  display: 'block',\n                  marginBottom: '0.5rem'\n                },\n                children: \"\\uD83D\\uDCF8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: styles.currentTheme.textLight,\n                  fontSize: '0.9rem'\n                },\n                children: profilePic ? 'Change Profile Picture' : 'Upload Profile Picture'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                onChange: handleProfilePicChange,\n                accept: \"image/*\",\n                style: {\n                  display: 'none'\n                },\n                disabled: isLoading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 19\n            }, this), profilePic && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '1rem',\n                position: 'relative',\n                display: 'inline-block'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: profilePic,\n                alt: \"Profile Preview\",\n                style: {\n                  ...styles.authProfilePreview,\n                  animation: 'fadeIn 0.5s ease'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: '-5px',\n                  right: '-5px',\n                  background: styles.currentTheme.primary,\n                  color: 'white',\n                  borderRadius: '50%',\n                  width: '24px',\n                  height: '24px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  fontSize: '0.8rem',\n                  animation: 'pulse 2s infinite'\n                },\n                children: \"\\u2713\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.authButtonGroup,\n          children: isSignupMode ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSignup,\n              disabled: isLoading,\n              style: styles.authButtonPrimary,\n              children: [isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.authLoadingSpinner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 35\n              }, this), isLoading ? '🚀 Creating Magic...' : '✨ Create Account']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsSignupMode(false),\n              disabled: isLoading,\n              style: styles.authButtonSecondary,\n              children: \"\\uD83D\\uDD11 Login Instead\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleLogin,\n              disabled: isLoading,\n              style: styles.authButtonPrimary,\n              children: [isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.authLoadingSpinner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 35\n              }, this), isLoading ? '🎯 Signing In...' : '🚀 Sign In']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsSignupMode(true),\n              disabled: isLoading,\n              style: styles.authButtonSecondary,\n              children: \"\\u2728 Create Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true) :\n      /*#__PURE__*/\n      /* OTP Verification Section */\n      _jsxDEV(\"div\", {\n        style: styles.authOtpContainer,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '3rem',\n              marginBottom: '1rem',\n              animation: 'pulse 2s infinite'\n            },\n            children: \"\\uD83D\\uDCE7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            style: styles.authOtpTitle,\n            children: \"\\uD83D\\uDD10 Verify Your Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: styles.currentTheme.textLight,\n              marginBottom: '1rem'\n            },\n            children: [\"\\u2728 We've sent a magical 6-digit code to\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 55\n            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n              style: {\n                color: styles.currentTheme.primary\n              },\n              children: email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 522,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              position: 'absolute',\n              left: '1rem',\n              top: '50%',\n              transform: 'translateY(-50%)',\n              fontSize: '1.2rem',\n              color: styles.currentTheme.textLight,\n              zIndex: 1\n            },\n            children: \"\\uD83D\\uDD22\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: otp,\n            onChange: e => setOtp(e.target.value),\n            placeholder: \"Enter 6-digit code\",\n            maxLength: \"6\",\n            style: {\n              ...styles.authOtpInput,\n              paddingLeft: '3rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleOtpVerification,\n          style: {\n            ...styles.authOtpButton,\n            background: styles.currentTheme.gradient,\n            animation: otp.length === 6 ? 'buttonPulse 2s infinite' : 'none'\n          },\n          children: \"\\uD83C\\uDFAF Verify & Continue\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 560,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 521,\n        columnNumber: 11\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          ...styles.authErrorMessage,\n          animation: 'slideIn 0.3s ease'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            marginRight: '0.5rem'\n          },\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 579,\n          columnNumber: 13\n        }, this), error]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 575,\n        columnNumber: 11\n      }, this), email && password && !error && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginTop: '1rem',\n          color: styles.currentTheme.primary,\n          fontSize: '0.9rem',\n          animation: 'fadeIn 0.5s ease'\n        },\n        children: [\"\\u2705 Ready to \", isSignupMode ? 'create your account' : 'sign in', \"!\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 586,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 171,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthPage, \"io/4I3IjXRUi7X06aARPxDqYafI=\");\n_c = AuthPage;\nexport default AuthPage;\nvar _c;\n$RefreshReg$(_c, \"AuthPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "initializeApp", "getAuth", "createUserWithEmailAndPassword", "signInWithEmailAndPassword", "getFirestore", "doc", "setDoc", "emailjs", "styles", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "measurementId", "app", "auth", "db", "EMAILJS_SERVICE_ID", "EMAILJS_TEMPLATE_ID", "EMAILJS_PUBLIC_KEY", "AuthPage", "onAuthSuccess", "_s", "email", "setEmail", "password", "setPassword", "error", "setError", "otp", "setOtp", "generatedOtp", "setGeneratedOtp", "isOtpSent", "setIsOtpSent", "profilePic", "setProfilePic", "isLoading", "setIsLoading", "isSignupMode", "setIsSignupMode", "showPassword", "setShowPassword", "particles", "setParticles", "mousePosition", "setMousePosition", "x", "y", "showCelebration", "setShowCelebration", "createParticles", "newParticles", "i", "push", "id", "Math", "random", "size", "speed", "opacity", "handleMouseMove", "e", "clientX", "window", "innerWidth", "clientY", "innerHeight", "addEventListener", "removeEventListener", "generateOtp", "floor", "sendOtpEmail", "templateParams", "to_email", "send", "then", "response", "console", "log", "status", "text", "err", "handleProfilePicChange", "file", "target", "files", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "handleSignup", "userCredential", "userRef", "user", "uid", "createdAt", "Date", "dp", "message", "handleOtpVerification", "toString", "setTimeout", "alert", "handleLogin", "style", "authPageContainer", "background", "currentTheme", "primary", "accent", "primaryLight", "secondary", "children", "authBackgroundAnimation", "transform", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "particle", "position", "left", "top", "width", "height", "borderRadius", "animation", "zIndex", "pointerEvents", "right", "bottom", "Array", "_", "fontSize", "animationDelay", "authBrandingSection", "src", "require", "alt", "auth<PERSON><PERSON><PERSON><PERSON><PERSON>", "authBrandingTitle", "authBrandingSubtitle", "authBrandingFeatures", "authBrandingFeature", "authWelcomeSection", "authWelcomeTitle", "authWelcomeText", "authBenefitsList", "authBenefitItem", "authFormContainer", "authFormTitle", "authFormSubtitle", "display", "justifyContent", "marginBottom", "gap", "border", "transition", "authInputGroup", "color", "textLight", "type", "value", "onChange", "placeholder", "authInput", "paddingLeft", "disabled", "paddingRight", "onClick", "cursor", "textAlign", "padding", "accept", "marginTop", "authProfilePreview", "alignItems", "authButtonGroup", "authButtonPrimary", "authLoading<PERSON><PERSON>ner", "authButtonSecondary", "authOtpContainer", "authOtpTitle", "max<PERSON><PERSON><PERSON>", "authOtpInput", "authOtpButton", "gradient", "length", "authErrorMessage", "marginRight", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(5)/src/AuthPage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { initializeApp } from 'firebase/app';\nimport { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword } from 'firebase/auth';\nimport { getFirestore, doc, setDoc } from 'firebase/firestore';\nimport emailjs from 'emailjs-com';\nimport styles from './styles';\n\n// Firebase Config\nconst firebaseConfig = {\n  apiKey: \"AIzaSyDmRAxMJvEKmel3JpUF1dXbBNccQJibcGo\",\n  authDomain: \"exploit-f41eb.firebaseapp.com\",\n  projectId: \"exploit-f41eb\",\n  storageBucket: \"exploit-f41eb.firebasestorage.app\",\n  messagingSenderId: \"849491574761\",\n  appId: \"1:849491574761:web:22ddb4ea62063d7a709578\",\n  measurementId: \"G-R0SWYQKXCV\"\n};\n\nconst app = initializeApp(firebaseConfig);\nconst auth = getAuth(app);\nconst db = getFirestore(app);\n\n// EmailJS Config\nconst EMAILJS_SERVICE_ID = 'service_sthdzci';\nconst EMAILJS_TEMPLATE_ID = 'template_8mnpzqq';\nconst EMAILJS_PUBLIC_KEY = 'jEfQd05ItbAthM3QN';\n\nconst AuthPage = ({ onAuthSuccess }) => {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState('');\n  const [otp, setOtp] = useState('');\n  const [generatedOtp, setGeneratedOtp] = useState('');\n  const [isOtpSent, setIsOtpSent] = useState(false);\n  const [profilePic, setProfilePic] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isSignupMode, setIsSignupMode] = useState(true);\n  const [showPassword, setShowPassword] = useState(false);\n  const [particles, setParticles] = useState([]);\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n  const [showCelebration, setShowCelebration] = useState(false);\n\n  // Create floating particles effect\n  useEffect(() => {\n    const createParticles = () => {\n      const newParticles = [];\n      for (let i = 0; i < 15; i++) {\n        newParticles.push({\n          id: i,\n          x: Math.random() * 100,\n          y: Math.random() * 100,\n          size: Math.random() * 4 + 2,\n          speed: Math.random() * 2 + 1,\n          opacity: Math.random() * 0.5 + 0.2,\n        });\n      }\n      setParticles(newParticles);\n    };\n    createParticles();\n\n    // Mouse tracking for interactive effects\n    const handleMouseMove = (e) => {\n      setMousePosition({\n        x: (e.clientX / window.innerWidth) * 100,\n        y: (e.clientY / window.innerHeight) * 100,\n      });\n    };\n\n    window.addEventListener('mousemove', handleMouseMove);\n    return () => window.removeEventListener('mousemove', handleMouseMove);\n  }, []);\n\n  const generateOtp = () => {\n    const otp = Math.floor(100000 + Math.random() * 900000); // Generate a 6-digit OTP\n    setGeneratedOtp(otp);\n    return otp;\n  };\n\n  const sendOtpEmail = (email, otp) => {\n    const templateParams = {\n      to_email: email,\n      otp: otp\n    };\n\n    emailjs.send(EMAILJS_SERVICE_ID, EMAILJS_TEMPLATE_ID, templateParams, EMAILJS_PUBLIC_KEY)\n      .then((response) => {\n        console.log('OTP sent!', response.status, response.text);\n      }, (err) => {\n        console.error('OTP sending failed:', err);\n      });\n  };\n\n  const handleProfilePicChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setProfilePic(reader.result); // Store image in base64\n      };\n      reader.readAsDataURL(file); // Convert file to base64\n    }\n  };\n\n  const handleSignup = async () => {\n    if (!email || !password) {\n      setError('Please fill in all fields');\n      return;\n    }\n\n    setIsLoading(true);\n    setError('');\n\n    try {\n      const userCredential = await createUserWithEmailAndPassword(auth, email, password);\n      const otp = generateOtp();\n      sendOtpEmail(email, otp);\n      setIsOtpSent(true);\n\n      // Save user data to Firestore\n      const userRef = doc(db, \"users\", userCredential.user.uid);\n      await setDoc(userRef, {\n        email: email,\n        uid: userCredential.user.uid,\n        createdAt: new Date(),\n        dp: profilePic,\n      });\n\n    } catch (error) {\n      setError(error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleOtpVerification = () => {\n    if (otp === generatedOtp.toString()) {\n      setShowCelebration(true);\n      setTimeout(() => {\n        setIsOtpSent(false);\n        setOtp('');\n        setIsSignupMode(false);\n        setError('');\n        setShowCelebration(false);\n        alert('🎉 OTP Verified! You can now login with your credentials.');\n      }, 2000);\n    } else {\n      setError('Invalid OTP. Please try again.');\n    }\n  };\n\n  const handleLogin = async () => {\n    if (!email || !password) {\n      setError('Please fill in all fields');\n      return;\n    }\n\n    setIsLoading(true);\n    setError('');\n\n    try {\n      const userCredential = await signInWithEmailAndPassword(auth, email, password);\n      onAuthSuccess();\n    } catch (error) {\n      setError(error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div style={{\n      ...styles.authPageContainer,\n      background: `\n        linear-gradient(135deg, ${styles.currentTheme.primary}15 0%, ${styles.currentTheme.accent}10 25%, ${styles.currentTheme.primaryLight}20 50%, ${styles.currentTheme.secondary}30 75%, ${styles.currentTheme.primary}10 100%),\n        radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, ${styles.currentTheme.primary}20 0%, transparent 50%),\n        radial-gradient(circle at 80% 20%, ${styles.currentTheme.accent}20 0%, transparent 50%),\n        radial-gradient(circle at 40% 40%, ${styles.currentTheme.primaryLight}15 0%, transparent 50%)\n      `,\n    }}>\n      {/* Animated Background */}\n      <div style={{\n        ...styles.authBackgroundAnimation,\n        transform: `translate(${mousePosition.x * 0.02}px, ${mousePosition.y * 0.02}px)`,\n      }}></div>\n\n      {/* Floating Particles */}\n      {particles.map((particle) => (\n        <div\n          key={particle.id}\n          style={{\n            position: 'absolute',\n            left: `${particle.x}%`,\n            top: `${particle.y}%`,\n            width: `${particle.size}px`,\n            height: `${particle.size}px`,\n            background: styles.currentTheme.primary,\n            borderRadius: '50%',\n            opacity: particle.opacity,\n            animation: `floatParticle${(particle.id % 2) + 1} ${12 + particle.speed * 3}s infinite linear`,\n            zIndex: 1,\n            pointerEvents: 'none',\n          }}\n        />\n      ))}\n\n      {/* Celebration Effect */}\n      {showCelebration && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          pointerEvents: 'none',\n          zIndex: 1000,\n        }}>\n          {[...Array(20)].map((_, i) => (\n            <div\n              key={i}\n              style={{\n                position: 'absolute',\n                left: `${Math.random() * 100}%`,\n                top: `${Math.random() * 100}%`,\n                fontSize: '2rem',\n                animation: `confetti ${2 + Math.random() * 2}s ease-out forwards`,\n                animationDelay: `${Math.random() * 0.5}s`,\n              }}\n            >\n              {['🎉', '✨', '🎊', '🌟', '💫'][Math.floor(Math.random() * 5)]}\n            </div>\n          ))}\n        </div>\n      )}\n\n      {/* Branding Section */}\n      <div style={styles.authBrandingSection}>\n        <img\n          src={require('./eduai-logo.jpg')}\n          alt=\"EduNova Logo\"\n          style={styles.authBrandingLogo}\n        />\n        <h2 style={styles.authBrandingTitle}>EDU NOVA</h2>\n        <p style={styles.authBrandingSubtitle}>AI POWERED LEARNING SYSTEM</p>\n        <div style={styles.authBrandingFeatures}>\n          <div style={styles.authBrandingFeature}>\n            <span>🎯</span>\n            <span>Personalized Learning</span>\n          </div>\n          <div style={styles.authBrandingFeature}>\n            <span>🚀</span>\n            <span>AI-Powered Assistance</span>\n          </div>\n          <div style={styles.authBrandingFeature}>\n            <span>📊</span>\n            <span>Progress Tracking</span>\n          </div>\n          <div style={styles.authBrandingFeature}>\n            <span>🎓</span>\n            <span>Expert Content</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Welcome Section */}\n      <div style={styles.authWelcomeSection}>\n        <h3 style={styles.authWelcomeTitle}>🌟 Why Join EduNova?</h3>\n        <p style={styles.authWelcomeText}>\n          Transform your learning journey with our cutting-edge AI platform\n        </p>\n        <div style={styles.authBenefitsList}>\n          <div style={styles.authBenefitItem}>\n            <span>✨</span>\n            <span>Smart Study Plans</span>\n          </div>\n          <div style={styles.authBenefitItem}>\n            <span>💡</span>\n            <span>Instant Doubt Resolution</span>\n          </div>\n          <div style={styles.authBenefitItem}>\n            <span>🏆</span>\n            <span>Achievement Tracking</span>\n          </div>\n          <div style={styles.authBenefitItem}>\n            <span>🌐</span>\n            <span>24/7 Learning Support</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Form Container */}\n      <div style={styles.authFormContainer}>\n        <h1 style={styles.authFormTitle}>\n          {isSignupMode ? '✨ Create Account' : '🎯 Welcome Back'}\n        </h1>\n        <p style={styles.authFormSubtitle}>\n          {isSignupMode\n            ? '🚀 Join our learning platform and start your amazing journey'\n            : '💫 Sign in to continue your learning adventure'\n          }\n        </p>\n\n        {/* Progress Indicator */}\n        {isSignupMode && !isOtpSent && (\n          <div style={{\n            display: 'flex',\n            justifyContent: 'center',\n            marginBottom: '2rem',\n            gap: '0.5rem',\n          }}>\n            <div style={{\n              width: '12px',\n              height: '12px',\n              borderRadius: '50%',\n              background: email && password ? styles.currentTheme.primary : styles.currentTheme.border,\n              transition: 'all 0.3s ease',\n              animation: email && password ? 'pulse 2s infinite' : 'none',\n            }}></div>\n            <div style={{\n              width: '12px',\n              height: '12px',\n              borderRadius: '50%',\n              background: profilePic ? styles.currentTheme.primary : styles.currentTheme.border,\n              transition: 'all 0.3s ease',\n              animation: profilePic ? 'pulse 2s infinite' : 'none',\n            }}></div>\n            <div style={{\n              width: '12px',\n              height: '12px',\n              borderRadius: '50%',\n              background: styles.currentTheme.border,\n              transition: 'all 0.3s ease',\n            }}></div>\n          </div>\n        )}\n\n        {!isOtpSent ? (\n          <>\n            {/* Email Input */}\n            <div style={styles.authInputGroup}>\n              <div style={{ position: 'relative' }}>\n                <span style={{\n                  position: 'absolute',\n                  left: '1.25rem',\n                  top: '50%',\n                  transform: 'translateY(-50%)',\n                  fontSize: '1.2rem',\n                  color: styles.currentTheme.textLight,\n                  zIndex: 1,\n                }}>\n                  📧\n                </span>\n                <input\n                  type=\"email\"\n                  value={email}\n                  onChange={(e) => setEmail(e.target.value)}\n                  placeholder=\"Enter your email address\"\n                  style={{\n                    ...styles.authInput,\n                    paddingLeft: '3.5rem',\n                  }}\n                  disabled={isLoading}\n                />\n              </div>\n            </div>\n\n            {/* Password Input */}\n            <div style={styles.authInputGroup}>\n              <div style={{ position: 'relative' }}>\n                <span style={{\n                  position: 'absolute',\n                  left: '1.25rem',\n                  top: '50%',\n                  transform: 'translateY(-50%)',\n                  fontSize: '1.2rem',\n                  color: styles.currentTheme.textLight,\n                  zIndex: 1,\n                }}>\n                  🔒\n                </span>\n                <input\n                  type={showPassword ? 'text' : 'password'}\n                  value={password}\n                  onChange={(e) => setPassword(e.target.value)}\n                  placeholder=\"Enter your password\"\n                  style={{\n                    ...styles.authInput,\n                    paddingLeft: '3.5rem',\n                    paddingRight: '3.5rem',\n                  }}\n                  disabled={isLoading}\n                />\n                <button\n                  type=\"button\"\n                  onClick={() => setShowPassword(!showPassword)}\n                  style={{\n                    position: 'absolute',\n                    right: '1.25rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    background: 'none',\n                    border: 'none',\n                    fontSize: '1.2rem',\n                    cursor: 'pointer',\n                    color: styles.currentTheme.textLight,\n                    zIndex: 1,\n                  }}\n                  disabled={isLoading}\n                >\n                  {showPassword ? '🙈' : '👁️'}\n                </button>\n              </div>\n            </div>\n\n            {/* Profile Picture Upload (only for signup) */}\n            {isSignupMode && (\n              <div style={styles.authInputGroup}>\n                <div style={{ textAlign: 'center' }}>\n                  <label style={{\n                    display: 'block',\n                    padding: '1.5rem',\n                    border: `2px dashed ${styles.currentTheme.primary}`,\n                    borderRadius: '16px',\n                    background: 'rgba(255, 255, 255, 0.5)',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    ':hover': {\n                      background: 'rgba(255, 255, 255, 0.8)',\n                      transform: 'translateY(-2px)',\n                    }\n                  }}>\n                    <span style={{ fontSize: '2rem', display: 'block', marginBottom: '0.5rem' }}>\n                      📸\n                    </span>\n                    <span style={{ color: styles.currentTheme.textLight, fontSize: '0.9rem' }}>\n                      {profilePic ? 'Change Profile Picture' : 'Upload Profile Picture'}\n                    </span>\n                    <input\n                      type=\"file\"\n                      onChange={handleProfilePicChange}\n                      accept=\"image/*\"\n                      style={{ display: 'none' }}\n                      disabled={isLoading}\n                    />\n                  </label>\n                  {profilePic && (\n                    <div style={{ marginTop: '1rem', position: 'relative', display: 'inline-block' }}>\n                      <img\n                        src={profilePic}\n                        alt=\"Profile Preview\"\n                        style={{\n                          ...styles.authProfilePreview,\n                          animation: 'fadeIn 0.5s ease',\n                        }}\n                      />\n                      <div style={{\n                        position: 'absolute',\n                        top: '-5px',\n                        right: '-5px',\n                        background: styles.currentTheme.primary,\n                        color: 'white',\n                        borderRadius: '50%',\n                        width: '24px',\n                        height: '24px',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        fontSize: '0.8rem',\n                        animation: 'pulse 2s infinite',\n                      }}>\n                        ✓\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            )}\n\n            {/* Action Buttons */}\n            <div style={styles.authButtonGroup}>\n              {isSignupMode ? (\n                <>\n                  <button\n                    onClick={handleSignup}\n                    disabled={isLoading}\n                    style={styles.authButtonPrimary}\n                  >\n                    {isLoading && <div style={styles.authLoadingSpinner}></div>}\n                    {isLoading ? '🚀 Creating Magic...' : '✨ Create Account'}\n                  </button>\n                  <button\n                    onClick={() => setIsSignupMode(false)}\n                    disabled={isLoading}\n                    style={styles.authButtonSecondary}\n                  >\n                    🔑 Login Instead\n                  </button>\n                </>\n              ) : (\n                <>\n                  <button\n                    onClick={handleLogin}\n                    disabled={isLoading}\n                    style={styles.authButtonPrimary}\n                  >\n                    {isLoading && <div style={styles.authLoadingSpinner}></div>}\n                    {isLoading ? '🎯 Signing In...' : '🚀 Sign In'}\n                  </button>\n                  <button\n                    onClick={() => setIsSignupMode(true)}\n                    disabled={isLoading}\n                    style={styles.authButtonSecondary}\n                  >\n                    ✨ Create Account\n                  </button>\n                </>\n              )}\n            </div>\n          </>\n        ) : (\n          /* OTP Verification Section */\n          <div style={styles.authOtpContainer}>\n            <div style={{ textAlign: 'center', marginBottom: '1.5rem' }}>\n              <div style={{\n                fontSize: '3rem',\n                marginBottom: '1rem',\n                animation: 'pulse 2s infinite',\n              }}>\n                📧\n              </div>\n              <h3 style={styles.authOtpTitle}>🔐 Verify Your Email</h3>\n              <p style={{ color: styles.currentTheme.textLight, marginBottom: '1rem' }}>\n                ✨ We've sent a magical 6-digit code to<br />\n                <strong style={{ color: styles.currentTheme.primary }}>{email}</strong>\n              </p>\n            </div>\n            <div style={{ position: 'relative' }}>\n              <span style={{\n                position: 'absolute',\n                left: '1rem',\n                top: '50%',\n                transform: 'translateY(-50%)',\n                fontSize: '1.2rem',\n                color: styles.currentTheme.textLight,\n                zIndex: 1,\n              }}>\n                🔢\n              </span>\n              <input\n                type=\"text\"\n                value={otp}\n                onChange={(e) => setOtp(e.target.value)}\n                placeholder=\"Enter 6-digit code\"\n                maxLength=\"6\"\n                style={{\n                  ...styles.authOtpInput,\n                  paddingLeft: '3rem',\n                }}\n              />\n            </div>\n            <button\n              onClick={handleOtpVerification}\n              style={{\n                ...styles.authOtpButton,\n                background: styles.currentTheme.gradient,\n                animation: otp.length === 6 ? 'buttonPulse 2s infinite' : 'none',\n              }}\n            >\n              🎯 Verify & Continue\n            </button>\n          </div>\n        )}\n\n        {/* Error Message */}\n        {error && (\n          <div style={{\n            ...styles.authErrorMessage,\n            animation: 'slideIn 0.3s ease',\n          }}>\n            <span style={{ marginRight: '0.5rem' }}>⚠️</span>\n            {error}\n          </div>\n        )}\n\n        {/* Success Indicators */}\n        {email && password && !error && (\n          <div style={{\n            textAlign: 'center',\n            marginTop: '1rem',\n            color: styles.currentTheme.primary,\n            fontSize: '0.9rem',\n            animation: 'fadeIn 0.5s ease',\n          }}>\n            ✅ Ready to {isSignupMode ? 'create your account' : 'sign in'}!\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default AuthPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,QAAQ,cAAc;AAC5C,SAASC,OAAO,EAAEC,8BAA8B,EAAEC,0BAA0B,QAAQ,eAAe;AACnG,SAASC,YAAY,EAAEC,GAAG,EAAEC,MAAM,QAAQ,oBAAoB;AAC9D,OAAOC,OAAO,MAAM,aAAa;AACjC,OAAOC,MAAM,MAAM,UAAU;;AAE7B;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAc,GAAG;EACrBC,MAAM,EAAE,yCAAyC;EACjDC,UAAU,EAAE,+BAA+B;EAC3CC,SAAS,EAAE,eAAe;EAC1BC,aAAa,EAAE,mCAAmC;EAClDC,iBAAiB,EAAE,cAAc;EACjCC,KAAK,EAAE,2CAA2C;EAClDC,aAAa,EAAE;AACjB,CAAC;AAED,MAAMC,GAAG,GAAGrB,aAAa,CAACa,cAAc,CAAC;AACzC,MAAMS,IAAI,GAAGrB,OAAO,CAACoB,GAAG,CAAC;AACzB,MAAME,EAAE,GAAGnB,YAAY,CAACiB,GAAG,CAAC;;AAE5B;AACA,MAAMG,kBAAkB,GAAG,iBAAiB;AAC5C,MAAMC,mBAAmB,GAAG,kBAAkB;AAC9C,MAAMC,kBAAkB,GAAG,mBAAmB;AAE9C,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EACtC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkC,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoC,KAAK,EAAEC,QAAQ,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsC,GAAG,EAAEC,MAAM,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC0C,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC8C,SAAS,EAAEC,YAAY,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoD,SAAS,EAAEC,YAAY,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACsD,aAAa,EAAEC,gBAAgB,CAAC,GAAGvD,QAAQ,CAAC;IAAEwD,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAClE,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACAC,SAAS,CAAC,MAAM;IACd,MAAM2D,eAAe,GAAGA,CAAA,KAAM;MAC5B,MAAMC,YAAY,GAAG,EAAE;MACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3BD,YAAY,CAACE,IAAI,CAAC;UAChBC,EAAE,EAAEF,CAAC;UACLN,CAAC,EAAES,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACtBT,CAAC,EAAEQ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACtBC,IAAI,EAAEF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;UAC3BE,KAAK,EAAEH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;UAC5BG,OAAO,EAAEJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;QACjC,CAAC,CAAC;MACJ;MACAb,YAAY,CAACQ,YAAY,CAAC;IAC5B,CAAC;IACDD,eAAe,CAAC,CAAC;;IAEjB;IACA,MAAMU,eAAe,GAAIC,CAAC,IAAK;MAC7BhB,gBAAgB,CAAC;QACfC,CAAC,EAAGe,CAAC,CAACC,OAAO,GAAGC,MAAM,CAACC,UAAU,GAAI,GAAG;QACxCjB,CAAC,EAAGc,CAAC,CAACI,OAAO,GAAGF,MAAM,CAACG,WAAW,GAAI;MACxC,CAAC,CAAC;IACJ,CAAC;IAEDH,MAAM,CAACI,gBAAgB,CAAC,WAAW,EAAEP,eAAe,CAAC;IACrD,OAAO,MAAMG,MAAM,CAACK,mBAAmB,CAAC,WAAW,EAAER,eAAe,CAAC;EACvE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMS,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMzC,GAAG,GAAG2B,IAAI,CAACe,KAAK,CAAC,MAAM,GAAGf,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;IACzDzB,eAAe,CAACH,GAAG,CAAC;IACpB,OAAOA,GAAG;EACZ,CAAC;EAED,MAAM2C,YAAY,GAAGA,CAACjD,KAAK,EAAEM,GAAG,KAAK;IACnC,MAAM4C,cAAc,GAAG;MACrBC,QAAQ,EAAEnD,KAAK;MACfM,GAAG,EAAEA;IACP,CAAC;IAED7B,OAAO,CAAC2E,IAAI,CAAC1D,kBAAkB,EAAEC,mBAAmB,EAAEuD,cAAc,EAAEtD,kBAAkB,CAAC,CACtFyD,IAAI,CAAEC,QAAQ,IAAK;MAClBC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEF,QAAQ,CAACG,MAAM,EAAEH,QAAQ,CAACI,IAAI,CAAC;IAC1D,CAAC,EAAGC,GAAG,IAAK;MACVJ,OAAO,CAACnD,KAAK,CAAC,qBAAqB,EAAEuD,GAAG,CAAC;IAC3C,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,sBAAsB,GAAIrB,CAAC,IAAK;IACpC,MAAMsB,IAAI,GAAGtB,CAAC,CAACuB,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAIF,IAAI,EAAE;MACR,MAAMG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACvBrD,aAAa,CAACmD,MAAM,CAACG,MAAM,CAAC,CAAC,CAAC;MAChC,CAAC;MACDH,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC,CAAC,CAAC;IAC9B;EACF,CAAC;EAED,MAAMQ,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACrE,KAAK,IAAI,CAACE,QAAQ,EAAE;MACvBG,QAAQ,CAAC,2BAA2B,CAAC;MACrC;IACF;IAEAU,YAAY,CAAC,IAAI,CAAC;IAClBV,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMiE,cAAc,GAAG,MAAMlG,8BAA8B,CAACoB,IAAI,EAAEQ,KAAK,EAAEE,QAAQ,CAAC;MAClF,MAAMI,GAAG,GAAGyC,WAAW,CAAC,CAAC;MACzBE,YAAY,CAACjD,KAAK,EAAEM,GAAG,CAAC;MACxBK,YAAY,CAAC,IAAI,CAAC;;MAElB;MACA,MAAM4D,OAAO,GAAGhG,GAAG,CAACkB,EAAE,EAAE,OAAO,EAAE6E,cAAc,CAACE,IAAI,CAACC,GAAG,CAAC;MACzD,MAAMjG,MAAM,CAAC+F,OAAO,EAAE;QACpBvE,KAAK,EAAEA,KAAK;QACZyE,GAAG,EAAEH,cAAc,CAACE,IAAI,CAACC,GAAG;QAC5BC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBC,EAAE,EAAEhE;MACN,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdC,QAAQ,CAACD,KAAK,CAACyE,OAAO,CAAC;IACzB,CAAC,SAAS;MACR9D,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM+D,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAIxE,GAAG,KAAKE,YAAY,CAACuE,QAAQ,CAAC,CAAC,EAAE;MACnCpD,kBAAkB,CAAC,IAAI,CAAC;MACxBqD,UAAU,CAAC,MAAM;QACfrE,YAAY,CAAC,KAAK,CAAC;QACnBJ,MAAM,CAAC,EAAE,CAAC;QACVU,eAAe,CAAC,KAAK,CAAC;QACtBZ,QAAQ,CAAC,EAAE,CAAC;QACZsB,kBAAkB,CAAC,KAAK,CAAC;QACzBsD,KAAK,CAAC,2DAA2D,CAAC;MACpE,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM;MACL5E,QAAQ,CAAC,gCAAgC,CAAC;IAC5C;EACF,CAAC;EAED,MAAM6E,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAAClF,KAAK,IAAI,CAACE,QAAQ,EAAE;MACvBG,QAAQ,CAAC,2BAA2B,CAAC;MACrC;IACF;IAEAU,YAAY,CAAC,IAAI,CAAC;IAClBV,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMiE,cAAc,GAAG,MAAMjG,0BAA0B,CAACmB,IAAI,EAAEQ,KAAK,EAAEE,QAAQ,CAAC;MAC9EJ,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,QAAQ,CAACD,KAAK,CAACyE,OAAO,CAAC;IACzB,CAAC,SAAS;MACR9D,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,oBACEnC,OAAA;IAAKuG,KAAK,EAAE;MACV,GAAGzG,MAAM,CAAC0G,iBAAiB;MAC3BC,UAAU,EAAE;AAClB,kCAAkC3G,MAAM,CAAC4G,YAAY,CAACC,OAAO,UAAU7G,MAAM,CAAC4G,YAAY,CAACE,MAAM,WAAW9G,MAAM,CAAC4G,YAAY,CAACG,YAAY,WAAW/G,MAAM,CAAC4G,YAAY,CAACI,SAAS,WAAWhH,MAAM,CAAC4G,YAAY,CAACC,OAAO;AAC1N,oCAAoCjE,aAAa,CAACE,CAAC,KAAKF,aAAa,CAACG,CAAC,MAAM/C,MAAM,CAAC4G,YAAY,CAACC,OAAO;AACxG,6CAA6C7G,MAAM,CAAC4G,YAAY,CAACE,MAAM;AACvE,6CAA6C9G,MAAM,CAAC4G,YAAY,CAACG,YAAY;AAC7E;IACI,CAAE;IAAAE,QAAA,gBAEA/G,OAAA;MAAKuG,KAAK,EAAE;QACV,GAAGzG,MAAM,CAACkH,uBAAuB;QACjCC,SAAS,EAAE,aAAavE,aAAa,CAACE,CAAC,GAAG,IAAI,OAAOF,aAAa,CAACG,CAAC,GAAG,IAAI;MAC7E;IAAE;MAAAqE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAGR7E,SAAS,CAAC8E,GAAG,CAAEC,QAAQ,iBACtBvH,OAAA;MAEEuG,KAAK,EAAE;QACLiB,QAAQ,EAAE,UAAU;QACpBC,IAAI,EAAE,GAAGF,QAAQ,CAAC3E,CAAC,GAAG;QACtB8E,GAAG,EAAE,GAAGH,QAAQ,CAAC1E,CAAC,GAAG;QACrB8E,KAAK,EAAE,GAAGJ,QAAQ,CAAChE,IAAI,IAAI;QAC3BqE,MAAM,EAAE,GAAGL,QAAQ,CAAChE,IAAI,IAAI;QAC5BkD,UAAU,EAAE3G,MAAM,CAAC4G,YAAY,CAACC,OAAO;QACvCkB,YAAY,EAAE,KAAK;QACnBpE,OAAO,EAAE8D,QAAQ,CAAC9D,OAAO;QACzBqE,SAAS,EAAE,gBAAiBP,QAAQ,CAACnE,EAAE,GAAG,CAAC,GAAI,CAAC,IAAI,EAAE,GAAGmE,QAAQ,CAAC/D,KAAK,GAAG,CAAC,mBAAmB;QAC9FuE,MAAM,EAAE,CAAC;QACTC,aAAa,EAAE;MACjB;IAAE,GAbGT,QAAQ,CAACnE,EAAE;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAcjB,CACF,CAAC,EAGDvE,eAAe,iBACd9C,OAAA;MAAKuG,KAAK,EAAE;QACViB,QAAQ,EAAE,OAAO;QACjBE,GAAG,EAAE,CAAC;QACND,IAAI,EAAE,CAAC;QACPQ,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTF,aAAa,EAAE,MAAM;QACrBD,MAAM,EAAE;MACV,CAAE;MAAAhB,QAAA,EACC,CAAC,GAAGoB,KAAK,CAAC,EAAE,CAAC,CAAC,CAACb,GAAG,CAAC,CAACc,CAAC,EAAElF,CAAC,kBACvBlD,OAAA;QAEEuG,KAAK,EAAE;UACLiB,QAAQ,EAAE,UAAU;UACpBC,IAAI,EAAE,GAAGpE,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;UAC/BoE,GAAG,EAAE,GAAGrE,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;UAC9B+E,QAAQ,EAAE,MAAM;UAChBP,SAAS,EAAE,YAAY,CAAC,GAAGzE,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,qBAAqB;UACjEgF,cAAc,EAAE,GAAGjF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;QACxC,CAAE;QAAAyD,QAAA,EAED,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC1D,IAAI,CAACe,KAAK,CAACf,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;MAAC,GAVxDJ,CAAC;QAAAgE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAWH,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,eAGDrH,OAAA;MAAKuG,KAAK,EAAEzG,MAAM,CAACyI,mBAAoB;MAAAxB,QAAA,gBACrC/G,OAAA;QACEwI,GAAG,EAAEC,OAAO,CAAC,kBAAkB,CAAE;QACjCC,GAAG,EAAC,cAAc;QAClBnC,KAAK,EAAEzG,MAAM,CAAC6I;MAAiB;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eACFrH,OAAA;QAAIuG,KAAK,EAAEzG,MAAM,CAAC8I,iBAAkB;QAAA7B,QAAA,EAAC;MAAQ;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClDrH,OAAA;QAAGuG,KAAK,EAAEzG,MAAM,CAAC+I,oBAAqB;QAAA9B,QAAA,EAAC;MAA0B;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACrErH,OAAA;QAAKuG,KAAK,EAAEzG,MAAM,CAACgJ,oBAAqB;QAAA/B,QAAA,gBACtC/G,OAAA;UAAKuG,KAAK,EAAEzG,MAAM,CAACiJ,mBAAoB;UAAAhC,QAAA,gBACrC/G,OAAA;YAAA+G,QAAA,EAAM;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACfrH,OAAA;YAAA+G,QAAA,EAAM;UAAqB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACNrH,OAAA;UAAKuG,KAAK,EAAEzG,MAAM,CAACiJ,mBAAoB;UAAAhC,QAAA,gBACrC/G,OAAA;YAAA+G,QAAA,EAAM;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACfrH,OAAA;YAAA+G,QAAA,EAAM;UAAqB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACNrH,OAAA;UAAKuG,KAAK,EAAEzG,MAAM,CAACiJ,mBAAoB;UAAAhC,QAAA,gBACrC/G,OAAA;YAAA+G,QAAA,EAAM;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACfrH,OAAA;YAAA+G,QAAA,EAAM;UAAiB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACNrH,OAAA;UAAKuG,KAAK,EAAEzG,MAAM,CAACiJ,mBAAoB;UAAAhC,QAAA,gBACrC/G,OAAA;YAAA+G,QAAA,EAAM;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACfrH,OAAA;YAAA+G,QAAA,EAAM;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrH,OAAA;MAAKuG,KAAK,EAAEzG,MAAM,CAACkJ,kBAAmB;MAAAjC,QAAA,gBACpC/G,OAAA;QAAIuG,KAAK,EAAEzG,MAAM,CAACmJ,gBAAiB;QAAAlC,QAAA,EAAC;MAAoB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7DrH,OAAA;QAAGuG,KAAK,EAAEzG,MAAM,CAACoJ,eAAgB;QAAAnC,QAAA,EAAC;MAElC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJrH,OAAA;QAAKuG,KAAK,EAAEzG,MAAM,CAACqJ,gBAAiB;QAAApC,QAAA,gBAClC/G,OAAA;UAAKuG,KAAK,EAAEzG,MAAM,CAACsJ,eAAgB;UAAArC,QAAA,gBACjC/G,OAAA;YAAA+G,QAAA,EAAM;UAAC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACdrH,OAAA;YAAA+G,QAAA,EAAM;UAAiB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACNrH,OAAA;UAAKuG,KAAK,EAAEzG,MAAM,CAACsJ,eAAgB;UAAArC,QAAA,gBACjC/G,OAAA;YAAA+G,QAAA,EAAM;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACfrH,OAAA;YAAA+G,QAAA,EAAM;UAAwB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACNrH,OAAA;UAAKuG,KAAK,EAAEzG,MAAM,CAACsJ,eAAgB;UAAArC,QAAA,gBACjC/G,OAAA;YAAA+G,QAAA,EAAM;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACfrH,OAAA;YAAA+G,QAAA,EAAM;UAAoB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACNrH,OAAA;UAAKuG,KAAK,EAAEzG,MAAM,CAACsJ,eAAgB;UAAArC,QAAA,gBACjC/G,OAAA;YAAA+G,QAAA,EAAM;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACfrH,OAAA;YAAA+G,QAAA,EAAM;UAAqB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrH,OAAA;MAAKuG,KAAK,EAAEzG,MAAM,CAACuJ,iBAAkB;MAAAtC,QAAA,gBACnC/G,OAAA;QAAIuG,KAAK,EAAEzG,MAAM,CAACwJ,aAAc;QAAAvC,QAAA,EAC7B3E,YAAY,GAAG,kBAAkB,GAAG;MAAiB;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eACLrH,OAAA;QAAGuG,KAAK,EAAEzG,MAAM,CAACyJ,gBAAiB;QAAAxC,QAAA,EAC/B3E,YAAY,GACT,8DAA8D,GAC9D;MAAgD;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEnD,CAAC,EAGHjF,YAAY,IAAI,CAACN,SAAS,iBACzB9B,OAAA;QAAKuG,KAAK,EAAE;UACViD,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,QAAQ;UACxBC,YAAY,EAAE,MAAM;UACpBC,GAAG,EAAE;QACP,CAAE;QAAA5C,QAAA,gBACA/G,OAAA;UAAKuG,KAAK,EAAE;YACVoB,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,KAAK;YACnBpB,UAAU,EAAErF,KAAK,IAAIE,QAAQ,GAAGxB,MAAM,CAAC4G,YAAY,CAACC,OAAO,GAAG7G,MAAM,CAAC4G,YAAY,CAACkD,MAAM;YACxFC,UAAU,EAAE,eAAe;YAC3B/B,SAAS,EAAE1G,KAAK,IAAIE,QAAQ,GAAG,mBAAmB,GAAG;UACvD;QAAE;UAAA4F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACTrH,OAAA;UAAKuG,KAAK,EAAE;YACVoB,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,KAAK;YACnBpB,UAAU,EAAEzE,UAAU,GAAGlC,MAAM,CAAC4G,YAAY,CAACC,OAAO,GAAG7G,MAAM,CAAC4G,YAAY,CAACkD,MAAM;YACjFC,UAAU,EAAE,eAAe;YAC3B/B,SAAS,EAAE9F,UAAU,GAAG,mBAAmB,GAAG;UAChD;QAAE;UAAAkF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACTrH,OAAA;UAAKuG,KAAK,EAAE;YACVoB,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,KAAK;YACnBpB,UAAU,EAAE3G,MAAM,CAAC4G,YAAY,CAACkD,MAAM;YACtCC,UAAU,EAAE;UACd;QAAE;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAEA,CAACvF,SAAS,gBACT9B,OAAA,CAAAE,SAAA;QAAA6G,QAAA,gBAEE/G,OAAA;UAAKuG,KAAK,EAAEzG,MAAM,CAACgK,cAAe;UAAA/C,QAAA,eAChC/G,OAAA;YAAKuG,KAAK,EAAE;cAAEiB,QAAQ,EAAE;YAAW,CAAE;YAAAT,QAAA,gBACnC/G,OAAA;cAAMuG,KAAK,EAAE;gBACXiB,QAAQ,EAAE,UAAU;gBACpBC,IAAI,EAAE,SAAS;gBACfC,GAAG,EAAE,KAAK;gBACVT,SAAS,EAAE,kBAAkB;gBAC7BoB,QAAQ,EAAE,QAAQ;gBAClB0B,KAAK,EAAEjK,MAAM,CAAC4G,YAAY,CAACsD,SAAS;gBACpCjC,MAAM,EAAE;cACV,CAAE;cAAAhB,QAAA,EAAC;YAEH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPrH,OAAA;cACEiK,IAAI,EAAC,OAAO;cACZC,KAAK,EAAE9I,KAAM;cACb+I,QAAQ,EAAGxG,CAAC,IAAKtC,QAAQ,CAACsC,CAAC,CAACuB,MAAM,CAACgF,KAAK,CAAE;cAC1CE,WAAW,EAAC,0BAA0B;cACtC7D,KAAK,EAAE;gBACL,GAAGzG,MAAM,CAACuK,SAAS;gBACnBC,WAAW,EAAE;cACf,CAAE;cACFC,QAAQ,EAAErI;YAAU;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNrH,OAAA;UAAKuG,KAAK,EAAEzG,MAAM,CAACgK,cAAe;UAAA/C,QAAA,eAChC/G,OAAA;YAAKuG,KAAK,EAAE;cAAEiB,QAAQ,EAAE;YAAW,CAAE;YAAAT,QAAA,gBACnC/G,OAAA;cAAMuG,KAAK,EAAE;gBACXiB,QAAQ,EAAE,UAAU;gBACpBC,IAAI,EAAE,SAAS;gBACfC,GAAG,EAAE,KAAK;gBACVT,SAAS,EAAE,kBAAkB;gBAC7BoB,QAAQ,EAAE,QAAQ;gBAClB0B,KAAK,EAAEjK,MAAM,CAAC4G,YAAY,CAACsD,SAAS;gBACpCjC,MAAM,EAAE;cACV,CAAE;cAAAhB,QAAA,EAAC;YAEH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPrH,OAAA;cACEiK,IAAI,EAAE3H,YAAY,GAAG,MAAM,GAAG,UAAW;cACzC4H,KAAK,EAAE5I,QAAS;cAChB6I,QAAQ,EAAGxG,CAAC,IAAKpC,WAAW,CAACoC,CAAC,CAACuB,MAAM,CAACgF,KAAK,CAAE;cAC7CE,WAAW,EAAC,qBAAqB;cACjC7D,KAAK,EAAE;gBACL,GAAGzG,MAAM,CAACuK,SAAS;gBACnBC,WAAW,EAAE,QAAQ;gBACrBE,YAAY,EAAE;cAChB,CAAE;cACFD,QAAQ,EAAErI;YAAU;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACFrH,OAAA;cACEiK,IAAI,EAAC,QAAQ;cACbQ,OAAO,EAAEA,CAAA,KAAMlI,eAAe,CAAC,CAACD,YAAY,CAAE;cAC9CiE,KAAK,EAAE;gBACLiB,QAAQ,EAAE,UAAU;gBACpBS,KAAK,EAAE,SAAS;gBAChBP,GAAG,EAAE,KAAK;gBACVT,SAAS,EAAE,kBAAkB;gBAC7BR,UAAU,EAAE,MAAM;gBAClBmD,MAAM,EAAE,MAAM;gBACdvB,QAAQ,EAAE,QAAQ;gBAClBqC,MAAM,EAAE,SAAS;gBACjBX,KAAK,EAAEjK,MAAM,CAAC4G,YAAY,CAACsD,SAAS;gBACpCjC,MAAM,EAAE;cACV,CAAE;cACFwC,QAAQ,EAAErI,SAAU;cAAA6E,QAAA,EAEnBzE,YAAY,GAAG,IAAI,GAAG;YAAK;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLjF,YAAY,iBACXpC,OAAA;UAAKuG,KAAK,EAAEzG,MAAM,CAACgK,cAAe;UAAA/C,QAAA,eAChC/G,OAAA;YAAKuG,KAAK,EAAE;cAAEoE,SAAS,EAAE;YAAS,CAAE;YAAA5D,QAAA,gBAClC/G,OAAA;cAAOuG,KAAK,EAAE;gBACZiD,OAAO,EAAE,OAAO;gBAChBoB,OAAO,EAAE,QAAQ;gBACjBhB,MAAM,EAAE,cAAc9J,MAAM,CAAC4G,YAAY,CAACC,OAAO,EAAE;gBACnDkB,YAAY,EAAE,MAAM;gBACpBpB,UAAU,EAAE,0BAA0B;gBACtCiE,MAAM,EAAE,SAAS;gBACjBb,UAAU,EAAE,eAAe;gBAC3B,QAAQ,EAAE;kBACRpD,UAAU,EAAE,0BAA0B;kBACtCQ,SAAS,EAAE;gBACb;cACF,CAAE;cAAAF,QAAA,gBACA/G,OAAA;gBAAMuG,KAAK,EAAE;kBAAE8B,QAAQ,EAAE,MAAM;kBAAEmB,OAAO,EAAE,OAAO;kBAAEE,YAAY,EAAE;gBAAS,CAAE;gBAAA3C,QAAA,EAAC;cAE7E;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPrH,OAAA;gBAAMuG,KAAK,EAAE;kBAAEwD,KAAK,EAAEjK,MAAM,CAAC4G,YAAY,CAACsD,SAAS;kBAAE3B,QAAQ,EAAE;gBAAS,CAAE;gBAAAtB,QAAA,EACvE/E,UAAU,GAAG,wBAAwB,GAAG;cAAwB;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACPrH,OAAA;gBACEiK,IAAI,EAAC,MAAM;gBACXE,QAAQ,EAAEnF,sBAAuB;gBACjC6F,MAAM,EAAC,SAAS;gBAChBtE,KAAK,EAAE;kBAAEiD,OAAO,EAAE;gBAAO,CAAE;gBAC3Be,QAAQ,EAAErI;cAAU;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,EACPrF,UAAU,iBACThC,OAAA;cAAKuG,KAAK,EAAE;gBAAEuE,SAAS,EAAE,MAAM;gBAAEtD,QAAQ,EAAE,UAAU;gBAAEgC,OAAO,EAAE;cAAe,CAAE;cAAAzC,QAAA,gBAC/E/G,OAAA;gBACEwI,GAAG,EAAExG,UAAW;gBAChB0G,GAAG,EAAC,iBAAiB;gBACrBnC,KAAK,EAAE;kBACL,GAAGzG,MAAM,CAACiL,kBAAkB;kBAC5BjD,SAAS,EAAE;gBACb;cAAE;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFrH,OAAA;gBAAKuG,KAAK,EAAE;kBACViB,QAAQ,EAAE,UAAU;kBACpBE,GAAG,EAAE,MAAM;kBACXO,KAAK,EAAE,MAAM;kBACbxB,UAAU,EAAE3G,MAAM,CAAC4G,YAAY,CAACC,OAAO;kBACvCoD,KAAK,EAAE,OAAO;kBACdlC,YAAY,EAAE,KAAK;kBACnBF,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACd4B,OAAO,EAAE,MAAM;kBACfwB,UAAU,EAAE,QAAQ;kBACpBvB,cAAc,EAAE,QAAQ;kBACxBpB,QAAQ,EAAE,QAAQ;kBAClBP,SAAS,EAAE;gBACb,CAAE;gBAAAf,QAAA,EAAC;cAEH;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDrH,OAAA;UAAKuG,KAAK,EAAEzG,MAAM,CAACmL,eAAgB;UAAAlE,QAAA,EAChC3E,YAAY,gBACXpC,OAAA,CAAAE,SAAA;YAAA6G,QAAA,gBACE/G,OAAA;cACEyK,OAAO,EAAEhF,YAAa;cACtB8E,QAAQ,EAAErI,SAAU;cACpBqE,KAAK,EAAEzG,MAAM,CAACoL,iBAAkB;cAAAnE,QAAA,GAE/B7E,SAAS,iBAAIlC,OAAA;gBAAKuG,KAAK,EAAEzG,MAAM,CAACqL;cAAmB;gBAAAjE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAC1DnF,SAAS,GAAG,sBAAsB,GAAG,kBAAkB;YAAA;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACTrH,OAAA;cACEyK,OAAO,EAAEA,CAAA,KAAMpI,eAAe,CAAC,KAAK,CAAE;cACtCkI,QAAQ,EAAErI,SAAU;cACpBqE,KAAK,EAAEzG,MAAM,CAACsL,mBAAoB;cAAArE,QAAA,EACnC;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT,CAAC,gBAEHrH,OAAA,CAAAE,SAAA;YAAA6G,QAAA,gBACE/G,OAAA;cACEyK,OAAO,EAAEnE,WAAY;cACrBiE,QAAQ,EAAErI,SAAU;cACpBqE,KAAK,EAAEzG,MAAM,CAACoL,iBAAkB;cAAAnE,QAAA,GAE/B7E,SAAS,iBAAIlC,OAAA;gBAAKuG,KAAK,EAAEzG,MAAM,CAACqL;cAAmB;gBAAAjE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAC1DnF,SAAS,GAAG,kBAAkB,GAAG,YAAY;YAAA;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACTrH,OAAA;cACEyK,OAAO,EAAEA,CAAA,KAAMpI,eAAe,CAAC,IAAI,CAAE;cACrCkI,QAAQ,EAAErI,SAAU;cACpBqE,KAAK,EAAEzG,MAAM,CAACsL,mBAAoB;cAAArE,QAAA,EACnC;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA,eACN,CAAC;MAAA;MAEH;MACArH,OAAA;QAAKuG,KAAK,EAAEzG,MAAM,CAACuL,gBAAiB;QAAAtE,QAAA,gBAClC/G,OAAA;UAAKuG,KAAK,EAAE;YAAEoE,SAAS,EAAE,QAAQ;YAAEjB,YAAY,EAAE;UAAS,CAAE;UAAA3C,QAAA,gBAC1D/G,OAAA;YAAKuG,KAAK,EAAE;cACV8B,QAAQ,EAAE,MAAM;cAChBqB,YAAY,EAAE,MAAM;cACpB5B,SAAS,EAAE;YACb,CAAE;YAAAf,QAAA,EAAC;UAEH;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNrH,OAAA;YAAIuG,KAAK,EAAEzG,MAAM,CAACwL,YAAa;YAAAvE,QAAA,EAAC;UAAoB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzDrH,OAAA;YAAGuG,KAAK,EAAE;cAAEwD,KAAK,EAAEjK,MAAM,CAAC4G,YAAY,CAACsD,SAAS;cAAEN,YAAY,EAAE;YAAO,CAAE;YAAA3C,QAAA,GAAC,6CAClC,eAAA/G,OAAA;cAAAkH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5CrH,OAAA;cAAQuG,KAAK,EAAE;gBAAEwD,KAAK,EAAEjK,MAAM,CAAC4G,YAAY,CAACC;cAAQ,CAAE;cAAAI,QAAA,EAAE3F;YAAK;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNrH,OAAA;UAAKuG,KAAK,EAAE;YAAEiB,QAAQ,EAAE;UAAW,CAAE;UAAAT,QAAA,gBACnC/G,OAAA;YAAMuG,KAAK,EAAE;cACXiB,QAAQ,EAAE,UAAU;cACpBC,IAAI,EAAE,MAAM;cACZC,GAAG,EAAE,KAAK;cACVT,SAAS,EAAE,kBAAkB;cAC7BoB,QAAQ,EAAE,QAAQ;cAClB0B,KAAK,EAAEjK,MAAM,CAAC4G,YAAY,CAACsD,SAAS;cACpCjC,MAAM,EAAE;YACV,CAAE;YAAAhB,QAAA,EAAC;UAEH;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPrH,OAAA;YACEiK,IAAI,EAAC,MAAM;YACXC,KAAK,EAAExI,GAAI;YACXyI,QAAQ,EAAGxG,CAAC,IAAKhC,MAAM,CAACgC,CAAC,CAACuB,MAAM,CAACgF,KAAK,CAAE;YACxCE,WAAW,EAAC,oBAAoB;YAChCmB,SAAS,EAAC,GAAG;YACbhF,KAAK,EAAE;cACL,GAAGzG,MAAM,CAAC0L,YAAY;cACtBlB,WAAW,EAAE;YACf;UAAE;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNrH,OAAA;UACEyK,OAAO,EAAEvE,qBAAsB;UAC/BK,KAAK,EAAE;YACL,GAAGzG,MAAM,CAAC2L,aAAa;YACvBhF,UAAU,EAAE3G,MAAM,CAAC4G,YAAY,CAACgF,QAAQ;YACxC5D,SAAS,EAAEpG,GAAG,CAACiK,MAAM,KAAK,CAAC,GAAG,yBAAyB,GAAG;UAC5D,CAAE;UAAA5E,QAAA,EACH;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGA7F,KAAK,iBACJxB,OAAA;QAAKuG,KAAK,EAAE;UACV,GAAGzG,MAAM,CAAC8L,gBAAgB;UAC1B9D,SAAS,EAAE;QACb,CAAE;QAAAf,QAAA,gBACA/G,OAAA;UAAMuG,KAAK,EAAE;YAAEsF,WAAW,EAAE;UAAS,CAAE;UAAA9E,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAChD7F,KAAK;MAAA;QAAA0F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAjG,KAAK,IAAIE,QAAQ,IAAI,CAACE,KAAK,iBAC1BxB,OAAA;QAAKuG,KAAK,EAAE;UACVoE,SAAS,EAAE,QAAQ;UACnBG,SAAS,EAAE,MAAM;UACjBf,KAAK,EAAEjK,MAAM,CAAC4G,YAAY,CAACC,OAAO;UAClC0B,QAAQ,EAAE,QAAQ;UAClBP,SAAS,EAAE;QACb,CAAE;QAAAf,QAAA,GAAC,kBACU,EAAC3E,YAAY,GAAG,qBAAqB,GAAG,SAAS,EAAC,GAC/D;MAAA;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClG,EAAA,CA3jBIF,QAAQ;AAAA6K,EAAA,GAAR7K,QAAQ;AA6jBd,eAAeA,QAAQ;AAAC,IAAA6K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}